# cursorrules 规则- 让 cursor 了解当前项目

# 前置
- 你是一个前端开发专家, 专注于现代化网络开发

# 项目信息
project_name: Web Application
description: 基于 Nuxt 3 + Nuxt UI 的现代化网页应用

# 技术架构
framework: 
  - Nuxt 3
  - Nuxt UI
  - Vue 3
  - pinia

node_modules:
  - 使用的是 pnpm
language: 
  - TypeScript
  - JavaScript
ui_components: 
  - Nuxt UI 组件库
  - 自定义 Vue 组件

# 目录结构
file_structure:
  - web/                      # 项目根目录
    - app/                    # 应用主目录
      - assets/              # 静态资源
        - images/            # 图片资源
        - styles/            # 样式文件
      - components/          # 公共组件
      - composables/         # 组合式函数
      - layouts/             # 布局组件
      - pages/               # 页面组件（自动路由）
        - text/
          - components/      # 文本编辑相关组件
            - TextareaText.vue
      - plugins/             # Nuxt 插件
      - server/              # 服务端接口
      - utils/               # 工具函数
    - public/                # 公共静态资源
    - tests/                 # 测试文件目录
    - .cursorrules          # Cursor 配置
    - .cursorignore         # Cursor 忽略文件
    - .editorconfig         # 编辑器配置
    - nuxt.config.ts        # Nuxt 配置文件
    - app.config.ts         # 应用配置文件

# 开发规范
## 代码风格
- 遵循 .editorconfig 配置
- 使用 Nuxt 3 最佳实践
- 优先使用 Nuxt UI 组件
- 基于组件化架构开发
- 使用组合式 API (Composition API)
- 注意不要使用 @apply

## 忽略文件
- .nuxt 构建目录
- node_modules 依赖目录
- 环境配置文件
- 构建产物

## 项目结构规范
- 遵循 Nuxt 3 目录约定
- 按功能/页面组织组件
- 保持组件可复用性
- 使用 TypeScript 类型定义

## 代码质量要求
- 保持一致的代码格式
- 编写清晰的组件文档
- 遵循 Vue 3 代码风格指南
- 确保 TypeScript 类型安全
