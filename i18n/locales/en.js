// 英语
export default {
  common: {
    site_name: 'SelectTranslate', // 精挑翻译
    title: 'SelectTranslate - AI-selected Translation, Bilingual Webpage Translation', // SelectTranslate - AI 择优翻译、双语对照网页翻译
    description:
      'SelectTranslate is a free browser translation extension that offers a variety of features, including AI-selected Translation, Bilingual Webpage Translation, Input Translation, Text Translation, Mouse Hover Translation, Highlight Translation, and PDF Document Translation. It supports multiple AI translation models, such as Microsoft Translator, Google Translate, DeepSeek, Qwen, DeepL, OpenAI (ChatGPT), Gemini and Claude. A translation tool that improves work and study efficiency, providing you with a translation experience that is good, accurate, and fits the target context.',
    page_does_not_exist: 'Page does not exist', // 页面不存在
    page_does_not_exist_tip: 'Sorry! The page you are trying to access does not exist', // 抱歉！您要访问的页面不存在
    back_to_home_page: 'Back to home page', // 返回首页
    confirm: 'Confirm', // 确认
    cancel: 'Cancel', // 取消
    ai_selected: 'AI-selected', // AI 择优
    translation_results: 'Results', // 翻译结果
    engins: {
      google: 'Google', // 谷歌翻译
      microsoft: 'Microsoft', // 微软翻译
      transmart: 'TranSmart', // 腾讯交互翻译
      deepl: 'DeepL', // DeepL 翻译
      openai: 'OpenAI',
      gemini: 'Gemini',
      claude: 'Claude',
      zhipu: 'GLM',
      qwen: 'Qwen',
      lingyiwanwu: '01.AI',
      deepseek: 'DeepSeek',
      doubao: 'Doubao',
      grok: 'Grok',
      yandex: 'Yandex'
    },
    close: 'Close',
    model_unavailable: 'Unavailable, Please reselect', // 模型不可用，请重新选择
    error_tip: 'Alert messages', // 提示
    swipe_to_see_more: 'Swipe left and right to see more', // 左右滑动查看更多
    loading: 'Loading...', // 加载中...
    retry: 'Retry'
  },
  theme: {
    light: 'Light',
    dark: 'Dark',
    system: 'System'
  },
  header: {
    // 页头
    home: 'Home', // 首页
    pricing: 'Pricing', // 价格
    docs: 'Tutorial', // 教程
    selected_translation: 'AI-selected Translation', // AI 择优翻译
    file_translation: 'Document Translation', // 文件翻译
    account: 'Account', // 账户
    billing: 'Billing', // 账单
    pdf_plus: 'PDF Plus',
    feedback: 'Feedback', // 问题反馈
    signup: 'Sign up', // 注册(报名)
    login: 'Log in', // 登录
    logout: 'Log out' // 退出
  },
  auth: {
    // 用户授权认证模块：注册、激活、登录、重置密码
    common: {
      home: 'Home', // 首页
      email_label: 'Email address', // 邮箱地址
      password_label: 'Password', // 密码
      verify_code_label: 'Verification code', // 验证码
      verify_code_send_label: 'Send verification code', // 获取验证码
      verify_code_has_been_send: 'Verification code has been send', // 验证码已发送
      verify_code_countdown_retry: 'Retry after {seconds} seconds', // {seconds}秒后重试
      resend: 'Resend', // 重新发送
      no_account_yet: 'No account yet?', // 还没有账号？
      have_an_account: 'Already have an account? ', // 已经有账号?
      or_login_using: 'Or', // 或
      login_with_wechat: 'Login with WeChat account', // 使用微信账号登录
      login_with_google: 'Login with Google account', // 使用 Google 账号登录
      welcome_to_login: 'Welcome to login', // 欢迎登录
      confirm_agreement: 'By signing up, you agree to', // 注册即表示您同意
      terms_of_service: 'Terms Of Service', // 服务条款
      and: 'and', // 和
      privacy_policy: 'Privacy Policy', // 隐私政策
      messages: {
        email_required: 'Please enter your e-mail address', // 请输入邮箱地址
        email_format_incorrect: 'Please enter the correct e-mail address', // 请输入正确的邮箱地址
        email_confirm_success: 'E-mail address has been confirmed, please login', // 验证成功，请登录
        password_required: 'Please enter your password', // 请输入密码
        password_format_incorrect_1: 'Must consist of letters, numbers or symbols', // 密码必须是字母、数字或符号组成
        password_format_incorrect_2: 'Length is 6 to 20 characters', // 密码长度为6到20个字符
        verify_code_required: 'Please enter the e-mail verification code', // 请输入邮件验证码
        verify_code_email_send_tip_1: 'A verification code has been sent to your e-mail address【', // 已向您的邮箱地址发送了验证码【
        verify_code_email_send_tip_2: '】, please check your e-mail!', // 】请查收邮件！
        new_password_required: 'Please set a new password', // 请设置新密码
        reset_password_success: 'Reset password success' // 重置密码成功
      }
    },
    signup: {
      title: 'Create an account' // 注册账号
    },
    signupconfirm: {
      title: 'Verify email address' // 验证邮箱地址
    },
    login: {
      title: 'Login' // 登录
    },
    reset_password: {
      title: 'Reset password', // 重设密码
      new_password_label: 'New password', // 新密码
      new_password_placeholder: 'Set a new password' // 设置新密码
    }
  },
  account: {
    account: 'Account', // 账户
    plan: 'Plan', // 套餐
    email_address: 'Email address', // 邮箱地址
    wechat_nickname: 'WeChat nickname', // 微信昵称
    google_nickname: 'Google nickname', // Google 昵称
    current_plan: 'Current Plan', // 当前套餐
    free_version: 'Free', // 免费版
    trial_version: 'Trial', // 试用版
    free_trial_plus: 'Free trial of Plus plan', // 免费试用 Plus 会员
    apply_for_free_trial_plus_tip_1: 'You can apply for a free trial of Plus plan for【 ', // 您可免费申请试用 Plus 会员【
    apply_for_free_trial_plus_tip_2: ' 】 days!', // X 】天！
    apply_for_free_trial_plus_tip_3: 'Confirm to apply?', // 确认要申请吗？
    upgrade_to_plus: 'Upgrade to Plus', // 升级为 Plus 会员
    upgrade_to_official_version_plus: 'Upgrade to Plus', // 升级为正式版 Plus 会员
    subscription_billing_time_tip1: 'Your Plus plan subscription will automatically be charged and activated on', // 订阅扣费 提示1
    subscription_billing_time_tip2: 'after the trial period ends.', // 订阅扣费 提示2
    renewal_or_subscribe: 'Renewal / Subscribe', // 续费 / 订阅
    monthly_subscription: 'Monthly subscription', // 按月订阅
    yearly_subscription: 'Annual subscription', // 按年订阅
    next_renewal_time: 'Next renewal time', // 下次续费时间
    unsubscribe: 'Unsubscribe', // 取消订阅
    unsubscribe_dialog_title: 'Cancel your subscription', // 取消您的订阅
    unsubscribe_dialog_content_1: 'The plan service you are currently subscribing to will expire on ', // 您当前订阅的套餐服务将于
    unsubscribe_dialog_content_2: '. After canceling the subscription, you can still continue to use it before the end of the service period.', // 取消订阅后，仍可在服务期结束前继续使用。
    unsubscribe_dialog_content_3: 'If you are sure you want to unsubscribe, please click the [Unsubscribe] button.', // 如果您确认要取消订阅，请点击【取消订阅】按钮。
    unsubscribe_dialog_content_4: 'If you want to keep your current subscription, please click the [Back] button.', // 如果您想继续保留当前订阅，请点击【返回】按钮。
    unsubscribe_dialog_btn_back: 'Back', // 返回
    unsubscribe_success_info: 'You have successfully unsubscribed', // 您已成功取消订阅
    unsubscribe_failed_info: 'Unsubscription failed, please try again later', // 取消订阅失败，请稍后再试
    usage_of_trial_plan_quota: 'Usage of trial plan quota', // 试用餐额度使用情况
    usage_of_this_month_plan_quota: 'Usage of this month\'s plan quota', // 本月套餐额度使用情况
    remaining_translation_quota: 'Remaining translation quota', // 翻译额度剩余
    pdf_plus_pages_remaining: 'PDF Plus pages remaining', // PDF Plus 页数剩余
    pages: 'pages', // 页
    quota_reset_time: 'Next quota reset time', // 下次额度重置时间
    plan_will_expire_on_1: 'plan service will expire on', // 套餐服务将于
    plan_will_expire_on_2: '', // 到期
    plan_expired_on_1: 'plan service expired on', // 套餐服务已于
    plan_expired_on_2: '', // 到期
    usage_of_add_on_quota: 'Usage of add-on quota', // 加量包额度使用情况
    purchase_translation_quota_add_on_package: 'Purchase TQ add-on package', // 购买翻译额度加量包
    purchase_pdf_plus_add_on_package: 'Purchase PDF Plus add-on package', // 购买 PDF Plus 加量包
    purchase_add_on_package_tips: 'The add-on quota can only be purchased and used during the validity period of Plus membership.', // 加量包额度仅限 Plus 会员有效期内购买和使用
    not_logged_in: 'Not logged in', // 未登录
    not_logged_in_tip: 'Please log in first', // 请先登录
    login: 'Login', // 登录
    subscription_renewal_failed: 'Subscription renewal failed', // 订阅续费失败
    subscription_renewal_failed_tip_1:
      'Your Plus membership renewal was unsuccessful due to insufficient balance on your payment card. Please recharge and try subscribing again. If you have any questions, please contact us for support via email at {email}.', // 您的 Plus 会员续费未成功，因您的支付卡余额不足。请在充值后重新尝试订阅，如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。
    subscription_renewal_failed_tip_2:
      'Your Plus membership renewal was unsuccessful because your payment card has expired. Please try subscribing again after replacing your payment card. If you have any questions, please contact us for support via email at {email}.', // 您的 Plus 会员续费未成功，因您的支付卡已过期。请在更换支付卡后重新尝试订阅，如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。
    subscription_renewal_failed_tip_3:
      ' The renewal payment for your Plus membership subscription has failed. The reason may be a bank block. Please try subscribing again after changing your payment card. If you have any questions, please contact us via email at {email} for support.' // 您的 Plus 会员订阅续费扣款失败，原因可能是银行拦截，请在更换支付卡后重新尝试订阅。 如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。
  },
  billing: {
    title: 'Billing', // 账单
    service_order: 'Service Orders', // 服务订单
    order_columns: {
      // 订单列表表头
      index: 'Index', // 序号
      pay_time: 'Payment Time', // 付款时间
      pay_order_no: 'Order No.', // 订单号
      product_name: 'Product Name', // 产品名称
      buy_mode: 'Service Period', // 服务周期
      amount: 'Amount', // 金额
      pay_way: 'Payment Method', // 支付方式
      service_begin_time: 'Service start time', // 服务开始时间
      service_end_time: 'Service end time' // 服务结束时间
    },
    product_names: {
      // 产品名称
      plus_name: 'Plus Membership', // Plus 会员
      general_token_name: 'Translation Quota Add-on Package', // 翻译额度加量包
      pdf_plus_name: 'PDF Plus Add-on Package', // PDF Plus 加量包
      pdf_plus: 'PDF Plus',
      translation_quota: 'Translation Quota', // 翻译额度
      quantity: 'Quantity', // 数量
      tatal: 'Total', // 总计
      pages: 'Pages' // 页(复数)
    },
    buy_modes: {
      // 购买模式
      try: 'Trial', // 申请试用
      weekly: 'Weekly', // 按周(购买/订阅)  (purchase/subscribe)
      monthly: 'Monthly', // 按月(购买/订阅)  (purchase/subscribe)
      quarterly: 'Quarterly', // 按季度(购买/订阅)  (purchase/subscribe)
      yearly: 'Annual', // 按年(购买/订阅)  (purchase/subscribe)
      perpetual: 'Permanently' // 永久
    },
    pay_ways: {
      // 支付方式
      platform_gift: 'Platform Gift', // 平台赠送
      wechat: 'WeChat Pay', // 微信支付
      alipay: 'Alipay', // 支付宝
      stripe: 'Stripe',
      coupon_code: 'Redemption code'
    },
    refund_order: {
      // 退款订单
      refund_success: 'Refunded', // 已退款
      refund_amount: 'Refund Amount', // 退款金额
      refund_time: 'Refund Time' // 退款时间
    }
  },
  home: {
    // 首页
    hero: {
      title: 'Smarter AI Translation Extension', // 精挑翻译 : 更智能的 AI 翻译插件！
      description_1: 'Supports commonly used translation functions such as bilingual web page translation, input translation, highlight translation, PDF translation, mouse hover translation, etc.',
      description_2:
        'Industry-first AI-selected translation function: Multi-model translation + AI scoring and selection, accurately presenting the most contextually appropriate professional-level translation results for you.',
      extension_pc_chrome_label: 'Chrome Extension', // Chrome 扩展
      extension_pc_edge_label: 'Edge Extension', // Edge 扩展
      extension_pc_safari_label: 'Safari Extension', // Safari 扩展
      extension_pc_firefox_label: 'Firefox Extension', // Firefox 扩展
      extension_pc_360browser_label: '360 Browser Extension', // 360浏览器扩展
      extension_pc_crx_label: 'crx Installer', // crx 安装包
      extension_pc_zip_label: 'zip Installer' // zip 安装包
    },
    scenarios: {
      // 应用场景
      social: {
        title: 'Social Interaction', // 社交互动
        description: 'Barrier-free reading and cross-language communication' // 无障碍阅读，跨语言交流
      },
      information: {
        title: 'Information Browsing', // 资讯浏览
        description: 'International news, real-time grasp' // 国际资讯，实时掌握
      },
      study: {
        title: 'Study and Research', // 学习研究
        description: 'Global knowledge, easily accessible' // 全球知识，轻松获取
      },
      document: {
        title: 'Document Translation', // 文档翻译
        description: 'Academic and office, convenient and efficient' // 学术、办公，便捷高效
      },
      international_trade: {
        title: 'International Trade', // 国际贸易
        description: 'Free communication, serving the world' // 自由沟通，服务全球
      }
    },
    engins: {
      title: 'Bringing together the world\'s top-tier AI translation models' // 汇聚全球顶级 AI 翻译模型
    },
    mission: {
      // 使命
      title: 'Advancing information equality and achieving native language freedom.', // 推动信息平权，实现母语自由
      description1:
        'The name "SelectTranslate" originates from our pioneering "AI-selected Translation" feature. Based on intelligence and application innovation, we are committed to continually developing better and more user-friendly translation products.', // 「精挑翻译」名称来自于由我们创造的「择优翻译」功能。以智能和应用创新为基础，我们将持续打造更好用翻译产品。
      description2:
        'Our mission is to enable everyone to read without barriers and to work and communicate across languages using their native language. Advancing information equality and achieving native language freedom!' // 我们的使命是让每个人都能无障碍阅读，并且用母语就能跨语言办公和交流。推动信息平权，实现母语自由！
    },
    bilingual_translation: {
      title: 'Bilingual Web Page Translation', // 双语对照网页翻译
      description:
        'Intelligently recognize and translate web page content, display the translation after the original text, and achieve bilingual reading. Supports webpage translation in various languages, helping you to navigate the global ocean of knowledge without any barriers!', // 智能识别并翻译网页内容，在原文后显示译文，实现双语对照阅读。支持各种语言的网页翻译，助您无障碍畅游全球知识海洋！
      feature1: 'Click on the extension floating button to enable bilingual reading with one click. ', // 点击扩展插件悬浮按钮，一键开启双语阅读。
      feature2: 'Provides a variety of preset translation styles and supports customized translation styles.', // 提供多种预设译文样式，同时支持自定义译文样式。
      feature3: 'Freely choose AI translation models, it supports translation between more than 100 languages.' // 自由选择 AI 翻译模型，支持 100 多种语言互译。
    },
    ai_selected_translation: {
      title: 'AI-selected Translation', // AI 择优翻译
      description:
        'Using multiple translation models to translate the same original text will yield multiple different results, and the best translation can be selected through intelligent scoring, which can greatly improve the accuracy and reliability of the translation results.', // 使用多個翻譯模型對同一原文進行翻譯會得到多個不同的結果，透過智慧評分從中擇優獲取最佳譯文，能大幅提升翻譯結果的準確度和可靠性。
      feature1: 'Multiple translation models can be set up to translate simultaneously. ', // 可设置多个翻译模型同时进行翻译。
      feature2: 'Use a more powerful AI model to score and select the best among multiple translation results.', // 使用较强 AI 模型对多个翻译结果进行评分择优。
      feature3: 'Each model\'s translation result has an intuitive score and analysis description, making the quality difference clear at a glance.' // 每个模型的翻译结果都有直观的评分及分析说明，质量差异对比一目了然。
    },
    input_translation: {
      title: 'Input Translation', // 输入翻译
      description:
        'Enter content in the webpage input box for one-click translation. Let you input, reply, and communicate across languages using your native language, easily achieving native language freedom.', // 在网页输入框中输入内容即可一键翻译。让您用母语就能跨语言输入、回复和交流，轻松实现母语自由。
      feature1: 'Dive into the application scenarios and get rid of the cumbersome process of copying, switching, and pasting using traditional translation tools.', // 深入应用场景，摆脱使用传统翻译工具的复制、切换、粘贴等繁琐流程。
      feature2: 'Based on AI-selected translation, it fetches the best translation results in real-time, offering you an input-method-like user experience.', // 基于择优翻译，实时获取最佳译文结果，给您输入法般的使用体验。
      feature3: 'Translation record management, making it easy for you to view and reuse translation records at any time.' // 翻译记录管理，方便您随时查看和复用翻译记录。
    },
    pdf_free_translation: {
      title: 'PDF Translation', // PDF 翻译
      description: 'Completely free PDF translation feature, quickly translate daily foreign language documents, meeting most of your PDF document translation needs.', // 完全免费的 PDF 翻译功能，日常外语文档快速翻译，满足您大部分 PDF 文档的翻译需求。
      feature1: 'Keep the original typesetting: The position of each translation should be consistent with the original text.', // 保留原文排版：每段译文的位置与原文保持一致。
      feature2: 'Page-by-page bilingual comparison: Original document pages and translated pages correspond side-by-side, allowing for easy comparative reading.', // 逐页双语对照：原文档页面和译文页面左右逐页对应，轻松对比阅读。
      feature3: 'Export translation results: You can customize the display style of the translated text and the format of the exported document.' // 翻译结果导出：可自定义译文的显示样式和导出文档格式。
    },
    pdf_plus_translation: {
      title: 'PDF Plus',
      description:
        ' PDF Plus is designed for the translation of academic papers and various professional documents. Utilizing advanced AI visual processing technology, it overcomes the challenge of inaccurate translation and layout distortion caused by the misrecognition of various formulas, charts, and other elements in complex documents.', // PDF Plus 为学术论文和各类专业文档的翻译而设计。基于领先的 AI 视觉处理技术，解决了复杂文档中因为各类公式、图表等识别不准确而导致翻译结果和排版错乱的难题。
      feature1: 'Accurately parse various formulas, complex charts, code snippets and other professional content.', // 精确解析各类公式、复杂图表、代码片段等专业内容。
      feature2: 'Supports parsing and translation of scanned PDF files.', // 支持扫描版 PDF 的解析翻译。
      feature3: 'Recognition and conversion of two-column and three-column layouts.' // 双栏、三栏布局识别转换。
    },
    epub_translation: {
      title: 'ePub Translation', // ePub 电子书翻译
      description: 'The ePub e-book translation function can translate various foreign language ePub e-books into bilingual form or pure target language form for reading, and is perfectly compatible with various e-book readers such as Kindle.' // ePub 电子书翻译功能，可以将各种外语 ePub 电子书翻译为双语形式或纯目标语言形式进行阅读，完美兼容 Kindle 等各种电子书阅读器。
    },
    mouse_hover_translation: {
      title: 'Mouse Hover Translation', // 鼠标悬停翻译
      description:
        'Support two modes of [Paragraph Translation] and [Area Translation], mouse hover + shortcut key, you can quickly translate a single paragraph text in a web page or multiple paragraphs in an area.', // 支持【段落翻译】和【区域翻译】两种模式，鼠标悬停 + 快捷键，可快速翻译网页中单个段落文本或区域中的多个段落文本。
      feature1: 'Paragraph translation: Only the text of the mouse-over paragraph will be translated.', // 段落翻译: 只翻译鼠标悬停段落的单段文字。
      feature2: 'Area translation: Translate all paragraphs under the area of the page selected by hovering the mouse, making hover translation more flexible.', // 区域翻译: 翻译鼠标悬停所选中网页区域下的所有段落，让悬停翻译更灵活。
      feature3: 'You can customize the hover translation shortcut keys to meet your personalized needs.' // 可自定义悬停翻译的快捷键，满足您的个性化需求。
    },
    highlight_translation: {
      title: 'Highlight Translation', // 划词翻译
      description: 'Directly select words or text to translate. Provide you with a lightweight translation experience at your fingertips and on demand. ', // 直接划选单词或文本即可翻译。给您随手、按需的轻量级翻译体验。
      feature1: 'Word translation: part of speech, phonetic symbols, pronunciation, example sentences, comprehensive learning. ', // 单词翻译：词性、音标、发音、例句，全方位学习。
      feature2: 'Provide a detailed analysis of the translation results by considering the contextual meaning of the words.', // 结合单词的上下文语境，提供详细的翻译结果分析。
      feature3: 'Translation model can be switched on demand.' // 翻译模型按需切换。
    },
    text_translation: {
      title: 'Text Translation', // 文本翻译
      description: 'Aggregate translation engines from multiple platforms for you to switch between as you wish.' // 聚合多个翻译平台的引擎，供您随心切换。
    },
    compare_translation: {
      title: 'Compare Translation', // 对比翻译
      description:
        'Enable multiple translation models with one click, instantly present translations from various sources side by side, making differences clear at a glance, and easily select the version that best suits your needs.' // 同时使用多个翻译模型对比翻译结果。
    }
  },
  pricing: {
    // 价格
    hero: {
      title: 'Step into a New Era of AI Translation', // 迈入 AI 翻译新时代
      description1: 'Smarter, more accurate, and more user-friendly', // 更智能、更精准、更好用
      description2: 'Upgrade to Plus membership to enjoy top-tier AI translation services such as', // 升级 Plus 会员尊享 AAA、BBB、CCC、XXX 顶级 AI 翻译服务
      description3: '.' //
    },
    dialog: {
      // 用户未登录弹窗提示
      title: 'Not logged in', // 未登录
      message: 'Please log in first', // 请先登录
      button: 'Login' // 登录
    },
    cycle_name: {
      weekly: 'Weekly Plan', // 周会员
      monthly: 'Monthly Plan', // 月度会员
      quarterly: 'Quarterly Plan', // 季度会员
      yearly: 'Annual Plan' // 年度会员
    },
    cycle_unit: {
      weekly: '/ Week', // 周
      monthly: '/ Month', // 月
      quarterly: '/ Quarter', // 季度
      yearly: '/ Year' // 年
    },
    plans: {
      current_plan: 'Current Plan', // 当前套餐
      free: {
        title: 'Free Plan', // 免费版
        description: 'Completely free, install and use', // 完全免费，安装即用
        button: {
          login_or_signup: 'Log in / Sign up' // 登录 / 注册
        },
        options: {
          title: '', // 无
          description: '', // 无
          models_title: 'Translation models', // 翻译模型
          models: {
            model0: {
              title: 'Microsoft Translate', // 微软翻译
              sub_title: '',
              tips: ''
            },
            model1: {
              title: 'Google Translate', // 谷歌翻译
              sub_title: '',
              tips: ''
            },
            model2: {
              title: 'Zhipu GLM Translate', // 智谱 GLM 翻译
              sub_title: 'GLM-4.5-Flash',
              tips: ''
            },
            model3: {
              title: 'TranSmart Translate', // 腾讯翻译
              sub_title: '',
              tips: ''
            },
            model4: {
              title: 'Yandex Translate', // Yandex 翻译
              sub_title: '',
              tips: ''
            },
            model5: {
              title: 'Other Models: ', // 其他翻译模型：可自定义 API Key
              sub_title: 'Customizable API Key',
              tips: ''
            }
          },
          features_title: 'Free plan features', // 免费版的功能
          features: {
            feature0: {
              title: 'Web Page Translation', // 网页翻译
              sub_title: 'unlimited', // 开放使用
              tips: ''
            },
            feature1: {
              title: 'AI-selected Translation', // AI 择优翻译
              sub_title: 'unlimited', // 开放使用
              tips: ''
            },
            feature2: {
              title: 'Input Translation', // 输入翻译
              sub_title: 'unlimited', // 开放使用
              tips: ''
            },
            feature3: {
              title: 'Highlight Translation', // 划词翻译：无限制
              sub_title: 'unlimited', // 开放使用
              tips: ''
            },
            feature4: {
              title: 'PDF Translation', // PDF 翻译
              sub_title: 'unlimited', // 开放使用
              tips: ''
            },
            feature5: {
              title: 'ePub Translation', // ePub 电子书翻译
              sub_title: 'unlimited', // 开放使用
              tips: ''
            },
            feature6: {
              title: 'MinerU Document Translation', // MinerU 文档翻译
              sub_title: 'Customizable API Key',
              tips: 'You can apply for an API Token on the MinerU official website. Each account is entitled to 2000 pages of the highest priority parsing quota per day.'
            }
          }
        }
      },
      plus: {
        title: 'Plus Membership', // Plus 会员
        description: 'Enjoy a new experience of top AI translation service!', // 智享顶级 AI 翻译服务新体验！
        button: {
          free_trial: 'Free trial for 3 days', // 免费试用 3 天
          purchase: 'Purchase', // 购买
          renewal: 'Renewal', // 续费
          subscribe: 'Subscribe' // 订阅
        },
        options: {
          title: 'Includes all features of the free plan', // 包含免费版所有功能
          description: '', // 无
          models_title: 'Plus exclusive AI translation models', // Plus 尊享顶级 AI 翻译模型
          models_description: 'Plus members can directly use the advanced translation models such as DeepSeek, OpenAI, Gemini, ZhipuAI, Doubao, and Qwen. Advanced models support multiple versions for self-selection, and you can choose the appropriate version according to the scenario.',
          models: {
            model0: {
              title: 'DeepSeek Translate',
              sub_title: '',
              tips: ''
            },
            model1: {
              title: 'OpenAI Translate',
              sub_title: 'GPT-5 optional',
              tips: ''
            },
            model2: {
              title: 'Gemini Translate',
              sub_title: '',
              tips: ''
            },
            model3: {
              title: 'ByteDance Doubao Translate',
              sub_title: '',
              tips: ''
            },
            model4: {
              title: 'ZhipuAI GLM Translate',
              sub_title: '',
              tips: ''
            },
            model5: {
              title: 'Alibaba Qwen Translate',
              sub_title: '',
              tips: ''
            }
          },
          features_title: 'Plus exclusive advanced features', // Plus 尊享高级功能
          features: {
            feature0: {
              title: 'AI-selected Translation (Advanced Edition)', // 择优翻译（高级版）
              sub_title: '',
              tips: 'Use multiple advanced models exclusive to Plus members to translate simultaneously, and select the best translation by scoring, which can significantly improve the accuracy and reliability of the translation results.'
            },
            feature1: {
              title: 'Input Translation (Advanced Edition)', // 输入翻译（高级版）
              sub_title: '',
              tips: 'Use multiple advanced models exclusive to Plus members to translate simultaneously, and select the best translation by scoring, which can significantly improve the accuracy and reliability of the translation results.'
            },
            feature2: {
              title: 'MinerU Complex Format Document Translation (Advanced Edition)',
              sub_title: '',
              tips: 'Plus members can use it directly, with a monthly quota of 10,000 pages with the highest priority parsing. It supports the translation of PDF, Word, PPT, and images, and can recognize and translate various complex formulas, tables, and scanned documents.'
            },
            feature3: {
              title: 'PDF Plus',
              sub_title: '',
              tips: '300 pages per month of AI-powered high-fidelity PDF document translation, supporting the recognition and translation of complex formulas, tables, and scanned documents.'
            },
            feature4: {
              title: 'Priority after-sales service support', // 优先售后服务支持
              sub_title: '',
              tips: 'Service support email:'
            }
          }
        }
      }
    }
  },
  buy: {
    // 购买页面（选择套餐、购买模式、支付方式）
    title: 'Purchase / Subscribe Plus Membership Plan', // 购买 / 订阅 Plus 会员
    plus_membership: 'Plus Membership', // Plus 会员
    purchase_mode: 'Purchase Mode', // 购买模式
    purchase: 'Purchase', // 购买
    subscribe: 'Subscribe', // 订阅
    plans: 'Plans', // 套餐
    plans_detail: 'Plans detail', // 套餐详情
    purchase_cycle: {
      // 周期
      week: 'Week', // 周
      month: 'Month', // 月
      quarter: 'Quarter', // 季
      year: 'Year' // 年
    },
    pay_way: 'Payment Method', // 支付方式
    pay_way_items: {
      // 支付方式
      wechat: 'WeChat Pay', // 微信支付
      alipay: 'Alipay', // 支付宝
      stripe: 'Stripe',
      coupon_code: 'Redemption code'
    },
    confirm_payment: 'Confirm Payment', // 确认支付
    confirm_subscribe: 'Confirm Subscription', // 确认订阅
    remaining_payment_time: 'Remaining payment time', // 剩余支付时间
    payment_amount: 'Payment Amount', // 支付金额
    wechat_scan_qrcode_to_pay: 'Please use WeChat to scan the QR code to pay', // 请使用微信扫码支付
    alipay_scan_qrcode_to_pay: 'Please use Alipay to scan the QR code to pay', // 请使用支付宝扫码支付
    please_pay_on_time: 'Please pay on time' // 请及时支付
  },
  resource: {
    title: 'Service Resource Add-on Package', // 服务资源加量包
    resource_ype: 'Resource Type', // 资源类型
    translation_quota_add_on_package: 'Translation Quota (TQ) Add-on Package', // 翻译额度（TQ）加量包
    pdf_plus_add_on_package: 'PDF Plus Add-on Package', // PDF Plus 加量包
    resource_specifications: 'Resource Specifications', // 资源规格
    add_on_package: 'Add-on Package', // 加量包
    pages: 'Pages', // 页
    quantity_to_purchase: 'Quantity to purchase', // 购买数量
    no_data: 'No data', // 暂无数据
    payment_methods: 'Payment Methods', // 支付方式
    amount_to_pay: 'Amount to pay', // 支付金额
    buy_now: 'Buy Now' // 立即购买
  },
  selected_tsl_desc: {
    title: 'Why use AI-selected Translation?', // 为什么要用择优翻译？
    description_1: 'A single model is difficult to meet the diverse needs of multiple scenarios, multiple languages, and complex contexts in translation.', // 单一模型难以应对翻译中多场景、多语种及复杂语境等多样化需求。
    description_2: 'AI-selected Translation: Multi-model collaborative translation + AI scoring for optimal selection, accurately outputting the best translation.', // 择优翻译：多模型协同翻译 + AI评分择优，精准输出最佳译文。
    description_3: 'Advantages: Effectively eliminate erroneous results, significantly improve translation quality, especially suitable for translation scenarios requiring high accuracy.', // 优势：有效排除错误结果，显著提升翻译质量，尤其适合高准确度需求的翻译场景。
    description_4: 'Please see the comparison effect of the AI-selected translations below', // 请看以下精挑翻译的对比效果
    examples: {
      original_text: 'Original Text',
      best_translation: 'Best Translation',
      selected_model: 'Scoring Model',
      table: {
        translation_model: 'Translation Model',
        translation_result: 'Translation Result',
        translation_score: 'Score',
        translation_rationale: 'Rationale',
        translation_model_version: 'Model Version'
      },
      note_01: 'Note: The above samples were translated using different models in the same price tier and the data obtained by scoring the translation results. ',
      note_02:
        'There is a certain degree of randomness in the translation and scoring of AI models, and the results may be slightly different when reproducing the test, but they are generally similar. ',
      note_03: 'The above data is for reference only. It is highly recommended that you experience directly: '
    }
  },
  faq_common: {
    title: 'Frequently asked questions', // 常见问题
    description: 'Questions? Please check the following FAQs first, or contact our customer service.', // 有疑问？请先查看以下常见问题解答，或联系我们的客服。
    items: {
      item_10: {
        label: 'What are the advantages of using AI-selected Translation?', // 使用 AI 择优翻译有什么优势？
        content:
          '- Different AI translation models use different model architectures, training data, training algorithms, generation strategies, etc., and each AI translation model will have its own unique advantages and disadvantages. Therefore, it is difficult for a single translation model to achieve optimal translation results in different languages, sentences and contexts.   \n - SelectTranslate pioneered the __AI-selected Translation__ feature. It simultaneously invokes multiple AI translation models for translation, and then uses a more powerful AI model to score and select the best from multiple translation results, effectively eliminating obviously incorrect translation results and __ensuring that each original sentence can obtain the optimal translation__ 🎯.  \n - There is no harm without comparison. Welcome to experience 👉 [AI-selected Translation](text) for yourself.'
      },
      item_20: {
        label: 'Can I use SelecTranslate for free?', // 我可以免费使用精挑翻译吗？
        content:
          '- Yes, you can use all the features of SelectTranslate completely free of charge. There are several free translation models that can be used directly, and other translation models can be used after you configure the API Key.（[Each model API Key application and configuration](docs/service/configure)）。   \n - If you find it troublesome to apply and configure API Keys for translation models one by one, you can also purchase our Plus membership plan service directly. Plus members can directly use the advanced translation models included in the plan such as DeepSeek, OpenAI, Gemini, ZhipuAI, Doubao, Tongyi Qwen, etc.'
      },
      item_30: {
        label: 'Which model versions are used for the DeepSeek, OpenAI, Gemini, ZhipuAI, Doubao, and Qwen advanced models offered in the Plus membership plan?', // Plus 会员提供的 DeepSeek、OpenAI、Gemini、智谱AI、豆包、通义千问 高级模型分别使用哪个模型版本？
        content:
          '- Plus members can directly use the advanced translation models such as DeepSeek, OpenAI, Gemini, ZhipuAI, Doubao, and Qwen. Advanced models support multiple versions for self-selection, and you can choose the appropriate version according to the scenario.  \n - The default recommended versions for each advanced model are as follows:  \n   1、DeepSeek：DeepSeek-V3-0324  \n   2、OpenAI： GPT-4.1-mini   \n  3、Gemini： Gemini-2.5-Flash   \n  4、ZhipuAI： GLM-4-Plus    \n  5、Doubao： Doubao-1.5-Pro-32k   \n   6、Tongyi Qwen： Qwen-Plus'
      },
      item_35: {
        label: 'I often have important copy to translate. Can Plus members choose the advanced version of the model to translate?  ', // 我经常有重要的文案需要翻译，Plus 会员可以选择模型的高级版本来翻译吗？
        content:
          '- Of course! Although the advanced version is more expensive, it has better translation accuracy and is especially suitable for the translation of important copy or scenes. The advanced models for Plus members support multiple versions for self-selection, which can be selected as needed in the model settings.'
      },
      item_40: {
        label: 'What is the monthly translation quota for the Plus membership plan?', // Plus 会员套餐中每月翻译额度是多少？
        content:
          '- The monthly translation quota for the Plus plan is __4 million TQ__   \n &nbsp;   \n Please see the conversion rules between TQ and Token: [Translation Quota and Model Token Conversion Rules](docs/translation_quota)'
      },
      item_45: {
        label: 'How many tokens is the translation quota of 4 million TQ equal to the translation model?', // 400万 TQ 的翻译额度相当于翻译模型多少个Token？
        content:
          '- The __4 million TQ__ translation quota is approximately equal to the number of tokens that can be used for translation in the default version of each advanced model:  \n  1、 DeepSeek-V3 ： __10 million Token__  \n  2、GPT-4.1-mini ： __6.85 million Token__   \n   3、Gemini-2.5-Flash ： __18 million Token__  \n  4、Zhipu GLM-4-Plus ： __8 million Token__   \n   5、Doubao-1.5-Pro-32k ： __33 million Token__  \n  6、Tongyi Qwen-Plus ： __33 million Token__  \n &nbsp;   \n Please see the conversion rules between TQ and Token: [Translation Quota and Model Token Conversion Rules](docs/translation_quota)'
      },
      item_50: {
        label: 'Is the translation quota shared by all advanced models?', // 翻译额度是所有高级模型共用的吗？
        content: '- Yes, the translation quota is shared across all advanced models supported by Plus plan.' // 是的，翻译额度是 Plus 会员所支持的所有高级模型共用的。
      },
      item_60: {
        label: 'Can I have a free trial of the Plus plan?', // Plus 会员可以免费试用吗？
        content:
          '- Yes, register an account and log in to apply for a free __3-day__ trial of Plus plan.  \n - The trial plan has translation quota limits. Once the trial quota is used up or the trial period ends, you can choose to purchase/subscribe to the official Plus membership plan.'
      },
      item_70: {
        label: 'Can I subscribe to or purchase a Plus membership plan using a UnionPay credit card?', // 可以用银联的信用卡订阅或购买 Plus 会员吗？
        content:
          '- Yes, you can choose Stripe for payment. It supports subscriptions or purchases using credit cards issued by all major credit card institutions, including UnionPay ![pay_card](assets/images/logos/pay_unionpay.svg), Visa ![pay_card](assets/images/logos/pay_visa.svg), and Mastercard ![pay_card](assets/images/logos/pay_mastercard.svg) .'
      },
      item_75: {
        label: 'Can I issue a general VAT invoice for Chinese mainland?', // 可以开中国大陆的增值税普通发票吗？
        content:
          '- If you need to issue a general VAT invoice for the Chinese mainland, please use WeChat Pay or Alipay to pay for the purchase, and then send the following invoicing information to the service email service&#64;selecttranslate.com , We will confirm and issue the invoice to you in a timely manner.  \n __Invoice Information__: Account ID, Service Order Number, Invoice Title, Unified Social Credit Code/Taxpayer Identification Number, Email Address for Receiving Invoice  \n - __Please note__: If you pay with Stripe, you cannot apply for a general VAT invoice in the Chinese mainland. However, Stripe will send invoices and receipts to your email. Most companies support using Stripe\'s receipts as reimbursement proof, and you can confirm with the finance department whether Stripe\'s receipts can be used as reimbursement proof.'
      },
      item_80: {
        label: 'What should I do if the translation quota for the Plus membership plan is not enough?', // Plus 会员套餐翻译额度不够用怎么办？
        content:
          '- The monthly translation quota in the Plus membership plan can meet the needs of the vast majority of users.  \n If the monthly translation quota is not enough, we also offer various specifications of translation quota add-on packages. You can purchase [Translation Quota Add-on Packages](resource?type=general_token) as needed.'
      },
      item_90: {
        label: 'Can I apply for a refund for my Plus membership plan?', // 购买了 Plus 会员套餐，可以申请退款吗？
        content:
          '- __Monthly orders/subscriptions__: Refunds can be requested within 24 hours of the initial purchase.  \n - __Quarterly orders/subscriptions__: Refunds can be requested within 48 hours of the initial purchase.    \n - __Annual orders/subscriptions__: Refunds can be requested within 72 hours of the initial purchase.   \n &nbsp;  \n __Please note__ that requests submitted after the deadline will not be refunded. When applying for a refund, please state the reason for the refund request.  \n Refunds are only available if the translation quota for the order has not been heavily used. If the translation quota corresponding to the order has been heavily used, your refund request may be denied.   \n If you have previously successfully applied for and received a refund for our services, we reserve the right to refuse any further refund requests from you.   \n We will review refund requests based on the specific circumstances of usage, ensuring that users can fairly use our services and to prevent abuse of the refund rules.  \n &nbsp;  \n - __Refund Process__: If you meet the above refund conditions, please send your refund request to our service email service&#64;selecttranslate.com . Our service team will review your request and process and notify you within 7 business days.'
      },
      item_100: {
        label: 'I don\'t want to renew my Plus membership plan, how do I cancel my subscription?', // 不想再续订 Plus 会员套餐，如何取消订阅？
        content: '- After logging into your account, navigate to the "Account" page, click the "Unsubscribe" button in the "Current Plan" section, and follow the prompts to cancel your subscription.'
      },
      item_110: {
        label: 'Is there a limit on the number of devices that can be used with a membership account?', // 会员账号有使用设备数量限制吗？
        content: '- Yes, each membership account can be used on up to 10 devices (browsers) simultaneously. When the limit is exceeded, the earliest logged-in device will be logged out.'
      },
      item_120: {
        label: 'I would like to offer some suggestions for product feature improvements and innovations. How can I provide feedback?', // 我想提一些产品功能改进和创新的建议，如何反馈？
        content:
          '- We warmly welcome users to provide feedback on any issues encountered while using our products, and we are also eager to receive suggestions for product feature improvements and innovations based on their needs.   \n - You can click on the [Feedback](feedback) link at the bottom of the page to access the feedback/suggestion form.   \n - Your feedback and suggestions are very valuable to us, we will carefully read each piece of feedback and suggestion and response promptly. You can view responses and interact with us in the [Feedback List](feedback_list) section of the Account Center.'
      }
    }
  },
  faq_resource_pack: {
    items: {
      item_210: {
        label: 'What is the applicable scope of the Translation Quota Add-on Package?', // 翻译额度加量包的适用范围？
        content:
          '- The Translation Quota Add-on Package applies to all advanced translation models supported by Plus membership plan, including DeepSeek, OpenAI, Gemini, ZhipuAI, Doubao, and Tongyi Qwen.'
      },
      item_220: {
        label: 'Only Plus members can purchase the Translation Quota Add-on Package?', // 只有 Plus 会员才能购买翻译额度加量包？
        content: '- Yes, only Plus members can purchase and use the Translation Quota Add-on Package.' // 是的，只有 Plus 会员才能购买和使用翻译额度加量包。
      },
      item_230: {
        label: 'How long is the validity period of the Translation Quota Add-on Package?', // 翻译额度加量包的有效期是多久？
        content:
          '- The Translation Quota of the Add-on Package is permanently valid during the effective period of the Plus plan service. If the Plus plan service expires midway and there are unused Translation Quota from the Add-on Package, you can continue to use them after renewing the Plus membership.'
      },
      item_240: {
        label: 'What is the usage priority of the monthly Translation Quota for Plus membership plan and the Translation Quota in the Add-on Package?', // Plus 会员套餐的每月翻译额度和加量包中的翻译额度使用优先级？
        content:
          '- The monthly Translation Quota of the Plus membership plan will be used first. Only after the monthly Translation Quota of the plan has been used up will the Translation Quota in the Add-on Package be used.'
      }
    }
  },
  check_installed: {
    // 检查是否安装插件
    extension_name: 'SelectTranslate Extension', // 精挑翻译插件
    add_browser_1: 'Add to {browser}', // 添加到
    add_browser_2: 'Add to {browser} for free', // 免费添加到
    other_browsers: 'Other browsers', // 其他浏览器
    extension_not_installed: {
      tip_1: 'Your browser is not installed or not enabled', // 您的浏览器未安装/未启用
      tip_2: 'Please install and enable the extension and try again', // 请安装/启用插件后重试
      tip_3: 'You can experience it immediately after installing and enabling the SelectTranslate extension!' // 安装并启用精挑翻译插件后即可马上体验！
    },
    extension_version_too_low: {
      tip_1: 'It is detected that your', // 检测到您的
      tip_2: ' version is too low', // 版本过低
      tip_3: 'Please upgrade the extension to the latest version and try again' // 请升级插件至最新版本后重试
    }
  },
  extensions: {
    // crx 包安装
    crx: {
      title: 'Tutorial on Downloading and Installing the SelectTranslate Chrome Extension', // 精挑翻译 Chrome 插件下载安装教程
      download_button: 'Download the crx installation package', // 下载 crx 安装包
      download_package: {
        title: '1. Download the extension', // 1. 下载插件
        description:
          'Click the "Download the crx installation package" button above to download the SelectTranslate Chrome extension zip file. After decompressing, you will get the crx installation file for the extension.' // 点击上面【下载 crx 安装包】按钮，下载精挑翻译的 Chrome 插件 zip 压缩包，解压后即可得到插件的 crx 安装文件。
      },
      chrome_extensions_dev_mode: {
        title: '2. Extensions management settings', // 2. 扩展程序管理设置
        description:
          'Enter "chrome://extensions" in the Chrome browser address bar and press Enter to enter the extensions management interface and turn on "Developer Mode" in the upper right corner.' // 在 Chrome 浏览器地址栏输入 chrome://extensions 并回车，进入扩展程序管理界面，在右上角开启【开发者模式】。
      },
      install_step1: {
        title: '3. Drag and drop to install the extension', // 3. 拖入安装插件
        description: 'Drag the crx installation file of the extension into the extensions management interface.' // 把插件的 crx 安装文件，拖入到扩展程序管理界面。
      },
      install_step2: {
        title: '4. Add extension', // 4. 添加扩展程序
        description: 'A pop-up window will prompt you to add "SelectTranslate", click the "Add Extension" button to complete the installation.' // 会弹窗提示添加"精挑翻译"，点击【添加扩展程序】按钮即可完成安装。
      },
      install_step3: {
        title: 'Enable extension', // 5. 启用扩展
        description: 'Ensure the extension is in the 【Enabled】 state. Follow the steps shown in the left image to pin SelectTranslate to the extensions navigation bar for convenient future use.' // 确保插件处于【启用】状态，如左图步骤设置，将精挑翻译固定到插件导航栏，方便后续使用。
      },
      faq: {
        title: 'Crx installation FAQ', // crx 方式安装常见问题
        desc: 'Questions? Please check the FAQs below, or contact our customer service.', // 有疑问？请查看下面的常见问题，或者联系我们的客服。
        items: {
          item1: {
            title: 'Dragging the crx file into the extensions management interface does not respond, or it prompts that it has been blocked - unknown source.', // 将 crx 文件拖入扩展程序管理界面没反应，或者提示已阻止 - 不明来源。
            desc: 'Please check step 2 above: Enter "chrome://extensions" in the browser address bar and press Enter to enter the extensions management interface, turn on "Developer Mode" in the upper right corner, and re-drag the crx file.' // 请检查以上第2步操作：在浏览器地址栏输入 chrome://extensions 并回车，进入扩展程序管理界面，在右上角开启【开发者模式】，重新拖入 crx 文件即可。
          },
          item2: {
            title: 'Install without optional files by using "Load unpacked", or prompt "Failed to load the extension".', // 通过 "加载已解压的拓展程序" 安装没有可选的文件，或者提示 "未能成功加载扩展程序"。
            desc: 'Note: You must install by dragging the crx file. Do not import the crx by selecting "Load unpacked" or by double-clicking the crx file.' // 注意：必须用拖入 crx 文件的方式安装。不要通过【加载已解压的扩展程序】导入 crx，也不要双击 crx 文件。
          },
          item3: {
            title: 'The browser does not support crx files.', // 浏览器不支持 crx 文件。
            desc: 'If the browser does not support the installation of crx files, you can install using the zip file method.' // 如果浏览器不支持 crx 文件安装，可使用 zip 文件方式安装。
          }
        }
      }
    },
    // zip拓展程序
    zip: {
      title: 'SelectTranslate Extension Zip Installation Tutorial', // 精挑翻译 插件Zip安装教程
      download_button: 'Download Zip installation package', // 下载 Zip 安装包
      download_install_package: {
        title: 'Get the SelectTranslate extension installation package', // 获取 精挑翻译 插件安装包
        item1: 'Download and extract the zip installation package', // 下载并且解压zip安装包
        item2: 'The extracted folder needs to be permanently retained, otherwise it will affect the usage' // 解压后的文件夹需要永久保留，否则影响使用
      },
      open_dev_model: {
        title: 'Enable Developer Mode', // 开启开发者模式
        item1: 'Enter "chrome://extensions/" in the address bar and press Enter', // 在地址栏输入 "chrome://extensions/" 并回车
        item2: 'Enter the extension management page and turn on "Developer Mode" in the upper right corner' // 进入扩展程序管理页面，在右上角开启 "开发者模式"
      },
      install_package: {
        title: 'Install SelectTranslate extension', // 安装 精挑翻译 插件
        item1: 'Method 1: Drag the extracted folder into the extension management page', // 方式一：把解压出来的文件夹拖入扩展程序管理页面
        item2: 'Method 2: Click the "Load unpacked extension" in the upper left corner, and select the extracted folde' // 方式二：点击左上角 "加载已解压的拓展程序"，选择解压出来的文件夹
      },
      enable_package: {
        title: 'Enable SelectTranslate extension', // 启用 精挑翻译 插件
        item1: 'Click the "Extension" button in the status bar', // 点击状态栏 "拓展程序" 按钮
        item2: 'Click "Fix" to complete the settings' // 点击 "固定" 即可完成设置
      }
    }
  },
  selected_trans: {
    title: 'AI-selected Translation, Comparative Translation, Text Translation', // AI 择优翻译、对比翻译、文本翻译
    multiple_translation_results: 'The following are multiple translation results for this sentence:', // 以下为该句多个翻译结果
    auto_detect: 'Auto Detection', // 自动检测
    settings: {
      translation_models: 'Translation Models', // 翻译模型
      original_language: 'Original language', // 原文语言
      target_language: 'Target language', // 目标语言
      auto_translate: 'Auto Translate', // 自动翻译
      history: 'History', // 历史记录
      custom_results: 'Custom results', // 自选结果
      trans_models: {
        title: 'Comparative Translation Models', // 对比翻译模型
        selected_01: '',
        selected_02: 'models selected', // 已选择 {0} 模型
        collapse: 'Collapse', // 收起(折叠)
        expand: 'Expand', // 显示更多(展开)
        close: 'Close', // 关闭
        target_language_is_not_supported: 'The current target language is not supported' // 不支持当前目标语言
      },
      ai_selected_translation: 'AI-selected Translation', // AI 择优
      ai_selected_translation_desc: 'Based on comparative translation, AI automatically scores multiple translation results, intelligently sorts them, and selects the best translation.', // 基于对比翻译，AI 自动评分多个翻译结果，智能排序并选出最优译文。
      selected_mode: 'Score by sentence', // 逐句择优
      selected_mode_desc: 'Enable: Score by sentence, Disable: Full-text scoring', // 启用：逐句评分，关闭：全文评分
      selected_rationale: 'Scoring rationale', // 评分说明
      selected_rationale_desc: 'Display the rationale for the score (will increase the translation quota usage).', // 显示择优评分的说明（会增加翻译额度使用量）
      selected_model: 'Scoring model', // 评分模型
      selected_model_desc: 'AI models for scoring and analyzing translation results' // 对翻译结果进行精挑评分和分析的 AI 模型
    },
    main: {
      translating: 'Translating...', // 翻译中...
      scoring: 'Scoring...', // 评分中...
      scoring_and_analyzing: 'Scoring and analyzing...', // 评分分析中...
      waiting_for_optimization: 'Waiting for Selected...', // 等待择优...
      selected_score: 'Selected Score', // 择优评分
      ai_selected_result: 'AI-selected result', // AI 择优结果
      select_this_result: 'Select this result', // 选择此结果
      translation_exception: 'Translation exception', // 翻译异常
      copy: 'Copy' // 复制
    },
    operation: {
      traditional_ranslation_btn: 'Traditional Translation', // 传统翻译
      compare_translation_btn: 'Compare Translation', // 对比翻译
      compare_translation_btn2: 'Compare Translate', // 对比翻译
      ai_selected_translation_btn: 'AI-selected Translate', // AI 择优翻译
      traditional_ranslation_btn2: 'Translate', // 传统翻译 （英文下叫 translate 动词）
      restore: 'Restore', // 还原
      re_scoring: 'Re-scoring', // 重新择优
      re_translate: 'Re-translate', // 重新翻译
      re_compare: 'Re-compare', // 重新对比
      expand: 'Expand', // 展开
      collapse: 'Collapse' // 隐藏
    },
    textarea: {
      select_at_least_one_model: 'Please select at least 1 translation model', // 请至少选择一个翻译模型
      text_to_be_translated: 'Please enter the text to be translated', // 请输入要翻译的文本
      ai_selected_min_models_tip: 'AI-selected - Need to select 2 or more translation models' // AI 择优 - 需要选择 2 个或以上翻译模型
    },
    history: {
      no_history: 'No history records available', // 暂无历史记录
      search_history: 'Search history', // 搜索历史记录
      clear_history: 'Clear History', // 清空记录
      clear_history_confirm: 'Are you sure you want to clear all history?', // 确定要清空所有历史记录吗？
      by_sentence: 'Score by sentence', // 逐句择优
      by_fulltext: 'Full text Scoring', // 全文择优
      today: 'Today', // 今天
      yesterday: 'Yesterday' // 昨天
    },
    error_info: {
      original_text: 'Original Text', // 原文内容
      no_original_text: 'No original text', // 没有原文内容
      retry: 'Retry', // 重试
      retry_all: 'Retry All' // 重试全部
    },
    error: {
      text_length_exceeded: 'Text Length Exceeded',
      text_length_exceeded_desc: 'Text length cannot exceed {max} characters',
      same_language: 'Source and Target Languages Are the Same',
      same_language_desc: 'The original language is the same as the target language, please check the translation settings!' // 原文语言与目标语言相同，请检查翻译设置！
    }
  },
  footer: {
    // 页脚
    help_enter: 'Help Center', // 帮助中心
    use_doc: 'Tutorial', // 使用文档
    faq: 'FAQ', // 常见问题
    feedback: 'Feedback', // 问题反馈
    terms_policies: 'Terms & Policies', // 条款和政策
    terms_of_service: 'Terms Of Service', // 服务条款
    privacy_policy: 'Privacy Policy', // 隐私政策
    company: 'Company', // 公司
    about: 'About', // 关于
    information: 'Information', // 资讯
    wechat_official_account: 'WeChat Official Account', // 微信公众号
    wechat_group: 'WeChat Group', // 微信交流群
    copyright: 'Copyright © Zing Software Co.,Ltd. All Rights Reserved.' // 智应软件有限公司版权所有.
  },
  http_service: {
    http_no_content_returned_by_backend_api: 'No content returned by the backend API', // 后端 API 没有返回内容
    http_operation_failed_please_retry: 'Operation failed, please retry', // 操作失败，请重试
    http_400_request_error: 'Request error', // 请求错误
    http_401_authentication_has_expired_please_login_again: 'Authentication has expired, please login again', // 认证已过期，请重新登录
    http_403_no_access_permission_please_log_in_first: 'Please login first', // 请先登录
    http_404_error_with_the_requested_url: 'Error with the requested URL : ', // 请求的 URL 出错：
    http_408_request_timeout: 'Request timeout', // 请求超时
    http_500_internal_service_error: 'Internal service error', // 内部服务错误
    http_501_service_not_implemented: 'Service not implemented', // 服务未实现
    http_502_gateway_error: 'Gateway error', // 网关错误
    http_503_service_unavailable: 'Service unavailable', // 服务不可用
    http_504_gateway_timeout: 'Gateway timeout', // 网关超时
    http_505_http_version_not_supported: 'HTTP version not supported' // HTTP 版本不受支持
  },
  coupon: {
    code: {
      title: 'Redemption code',
      instructions: {
        title: 'Instructions',
        instruction_01: '1. Use the redemption code to upgrade directly to Plus membership plan.', // 1. 使用兑换码将直接升级为 Plus 会员。
        instruction_02: '2. If you are already a Plus member, using the redemption code will extend the validity of your Plus membership plan.', // 2. 如果你已经是 Plus 会员，使用兑换码将延长 Plus 会员有效期。
        instruction_03: '3. If you have received a specified redemption code, please find the "Unused" redemption code in the "My Redemption Codes" list and click "Redeem".', // 3. 如果你获得的是指定发放的兑换码，请在 "我的兑换码" 列表中查找 "未使用" 的兑换码并 "兑换"。
        instruction_04: '4. The redemption code has an expiration date. Please use it as soon as possible.', // 4. 兑换码存在有效期，过期无法兑换，请尽快使用。
        instruction_05: '5. This content is a virtual product and is non-refundable after redemption.' // 5. 本内容为虚拟产品，兑换后不可退款。
      },
      use_title: 'Use Redemption Code',
      input_tips: 'Please enter the redemption code',
      redeem: 'Redeem',
      redeem_success: 'Redemption successful',
      my_code: 'My Redemption Codes',
      table: {
        query: {
          use_all: 'All',
          use_unused: 'Unused',
          use_used: 'Used'
        },
        redemption_code: 'Redemption code',
        product_specification: 'Specification',
        usage_type: 'Usage type',
        claim_time: 'Claim Time',
        redemption_start_time: 'Redemption Start Time',
        redemption_end_time: 'Redemption End Time',
        redemption_time: 'Redemption Time',
        status: 'Status',
        operation: 'Operation',
        redeem: 'Redeem' // 兑换
      }
    }
  },
  feedback: {
    title: 'Issue Feedback', // 问题反馈
    form: {
      header: 'Create an Issue / Suggestion', // 创建 问题 / 建议
      type: 'Type', // 类型
      type_options: {
        feedback: 'Issue feedback', // 问题反馈
        suggestion: 'Feature suggestion' // 功能建议
      },
      content: {
        label: 'Content', // 内容
        placeholder: 'Please enter your feedback or suggestions (within 500 characters).' // 请输入您要反馈/建议的内容（500字以内）
      },
      screenshot: {
        label: 'Screenshot', // 截图
        upload_text: 'Click or drag images here to upload.', // 点击或拖拽到此处添加图片
        drop_text: 'Release mouse to upload image' // 释放鼠标上传图片
      },
      contact: {
        label: 'Contact Information', // 联系方式
        placeholder: 'Optional: email / WeChat / QQ, etc., to facilitate our quick contact.' // 可提供：邮箱 / 微信 / 电话等，以方便我们快速联系
      },
      submit: 'Submit', // 提交
      submitting: 'Submitting...', // 提交中...
      wechat_group_tip: 'It is recommended to join the WeChat group for quick feedback. ' // 建议加入微信交流群快速反馈
    },
    image_viewer: {
      title: 'Image Preview' // 图片预览
    },
    messages: {
      upload_limit_exceeded: 'The maximum upload limit is exceeded', // 超出最大上传限制
      max_images: 'You can upload a maximum of {count} images', // 最多只能上传{count}张图片
      invalid_file_type: 'File format error', // 文件格式错误
      only_jpg_png: 'Only JPG or PNG format images can be uploaded', // 只能上传JPG或PNG格式图片
      file_too_large: 'File is too large', // 文件过大
      max_file_size: 'Image size cannot exceed 3MB', // 图片大小不能超过3MB
      content_required: 'Please enter your feedback', // 请输入反馈内容
      success: 'Success', // 成功
      submit_with_images_success: 'Your feedback and images have been successfully submitted!', // 您的反馈和图片已成功提交！
      image_upload_failed: 'Image upload failed', // 图片上传失败
      partial_success: 'Feedback submitted, but image upload failed', // 反馈已提交，但图片上传失败
      submit_success: 'Your feedback has been successfully submitted! Our customer service team will get back to you as soon as possible.', // 您的反馈已成功提交！
      submit_failed: 'Submission failed', // 提交失败
      unknown_error: 'Unknown error' // 未知错误
    },
    list: {
      title: 'My Feedback', // 我的问题反馈
      columns: {
        no: 'No.', // 编号
        type: 'Type', // 类型
        time: 'Time', // 时间
        content: 'Content', // 内容
        status: 'Status', // 状态
        action: 'Action' // 操作
      },
      status: {
        pending: 'Pending', // 未处理
        processing: 'Processing', // 处理中
        completed: 'Completed' // 处理完成
      },
      type: {
        suggestion: 'Feature Suggestion', // 功能建议
        feedback: 'Issue Feedback' // 问题反馈
      },
      actions: {
        view_reply: 'View Replies', // 查看回复
        images: 'image(s)', // 张
        create_feedback: 'Create feedback' // 创建反馈
      },
      empty: 'You have no feedback yet', // 你还没有反馈
      errors: {
        no_id: 'Cannot navigate, no valid ID found', // 无法跳转，未找到有效的 ID
        navigation_failed: 'Navigation failed', // 跳转失败
        missing_feedback_no: 'Missing necessary feedback number information' // 缺少必要的反馈编号信息
      }
    }
  },
  reply_list: {
    title: 'Feedback Replies', // 问题反馈回复
    feedback_details: {
      number: 'No.', // 编号
      time: 'Time', // 时间
      type: 'Type', // 类型
      status: 'Status', // 状态
      content: 'Content', // 内容
      images: 'Images', // 图片
      no_images: 'No Images' // 无图片
    },
    images: {
      count: 'image(s)', // 张
      view_all: 'View All' // 查看全部
    },
    reply: {
      user: 'User', // 用户
      manager: 'After-sales manager', // 售后经理
      button: 'Reply', // 回复
      cancel: 'Cancel', // 取消
      submit: 'Submit', // 提交
      content_label: 'Content', // 内容
      content_placeholder: 'Please enter information. Image pasting and drag-and-drop upload are supported.', // 请输入信息，支持图片粘贴及拖拽上传
      images_label: 'Screenshots', // 截图
      upload_text: 'Click or drag images here to upload.', // 点击或拖拽到此处添加图片
      drop_text: 'Release mouse to upload image' // 释放鼠标上传图片
    },
    status: {
      completion: 'Is it finished', // 是否完结
      mark_as_completed: 'Mark as Completed', // 标记为已完成
      completed: 'Completed', // 已完结
      not_completed: 'Not Completed', // 未完结
      update_success: 'Update Successful', // 更新成功
      update_success_message: 'Issue status has been updated to Completed', // 问题状态已更新为处理完成
      update_failed: 'Update Failed', // 更新失败
      update_failed_message: 'Unable to update feedback status', // 无法更新反馈状态
      confirm_title: 'Confirmation message', // 确认提示
      confirm_complete:
        'If the issue you feedback has been resolved, you can click "Confirm" to close the issue. You may not receive a response after the issue is closed, but you can still create new feedback at any time.',
      confirm: 'Confirm', // 确认
      cancel: 'Cancel' // 取消
    },
    loading: {
      loading_more: 'Loading more replies...', // 正在加载更多回复...
      all_loaded: 'All loaded', // 已加载所有
      waiting: 'Waiting for reply' // 回复中
    },
    messages: {
      limit_exceeded: 'Exceeds maximum upload limit', // 超出最大上传限制
      max_images: 'You can upload up to {count} images', // 最多只能上传{count}张图片
      invalid_file_type: 'File format error', // 文件格式错误
      only_jpg_png: 'Only JPG or PNG format images can be uploaded', // 只能上传JPG或PNG格式图片
      file_too_large: 'File too large', // 文件过大
      max_file_size: 'Image size cannot exceed 3MB', // 图片大小不能超过3MB
      content_required: 'Please enter reply content', // 请输入回复内容
      success: 'Success', // 成功
      submit_success: 'Your response has been successfully submitted! Our customer service team will get back to you as soon as possible.', // 您的回复已成功提交！
      image_upload_failed: 'Image upload failed', // 图片上传失败
      partial_success: 'Reply submitted, but image upload failed', // 回复已提交，但图片上传失败
      missing_data: 'Missing Issue data', // 缺少问题数据
      submit_failed: 'Submission failed', // 提交失败
      unknown_error: 'Unknown error', // 未知错误
      file_input_error: 'The file picker cannot be opened ', // 文件选择器无法打开
      reload_or_use_drag: 'Please try refreshing the page or uploading images using drag and drop instead. ' // 请尝试刷新页面或使用拖放方式上传图片。
    }
  },
  error_info: {
    // 错误信息
    retry: 'Retry' // 重试
  },
  member_message: {
    title: 'Message Notification', // 消息通知
    tabs: {
      all_messages: 'All Messages', // 全部消息
      unread_messages: 'Unread Messages' // 未读消息
    },
    toast: {
      mark_read: 'Mark Read', // 标记已读
      succeed: 'Success', // 成功
      mark_read_success: 'Marked as Read Successfully', // 标记已读成功
      prompt: 'Prompt', // 提示
      prompt_content: 'Please select the message to mark as read', // 请选择要标记已读的消息
      prompt_failed: 'Marking Failed', // 标记失败
      prompt_failed_content: 'Marking Failed, Please Retry', // 标记失败，请重试
      loading_failed: 'Loading Failed', // 加载失败
      loading_failed_content: 'Loading Failed, Please Retry', // 加载失败，请重试
      get_message_failed: 'Failed to Retrieve Message', // 获取消息失败
      get_message_failed_content: 'Please check your network connection or try again later', // 请检查网络连接或稍后再试
      delete_success: 'Deleted successfully', // 删除成功
      delete_success_content: 'Message Deleted', // 消息已删除
      delete_failed: 'Delete Failed', // 删除失败
      delete_failed_content: 'Delete Failed, Please Retry', // 删除失败，请重试
      delete_prompt_content: 'No messages selected', // 未选中任何消息
      delete_title: 'Delete message', // 删除消息
      delete_tip: 'Are you sure you want to delete the selected message? This operation is irreversible, please consider carefully!' // 确认删除选定消息吗？此操作不可逆，请慎重考虑！
    },
    loading: {
      loading_message: 'Loading message...' // 正在加载消息...
    },
    read: 'Read', // 已读
    unread: 'Unread', // 未读
    message_status: 'Message Status', // 消息状态
    message_title: 'Message Title', // 消息标题
    create_datetime: 'Received Time', // 接收时间
    message_preview: 'Message Preview', // 消息预览
    action: 'Action', // 操作
    delete: 'Delete', // 删除
    more_actions: 'More Actions' // 更多操作
  },
  translate_type: {
    text: {
      title: 'AI-selected Translation', // 择优翻译
      description: 'Traditional translation, AI-selected translation' // 传统翻译、AI 择优翻译
    },
    file: {
      title: 'Document Translation', // 文档翻译
      description: 'Supports multiple file formats.' // 支持多种文件格式
    },
    miner_u: {
      title: 'MinerU Document Translation', // MinerU 文档翻译
      description: 'Accurately parse complex format content.', // 精准解析复杂格式内容
      display_mode: {
        original_text_comparison: 'Original text comparison mode', // 原文对照模式
        results_focus: 'Results Focus Mode' // 结果聚焦模式
      }
    },
    pdf_plus: {
      title: 'PDF Plus',
      description: 'Professional PDF document translation, high-fidelity restoration of formulas and charts.' // 专业 PDF 文档翻译，公式、图表高保真还原
    }
  },
  document_translation: {
    // 文档翻译
    title: 'Document Translation', // 文档翻译
    description: 'The free document translation function supports the translation of documents in various formats, including PDF files, Markdown files, HTML files, text files, etc.', // 免费的文档翻译功能，支持 PDF文件、Markdown文件、HTML文件、text文件等多种格式文档的翻译。
    pdf_bilingual_translation: 'PDF Bilingual Translation', // PDF 双语对照翻译
    pdf_bilingual_translation_desc:
      'Completely free PDF translation function, page-by-page bilingual comparison, quick translation of daily foreign language documents, meeting most PDF translation and reading needs.', // 完全免费的 PDF 翻译功能，逐页双语对照，日常外语文档快速翻译，满足大部分 PDF 翻译阅读需求。
    open_file: 'Open File', // 打开文件
    open_new_file: 'Open New File', // 打开新文件
    select_new_file: 'Select New File', // 选择新文件
    open_file_tips: 'Supports pdf epub txt html docx md srt ass and other format files', // 支持 pdf、epub、txt、html、docx、md、srt、ass 等格式文件
    // DOCX 文档翻译
    docx_title: 'Word Document Translation',
    docx_description: 'Free Word document translation function, supporting intelligent parsing and translation of .doc and .docx format documents.',
    parsing_document: 'Parsing document...',
    parsing_progress: 'Parsing progress',
    parse_error: 'Parse error',
    parse_failed: 'Document parsing failed',
    invalid_file_type: 'Unsupported file format, please upload .doc or .docx files',
    file_too_large: 'File too large, please upload files smaller than 50MB',
    upload_link_failed: 'Failed to get upload link',
    query_failed: 'Failed to query parsing status',
    content_empty: 'Parsed content is empty',
    download_url_missing: 'Download URL missing',
    parse_timeout: 'Parse timeout, please try again later',
    single_column: 'Single Column',
    split_view: 'Split View',
    show_original: 'Show Original',
    show_translation: 'Show Translation',
    original_text: 'Original Text',
    // 注意保留 空格
    translated_text: ' Translation', // 翻译
    select_view_mode: 'Please select a view mode',
    sync_scroll: 'Sync Scroll',
    split_ratio: 'Split Ratio',
    export_file: 'Export File', // 导出文件
    export_epub: 'Export EPUB', // 导出 EPUB
    export_html: 'Export HTML', // 导出 HTML
    export_txt: 'Export TXT', // 导出 TXT
    export_markdown: 'Export Markdown', // 导出 Markdown
    export_docx: 'Export Word', // 导出 Word
    export_pdf: 'Export PDF', // 导出 PDF
    cancel_translation: 'Cancel Translation', // 取消翻译
    start_translation: 'Start Translation', // 开始翻译
    bilingual_translation: 'Bilingual Comparison Translation', // 双语对照翻译
    fulltext_translation: 'Full-text Translation (Translation only)', // 全文翻译 (仅译文)
    pdf: {
      label: 'PDF File', // PDF 文件
      open_file_tip: 'Open the PDF file to begin translation' // 打开 PDF 文件开始翻译
    },
    ppt: {
      // title: 'PPT 文件翻译',
      label: 'PPT File' // PPT 文件
      // open_file_tip: '打开 ppt/pptx 文件开始翻译'
    },
    image: {
      label: 'Image' // 图片
    },
    epub: {
      title: 'Ebook EPUB Translation', // 电子书 Epub 翻译
      label: 'Epub File', // Epub 文件
      open_file_tip: 'Open the Epub file to start translating' // 打开 Epub 文件开始翻译
    },
    html: {
      title: 'HTML File Translation', // HTML 文件翻译
      label: 'HTML File', // HTML 文件
      open_file_tip: 'Open the html/txt file to start translating' // 打开 html/txt 文件开始翻译
    },
    txt: {
      label: 'TXT File' // TXT 文件
    },
    markdown: {
      title: 'Markdown File Translation', // Markdown 文件翻译
      label: 'Markdown File', // Markdown 文件
      open_file_tip: 'Open the markdown file to begin translation' // 打开 Markdown 文件开始翻译
    },
    docx: {
      title: 'Docx File Translation', // Docx 文件翻译
      label: 'Docx File', // Docx 文件
      open_file_tip: 'Open the doc/docx file to begin translation' // 打开 doc/docx 文件开始翻译
    },
    subtitle: {
      open_file_tip: 'Open srt/vtt/ass/ssa/sbv/lrc files to begin translating', // 打开 srt/vtt/ass/ssa/sbv/lrc 文件开始翻译
      title: 'Subtitle File Translation', // 字幕文件翻译
      label: 'Subtitle File', // 字幕文件
      export_trans_subtitle: 'Export Translated Subtitle', // 导出译文字幕
      export_bilingual_subtitle: 'Export Bilingual Subtitle', // 导出双语字幕
      viewer_table_head_start_time: 'Start Time', // 开始时间
      viewer_table_head_end_time: 'End Time', // 结束时间
      viewer_table_head_content: 'Content', // 内容
      viewer_table_head_translation: 'Translation (Editable)', // 译文(可编辑)
      please_select_subtitle_file: 'Please select a subtitle file', // 请选择字幕文件
      unsupported_subtitle_file_format: 'Unsupported subtitle file format', // 不支持的字幕文件格式
      failed_read_subtitle_file: 'Failed to read subtitle file', // 字幕文件读取失败
      subtitle_file_content_error: 'No subtitles were parsed. Please check the file content.', // 未解析到任何字幕内容，请检查文件内容
      export_settings: {
        title: 'Export Subtitle Settings', // 导出字幕设置
        subtitle_order: 'Subtitle Order:', // 字幕顺序
        translation_first: 'Translation first', // 译文在前
        original_first: 'Original first', // 原文在前
        subtitle_track: 'Subtitle Track:', // 字幕轨道
        single_track: 'Single Track', // 单轨
        double_track: 'Double Track' // 双轨
      }
    },
    miner_u: {
      label: 'MinerU'
    },
    pdf_plus: {
      label: 'PDF Plus'
    }
  },
  free_pdf_translation: {
    title: 'PDF Document Translation', // PDF 文档翻译
    description: 'Completely free PDF bilingual translation function, meeting most of your foreign language PDF document translation and reading needs.', // 完全免费的 PDF 双语对照翻译功能，满足您大部分外语 PDF 文档的翻译阅读需求。
    open_a_new_pdf_file: 'Open a new PDF file', // 打开新 PDF 文件
    empty_state_title: 'Open a new PDF file to begin translating', // 打开新 PDF 文件开始翻译
    empty_state_description: 'Drag and drop files here or click to select files', // 拖拽文件到此处或点击选择文件
    select_file: 'Select File', // 选择文件
    drop_file_here: 'Release files here', // 释放文件到此处
    translate_all_and_download: 'Translate All and Download', // 翻译全部并下载
    please_select_pdf_file: 'Please select a PDF file', // 请选择 PDF 文件
    download_pdf_dialog_title: 'Download File', // 下载文件
    download_pdf_dialog_description: 'Please check the translation effect and output format before downloading.', // 下载前请先检查翻译效果和输出格式
    translated: 'Translated', // 已翻译
    the: '', // 第
    page: 'Page',
    pages: 'pages', // 页
    total: 'Total', // 共
    translate_all_pages_before_downloading: 'Translate all pages before downloading', // 翻译所有页面后再下载
    translation_mode: 'Translation Mode', // 翻译模式
    export_format: 'Export Format', // 导出格式
    bilingual_translation: 'Bilingual Comparison (Grey background)', // 双语对照（灰色背景）
    fulltext_translation: 'Full-text Translation (Translation only)', // 全文翻译（仅译文）
    text_based_pdf: 'Text-based PDF', // 文字版 PDF
    image_based_pdf: 'Image-based PDF', // 图片版 PDF
    text_based_pdf_download_tips_1: 'Download text-based PDF', // 下载基于文本的 PDF
    text_based_pdf_download_tips_2: 'Click【Download】, and the browser\'s print interface will appear. Select【Save as PDF】in【Destination】and click【Save】.', // 点击【下载】会显示浏览器的打印界面，在【目的地】中选择【另存为PDF】，点击【保存】即可。
    download_pdf_dialog_tips:
      'Note: It is recommended to avoid downloading PDF files that exceed 300 pages or are too large in content to prevent download failure due to insufficient system resources. ', // 注： 建议避免下载超过 300 页或内容过大的 PDF 文件，以防止因系统资源不足而导致下载失败。
    download: 'Download', // 下载
    no_content_to_print: 'There is no content to print', // 没有需要打印的内容
    translating_please_wait: 'Translating, please do not close the window...', // 正在翻译，请勿关闭窗口...
    downloading_please_wait: 'Downloading, please do not close the window...', // 正在下载，请勿关闭窗口...
    loaded: 'Loaded' // 已加载
  },
  mineru_doc_translation: {
    title: 'MinerU Document Translation', // MinerU 文档翻译
    description: 'MinerU Document Translation uses MinerU to accurately parse and extract text, images, tables, formulas, and other elements from documents, and then uses SelectTranslate to intelligently translate the parsed text. The perfect combination of online document browsing and document parsing translation provides you with an unparalleled document translation experience!'
  },
  pdf_plus: {
    title: 'PDF Plus（Professional PDF translation）', // PDF Plus（专业 PDF 翻译）
    description: 'Professional PDF document translation with high-fidelity restoration of formulas and charts.', // 专业 PDF 文档翻译，公式、图表高保真还原
    please_log_in_first: 'Please login first', // 请先登录
    upload_file: 'Upload File', // 上传文件
    upload_file_tips: 'Please upload a PDF file', // 请上传 PDF 格式文件
    upload_file_name: 'File Name', // 文件名
    pages: 'pages', // 页
    total: 'Total', // 共
    translation_model: 'Translation Model', // 翻译模型
    target_language: 'Target language', // 目标语言
    translation_mode: 'Translation Mode', // 译文显示
    page_range: 'Page Range', // 页面范围
    specify_page_range: 'Specify Page Range', // 指定页面范围
    specify_page_range_tips_1: 'If not set, the entire PDF will be translated by default.', // 不设置，则默认翻译整个 PDF
    specify_page_range_tips_2: 'Specify page numbers, separated by English commas: 1,3,-1', // 指定页码，使用英文逗号分隔：1,3,-1
    specify_page_range_tips_3: 'Use "-" to specify a range, such as pages 1 to 5: 1-5', // 使用"-"指定范围，如第1页到第5页：1-5
    specify_page_range_tips_4: 'Page 5 to the third last page: 5--3', // 第5页到倒数第3页：5--3
    specify_page_range_tips_5: 'From the 5th last page to the 2nd last page: -5--2', // 倒数第5页到倒数第2页：-5--2
    specify_page_range_tips_6: 'Use a combination of page numbers and ranges, separated by commas: 1, 3, 5-8, -4--2', // 组合使用页码及范围，使用英文逗号分隔：1,3,5-8,-4--2
    exceeds_pdf_plus_limit_tips_1: 'The number of pages to be translated is', // 要翻译页数 AA
    exceeds_pdf_plus_limit_tips_2: 'pages, which exceeds the remaining pages (', // 页，超过了您的 PDF Plus 剩余页数 (BB
    exceeds_pdf_plus_limit_tips_3: 'pages) of your PDF Plus.', // 页
    page_range_limit_tips_1: 'The current file has only', // 当前文件仅有 XX
    page_range_limit_tips_2: 'pages, please enter the correct page range.', // 页，请输入正确页码范围
    translate_now: 'Translate Now', // 立即翻译
    pdf_plus_remaining_pages_tips_1: 'PDF Plus remaining pages: ', // PDF Plus 剩余页数：本月会员套餐
    pdf_plus_remaining_pages_tips_2: 'pages in this month\'s Plus plan,', // 页，加量包
    pdf_plus_remaining_pages_tips_3: 'pages in the add-on package', // 页
    purchase_add_on_package: 'Purchase Add-on Package', // 购买加量包
    parsing_the_file: 'Parsing the file...', // 正在解析文件...
    bilingual_translation: 'Bilingual Comparison (Grey background)', // 双语对照（灰色背景）
    fulltext_translation: 'Full-text Translation (Translation only)', // 全文翻译（仅译文）
    messages: {
      parsing_failed: 'Parsing failed', // 解析失败
      parsing_failed_tips_1: 'The size of the uploaded file cannot exceed 100MB', // 上传文件大小不能超过 100MB
      parsing_failed_tips_2: 'Unable to parse the file - the file may be corrupted or the encryption method is not supported', // 无法解析文件 - 文件可能已损坏或加密方式不受支持
      parsing_failed_tips_3: 'Unable to parse encrypted file', // 无法解析加密文件
      parsing_failed_tips_4: 'Please check the file', // 请检查文件
      loading_failed: 'Failed to load', // 加载失败
      loading_failed_tips_1: 'This file has been encrypted. Please decrypt it and upload it again.', // 该文件已加密，请解密后重新上传
      loading_failed_tips_2: 'The product/membership service has expired. Please renew and try again.' // 产品/会员服务已过期，请续费后重试
    },
    back: 'Back' // 返回
  },
  pdf_plus_record: {
    // PDF 解析记录
    title: 'PDF Parsing Records', // PDF 解析记录
    table: {
      index: 'Index', // 序号
      file_name: 'File Name', // 文件名称
      parsed_page_count: 'Parsed Pages', // 解释页数
      create_datetime: 'Time', // 时间
      parsing_status: 'Status', // 状态
      actions: 'Actions' // 操作
    },
    parsing_status: {
      unparsed: 'Unparsed', // 未解析
      parsing: 'Parsing', // 解析中
      failed: 'Failed', // 解析失败
      success: 'Success' // 解析成功
    },
    view: 'View', // 查看
    delete: 'Delete', // 删除
    delete_confirm_title: 'Delete Record', // 删除记录
    delete_confirm_content: 'Are you sure you want to delete the selected record?', // 确认删除选中记录吗？
    delete_success: 'Deleted successfully', // 删除成功
    delete_success_tips: 'Deleted successfully' // 已删除
  },
  pdf_plus_introduction: {
    // PDF Plus 介绍
    title: ' AI-powered professional PDF document translation', // AI 驱动的专业 PDF 文档翻译
    description:
      'PDF Plus is based on leading AI visual processing technology, capable of accurately parsing handwritten text, mathematical equations, complex tables, code snippets, and chemical formulas in PDF documents or images, making it suitable for translating academic papers and various professional documents.', // PDF Plus 基于领先的 AI 视觉处理技术，能精确解析 PDF 文档或图片中的手写文本、数学方程式、复杂表格、代码片段、化学公式等内容，适用于学术论文和各类专业文档的翻译。
    parsing_type: {
      // 解析类型
      text_and_handwriting: 'Text and handwriting', // 文本和手写内容
      text_and_handwriting_desc: 'Intelligently extract printed text and handwritten content from images and PDFs. ', // 智能提取图片和 PDF 中的印刷文本和手写内容。
      mathematical_equations: 'Mathematical equations', // 数学方程式
      mathematical_equations_desc: 'Employing PhD-level mathematical algorithms to accurately parse equations in images and documents', // 采用博士级别的数学算法，精确解析图片和文档中的方程式。
      complex_tables: 'Complex tables', // 复杂表格
      complex_tables_desc: 'AI visual processing, easily handles complex tables.', // AI 视觉处理，轻松应对复杂表格。
      code_snippets: 'Code snippets', // 代码片段
      code_snippets_desc: 'Extract perfectly formatted code snippets from images and PDFs.', // 从图片和 PDF 中提取格式完美的代码片段。
      chemical_formulas: 'Chemical formulas', // 化学公式
      chemical_formulas_desc: 'Based on OCR technology, recognize and extract chemical formulas and diagrams.' // 基于 OCR 技术，识别并提取化学公式和图表。
    },
    features: {
      bilingual_translation: 'Paragraph-by-paragraph bilingual comparison', // 逐段双语对照
      bilingual_translation_desc:
        'Display the translated text in a prominent format after each original text paragraph, allowing for a paragraph-by-paragraph comparison that is clear and easy to read.', // 在每段原文后以鲜明的格式显示译文，逐段对比，清晰易读。
      supports_scanned_versions: 'Scanned PDFs are supported', // 支持扫描版 PDF
      supports_scanned_versions_desc: 'Using AI + OCR (Optical Character Recognition) technology, it supports precise parsing and translation of scanned PDFs or image-format content in documents.', // 采用 AI + OCR（光学字符识别）技术，支持扫描版 PDF 或文档中图片格式内容的精确解析翻译。
      layout_conversion: 'Two-column and three-column layout conversion', // 双栏、三栏布局转换
      layout_conversion_desc:
        'For the commonly used two-column and three-column layouts in academic papers, they are uniformly converted into an easy-to-read single-column format, achieving more elegant content layout and enhancing the reading experience.' // 针对学术论文常用的双栏、三栏布局，统一转换为易于阅读的单栏格式，实现更优雅的内容排版，提升阅读体验。
    },
    export: {
      title: ' Export translated content', // 导出翻译内容
      description: 'After translation, you can export the translated content in various formats, including HTML and PDF, to facilitate reading and sharing.', // 翻译后可将翻译内容导出为多种格式，包括 HTML 和 PDF，方便阅读和分享。
      export_format: 'Export Format', // 导出格式
      html: 'Export as HTML ', // 导出为 HTML
      html_desc: 'The translated content can be exported and saved in HTML format, making it easy to read in browsers on different devices.', // 可将翻译后的内容以 HTML 格式导出保存，方便在不同设备的浏览器中阅读。
      pdf: 'Export as PDF', // 导出为 PDF
      pdf_desc: 'You can export and save the translated content in PDF format, preserving the original layout and style as seen in the online translation reading.' // 可将翻译后的内容以 PDF 格式导出保存，会原样保留在线翻译阅读时的排版和样式。
    }
  },
  tutorial: {
    table_of_contents: 'Table of Contents' // 内容目录
  },
  safari_guide: {
    title: 'Extension Initialization Settings', // 扩展初始化设置
    description: 'Quickly learn how to enable the SelectTranslate extension in the Safari browser', // 快速了解如何在 Safari 浏览器中启用精挑翻译扩展
    previous_step: 'Previous', // 上一步
    next_step: 'Next', // 下一步
    get_started: 'Get Started', // 开始使用
    step1: {
      title: '🎉 Welcome! Now enable the plugin',
      description: 'SelectTranslate Safari extension has been successfully installed. Now you need to enable it and grant the necessary permissions.', // 精挑翻译 Safari 扩展已经成功安装，现在需要启用它并授予必要的权限。
      content: {
        click_icon: '1. Click the icon to the left of the search box', // 点击搜索框左侧图标
        click_manage_extensions: '2. Click on "Manage Extensions" in the options', // 点击选项里的「管理扩展」
        open_selecttranslate: '3. Open "SelectTranslate"' // 开启「精挑翻译」
      }
    },
    step2: {
      title: 'Allow extended access to the website', // 允许扩展访问网站
      description: 'Please select "Allow" to ensure translation functionality on all websites', // 请选择"允许"以确保在所有网站上获得翻译功能
      content: {
        click_selecttranslate: '1. Click on "SelectTranslate" in the options', // 点击选项里的「精挑翻译」
        select_always: '2. Select "Always Allow"', // 选择「始终允许」
        select_always_website: '3. Select "AlwaysAllow on Every Website"' // 选择「在每个网站上始终允许」
      }
    },
    step3: {
      title: 'Experience the web page translation',
      article: {
        title: '현대 사회의 디지털 라이프'
      }
    }
  },
  doc_trans_progress: {
    trans_progress: 'Translation Progress',
    doc_trans_progress: 'Document translation progress',
    the: '',
    page: 'Page', // 页
    translated: 'Translated', // 已翻译
    translation_in_progress: 'Translating...', // 翻译中
    no_translation_needed: 'No translation needed',
    waiting: 'Waiting',
    failed_nodes: 'Failed to process', // 处理失败
    failed: 'Failed', // 失败
    total: 'Total'
  },
  // 欢迎页
  welcome: {
    title: 'Welcome to use SelectTranslate', // 欢迎使用精挑翻译
    description: 'Here are a few steps to quickly understand our core features👇 ', // 下面通过几个步骤快速了解我们的核心功能 👇
    tooltip_pc: 'Scroll down to start exploring', // 向下滚动开始探索 // 桌面端描述
    tooltip_mobile: 'Swipe down to start exploring', // 向下滑动开始探索 // 移动端描述
    click_floating_button: 'You can click the floating button on the right', // 你可以通过点击右侧的悬浮按钮
    to_translate: 'to translate', // 进行翻译
    button: {
      experience: 'Try it now', // 立即体验
      go_experience: 'Go to experience' // 前往体验
    },
    more_translation_features: {
      title: 'More Translation Features', // 更多翻译功能
      description: 'Delve into application scenarios, a brand new translation experience awaits your exploration!' // 深入应用场景，全新的翻译体验，等您来探索！
    }
  }
}
