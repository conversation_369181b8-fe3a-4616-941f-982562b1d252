// 简体中文
export default {
  common: {
    site_name: '精挑翻译',
    title: '精挑翻译 - AI 择优翻译、双语对照网页翻译',
    description:
      '精挑翻译是一款可以免费使用的浏览器翻译插件, 支持AI择优翻译、双语对照网页翻译、输入翻译、文本翻译、鼠标悬停翻译、划词翻译、PDF文档翻译等功能。支持微软翻译、谷歌翻译、DeepSeek、DeepL、OpenAI（ChatGPT）、Gemini、Claude、通义千问等AI翻译模型。一款能提高工作与学习效率的翻译工具，为您提供好用、准确、符合目标语境的翻译体验。',
    page_does_not_exist: '页面不存在',
    page_does_not_exist_tip: '抱歉！您要访问的页面不存在',
    back_to_home_page: '返回首页',
    confirm: '确认',
    cancel: '取消',
    ai_selected: 'AI 择优',
    translation_results: '翻译结果',
    engins: {
      google: '谷歌翻译',
      microsoft: '微软翻译',
      transmart: '腾讯翻译',
      deepl: 'DeepL',
      openai: 'OpenAI',
      gemini: 'Gemini',
      claude: '<PERSON>',
      zhipu: '智谱GLM',
      qwen: '通义千问',
      lingyiwanwu: '零一万物',
      deepseek: 'DeepSeek',
      doubao: '豆包',
      grok: 'Grok',
      yandex: 'Yandex'
    },
    close: '关闭',
    model_unavailable: '模型不可用，请重新选择',
    error_tip: '提示',
    swipe_to_see_more: '左右滑动查看更多',
    loading: '加载中...',
    retry: '重试'
  },
  theme: {
    light: '浅色',
    dark: '深色',
    system: '跟随系统'
  },
  header: {
    // 页头
    home: '首页', // 首页
    pricing: '价格', // 价格
    docs: '教程', // 教程
    selected_translation: 'AI 择优翻译',
    file_translation: '文档翻译', // 文档翻译
    account: '账户', // 账户
    billing: '账单', // 账单
    pdf_plus: 'PDF Plus', // PDF Plus
    feedback: '问题反馈', // 反馈
    signup: '注册', // 注册(报名)
    login: '登录', // 登录
    logout: '退出' // 退出
  },
  auth: {
    // 用户授权认证模块：注册、激活、登录、重置密码
    common: {
      home: '首页',
      email_label: '邮箱地址',
      password_label: '密码',
      verify_code_label: '验证码',
      verify_code_send_label: '获取验证码',
      verify_code_has_been_send: '验证码已发送',
      verify_code_countdown_retry: '{seconds}秒后重试',
      resend: '重新发送',
      no_account_yet: '还没有账号？',
      have_an_account: '已经有账号？',
      or_login_using: '或',
      login_with_wechat: '使用微信账号登录',
      login_with_google: '使用 Google 账号登录',
      welcome_to_login: '欢迎登录',
      confirm_agreement: '注册即表示您同意',
      terms_of_service: '服务条款',
      and: '和',
      privacy_policy: '隐私政策',
      messages: {
        email_required: '请输入邮箱地址',
        email_format_incorrect: '请输入正确的邮箱地址',
        email_confirm_success: '邮箱地址验证成功，请登录',
        password_required: '请输入密码',
        password_format_incorrect_1: '密码必须是字母、数字或符号组成',
        password_format_incorrect_2: '密码长度为6到20个字符',
        verify_code_required: '请输入邮件验证码',
        verify_code_email_send_tip_1: '已向您的邮箱地址【',
        verify_code_email_send_tip_2: '】发送了验证码，请查收邮件！',
        new_password_required: '请设置新密码',
        reset_password_success: '重置密码成功'
      }
    },
    signup: {
      title: '注册账号'
    },
    signupconfirm: {
      title: '验证邮箱地址'
    },
    login: {
      title: '登录'
    },
    reset_password: {
      title: '重置密码',
      new_password_label: '新密码',
      new_password_placeholder: '设置新密码'
    }
  },
  account: {
    account: '账户',
    plan: '套餐',
    email_address: '邮箱地址',
    wechat_nickname: '微信昵称',
    google_nickname: 'Google 昵称',
    current_plan: '当前套餐',
    free_version: '免费版',
    trial_version: '试用版',
    free_trial_plus: '免费试用 Plus 会员',
    apply_for_free_trial_plus_tip_1: '您可免费申请试用 Plus 会员【 ',
    apply_for_free_trial_plus_tip_2: ' 】天！',
    apply_for_free_trial_plus_tip_3: '确认要申请吗？',
    upgrade_to_plus: '升级为 Plus 会员',
    upgrade_to_official_version_plus: '升级为正式版 Plus 会员',
    subscription_billing_time_tip1: '您订阅的 Plus 会员将在试用期结束后于', // 订阅扣费 提示1
    subscription_billing_time_tip2: '自动扣费生效', // 订阅扣费 提示2
    renewal_or_subscribe: '续费 / 订阅',
    monthly_subscription: '月度订阅会员',
    yearly_subscription: '年度订阅会员',
    next_renewal_time: '下次续费时间',
    unsubscribe: '取消订阅',
    unsubscribe_dialog_title: '取消您的订阅',
    unsubscribe_dialog_content_1: '您当前订阅的套餐服务将于',
    unsubscribe_dialog_content_2: '到期，取消订阅后您仍可在服务期结束前继续使用。',
    unsubscribe_dialog_content_3: '如果您确认要取消订阅，请点击【取消订阅】按钮。',
    unsubscribe_dialog_content_4: '如果您想继续保留当前订阅，请点击【返回】按钮。',
    unsubscribe_dialog_btn_back: '返回',
    unsubscribe_success_info: '您已成功取消订阅',
    unsubscribe_failed_info: '取消订阅失败，请稍后再试',
    usage_of_trial_plan_quota: '试用餐额度',
    usage_of_this_month_plan_quota: '本月套餐额度',
    remaining_translation_quota: '翻译额度剩余',
    pdf_plus_pages_remaining: 'PDF Plus 页数剩余',
    pages: '页',
    quota_reset_time: '下次额度重置时间',
    plan_will_expire_on_1: '套餐服务将于',
    plan_will_expire_on_2: '到期',
    plan_expired_on_1: '套餐服务已于',
    plan_expired_on_2: '到期',
    usage_of_add_on_quota: '加量包额度',
    purchase_translation_quota_add_on_package: '购买翻译额度加量包',
    purchase_pdf_plus_add_on_package: '购买 PDF Plus 加量包',
    purchase_add_on_package_tips: '加量包额度仅限 Plus 会员有效期内购买和使用',
    not_logged_in: '未登录', // Not logged in
    not_logged_in_tip: '请先登录', // Not logged in
    login: '登录', // Login
    subscription_renewal_failed: '订阅续费失败',
    subscription_renewal_failed_tip_1: '您的 Plus 会员续费未成功，因您的支付卡余额不足。请在充值后重新尝试订阅，如有疑问，请通过电子邮件 {email} 联系我们获取支持。',
    subscription_renewal_failed_tip_2: '您的 Plus 会员续费未成功，因您的支付卡已过期。请在更换支付卡后重新尝试订阅，如有疑问，请通过电子邮件 {email} 联系我们获取支持。',
    subscription_renewal_failed_tip_3: '您的 Plus 会员订阅续费失败。如有疑问，请通过电子邮件 {email} 联系我们获取支持。'
  },
  billing: {
    title: '账单',
    service_order: '服务订单',
    order_columns: {
      // 订单列表表头
      index: '序号',
      pay_time: '付款时间',
      pay_order_no: '订单号',
      product_name: '产品名称',
      buy_mode: '服务周期',
      amount: '金额',
      pay_way: '支付方式',
      service_begin_time: '服务开始时间',
      service_end_time: '服务结束时间'
    },
    product_names: {
      // 产品名称
      plus_name: 'Plus 会员',
      general_token_name: '翻译额度加量包',
      pdf_plus_name: 'PDF Plus 加量包',
      pdf_plus: 'PDF Plus',
      translation_quota: '翻译额度',
      quantity: '数量',
      tatal: '总计',
      pages: '页'
    },
    buy_modes: {
      // 购买模式
      try: '试用',
      weekly: '按周',
      monthly: '按月',
      quarterly: '按季',
      yearly: '按年',
      perpetual: '永久'
    },
    pay_ways: {
      // 支付方式
      platform_gift: '平台赠送',
      wechat: '微信支付',
      alipay: '支付宝',
      stripe: 'Stripe',
      coupon_code: '兑换码'
    },
    refund_order: {
      // 退款订单
      refund_success: '已退款',
      refund_amount: '退款金额',
      refund_time: '退款时间'
    }
  },
  home: {
    // 首页
    hero: {
      title: '更智能的 AI 翻译插件！',
      description_1: '支持双语对照网页翻译、输入翻译、划词翻译、PDF 翻译、鼠标悬停翻译等常用的翻译功能。',
      description_2: '行业首创的 AI 择优翻译功能：多模型翻译 + AI 评分择优，为您精准呈现最符合语境的专业级翻译结果。',
      extension_pc_chrome_label: 'Chrome 扩展', // Chrome 扩展
      extension_pc_edge_label: 'Edge 扩展', // Edge 扩展
      extension_pc_safari_label: 'Safari 扩展', // Safari 扩展
      extension_pc_firefox_label: 'Firefox 扩展', // Firefox 扩展
      extension_pc_360browser_label: '360浏览器扩展', // 360浏览器扩展
      extension_pc_crx_label: 'crx 安装包', // crx 安装包
      extension_pc_zip_label: 'zip 安装包' // zip 安装包
    },
    scenarios: {
      // 应用场景
      social: {
        title: '社交互动',
        description: '无障碍阅读，跨语言交流'
      },
      information: {
        title: '资讯浏览',
        description: '国际资讯，实时掌握'
      },
      study: {
        title: '学习研究',
        description: '全球知识，轻松获取'
      },
      document: {
        title: '文档翻译',
        description: '学术、办公，便捷高效'
      },
      international_trade: {
        title: '国际贸易',
        description: '自由沟通，服务全球'
      }
    },
    engins: {
      title: '汇聚全球顶级 AI 翻译模型'
    },
    mission: {
      // 使命
      title: '推动信息平权，实现母语自由',
      description1: '「精挑翻译」名称来自于我们首创的「AI择优翻译」功能。以智能和应用创新为基础，我们将持续打造更好用翻译产品。',
      description2: '我们的使命是让每个人都能无障碍阅读，用母语就能跨语言办公和交流。推动信息平权，实现母语自由！'
    },
    bilingual_translation: {
      title: '双语对照网页翻译',
      description: '智能识别并翻译网页内容，在原文后显示译文，实现双语对照阅读。支持各种语言的网页翻译，助您无障碍畅游全球知识海洋！',
      feature1: '点击扩展插件悬浮按钮，一键开启双语阅读。',
      feature2: '提供多种预设译文样式，同时支持自定义译文样式。',
      feature3: '自由选择 AI 翻译模型，支持 100 多种语言互译。'
    },
    ai_selected_translation: {
      title: 'AI 择优翻译',
      description: '使用多个翻译模型对同一原文进行翻译会得到多个不同的结果，通过智能评分从中择优获取最佳译文，能大幅提升翻译结果的准确度和可靠性。',
      feature1: '可设置多个翻译模型同时进行翻译。',
      feature2: '使用较强 AI 模型对多个翻译结果进行评分择优。',
      feature3: '每个模型的翻译结果都有直观的评分及分析说明，质量差异对比一目了然。'
    },
    input_translation: {
      title: '输入翻译',
      description: '在网页输入框中输入内容即可一键翻译。让您用母语就能跨语言输入、回复和交流，轻松实现母语自由。',
      feature1: '深入应用场景，摆脱使用传统翻译工具的复制、切换、粘贴等繁琐流程。',
      feature2: '基于择优翻译，实时获取最佳译文结果，给您输入法般的使用体验。',
      feature3: '翻译记录管理，方便您随时查看和复用翻译记录。'
    },
    pdf_free_translation: {
      title: 'PDF 翻译',
      description: '完全免费的 PDF 翻译功能，日常外语文档快速翻译，满足您大部分 PDF 文档的翻译需求。',
      feature1: '保留原文排版：每段译文的位置与原文保持一致。',
      feature2: '逐页双语对照：原文档页面和译文页面左右逐页对应，轻松对比阅读。',
      feature3: '翻译结果导出：可自定义译文的显示样式和导出文档格式。'
    },
    pdf_plus_translation: {
      title: 'PDF Plus',
      description: 'PDF Plus 为学术论文和各类专业文档的翻译而设计。基于领先的 AI 视觉处理技术，解决了复杂文档中因为各类公式、图表等识别不准确而导致翻译结果和排版错乱的难题。',
      feature1: '精确解析各类公式、复杂图表、代码片段等专业内容.',
      feature2: '支持扫描版 PDF 的解析翻译。',
      feature3: '双栏、三栏布局识别转换。'
    },
    epub_translation: {
      title: 'ePub 电子书翻译',
      description: 'ePub 电子书翻译功能，可以将各种外语 ePub 电子书翻译为双语形式或纯目标语言形式进行阅读，完美兼容 Kindle 等各种电子书阅读器。'
    },
    mouse_hover_translation: {
      title: '鼠标悬停翻译',
      description: '支持【段落翻译】和【区域翻译】两种模式，鼠标悬停 + 快捷键，可快速翻译网页中单个段落文本或区域中的多个段落文本。',
      feature1: '段落翻译：只翻译鼠标悬停段落的单段文字。',
      feature2: '区域翻译：翻译鼠标悬停所选中网页区域下的所有段落，让悬停翻译更灵活。',
      feature3: '可自定义悬停翻译的快捷键，满足您的个性化需求。'
    },
    highlight_translation: {
      title: '划词翻译',
      description: '直接划选单词或文本即可翻译。给您随手、按需的轻量级翻译体验。',
      feature1: '单词翻译：词性、音标、发音、例句，全方位学习。',
      feature2: '结合单词的上下文语境，提供详细的翻译结果分析。',
      feature3: '翻译模型可按需切换。'
    },
    text_translation: {
      title: '文本翻译',
      description: '聚合多个翻译平台的引擎，供您随心切换。'
    },
    // 对比翻译
    compare_translation: {
      title: '对比翻译',
      description: '一键启用多个翻译模型，即时并列呈现各家译文，差异一目了然，轻松选出最合心意的版本。'
    }
  },
  pricing: {
    // 价格
    hero: {
      title: '迈入 AI 翻译新时代',
      description1: '更智能、更精准、更好用',
      description2: '升级 Plus 会员，尊享',
      description3: '顶级 AI 翻译服务'
    },
    dialog: {
      // 用户未登录弹窗提示
      title: '未登录',
      message: '请先登录',
      button: '登 录'
    },
    cycle_name: {
      weekly: '周会员',
      monthly: '月度会员',
      quarterly: '季度会员',
      yearly: '年度会员'
    },
    cycle_unit: {
      weekly: '/ 周',
      monthly: '/ 月',
      quarterly: '/ 季',
      yearly: '/ 年'
    },
    plans: {
      current_plan: '当前套餐', // 当前套餐
      free: {
        title: '免费版',
        description: '完全免费，安装即用',
        button: {
          login_or_signup: '登录 / 注册'
        },
        options: {
          title: '', // 无
          description: '', // 无
          models_title: '翻译模型',
          models: {
            model0: {
              title: '微软翻译',
              sub_title: '',
              tips: ''
            },
            model1: {
              title: '谷歌翻译',
              sub_title: '',
              tips: ''
            },
            model2: {
              title: '智谱 GLM 翻译',
              sub_title: 'GLM-4.5-Flash',
              tips: ''
            },
            model3: {
              title: '腾讯翻译',
              sub_title: '',
              tips: ''
            },
            model4: {
              title: 'Yandex 翻译',
              sub_title: '',
              tips: ''
            },
            model5: {
              title: '其他翻译模型',
              sub_title: '可自定义 API Key',
              tips: ''
            }
          },
          features_title: '免费版的功能',
          features: {
            feature0: {
              title: '网页翻译',
              sub_title: '开放使用',
              tips: ''
            },
            feature1: {
              title: 'AI 择优翻译',
              sub_title: '开放使用',
              tips: ''
            },
            feature2: {
              title: '输入翻译',
              sub_title: '开放使用',
              tips: ''
            },
            feature3: {
              title: '划词翻译',
              sub_title: '开放使用',
              tips: ''
            },
            feature4: {
              title: 'PDF 翻译',
              sub_title: '开放使用',
              tips: ''
            },
            feature5: {
              title: 'ePub 电子书翻译',
              sub_title: '开放使用',
              tips: ''
            },
            feature6: {
              title: 'MinerU 文档翻译',
              sub_title: '自定义 API Key',
              tips: '可到 MinerU 官网申请 API Token，每个账号每天享有 2000 页最高优先级解析额度。'
            }
          }
        }
      },
      plus: {
        title: 'Plus 会员',
        description: '尊享顶级 AI 翻译服务新体验！',
        button: {
          free_trial: '免费试用 3 天',
          purchase: '购买',
          renewal: '续费',
          subscribe: '订阅'
        },
        options: {
          title: '包含免费版所有功能',
          description: '', // 无
          models_title: 'Plus 尊享顶级 AI 翻译模型',
          models_description: 'Plus 会员可以直接使用 DeepSeek、OpenAI、Gemini、智谱AI、豆包、通义千问 高级翻译模型。高级模型支持多版本自选，您可以根据场景选择适合的版本。',
          models: {
            model0: {
              title: 'DeepSeek 翻译',
              sub_title: '',
              tips: ''
            },
            model1: {
              title: 'OpenAI 翻译',
              sub_title: '可选 GPT-5',
              tips: ''
            },
            model2: {
              title: 'Gemini 翻译',
              sub_title: '',
              tips: ''
            },
            model3: {
              title: '豆包 翻译',
              sub_title: '',
              tips: ''
            },
            model4: {
              title: '智谱 GLM 翻译',
              sub_title: '',
              tips: ''
            },
            model5: {
              title: '通义千问 翻译',
              sub_title: '',
              tips: ''
            }
          },
          features_title: 'Plus 尊享高级功能',
          features: {
            feature0: {
              title: 'AI 择优翻译（高级版）',
              sub_title: '',
              tips: '使用 Plus 会员尊享的多个高级模型同时进行翻译，并对翻译结果进行评分择优，可显著提升翻译结果的准确度与可靠性。'
            },
            feature1: {
              title: '输入翻译（高级版）',
              sub_title: '',
              tips: '使用 Plus 会员尊享的多个高级模型同时进行翻译，并对翻译结果进行评分择优，可显著提升翻译结果的准确度与可靠性。'
            },
            feature2: {
              title: 'MinerU 复杂格式文档翻译（高级版）',
              sub_title: '',
              tips: 'Plus会员可直接使用，每月 10000 页最高优先级解析额度。支持 PDF、Word、PPT、图片的翻译，能实现各种复杂公式、表格、扫描版文档的识别和翻译。'
            },
            feature3: {
              title: 'PDF Plus',
              sub_title: '',
              tips: '每月 300 页 AI 高保真 PDF 文档翻译，支持复杂公式、表格、扫描版文档的识别和翻译。'
            },
            feature4: {
              title: '优先的售后服务支持',
              sub_title: '',
              tips: '服务支持邮箱：'
            }
          }
        }
      }
    }
  },
  buy: {
    // 购买页面（选择套餐、购买模式、支付方式）
    title: '购买 / 订阅 Plus 会员', // 购买 / 订阅 Plus 会员
    plus_membership: 'Plus 会员', // Plus 会员
    purchase_mode: '购买模式',
    purchase: '购买', // 购买
    subscribe: '订阅', // 订阅
    plans: '套餐', // 套餐
    plans_detail: '套餐明细', // 套餐明细
    purchase_cycle: {
      // 订阅周期
      week: '周',
      month: '月',
      quarter: '季',
      year: '年'
    },
    pay_way: '支付方式',
    pay_way_items: {
      // 支付方式
      wechat: '微信支付',
      alipay: '支付宝',
      stripe: 'Stripe',
      coupon_code: '兑换码'
    },
    confirm_payment: '确认支付', // 确认支付
    confirm_subscribe: '确认订阅', // 确认订阅
    remaining_payment_time: '剩余支付时间', // 剩余支付时间
    payment_amount: '支付金额', // 支付金额
    wechat_scan_qrcode_to_pay: '请使用微信扫码支付', //
    alipay_scan_qrcode_to_pay: '请使用支付宝扫码支付', //
    please_pay_on_time: '请及时支付' //
  },
  resource: {
    title: '服务资源加量包',
    resource_ype: '资源类型',
    translation_quota_add_on_package: '翻译额度（TQ）加量包',
    pdf_plus_add_on_package: 'PDF Plus 加量包',
    resource_specifications: '资源规格',
    add_on_package: '加量包',
    pages: '页',
    quantity_to_purchase: '购买数量',
    no_data: '暂无数据',
    payment_methods: '支付方式',
    amount_to_pay: '支付金额',
    buy_now: '立即购买'
  },
  selected_tsl_desc: {
    title: '为什么要用择优翻译？',
    description_1: '单一模型难以应对翻译中多场景、多语种及复杂语境等多样化需求。',
    description_2: '择优翻译：多模型协同翻译 + AI 评分择优，精准输出最佳译文。',
    description_3: '优势：有效排除错误结果，显著提升翻译质量，尤其适合高准确度需求的翻译场景。',
    description_4: '请看以下择优翻译的对比效果',
    examples: {
      original_text: '原文',
      best_translation: '最佳译文',
      selected_model: '择优模型',
      table: {
        translation_model: '翻译模型',
        translation_result: '译文',
        translation_score: '评分',
        translation_rationale: '评分说明',
        translation_model_version: '模型版本'
      },
      note_01: '说明：以上样例采用同价位层级的不同模型进行翻译，并对翻译结果进行评分得到的数据。',
      note_02: 'AI 模型翻译及评分存在一定的随机性，重现测试时结果可能略有差异，但总体相近。',
      note_03: '以上数据仅供参考。强烈建议您直接体验: '
    }
  },
  faq_common: {
    title: '常见问题',
    description: '有疑问？请先查看以下常见问题解答，或联系我们的客服。',
    items: {
      item_10: {
        label: '使用 AI 择优翻译有什么优势？',
        content:
          '- 不同的 AI 翻译模型使用的模型架构、训练数据、训练算法、生成策略等各有不同，每个 AI 翻译模型都会有其独特的优势和不足。因此，单一翻译模型难以在不同语种、不同语句、不同语境下都能达到最优的翻译效果。   \n - 精挑翻译首创 __AI 择优翻译__ 功能，同时调用多个 AI 翻译模型进行翻译，再用较强的 AI 模型对多个翻译结果进行评分择优，有效排除明显错误的翻译结果，并 __让每一句原文都能获得最优的译文__ 🎯。  \n - 没有对比就没有伤害，欢迎亲自体验 👉 [AI 择优翻译](text)'
      },
      item_20: {
        label: '我可以免费使用精挑翻译吗？',
        content:
          '- 是的，您可以完全免费地使用精挑翻译的所有功能。有多款免费翻译模型可以直接使用，其它翻译模型您可以配置 API Key 后使用（[各模型 API Key 申请及配置](docs/service/configure)）。   \n - 如果你觉得逐个申请和配置翻译模型的 API Key 比较麻烦，您也可以直接购买我们的 Plus 会员套餐服务。Plus 会员可以直接使用 DeepSeek、OpenAI、Gemini、智谱AI、豆包、通义千问 等套餐中包含的高级翻译模型。'
      },
      item_30: {
        label: 'Plus 会员提供的 DeepSeek、OpenAI、Gemini、智谱AI、豆包、通义千问 高级模型分别使用哪个模型版本？',
        content:
          '- Plus 会员可以直接使用 DeepSeek、OpenAI、Gemini、智谱AI、豆包、通义千问 高级翻译模型。高级模型支持多版本自选，您可以根据场景选择适合的版本。  \n - 各高级模型默认推荐的版本如下：  \n   1、DeepSeek：DeepSeek-V3-0324  \n   2、OpenAI： GPT-4.1-mini   \n  3、Gemini： Gemini-2.5-Flash   \n  4、智谱AI： GLM-4-Plus    \n  5、豆包： Doubao-1.5-Pro-32k   \n   6、通义千问： Qwen-Plus'
      },
      item_35: {
        label: '我经常有重要的文案需要翻译，Plus 会员可以选择模型的高级版本来翻译吗？',
        content: '- 当然可以！高级版本虽成本较高，但翻译精准度更优，尤其适用于重要文案或场景的翻译。Plus 会员的高级模型支持多版本自选，在模型设置中按需选用则可。'
      },
      item_40: {
        label: 'Plus 会员套餐中每月翻译额度是多少？',
        content: '- Plus 会员套餐中每月翻译额度为 __400万 TQ__  \n &nbsp;   \n TQ 与 Token 的换算规则请看：[《翻译额度与模型Token的换算规则》](docs/translation_quota)'
      },
      item_45: {
        label: '400万 TQ 的翻译额度相当于翻译模型多少个Token？',
        content:
          '- __400万 TQ__ 翻译额度约等于各高级模型默认版本可用于翻译的 Token 数：  \n  1、 DeepSeek-V3 ： __1000万 Token__  \n  2、GPT-4.1-mini ： __685万 Token__   \n   3、Gemini-2.5-Flash ： __1800万 Token__  \n  4、智谱 GLM-4-Plus ： __800万 Token__   \n   5、豆包 Doubao-1.5-Pro-32k ： __3300万 Token__  \n  6、通义千问 Qwen-Plus ： __3300万 Token__  \n &nbsp;   \n TQ 与 Token 的换算规则请看：[《翻译额度与模型Token的换算规则》](docs/translation_quota)'
      },
      item_50: {
        label: '翻译额度是所有高级模型共用的吗？',
        content: '- 是的，翻译额度是 Plus 会员所支持的所有高级模型共用的。'
      },
      item_60: {
        label: 'Plus 会员可以免费试用吗？',
        content: '- 可以，注册账号并登录，可免费申请试用 Plus 会员【 3 】天。  \n - 试用套餐有翻译额度限制，试用额度用完或试用期结束，你可以选择购买/订阅正式版的 Plus 会员套餐。'
      },
      item_70: {
        label: '可以用银联的信用卡订阅或购买 Plus 会员吗？',
        content:
          '- 可以，选择 Stripe 支付，支持使用 银联 ![pay_card](assets/images/logos/pay_unionpay.svg)、Visa ![pay_card](assets/images/logos/pay_visa.svg)、万事达 ![pay_card](assets/images/logos/pay_mastercard.svg) 在内的所有主流信用卡机构发行的信用卡进行订阅或购买。 '
      },
      item_75: {
        label: '可以开中国的增值税普通发票吗？',
        content:
          '- 如果你需要开中国的增值税普通发票，请使用【微信支付】或【支付宝】支付购买，然后将以下开票信息发送到服务邮箱 service&#64;selecttranslate.com ，我们将及时给您确认并开票。  \n __开票信息：__ 账户ID、服务订单号、发票抬头、统一社会信用代码/纳税人识别号、接收发票的电子邮箱  \n - __请注意：__ 如果你使用 Stripe 支付，则无法申请开中国增值税普通发票。但 Stripe 会向您的邮箱发送账单（Invoice）和收据（Receipt），绝大部份企业是支持使用 Stripe 的收据作为报销凭证的，您可以与财务部门确认是否可以使用 Stripe 的收据作为报销凭证。'
      },
      item_80: {
        label: 'Plus 会员套餐翻译额度不够用怎么办？',
        content:
          '- Plus 会员套餐中的每月翻译额度是可以满足绝大部分用户需求的。  \n 如果每月套餐翻译额度不够用，我们还提供多种规格的翻译额度加量包。您可以按需要[购买翻译额度加量包](resource?type=general_token)。'
      },
      item_90: {
        label: '购买了 Plus 会员套餐，可以申请退款吗？',
        content:
          '- __按月订单/订阅__：首次购买后 24 小时内可申请退款。  \n - __按季度订单/订阅__ ：首次购买后 48 小时内可申请退款。   \n - __年度订单/订阅__：首次购买后 72 小时内可申请退款。   \n &nbsp;  \n请注意，超过申请期限的申请将不予退款。申请退款时，请说明申请退款的原因。  \n只有在订单对应的翻译额度未被大量使用的情况下才可退款。如果订单对应的翻译额度已被大量使用，您的退款申请可能会被拒绝。   \n 如果您之前已成功申请并获得我们服务的退款，我们保留拒绝您任何其他退款请求的权利。   \n我们将根据具体使用情况审核退款申请，确保用户能公平使用我们的服务，以防止退款规则被滥用。  \n &nbsp;  \n - __退款流程__：如果您符合以上退款条件，请将退款申请发送到我们的服务邮箱 service&#64;selecttranslate.com 。我们的服务团队将审核您的申请，并会在 7 个工作日内处理并通知您。'
      },
      item_100: {
        label: '不想再续订 Plus 会员套餐，如何取消订阅？',
        content: '- 登录账户后，进入【账户】页面，在【当前套餐】中点击【取消订阅】按钮，按提示操作即可退订。'
      },
      item_110: {
        label: '会员账号有使用设备数量限制吗？',
        content: '- 有，每个会员账号最多可同时在 10 个设备（浏览器）上使用，超出限制数量时，最早登录的设备将会被退出。'
      },
      item_120: {
        label: '我想提一些产品功能改进和创新的建议，如何反馈？',
        content:
          '- 非常欢迎用户能向我们反馈产品使用中遇到的问题，也非常期待用户能结合自己的需求向我们提供更多产品功能改进和创新的建议。  \n - 您可以点击页面底部的【[问题反馈](feedback)】链接，即可进入反馈/建议表单。  \n - 您的反馈和建议对我们来说非常宝贵，我们会认真阅读每一条反馈和建议并及时回复。您可以在账户中心的【[问题反馈列表](feedback_list)】中查看回复并与我们进行沟通互动。'
      }
    }
  },
  faq_resource_pack: {
    items: {
      item_210: {
        label: '翻译额度加量包的适用范围？',
        content: '- 翻译额度加量包适用于 Plus 会员支持的所有高级翻译模型，包括 DeepSeek、OpenAI、Gemini、智谱AI、豆包、通义千问。'
      },
      item_220: {
        label: '只有 Plus 会员才能购买翻译额度加量包？',
        content: '- 是的，只有 Plus 会员才能购买和使用翻译额度加量包。'
      },
      item_230: {
        label: '翻译额度加量包的有效期是多久？',
        content: '- 加量包翻译额度在 Plus 会员服务生效期内永久有效。如果 Plus 会员服务中途到期了，还有未使用的加量包翻译额度，可以在续费 Plus 会员后继续使用。'
      },
      item_240: {
        label: 'Plus 会员套餐的每月翻译额度和加量包中的翻译额度使用优先级？',
        content: '- 会优先使用 Plus 会员套餐的每月翻译额度，只有套餐每月翻译额度使用完后，才会使用加量包中的翻译额度。'
      }
    }
  },
  check_installed: {
    // 检查是否安装插件
    extension_name: '精挑翻译插件',
    add_browser_1: '添加到',
    add_browser_2: '免费添加到',
    other_browsers: '其他浏览器',
    extension_not_installed: {
      tip_1: '您的浏览器【未安装】或【未启用】',
      tip_2: '请安装并启用插件后重试',
      tip_3: '安装并启用精挑翻译插件后即可马上体验！'
    },
    extension_version_too_low: {
      tip_1: '检测到您的',
      tip_2: '版本过低',
      tip_3: '请升级插件至最新版本后重试'
    }
  },
  extensions: {
    // crx 包安装
    crx: {
      title: '精挑翻译 Chrome 插件下载安装教程',
      download_button: '下载 crx 安装包',
      download_package: {
        title: '1. 下载插件',
        description: '点击上面的【下载 crx 安装包】按钮，下载精挑翻译的 Chrome 插件 zip 压缩包，解压后即可得到插件的 crx 安装文件。'
      },
      chrome_extensions_dev_mode: {
        title: '2. 扩展程序管理设置',
        description: '在 Chrome 浏览器地址栏输入 chrome://extensions 并回车，进入扩展程序管理界面，在右上角开启【开发者模式】。'
      },
      install_step1: {
        title: '3. 拖入安装插件',
        description: '把插件的 crx 安装文件，拖入到扩展程序管理界面。'
      },
      install_step2: {
        title: '4. 添加扩展程序',
        description: '会弹窗提示添加"精挑翻译"，点击【添加扩展程序】按钮即可完成安装。'
      },
      install_step3: {
        title: '5. 启用插件',
        description: '确保插件处于【启用】状态，如左图步骤设置，将精挑翻译固定到插件导航栏，方便后续使用。'
      },
      faq: {
        title: 'crx 方式安装常见问题',
        desc: '有疑问？请查看下面的常见问题，或者联系我们的客服。',
        items: {
          item1: {
            title: '将 crx 文件拖入扩展程序管理界面没反应，或者提示已阻止 - 不明来源。',
            desc: '请检查以上第2步操作：在浏览器地址栏输入 chrome://extensions 并回车，进入扩展程序管理界面，在右上角开启【开发者模式】，重新拖入 crx 文件即可。'
          },
          item2: {
            title: '通过 "加载已解压的拓展程序" 安装没有可选的文件，或者提示 "未能成功加载扩展程序"。',
            desc: '注意：必须用拖入 crx 文件的方式安装。不要通过【加载已解压的扩展程序】导入 crx，也不要双击 crx 文件。'
          },
          item3: {
            title: '浏览器不支持 crx 文件。',
            desc: '如果浏览器不支持 crx 文件安装，可使用 zip 文件方式安装。'
          }
        }
      }
    },
    // zip拓展程序
    zip: {
      title: '精挑翻译 插件Zip安装教程', // 精挑翻译 插件Zip安装教程
      download_button: '下载 Zip 安装包', // 下载 Zip 安装包
      download_install_package: {
        title: '获取 精挑翻译 插件安装包', // 获取 精挑翻译 插件安装包
        item1: '下载并且解压zip安装包', // 下载并且解压zip安装包
        item2: '解压后的文件夹需要永久保留，否则影响使用' // 解压后的文件夹需要永久保留，否则影响使用
      },
      open_dev_model: {
        title: '开启 开发者模式', // 开启开发者模式
        item1: '在地址栏输入 "chrome://extensions/" 并回车', // 在地址栏输入 "chrome://extensions/" 并回车
        item2: '进入扩展程序管理页面，在右上角开启 "开发者模式"' // 进入扩展程序管理页面，在右上角开启 "开发者模式"
      },
      install_package: {
        title: '安装 精挑翻译 插件', // 安装 精挑翻译 插件
        item1: '方式一：把解压出来的文件夹拖入扩展程序管理页面', // 方式一：把解压出来的文件夹拖入扩展程序管理页面
        item2: '方式二：点击左上角 "加载已解压的拓展程序"，选择解压出来的文件夹' // 方式二：点击左上角 "加载已解压的拓展程序"，选择解压出来的文件夹
      },
      enable_package: {
        title: '启用 精挑翻译 插件', // 启用 精挑翻译 插件
        item1: '点击状态栏 "拓展程序" 按钮', // 点击状态栏 "拓展程序" 按钮
        item2: '点击 "固定" 即可完成设置' // 点击 "固定" 即可完成设置
      }
    }
  },
  selected_trans: {
    title: 'AI 择优翻译、对比翻译、文本翻译', // AI 择优翻译、对比翻译、文本翻译
    multiple_translation_results: '以下为该句多个翻译结果：',
    auto_detect: '自动检测',
    settings: {
      translation_models: '翻译模型',
      original_language: '原文语言',
      target_language: '目标语言',
      auto_translate: '自动翻译',
      history: '历史记录',
      custom_results: '自选结果',
      trans_models: {
        title: '对比翻译模型',
        selected_01: '已选择',
        selected_02: '个模型',
        collapse: '收起', // 收起(折叠)
        expand: '显示更多', // 显示更多(展开)
        close: '关闭', // 关闭
        target_language_is_not_supported: '不支持当前目标语言'
      },
      ai_selected_translation: 'AI 择优翻译',
      ai_selected_translation_desc: '基于对比翻译，AI 自动评分多个翻译结果，智能排序并选出最优译文。',
      selected_mode: '逐句择优',
      selected_mode_desc: '启用：逐句择优，关闭：全文择优',
      selected_rationale: '择优分析',
      selected_rationale_desc: '显示择优评分的说明（会增加翻译额度使用量）',
      selected_model: '择优模型',
      selected_model_desc: '对翻译结果进行择优评分和分析的 AI 模型'
    },
    main: {
      translating: '翻译中...',
      scoring: '评分中...',
      scoring_and_analyzing: '择优分析中...',
      waiting_for_optimization: '等待择优...',
      selected_score: '择优评分',
      ai_selected_result: 'AI 择优结果',
      select_this_result: '选择此结果',
      translation_exception: '翻译异常',
      copy: '复制'
    },
    operation: {
      traditional_ranslation_btn: '传统翻译',
      compare_translation_btn: '对比翻译',
      compare_translation_btn2: '对比翻译',
      ai_selected_translation_btn: '择优翻译',
      traditional_ranslation_btn2: '传统翻译',
      restore: '还原',
      re_scoring: '重新择优',
      re_translate: '重新翻译',
      re_compare: '重新对比',
      expand: '展开',
      collapse: '隐藏'
    },
    textarea: {
      select_at_least_one_model: '请至少选择 1 个翻译模型',
      text_to_be_translated: '请输入要翻译的文本',
      ai_selected_min_models_tip: 'AI 择优 - 需要选择 2 个或以上翻译模型'
    },
    history: {
      no_history: '暂无历史记录',
      search_history: '搜索历史记录',
      clear_history: '清空历史',
      clear_history_confirm: '确认要清空所有历史记录吗？',
      by_sentence: '逐句择优',
      by_fulltext: '全文择优',
      today: '今天',
      yesterday: '昨天'
    },
    error_info: {
      original_text: '原文内容',
      no_original_text: '没有原文内容',
      retry: '重试',
      retry_all: '重试全部'
    },
    error: {
      text_length_exceeded: '文本长度超出限制',
      text_length_exceeded_desc: '文本长度不能超过 {max} 个字符',
      same_language: '原文语言与目标语言相同',
      same_language_desc: '原文语言与目标语言相同，请检查翻译设置！'
    }
  },
  footer: {
    // 页脚
    help_enter: '帮助中心', // 帮助中心
    use_doc: '使用文档', // 使用文档
    faq: '常见问题', // 常见问题
    feedback: '问题反馈', // 问题反馈
    terms_policies: '条款和政策', // 条款和政策
    terms_of_service: '服务条款', // 服务条款
    privacy_policy: '隐私政策', // 隐私政策
    company: '公司', // 公司
    about: '关于', // 关于
    information: '资讯', // 资讯
    wechat_official_account: '微信公众号', // 微信公众号
    wechat_group: '微信交流群', // 微信交流群
    copyright: 'Copyright © 智应软件有限公司版权所有' // 版权
  },
  http_service: {
    http_no_content_returned_by_backend_api: '后端 API 没有返回内容',
    http_operation_failed_please_retry: '操作失败，请重试',
    http_400_request_error: '请求错误',
    http_401_authentication_has_expired_please_login_again: '认证已过期，请重新登录',
    http_403_no_access_permission_please_log_in_first: '请先登录',
    http_404_error_with_the_requested_url: '请求的 URL 出错：',
    http_408_request_timeout: '请求超时',
    http_500_internal_service_error: '内部服务错误',
    http_501_service_not_implemented: '服务未实现',
    http_502_gateway_error: '网关错误',
    http_503_service_unavailable: '服务不可用',
    http_504_gateway_timeout: '网关超时',
    http_505_http_version_not_supported: 'HTTP 版本不受支持'
  },
  coupon: {
    code: {
      title: '兑换码',
      instructions: {
        title: '兑换说明',
        instruction_01: '1. 使用兑换码将直接升级为 Plus 会员。',
        instruction_02: '2. 如果你已经是 Plus 会员，使用兑换码将延长 Plus 会员有效期。',
        instruction_03: '3. 如果你获得的是指定发放的兑换码，请在 "我的兑换码" 列表中查找 "未使用" 的兑换码并 "兑换"。',
        instruction_04: '4. 兑换码存在有效期，过期无法兑换，请尽快使用。',
        instruction_05: '5. 本内容为虚拟产品，兑换后不可退款。'
      },
      use_title: '使用兑换码',
      input_tips: '请输入兑换码',
      redeem: '兑换',
      redeem_success: '兑换成功',
      my_code: '我的兑换码',
      table: {
        query: {
          use_all: '全部',
          use_unused: '未使用',
          use_used: '已使用'
        },
        redemption_code: '兑换码',
        product_specification: '规格',
        usage_type: '使用类型',
        claim_time: '领取时间',
        redemption_start_time: '兑换开始时间',
        redemption_end_time: '兑换结束时间',
        redemption_time: '兑换时间',
        status: '状态',
        operation: '操作',
        redeem: '兑换'
      }
    }
  },
  feedback: {
    title: '问题反馈',
    form: {
      header: '创建 问题 / 建议',
      type: '类型',
      type_options: {
        feedback: '问题反馈',
        suggestion: '功能建议'
      },
      content: {
        label: '内容',
        placeholder: '请输您要反馈/建议的内容（500字以内）'
      },
      screenshot: {
        label: '截图',
        upload_text: '点击或拖拽到此处添加图片',
        drop_text: '释放鼠标上传图片'
      },
      contact: {
        label: '联系方式',
        placeholder: '可提供：邮箱 / 微信 / QQ等，以方便我们快速联系'
      },
      submit: '提交',
      submitting: '提交中...',
      wechat_group_tip: '建议加入微信交流群快速反馈'
    },
    image_viewer: {
      title: '图片预览'
    },
    messages: {
      upload_limit_exceeded: '超出最大上传限制',
      max_images: '最多只能上传{count}张图片',
      invalid_file_type: '文件格式错误',
      only_jpg_png: '只能上传JPG或PNG格式图片',
      file_too_large: '文件过大',
      max_file_size: '图片大小不能超过3MB',
      content_required: '请输入反馈内容',
      success: '成功',
      submit_with_images_success: '您的反馈和图片已成功提交！',
      image_upload_failed: '图片上传失败',
      partial_success: '反馈已提交，但图片上传失败',
      submit_success: '您的反馈已成功提交！客服会尽快回复您。',
      submit_failed: '提交失败',
      unknown_error: '未知错误'
    },
    list: {
      title: '我的反馈',
      columns: {
        no: '编号',
        type: '类型',
        time: '时间',
        content: '内容',
        status: '状态',
        action: '操作'
      },
      status: {
        pending: '待处理',
        processing: '处理中',
        completed: '处理完成'
      },
      type: {
        suggestion: '功能建议',
        feedback: '问题反馈'
      },
      actions: {
        view_reply: '查看回复',
        images: '张',
        create_feedback: '创建反馈'
      },
      empty: '您还没有反馈',
      errors: {
        no_id: '无法跳转，未找到有效的 ID',
        navigation_failed: '跳转失败',
        missing_feedback_no: '缺少必要的反馈编号信息'
      }
    }
  },
  reply_list: {
    title: '问题反馈回复',
    feedback_details: {
      number: '编号',
      time: '时间',
      type: '类型',
      status: '状态',
      content: '内容',
      images: '图片',
      no_images: '无图片'
    },
    images: {
      count: '张',
      view_all: '查看全部'
    },
    reply: {
      user: '用户',
      manager: '售后经理',
      button: '回复',
      cancel: '取消',
      submit: '提交',
      content_label: '内容',
      content_placeholder: '请输入信息，支持图片粘贴及拖拽上传',
      images_label: '截图',
      upload_text: '点击或拖拽到此处添加图片',
      drop_text: '释放鼠标上传图片'
    },
    status: {
      completion: '是否完结',
      mark_as_completed: '标记为已完成',
      completed: '已完结',
      not_completed: '未完结',
      update_success: '更新成功',
      update_success_message: '问题状态已更新为处理完成',
      update_failed: '更新失败',
      update_failed_message: '无法更新反馈状态',
      confirm_title: '确认提示',
      confirm_complete: '如果您反馈的问题已解决，可以点击「确认」以关闭该问题。问题关闭后可能无法再收到回复，但您仍可以随时创建新的反馈。',
      confirm: '确定',
      cancel: '取消'
    },
    loading: {
      loading_more: '正在加载更多回复...',
      all_loaded: '已加载所有',
      waiting: '回复中'
    },
    messages: {
      limit_exceeded: '超出最大上传限制',
      max_images: '最多只能上传{count}张图片',
      invalid_file_type: '文件格式错误',
      only_jpg_png: '只能上传JPG或PNG格式图片',
      file_too_large: '文件过大',
      max_file_size: '图片大小不能超过3MB',
      content_required: '请输入回复内容',
      success: '成功',
      submit_success: '您的回复已成功提交！客服会尽快回复您。',
      image_upload_failed: '图片上传失败',
      partial_success: '回复已提交，但图片上传失败',
      missing_data: '缺少问题数据',
      submit_failed: '提交失败',
      unknown_error: '未知错误',
      file_input_error: '文件选择器无法打开',
      reload_or_use_drag: '请尝试刷新页面或改用拖拽方式上传图片'
    }
  },
  error_info: {
    // 错误信息
    retry: '重试' // 重试
  },
  member_message: {
    title: '消息通知',
    tabs: {
      all_messages: '全部消息',
      unread_messages: '未读消息'
    },
    toast: {
      mark_read: '标记已读',
      succeed: '成功',
      mark_read_success: '标记已读成功',
      prompt: '提示',
      prompt_content: '请选择要标记已读的消息',
      prompt_failed: '标记失败',
      prompt_failed_content: '标记失败，请重试',
      loading_failed: '加载失败',
      loading_failed_content: '获取取消息数据失败，请刷新页面重试',
      get_message_failed: '获取消息失败',
      get_message_failed_content: '请检查网络连接或稍后再试',
      delete_success: '删除成功',
      delete_success_content: '消息已删除',
      delete_failed: '删除失败',
      delete_failed_content: '删除失败，请重试',
      delete_prompt_content: '未选中任何消息',
      delete_title: '删除消息',
      delete_tip: '确认删除选定消息吗？此操作不可逆，请慎重考虑！'
    },
    loading: {
      loading_message: '正在加载消息...'
    },
    read: '已读',
    unread: '未读',
    message_title: '消息标题',
    message_status: '消息状态',
    create_datetime: '接收时间',
    delete: '删除',
    message_preview: '消息预览',
    action: '操作',
    more_actions: '更多操作'
  },
  translate_type: {
    text: {
      title: '择优翻译',
      description: '传统翻译、AI 择优翻译'
    },
    file: {
      title: '文档翻译',
      description: '支持多种文件格式'
    },
    miner_u: {
      title: 'MinerU 文档翻译',
      description: '精准解析复杂格式内容',
      display_mode: {
        original_text_comparison: '原文对照模式',
        results_focus: '结果聚焦模式'
      }
    },
    pdf_plus: {
      title: 'PDF Plus',
      description: '公式、图表高保真还原'
    }
  },
  document_translation: {
    // 文档翻译
    title: '文档翻译',
    description: '免费的文档翻译功能，支持 PDF文件、Markdown文件、HTML文件、text文件等多种格式文档的翻译。',
    pdf_bilingual_translation: 'PDF 双语对照翻译',
    pdf_bilingual_translation_desc: '完全免费的 PDF 翻译功能，逐页双语对照，日常外语文档快速翻译，满足大部分 PDF 翻译阅读需求。',
    open_file: '打开文件',
    open_new_file: '打开新文件',
    select_new_file: '选择新文件',
    open_file_tips: '支持 pdf、epub、txt、html、docx、md、srt、ass 等格式文件',
    // DOCX 文档翻译
    docx_title: 'Word 文档翻译',
    docx_description: '免费的 Word 文档翻译功能，支持 .doc、.docx 格式文档的智能解析和翻译。',
    parsing_document: '正在解析文档...',
    parsing_progress: '解析进度',
    parse_error: '解析失败',
    parse_failed: '文档解析失败',
    invalid_file_type: '不支持的文件格式，请上传 .doc 或 .docx 文件',
    file_too_large: '文件过大，请上传小于 50MB 的文件',
    upload_link_failed: '获取上传链接失败',
    query_failed: '查询解析状态失败',
    content_empty: '解析内容为空',
    download_url_missing: '下载链接缺失',
    parse_timeout: '解析超时，请稍后重试',
    single_column: '单栏模式',
    split_view: '分栏模式',
    show_original: '显示原文',
    show_translation: '显示翻译',
    original_text: '原文',
    translated_text: '翻译',
    select_view_mode: '请选择显示模式',
    sync_scroll: '同步滚动',
    split_ratio: '分栏比例',
    export_file: '导出文件',
    export_epub: '导出 EPUB',
    export_html: '导出 HTML',
    export_txt: '导出 TXT',
    export_markdown: '导出 Markdown',
    export_docx: '导出 Word',
    export_pdf: '导出 PDF',
    cancel_translation: '取消翻译',
    start_translation: '开始翻译',
    bilingual_translation: '双语对照翻译',
    fulltext_translation: '全文翻译 (仅译文)',
    pdf: {
      label: 'PDF 文件',
      open_file_tip: '打开 pdf 文件开始翻译'
    },
    ppt: {
      // title: 'PPT 文件翻译',
      label: 'PPT 文件'
      // open_file_tip: '打开 ppt/pptx 文件开始翻译'
    },
    image: {
      label: '图片'
    },
    epub: {
      title: '电子书 EPUB 翻译',
      label: 'ePub 文件',
      open_file_tip: '打开 epub 文件开始翻译'
    },
    html: {
      title: 'HTML 文件翻译',
      label: 'HTML 文件',
      open_file_tip: '打开 html/txt 文件开始翻译'
    },
    txt: {
      label: '文本翻译'
    },
    markdown: {
      title: 'Markdown 文件翻译',
      label: 'Markdown 文件',
      open_file_tip: '打开 markdown 文件开始翻译'
    },
    docx: {
      title: 'Docx 文件翻译',
      label: 'Docx 文件',
      open_file_tip: '打开 doc/docx 文件开始翻译'
    },
    subtitle: {
      open_file_tip: '打开 srt/vtt/ass/ssa/sbv/lrc 文件开始翻译',
      title: '字幕文件翻译',
      label: '字幕文件',
      export_trans_subtitle: '导出译文字幕',
      export_bilingual_subtitle: '导出双语字幕',
      viewer_table_head_start_time: '开始时间',
      viewer_table_head_end_time: '结束时间',
      viewer_table_head_content: '内容',
      viewer_table_head_translation: '译文（可编辑）',
      please_select_subtitle_file: '请选择 字幕 文件',
      unsupported_subtitle_file_format: '不支持的字幕文件格式',
      failed_read_subtitle_file: '字幕文件读取失败',
      subtitle_file_content_error: '未解析到任何字幕内容，请检查文件内容',
      export_settings: {
        title: '导出字幕设置',
        subtitle_order: '字幕顺序：',
        translation_first: '译文在前',
        original_first: '原文在前',
        subtitle_track: '字幕轨道：',
        single_track: '单轨',
        double_track: '双轨'
      }
    },
    miner_u: {
      label: 'MinerU'
    },
    pdf_plus: {
      label: 'PDF Plus'
    }
  },
  free_pdf_translation: {
    title: 'PDF 文档翻译',
    description: '完全免费的 PDF 双语对照翻译功能，满足您大部分外语 PDF 文档的翻译阅读需求。',
    open_a_new_pdf_file: '打开新 PDF 文件',
    empty_state_title: '打开 PDF 文件开始翻译',
    empty_state_description: '拖拽文件到此处或点击选择文件',
    select_file: '选择文件',
    drop_file_here: '释放文件到此处',
    translate_all_and_download: '翻译全部并下载',
    please_select_pdf_file: '请选择 PDF 文件',
    download_pdf_dialog_title: '下载文件',
    download_pdf_dialog_description: '下载前请先检查翻译效果和输出格式',
    translated: '已翻译',
    the: '第',
    page: '页',
    pages: '页',
    total: '共',
    translate_all_pages_before_downloading: '翻译所有页面后再下载',
    translation_mode: '翻译模式',
    export_format: '导出格式',
    bilingual_translation: '双语对照（灰色背景）', // 双语对照（灰色背景）
    fulltext_translation: '全文翻译（仅译文）', // 全文翻译（仅译文）
    text_based_pdf: '文字版 PDF', // 文字版 PDF
    image_based_pdf: '图片版 PDF', // 图片版 PDF
    text_based_pdf_download_tips_1: '文字版 PDF 下载',
    text_based_pdf_download_tips_2: '点击【下载】会显示浏览器的打印界面，在【目标打印机】中选择【另存为 PDF】，点击【保存】即可。',
    download_pdf_dialog_tips: '注意：建议避免下载超过 300 页或内容过大的 PDF 文件，以免因系统资源不足导致无法下载。',
    download: '下载',
    no_content_to_print: '没有需要打印的内容',
    translating_please_wait: '正在翻译，请勿关闭窗口...',
    downloading_please_wait: '正在下载，请勿关闭窗口...',
    loaded: '已加载'
  },
  mineru_doc_translation: {
    title: 'MinerU 文档翻译',
    description: 'MinerU 文档翻译，使用 MinerU 精确解析提取文档中的文本、图片、表格、公式等元素内容，再由精挑翻译对解析后的文本进行智能翻译。文档在线浏览与文档解析翻译完美结合，为您提供极致的文档翻译体验！'
  },
  pdf_plus: {
    title: 'PDF Plus（专业 PDF 翻译）', // PDF Plus（专业 PDF 翻译）
    description: '专业 PDF 文档翻译，公式、图表高保真还原', // 专业 PDF 文档翻译，公式、图表高保真还原
    please_log_in_first: '请先登录', // 请先登录
    upload_file: '上传文件', // 上传文件
    upload_file_tips: '请上传 PDF 格式文件', // 请上传 PDF 格式文件
    upload_file_name: '文件名', // 文件名
    pages: '页',
    total: '共',
    translation_model: '翻译模型', // 翻译模型
    target_language: '目标语言', // 目标语言
    translation_mode: '译文显示', // 译文显示
    page_range: '页面范围', // 页面范围
    specify_page_range: '指定页面范围', // 指定页面范围
    specify_page_range_tips_1: '不设置，则默认翻译整个 PDF',
    specify_page_range_tips_2: '指定页码，使用英文逗号分隔：1,3,-1',
    specify_page_range_tips_3: '使用"-"指定范围，如第1页到第5页：1-5',
    specify_page_range_tips_4: '第5页到倒数第3页：5--3',
    specify_page_range_tips_5: '倒数第5页到倒数第2页：-5--2',
    specify_page_range_tips_6: '组合使用页码及范围，使用英文逗号分隔：1,3,5-8,-4--2',
    exceeds_pdf_plus_limit_tips_1: '要翻译页数', // 要翻译页数 AA
    exceeds_pdf_plus_limit_tips_2: '页，超过了您的 PDF Plus 剩余页数', // 页，超过了您的 PDF Plus 剩余页数
    exceeds_pdf_plus_limit_tips_3: '页', // 页
    page_range_limit_tips_1: '当前文件仅有', // 当前文件仅有 XX
    page_range_limit_tips_2: '页，请输入正确页码范围', // 页，请输入正确页码范围
    translate_now: '立即翻译', // 立即翻译
    pdf_plus_remaining_pages_tips_1: 'PDF Plus 剩余页数：本月会员套餐', // PDF Plus 剩余页数：本月会员套餐
    pdf_plus_remaining_pages_tips_2: '页，加量包', // 页，加量包
    pdf_plus_remaining_pages_tips_3: '页', // 页
    purchase_add_on_package: '购买加量包', // 购买加量包
    parsing_the_file: '正在解析文件...', // 正在解析文件...
    bilingual_translation: '双语对照（灰色背景）', // 双语对照（灰色背景）
    fulltext_translation: '全文翻译（仅译文）', // 全文翻译（仅译文）
    messages: {
      parsing_failed: '解析失败',
      parsing_failed_tips_1: '上传文件大小不能超过 100MB',
      parsing_failed_tips_2: '无法解析文件 - 文件可能已损坏或加密方式不受支持',
      parsing_failed_tips_3: '无法解析加密文件',
      parsing_failed_tips_4: '请检查文件',
      loading_failed: '加载失败', // 加载失败
      loading_failed_tips_1: '该文件已加密，请解密后重新上传',
      loading_failed_tips_2: '产品/会员服务已过期，请续费后重试'
    },
    back: '返回'
  },
  pdf_plus_record: {
    // PDF 解析记录
    title: 'PDF 解析记录',
    table: {
      index: '序号',
      file_name: '文件名',
      parsed_page_count: '页数',
      create_datetime: '时间',
      parsing_status: '状态',
      actions: '操作'
    },
    parsing_status: {
      unparsed: '未解析',
      parsing: '解析中',
      failed: '解析失败',
      success: '解析成功'
    },
    view: '查看',
    delete: '删除',
    delete_confirm_title: '删除记录',
    delete_confirm_content: '确认删除选中记录吗？',
    delete_success: '删除成功',
    delete_success_tips: '已删除'
  },
  pdf_plus_introduction: {
    // PDF Plus 介绍
    title: 'AI 驱动的专业 PDF 文档翻译',
    description: 'PDF Plus 基于领先的 AI 视觉处理技术，能精确解析 PDF 文档或图片中的手写文本、数学方程式、复杂表格、代码片段、化学公式等内容，适用于学术论文和各类专业文档的翻译。',
    parsing_type: {
      // 解析类型
      text_and_handwriting: '文本和手写内容',
      text_and_handwriting_desc: '智能提取图片和 PDF 中的印刷文本和手写内容。',
      mathematical_equations: '数学公式',
      mathematical_equations_desc: '采用博士级别的数学算法，精确解析图片和文档中的方程式。',
      complex_tables: '复杂表格',
      complex_tables_desc: 'AI 视觉处理，轻松应对复杂的表格。',
      code_snippets: '代码片段',
      code_snippets_desc: '从图像和 PDF 中提取格式完美的代码片段。',
      chemical_formulas: '化学公式',
      chemical_formulas_desc: '基于 OCR 技术，识别并提取化学公式和图表。'
    },
    features: {
      bilingual_translation: '逐段双语对照',
      bilingual_translation_desc: '在每段原文后以鲜明的格式显示译文，逐段对比，清晰易读。',
      supports_scanned_versions: '支持扫描版 PDF',
      supports_scanned_versions_desc: '采用 AI + OCR（光学字符识别）技术，支持扫描版 PDF 或文档中图片格式内容的精确解析翻译。',
      layout_conversion: '双栏、三栏布局转换',
      layout_conversion_desc: '针对学术论文常用的双栏、三栏布局，统一转换为易于阅读的单栏格式，实现更优雅的内容排版，提升阅读体验。'
    },
    export: {
      title: '导出翻译内容',
      html: '导出为 HTML',
      html_desc: '可将翻译后的内容以 HTML 格式导出保存，方便在不同设备的浏览器中阅读。',
      pdf: '导出为 PDF',
      pdf_desc: '可将翻译后的内容以 PDF 格式导出保存，会原样保留在线翻译阅读时的排版和样式。'
    }
  },
  tutorial: {
    table_of_contents: '内容目录'
  },
  safari_guide: {
    title: '扩展初始化设置',
    description: '快速了解如何在 Safari 浏览器中启用精挑翻译扩展',
    previous_step: '上一步', // 上一步
    next_step: '下一步', // 下一步
    get_started: '开始使用', // 开始使用
    step1: {
      title: '🎉 欢迎！现在启用插件',
      description: '精挑翻译 Safari 扩展已经成功安装，现在需要启用它并授予必要的权限。',
      content: {
        click_icon: '1. 点击搜索框左侧图标',
        click_manage_extensions: '2. 点击选项里的「管理扩展」',
        open_selecttranslate: '3. 开启「精挑翻译」'
      }
    },
    step2: {
      title: '允许扩展访问网站',
      description: '请选择"允许"以确保在所有网站上获得翻译功能',
      content: {
        click_selecttranslate: '1. 点击选项里的「精挑翻译」',
        select_always: '2. 选择「始终允许」',
        select_always_website: '3. 选择「在每个网站上始终允许」'
      }
    },
    step3: {
      title: '体验网页翻译',
      article: {
        title: 'Digital Life in the Modern World'
      }
    }
  },
  doc_trans_progress: {
    trans_progress: '翻译进度',
    doc_trans_progress: '文档翻译进度',
    the: '第',
    page: '页',
    translated: '已翻译',
    translation_in_progress: '翻译中',
    no_translation_needed: '无需翻译',
    waiting: '等待中',
    failed_nodes: '处失败',
    failed: '失败',
    total: '共'
  },
  // 欢迎页
  welcome: {
    title: '欢迎使用精挑翻译',
    description: '下面通过几个步骤快速了解我们的核心功能 👇',
    tooltip_pc: '向下滚动开始探索', // 桌面端描述
    tooltip_mobile: '向下滑动开始探索', // 移动端描述
    click_floating_button: '你可以通过点击右侧的悬浮按钮',
    to_translate: '进行翻译',
    button: {
      experience: '立即体验',
      go_experience: '前往体验'
    },
    more_translation_features: {
      title: '更多翻译功能',
      description: '深入应用场景，全新的翻译体验，等您来探索！'
    }
  },
  epub: {
    reader: {
      title: 'EPUB 阅读器',
      loading: '正在加载 EPUB 文件...',
      error: '加载失败',
      error_invalid_file: '无效的 EPUB 文件',
      error_load_failed: '文件加载失败，请重试',
      prev_page: '上一页',
      next_page: '下一页',
      table_of_contents: '目录',
      bookmarks: '书签',
      add_bookmark: '添加书签',
      remove_bookmark: '移除书签',
      no_toc: '暂无目录',
      no_bookmarks: '暂无书签',
      progress: '阅读进度',
      chapter: '章节',
      page: '页',
      of: '/',
      close_sidebar: '关闭侧边栏',
      bookmark_added: '书签已添加',
      bookmark_removed: '书签已移除',
      jump_to_bookmark: '跳转到书签',
      current_location: '当前位置'
    }
  }
}
