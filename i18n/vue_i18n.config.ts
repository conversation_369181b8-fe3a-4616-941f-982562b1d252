// 自定义的国际化
// 官网文档：https://i18n.nuxtjs.org/docs/getting-started
export default {
  // 语言包路径
  langDir: '../i18n/locales',
  locales: [
    {
      name: 'Simplified Chinese - 简体中文',
      code: 'zhHans',
      language: 'zh-CN',
      file: 'zh_hans.js'
    },
    {
      name: 'Traditional Chinese - 繁體中文',
      code: 'zhHant',
      language: 'zh-TW',
      file: 'zh_hant.js'
    },
    {
      name: 'English - 英语',
      code: 'en',
      language: 'en-US',
      file: 'en.js'
    }
  ],
  lazy: true, // 是否启用懒加载,https://i18n.nuxtjs.org/docs/guide/lazy-load-translations
  // 默认语言
  defaultLocale: 'zhHans',
  // 设置翻译策略, 不同策略对应不同行为，如果我们希望在路由上添加翻译类型作为前缀，例如：/zh/home，可以使用 prefix、prefix_except_default(默认语言不增加翻译类型前缀)
  strategy: 'prefix',
  // 启用浏览器语言检测，以便在访问者第一次访问您的站点时自动将其重定向到首选语言环境。
  // nuxt-i18n 会在 cookie 中添加一个用于存储当前语言环境的变量，当我们修改语言时，nuxt-i18n 会更新它
  detectBrowserLanguage: {
    // 启动 cookie
    useCookie: true,
    // 用于存储当前语言环境的变量名
    cookieKey: 'ztsl_i18n_language',
    // (建议用于改进SEO) -仅检测站点根路径(/)上的浏览器区域设置。只有当使用策略而不是“no_prefix”时才有效。
    // 为了获得更好的 SEO，建议设置为 redirectOnroot （这是默认值）。设置后，仅当用户访问站点的根路径 （） '/' 时，才会尝试语言检测。
    // 这允许爬网程序访问请求的页面，而不是根据检测到的区域设置被重定向走。它还允许链接到特定区域设置中的页面。
    redirectOn: 'root',
    // https://i18n.nuxtjs.org/docs/guide/browser-language-detection
    // 要在用户每次访问应用程序时重定向用户并保留其所选选项，请启用 alwaysRedirect ：
    alwaysRedirect: true
  }
}
