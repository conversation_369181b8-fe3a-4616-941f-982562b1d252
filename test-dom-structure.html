<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM Structure Test</title>
    <style>
        .container {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            position: relative;
        }
        
        #outerContainer {
            display: block;
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        #viewerContainer {
            display: block;
            position: relative;
            width: 100%;
            height: 100%;
            overflow: auto;
        }
        
        #viewer {
            display: block;
            position: relative;
        }
    </style>
</head>
<body>
    <h1>DOM Structure Test</h1>
    <div id="status">Testing...</div>
    <button onclick="testDOMStructure()">Test DOM Structure</button>
    <button onclick="resetDOM()">Reset DOM</button>
    
    <div class="container">
        <div id="outerContainer">
            <div id="viewerContainer" tabindex="0">
                <div id="viewer" class="pdfViewer">
                    <p>Test content</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testDOMStructure() {
            const statusDiv = document.getElementById('status');
            const container = document.getElementById('outerContainer');
            const viewerContainer = document.getElementById('viewerContainer');
            const viewer = document.getElementById('viewer');
            
            console.log('Testing DOM structure...');
            
            // 检查元素是否存在
            if (!container || !viewerContainer || !viewer) {
                statusDiv.textContent = 'Error: Some elements are missing!';
                return;
            }
            
            // 检查 offsetParent
            console.log('Container offsetParent:', container.offsetParent);
            console.log('ViewerContainer offsetParent:', viewerContainer.offsetParent);
            console.log('Viewer offsetParent:', viewer.offsetParent);
            
            // 检查样式
            const containerStyle = getComputedStyle(container);
            const viewerContainerStyle = getComputedStyle(viewerContainer);
            const viewerStyle = getComputedStyle(viewer);
            
            console.log('Container display:', containerStyle.display, 'position:', containerStyle.position);
            console.log('ViewerContainer display:', viewerContainerStyle.display, 'position:', viewerContainerStyle.position);
            console.log('Viewer display:', viewerStyle.display, 'position:', viewerStyle.position);
            
            // 模拟 scrollIntoView 检查
            if (!viewer.offsetParent) {
                statusDiv.textContent = 'Error: viewer offsetParent is null!';
                statusDiv.style.color = 'red';
                
                // 尝试修复
                console.log('Attempting to fix offsetParent...');
                viewer.style.position = 'static';
                viewer.offsetHeight; // 触发重排
                viewer.style.position = 'relative';
                viewer.offsetHeight; // 再次触发重排
                
                if (viewer.offsetParent) {
                    statusDiv.textContent = 'Fixed: viewer offsetParent restored!';
                    statusDiv.style.color = 'green';
                } else {
                    statusDiv.textContent = 'Failed to fix viewer offsetParent';
                    statusDiv.style.color = 'red';
                }
            } else {
                statusDiv.textContent = 'Success: All offsetParents are valid!';
                statusDiv.style.color = 'green';
            }
        }
        
        function resetDOM() {
            const statusDiv = document.getElementById('status');
            const container = document.getElementById('outerContainer');
            const viewerContainer = document.getElementById('viewerContainer');
            const viewer = document.getElementById('viewer');
            
            // 重置样式
            container.style.display = 'block';
            container.style.position = 'relative';
            container.style.width = '100%';
            container.style.height = '100%';
            
            viewerContainer.style.display = 'block';
            viewerContainer.style.position = 'relative';
            viewerContainer.style.width = '100%';
            viewerContainer.style.height = '100%';
            viewerContainer.style.overflow = 'auto';
            
            viewer.style.display = 'block';
            viewer.style.position = 'relative';
            
            // 清理内容
            viewer.innerHTML = '<p>Test content</p>';
            viewerContainer.scrollTop = 0;
            viewerContainer.scrollLeft = 0;
            
            // 强制重排
            container.offsetHeight;
            viewerContainer.offsetHeight;
            viewer.offsetHeight;
            
            statusDiv.textContent = 'DOM reset complete';
            statusDiv.style.color = 'blue';
            
            console.log('DOM reset complete');
        }
        
        // 初始测试
        setTimeout(testDOMStructure, 100);
    </script>
</body>
</html>
