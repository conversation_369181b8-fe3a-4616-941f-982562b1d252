# Production license for @nuxt/ui-pro, get one at https://ui.nuxt.com/pro/purchase
NUXT_UI_PRO_LICENSE=6F38B90A-DC35-426D-A237-664819CEBD44
# Public URL, used for OG Image when running nuxt generate
NUXT_PUBLIC_SITE_URL=

# Cookie 加密密钥 a-random-password-with-at-least-32-characters
## NUXT_SESSION_PASSWORD=vf2600qtgrc93oaal2c4386frh4err95

# 可以通过 useRuntimeConfig().public.env 获取发布环境变量
# 生产环境
DEPLOY_ENV = pro

NUXT_SYSTEM_TITLE=精挑翻译

# APP_HEAD_META_ROBOTS=false :不限制搜索引擎抓取
APP_HEAD_META_ROBOTS=false

# 后端服务 API 地址
BACKEND_SERVICE_API_URL=https://api.selecttranslate.com # http://*************:9000

# Google登录：客户端 ID
GOOGLE_LOGIN_CLIENT_ID=553697912889-mg688r167gdc4dffv5rtodp71pt22jsp.apps.googleusercontent.com

## 使用 Stripe 支付的最小金额(人民币)
STRIPE_PAYMENT_MIN_AMOUNT = 10
