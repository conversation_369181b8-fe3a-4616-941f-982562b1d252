# 使用 Ubuntu 22.04
FROM ubuntu:22.04

# 设置非交互模式，避免 tzdata 等安装卡住
ENV DEBIAN_FRONTEND=noninteractive
ENV NODE_VERSION=22.16.0

# 更新软件包并安装依赖
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    gnupg \
    build-essential \
    git \
    openssh-client \
    && rm -rf /var/lib/apt/lists/*

# 安装 Node.js 22.16.0
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g pnpm@latest \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 默认启动 bash
CMD ["bash"]

# 1. 构建镜像
## 在你的 Dockerfile 所在目录运行：docker buildx build --platform linux/amd64 -t ztsl-web-ubuntu .
## 这里 --platform linux/amd64 是关键（如果要在MacOS上运行与 MacOS M 系统芯片原生架构匹配，请使用 --platform linux/arm64）。

# 2. 运行容器
## 使用以下命令运行容器：docker run -m 10g --memory-swap 12g --shm-size=2g -it --rm -v /Users/<USER>/ztsl-web:/app -v $HOME/.ssh:/root/.ssh ztsl-web-ubuntu
## -v /Users/<USER>/ztsl-web:/app 是将本地 ztsl-web 目录挂载到容器的 /app 目录。
## -v $HOME/.ssh:/root/.ssh 是将本地的 SSH 密钥挂载到容器中，以便在容器内使用宿主机的 SSH 密钥实现远程免密登录。
## --rm 参数会在容器停止后自动删除容器。
## 这会启动一个交互式终端，你可以在里面运行 Node.js 和 pnpm 命令

# 3. 验证安装
## 进入容器后，运行以下命令验证 Node.js 和 pnpm 是否安装成功：
## node -v
## pnpm -v