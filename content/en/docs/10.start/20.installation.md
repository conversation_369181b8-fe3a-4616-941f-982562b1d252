---
title: Installation
description: SelectTranslate is currently available as a browser extension. You can search for "SelectTranslate" in the respective browser extension store or follow the local installation method below to download and install the browser extension package.
---

## Browser Extension Store Installation

SelectTranslate extension currently primarily supports Google's Chrome browser (other browser extensions will be released soon):

- Chrome Browser Extension: [SelectTranslate on Chrome Web Store](https://selecttranslate.com/){target=\_blank}

## Local Installation of Extension

If you cannot access the Chrome Web Store, you can directly download the [latest SelectTranslate Chrome extension ZIP package](https://selecttranslate.com/en/extensions/zip){target=\_blank}. After downloading, extract it into a dedicated folder. Then, enter `chrome://extensions` in the Chrome browser address bar to open the extensions management window. Enable "Developer Mode" in the top-right corner of the window, click "Load Unpacked," and select the extracted SelectTranslate Chrome extension folder to complete the installation.
