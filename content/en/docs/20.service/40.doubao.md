---
title: Doubao
---

## Doubao

### Application Process

1. Open [Volcano Ark Management Console](https://console.volcengine.com/ark/region:ark+cn-beijing/overview?briefPage=0&briefType=introduce&type=new), login or register an account
2. **Create inference endpoint**:

   - Click the **Open Management** on the left, go in and then click the **Open Service** on the right according to the model you need to apply for an API Key
   - After activation is completed, return to the console and then create the **Inference Access Point** according to the following steps:
     1. Open the left sidebar **Online Inference**
     2. Click **Custom Inference Endpoint**
     3. Create inference endpoint
     4. Fill in the basic information
     5. Select the model and click **Confirm Access**

3. Get API Key:
   - Click the **API Key Management** in the left sidebar
   - Click **Create API Key**
   - Fill in the API Key name, click **Create**

### Usage Method

- Enter the **Extension Configuration** and select `Doubao - Using a custom API Key` to find **Inference Access Point ID** and **API Key** and paste them into the corresponding input box
