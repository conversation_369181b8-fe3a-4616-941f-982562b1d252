---
title: Gemini
---

## Gemini

### Application Process

1. Open [Gemini](https://ai.google.dev/aistudio) official website, click **Get APl key in Google Al Studio**
   <!-- ![Gemini-1](https://assets.selecttranslate.com/web/images/docs/en/gemini/gemini-api-apply-1.png) -->

2. Log in with Google Account

3. Click **Get API key** in the left menu
   <!-- ![Gemini-2](https://assets.selecttranslate.com/web/images/docs/en/gemini/gemini-api-apply-2.png) -->

4. Click **Create API key**
   <!-- ![Gemini-3](https://assets.selecttranslate.com/web/images/docs/en/gemini/gemini-api-apply-3.png) -->

5. Click **Create APl key in new project**, Create a new API key
   <!-- ![Gemini-4](https://assets.selecttranslate.com/web/images/docs/en/gemini/gemini-api-apply-4.png) -->

### Usage Method

- After creation, copy the **API key** into **Extended Configuration**, select `Gemini - Using a custom API Key` to find **API Key** and paste it into the corresponding input box
   <!-- ![Gemini-5](https://assets.selecttranslate.com/web/images/docs/en/gemini/gemini-api-apply-5.png) -->
