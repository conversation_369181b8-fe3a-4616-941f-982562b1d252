---
title: Gemini
---

<div style="letter-spacing: 0.5px">

## Gemini

Gemini 是由 Google DeepMind 团队研发的大语言模型系列，具备卓越的多语言理解、文本生成与多模态处理能力。作为 Google 推进通用人工智能（AGI）战略的重要模型，Gemini 已广泛应用于翻译、问答、代码生成等场景。

> <div style="color: #666666; font-style:normal">💡 如需了解模型的定价详情，请参阅其<a href="https://ai.google.dev/gemini-api/docs/pricing?hl=zh-cn" target="_blank">官方文档</a>。</div>

<br>

### 申请与配置流程

1. 登录 <a href="https://ai.google.dev/aistudio" target="_blank">Gemini</a> 官网，注册或登录账号。
2. 登录后，前往 <a href="https://aistudio.google.com/apikey" target="_blank">Google Al Studio</a> 获取 API Key。
3. 将 API Key 填入本扩展配置中，具体操作可参考<a href="/zhHans/docs/service/model-config-flow" target="_blank">自定义配置流程</a>。
4. 如在使用过程中遇到问题，可前往<a href="/feedback" target="_blank">问题反馈</a>页面提交反馈。

</div>
