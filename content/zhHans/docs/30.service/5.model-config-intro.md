---
title: 自定义配置说明
---

<div style="letter-spacing: 0.5px">

## 自定义配置说明

本节详细介绍翻译模型的各项配置参数及其作用，包括 API Key、接口地址、模型名称选择、关键性能参数及提示词设置。帮助您准确理解并正确填写配置项，以保证翻译服务稳定、高效运行，满足不同使用需求，实现高质量且可控的翻译体验。

<br>

### 🔑 API Key

API Key 是服务平台颁发的身份验证令牌，用于确认调用者的身份和权限。各平台的 Key 格式可能有所不同，通常需要登录对应服务商的管理控制台申请获得。

**常见问题及原因**：

- **API Key 格式错误**：例如多余的空格、换行符或非法字符，建议复制时仔细检查，避免隐藏字符干扰。
- **API Key 与所选服务不匹配**：比如使用了 OpenAI 的 Key，却配置为 DeepSeek 模型。
- **额度或权限不足**：账户余额为零、日配额已用尽，或超出服务商设定的调用频率限制（QPS）。

请确保 API Key 正确且匹配所使用的模型，同时关注账户额度和调用频率限制，以保证服务稳定运行。

<br>

<div style="
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-left: 4px solid #3B82F6;
  background-color: #F0F4FF;
  border-radius: 8px;
  font-size: 14px;
  color: #4B5563;
  font-family: 'Helvetica Neue', sans-serif;
">

  <div style="margin-right: 8px; font-size: 14px; color: #3B82F6;  ">💡</div> 
  <div>
     若使用第三方翻译服务商提供的 API Key，相关费用由对应服务商根据实际使用量结算，本扩展本身不收取任何费用。
  </div>
</div>

<br>

### 🌐 自定义 API 接口地址

一般情况下无需手动填写接口地址，系统会自动使用默认地址，该地址用于向外部翻译服务发送请求。请确保地址填写无误，否则可能导致模型调用失败。

- 你可以在服务商官网的 API 文档或控制台找到对应的接口地址。
- 不同厂商的接口地址格式各异，通常形如 `https://api.xxx.com/v1/translate` 或 `https://endpoint.xxx.cn`。
- 使用自定义模型时，请确保所填写的 API Key 与接口地址相匹配。

示例：OpenAI 的通用接口地址为 `https://api.openai.com/v1/chat/completions`，其他服务请参阅各自官方文档。

<br>

### ⚙️ 模型

模型用于准确指定所调用的翻译模型版本，确保系统正确识别并调用相应服务。由于各服务商命名规则不同，请务必填写准确以保障翻译功能的稳定性。我们已预设常见模型名称，您只需从下拉列表中选择即可，无需手动输入。

- OpenAI 常见模型名称示例：gpt-4.1-mini、gpt-4.1-nano、gpt-4o-mini 等。
- Gemini 常见模型名称示例： gemini-2.5-pro、gemini-2.5-flash 等。

<br>

### 📈 关键参数含义与设置建议

在模型配置中，还有一些关键参数可以调整，它们会影响翻译速度和效果：**每秒最大请求数**、**每次请求最大文本长度**、**每次请求最大段落数**和**温度（采样发散度）**。下面简单介绍它们的作用和推荐设置：

- **每秒最大请求数（QPS）**  
  每秒最大请求数（QPS）指插件向翻译服务发起请求的最高频率。该参数值越高，系统并发能力越强，翻译响应速度提升，但可能引发服务端速率限制，导致请求延迟或拒绝。对于高吞吐量场景（如批量网页翻译），建议适当调高该值（如 15）；若遇限流或接口调用受限（如 Gemini 翻译接口），应适当降低（如 10）以保障系统稳定性和服务连续性。

- **每次请求最大文本长度**  
  指每次发送给模型的原文字符数。设置过大（如 2000 字符）会导致单次请求内容过多，增加模型响应时间，且容易出现译文截断或顺序错乱等问题。设置过小则每次翻译内容过短，虽然响应迅速，但请求次数增加，整体效率下降。建议保持中等范围，约 1000～1500 字符，既保证上下文连贯，又兼顾响应速度和翻译质量。

- **每次请求最大段落数**  
  指单次请求中包含的原文段落数量。设置为 1 时，每次仅翻译一个段落，有助于避免多段合并带来的错位、漏译等问题。若希望模型更好地理解上下文，可适当提高此值（如 3～5）。当原文段落较短或使用上下文处理能力较强的模型（如 GPT-4 系列、Claude Opus）时，可设置为 3 或以上；若模型较弱或易出错，建议保持在 1～2，以提升译文稳定性。

- **温度（采样发散度）**  
  控制模型输出的随机性，取值范围通常为 0～2。值越低，输出越稳定，译文更准确、一致；值越高，输出更具变化性，可能产生更灵活或有创意的结果，但也可能降低准确性。一般翻译任务建议保持默认值或略低（如 0.5），以确保内容清晰可靠。如需增强译文风格或多样性，可适当调高（如 0.7）。

多数情况下，建议先使用默认参数开始翻译，根据实际效果进行微调。例如，若译文不连贯，可能是段落被过度拆分，可适当提高“每次请求最大段落数”；若翻译速度偏慢，可尝试提高“每秒最大请求数”。调整后建议进行多次测试，以确保译文质量符合预期。

<br>

### 📝 自定义提示词(System Prompt/User Prompt)说明与注意事项

一般情况下，插件内置的提示词已适用于大多数翻译场景，无需手动调整。但如果你发现译文风格过于生硬、信息遗漏，或不符合特定语气要求，可通过自定义提示词进一步引导模型生成更符合预期的译文。例如：

- **提升自然表达**：可在提示中加入“请使用通顺、符合目标语言习惯的表达方式进行翻译。”
- **强调语气一致**：可说明“请保持与原文一致的语气与正式程度。”
- **保证信息完整**：可补充“确保所有细节和隐含信息均被准确传达。”

合理设置提示词有助于提升译文质量，特别适用于对语调、准确性或风格有特殊要求的内容。

**注意事项**：

- **保持简洁明确**：提示词应简短清晰，避免包含多轮对话内容，避免信息过载导致模型理解偏差。
- **逐步验证**：每次修改后，先用少量文本测试效果，确认稳定再大规模应用。
- **针对模型调整**：不同模型对提示词的响应不同，更换模型后可能需重新优化提示词。
- **避免复杂格式要求**：不要在提示词中要求模型输出特殊格式（如 YAML 等），插件会自动解析标准翻译结果。

提示词调整属于高级配置，建议参考官方或其他示例模板进行设置。如效果不理想，可随时恢复默认，确保翻译稳定可靠。

</div>
