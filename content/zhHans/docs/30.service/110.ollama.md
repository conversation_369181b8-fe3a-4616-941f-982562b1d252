---
title: Ollama
---

<div style="letter-spacing: 0.5px">

## Ollama

Ollama 是一个开源的大型语言模型（LLM）本地部署框架，旨在简化在本地环境中运行和管理预训练语言模型的过程。它支持多种操作系统，包括 macOS、Linux 和 Windows（预览版），并提供命令行工具和 API 接口，方便用户与模型进行交互。

> <div style="color: #666666; font-style:normal">💡 本地部署的大型语言模型，完全免费使用</div>

### 本地部署及使用流程

1. 打开 <a href="https://ollama.com/" target="_blank">Ollama</a> 官网，下载适用于您操作系统的安装程序。
2. 前往<a href="https://ollama.com/library" target="_blank">模型列表</a>，选择所需模型，并在终端执行命令（例如 ollama pull gemma3:4b）以完成模型下载。
3. 启动 Ollama：在终端执行 ollama run gemma3:4b，系统自动使用默认 API Key，无需手动输入，即可开始翻译服务。
4. 如在使用过程中遇到问题，可前往<a href="/feedback" target="_blank">问题反馈</a>页面提交反馈。

### 常见问题

如果你按照教程成功运行了 Ollama 模型，并且在翻译模型配置中正确填写了模型名称和 API 接口地址，但仍无法正常使用，可能是遇到了局域网接口跨域问题。请根据你的操作系统，按以下步骤开启跨域支持：

- Windows
  打开控制面板 → 系统属性 → 环境变量 → 用户环境变量，新增两个环境变量：

  1. `OLLAMA_HOST`：设置为 `0.0.0.0`
  2. `OLLAMA_ORIGINS`：设置为 `*`（允许所有来源访问）
  3. 保存后，重启 Ollama 应用。

- macOS
  在终端中执行 `launchctl setenv OLLAMA_ORIGINS "*"`，再启动 Ollama 程序。
- Linux
  在终端中执行 `OLLAMA_ORIGINS="*" ollama serve`，再启动 Ollama 程序。
