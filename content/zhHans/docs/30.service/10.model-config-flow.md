---
title: 自定义配置流程
---

<div style="letter-spacing: 0.5px">

## 自定义配置流程

#### 1. 打开扩展设置

点击浏览器右上角扩展图标，进入扩展设置页面。

<img src="../../_nuxt/assets/images/model-setting/setting_01.png" alt="配置流程示意图"/>

<br>

#### 2. 选择自定义 API Key 并粘贴密钥

在"翻译模型"中选择"使用自定义 API Key"选项，并将您的 API Key 粘贴至输入框，完成基本接入。

<div style="display: inline-block; border: 1px solid #e5e7eb; padding: 0px 12px 12px 12px  ">
  <img style="border-radius: 8px; display: block;" src="../../_nuxt/assets/images/model-setting/setting_02.webp" alt="配置流程示意图"/>
</div>

<br>

#### 3. 配置模型参数及提示词（可选）

根据需求设置模型名称、接口地址、最大文本长度、请求频率、温度值等参数，并可自定义系统提示词和用户提示词模板。

<img style=" border-radius: 10px; border-bottom: 2px solid #e5e7eb ;" src="../../_nuxt/assets/images/model-setting/setting_03.webp" alt="配置流程示意图"/>

<br>

<div style="
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-left: 4px solid #3B82F6;
  background-color: #F0F4FF;
  border-radius: 8px;
  font-size: 14px;
  color: #4B5563;
  font-family: 'Helvetica Neue', sans-serif;
">

  <div style="margin-right: 8px; font-size: 14px; color: #3B82F6;">💡</div>
  <div>
      您可根据实际需求自定义修改这些可选项，但一般情况下，使用默认设置即可满足大多数场景。
  </div>
</div>

</div>
