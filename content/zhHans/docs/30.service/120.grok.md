---
title: Grok
---

<div style="letter-spacing: 0.5px;">

## Grok

Grok 由 xAI 团队精心打造，灵感源自经典科幻名著《银河系漫游指南》。它致力于成为一款实用且可靠的智能助手，帮助用户高效探索知识、解决复杂问题。作为先进的人工智能模型，Grok 具备持续学习和自我进化的能力，能够更深入地理解用户需求，并提供精准、个性化的回应，助力各类场景下的智能交互和决策支持。

> <div style="color: #666666; font-style:normal">💡 如需了解模型的定价详情，请参阅其<a href="https://docs.x.ai/docs/models" target="_blank">官方文档</a>。</div>

<br>

### 申请与配置流程

1. 打开<a href="https://accounts.x.ai/sign-in" target="_blank"> Grok 登录页面 </a>，注册或登录账号。
2. 登录后，前往 <a href="https://console.x.ai" target="_blank">xAI Console</a> 获取 API Key。
3. 将 API Key 填入本扩展配置中，具体操作可参考<a href="/zhHans/docs/service/model-config-flow" target="_blank">自定义配置流程</a>。
4. 如在使用过程中遇到问题，可前往<a href="/feedback" target="_blank">问题反馈</a>页面提交反馈。

</div>
