---
title: 豆包
---

<div style="letter-spacing: 0.5px">

## 豆包

豆包是字节跳动研发的多功能大语言模型助手，聚焦通用智能对话体验。产品融合中文语言理解、文本生成等核心能力，广泛适用于知识问答、内容创作、语言转换、代码辅助等场景，定位于“面向全民的实用型 AI 工具”。

> <div style="color: #666666; font-style:normal">💡 如需了解模型的定价详情，请参阅其<a href="https://bigmodel.cn/pricing" target="_blank">官方文档</a>。</div>

### 申请与配置流程

1. 打开<a href="https://console.volcengine.com/ark/region:ark+cn-beijing/overview?briefPage=0&briefType=introduce&type=new" target="_blank">火山方舟管理控制台</a>, 登录或者注册账号。
2. 登录后，在左侧点击开通管理，选择模型并开通服务，随后进入 在线推理 → 自定义推理接入点，填写信息并确认接入。
3. 接入成功后，在接入点详情页复制推理接入点 ID（如：ep-20250523163418-hwxld）。
4. 进入 API Key 管理点击创建，填写名称后生成并复制 API Key。
5. 将上述 API Key 和推理接入点 ID 填入扩展配置中，具体操作参考<a href="/zhHans/docs/service/model-config-flow" target="_blank">自定义配置流程</a>。
6. 如在使用过程中遇到问题，可前往<a href="/feedback" target="_blank">问题反馈</a>页面进行反馈。

<br>

</div>
