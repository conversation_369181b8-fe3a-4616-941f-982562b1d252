---
title: 阿里云百练大模型
---

<div style="letter-spacing: 0.5px">

## 阿里云百练大模型

通义千问（Qwen）是阿里云研发的大语言模型系列，具备强大的中文理解与生成能力，支持多轮对话、文本翻译、摘要生成、代码编写等多种自然语言处理任务。该系列模型在多项中文和多语种基准测试中表现出色，尤其适合中文语境下的翻译和内容生成场景。

> <div style="color: #666666; font-style:normal">💡 如需了解模型的定价详情，请参阅其<a href="https://help.aliyun.com/zh/model-studio/billing-for-model-studio" target="_blank">官方文档</a>。 </div>

<br>

### 申请与配置流程

1. 打开<a href="https://www.aliyun.com/product/bailian" target="_blank">阿里云百练</a> 官网， 注册或登录账号。
2. 登录后，前往 <a href="https://bailian.console.aliyun.com/?tab=app#/api-key" target="_blank">阿里云百练控制台</a> 获取 API Key。
3. 将 API Key 填入本扩展配置中，具体操作可参考<a href="/zhHans/docs/service/model-config-flow" target="_blank">自定义配置流程</a>。
4. 如在使用过程中遇到问题，可前往<a href="/feedback" target="_blank">问题反馈</a>页面提交反馈。

</div>
