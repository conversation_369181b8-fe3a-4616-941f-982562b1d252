---
title: Gemini
---

## Gemini

### 申請流程

1. 開啟[Gemini](https://ai.google.dev/aistudio)官網, 點選**Get APl key in Google Al Studio**
   <!-- ![Gemini-1](https://assets.selecttranslate.com/web/images/docs/zhHans/gemini/gemini-api-apply-1.png) -->

2. 使用谷歌帳號登入

3. 在左側選單中找到 **Get API key**
   <!-- ![Gemini-2](https://assets.selecttranslate.com/web/images/docs/zhHans/gemini/gemini-api-apply-2.png) -->

4. 點選 **Create API key**
   <!-- ![Gemini-3](https://assets.selecttranslate.com/web/images/docs/zhHans/gemini/gemini-api-apply-4.png) -->

5. 點選 **Create APl key in new project**, [創建新的]() API key
   <!-- ![Gemini-4](https://assets.selecttranslate.com/web/images/docs/zhHans/gemini/gemini-api-apply-4.png) -->

6. 創建完成後, 將**API key**複製到**擴充配置**中,選擇`Gemini-使用自訂API Key` 找到**API Key** 粘貼到對應的輸入框內
   <!-- ![Gemini-5](https://assets.selecttranslate.com/web/images/docs/zhHans/gemini/gemini-api-apply-5.png) -->
