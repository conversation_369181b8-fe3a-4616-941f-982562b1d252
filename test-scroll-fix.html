<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScrollIntoView Fix Test</title>
    <style>
        .container {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            position: relative;
            overflow: hidden;
        }
        
        #outerContainer {
            display: block;
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        #viewerContainer {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
        }
        
        #viewer {
            display: block;
            position: relative;
            padding: 20px;
        }
        
        .page {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            border: 1px solid #ddd;
            background: #f9f9f9;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .hidden-page {
            display: none;
        }
    </style>
</head>
<body>
    <h1>ScrollIntoView Fix Test</h1>
    <div id="status">Ready to test...</div>
    <button onclick="testScrollIntoView()">Test ScrollIntoView</button>
    <button onclick="hidePages()">Hide Pages</button>
    <button onclick="showPages()">Show Pages</button>
    <button onclick="testFixFunction()">Test Fix Function</button>
    
    <div class="container">
        <div id="outerContainer">
            <div id="viewerContainer" tabindex="0">
                <div id="viewer">
                    <div class="page" id="page1">Page 1</div>
                    <div class="page" id="page2">Page 2</div>
                    <div class="page" id="page3">Page 3</div>
                    <div class="page" id="page4">Page 4</div>
                    <div class="page" id="page5">Page 5</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟 PDF.js 的 scrollIntoView 函数
        function scrollIntoView(element, spot, scrollMatches = false) {
            let parent = element.offsetParent;
            if (!parent) {
                console.error('offsetParent is not set -- cannot scroll', element, spot, scrollMatches);
                return;
            }
            
            let offsetY = element.offsetTop + element.clientTop;
            let offsetX = element.offsetLeft + element.clientLeft;
            
            // 简化的滚动逻辑
            const container = document.getElementById('viewerContainer');
            if (container) {
                container.scrollTop = offsetY - 50; // 留一些边距
                console.log('Scrolled to:', offsetY);
            }
        }
        
        // 模拟我们的修复函数
        function fixOffsetParent(element, elementName = 'element') {
            if (!element) {
                console.error(`${elementName} 不存在`);
                return false;
            }

            console.log(`检查 ${elementName} 的 offsetParent...`);
            
            const style = getComputedStyle(element);
            console.log(`${elementName} 样式:`, {
                display: style.display,
                position: style.position,
                visibility: style.visibility
            });

            // 如果元素隐藏，显示它
            if (style.display === 'none') {
                console.log(`${elementName} 被隐藏，设置为可见`);
                element.style.display = 'block';
            }

            // 检查父元素链
            let parent = element.parentElement;
            while (parent && parent !== document.body) {
                const parentStyle = getComputedStyle(parent);
                console.log(`检查父元素 ${parent.tagName}#${parent.id}:`, {
                    display: parentStyle.display,
                    position: parentStyle.position
                });

                // 确保父元素可见
                if (parentStyle.display === 'none') {
                    console.log(`父元素 ${parent.tagName}#${parent.id} 被隐藏，设置为可见`);
                    parent.style.display = 'block';
                }

                // 确保至少有一个父元素有定位上下文
                if (parentStyle.position === 'static' && parent.id === 'viewerContainer') {
                    console.log(`为 ${parent.tagName}#${parent.id} 设置定位上下文`);
                    parent.style.position = 'absolute';
                }

                parent = parent.parentElement;
            }

            // 强制重排
            element.offsetHeight;

            // 检查修复结果
            const hasOffsetParent = !!element.offsetParent;
            console.log(`${elementName} offsetParent 修复结果:`, hasOffsetParent);
            
            if (hasOffsetParent) {
                console.log(`${elementName} offsetParent:`, element.offsetParent.tagName, element.offsetParent.id);
            }

            return hasOffsetParent;
        }
        
        // 拦截 console.error 并尝试修复
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args[0];
            if (typeof message === 'string' && message.includes('offsetParent is not set -- cannot scroll')) {
                console.warn('检测到 scrollIntoView offsetParent 错误，尝试修复...');
                
                const element = args[1];
                if (element && element.nodeType === Node.ELEMENT_NODE) {
                    console.log('尝试修复元素:', element);
                    const fixed = fixOffsetParent(element, 'scroll-target-element');
                    
                    if (fixed) {
                        console.log('成功修复 scrollIntoView 目标元素的 offsetParent，重试滚动...');
                        // 重试滚动
                        setTimeout(() => {
                            scrollIntoView(element, args[2], args[3]);
                        }, 100);
                        return;
                    } else {
                        console.warn('无法修复 scrollIntoView 目标元素的 offsetParent');
                    }
                }
            }
            
            originalConsoleError.apply(console, args);
        };
        
        function testScrollIntoView() {
            const statusDiv = document.getElementById('status');
            const page3 = document.getElementById('page3');
            
            statusDiv.textContent = 'Testing scrollIntoView...';
            console.log('Testing scrollIntoView with page3');
            
            scrollIntoView(page3, { top: 0, left: 0 });
            
            setTimeout(() => {
                statusDiv.textContent = 'ScrollIntoView test completed';
            }, 500);
        }
        
        function hidePages() {
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.classList.add('hidden-page');
            });
            document.getElementById('status').textContent = 'Pages hidden';
        }
        
        function showPages() {
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.classList.remove('hidden-page');
            });
            document.getElementById('status').textContent = 'Pages shown';
        }
        
        function testFixFunction() {
            const statusDiv = document.getElementById('status');
            const page2 = document.getElementById('page2');
            
            statusDiv.textContent = 'Testing fix function...';
            console.log('Testing fixOffsetParent with page2');
            
            const result = fixOffsetParent(page2, 'test-page2');
            
            statusDiv.textContent = `Fix function result: ${result}`;
        }
    </script>
</body>
</html>
