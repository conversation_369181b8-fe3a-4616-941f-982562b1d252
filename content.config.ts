import { defineCollection, defineContentConfig, z } from '@nuxt/content'

const navigationSchema = z.object({
  navigation: z.object({
    items: z.array(
      z.object({
        title: z.string(),
        icon: z.string(),
        path: z.string(),
        order: z.number(),
        children: z.array(
          z.object({
            title: z.string(),
            path: z.string(),
            order: z.number()
          })
        )
      })
    )
  })
})

// 读取 Markdown 文件
// https://content.nuxt.com/docs/files/markdown
export default defineContentConfig({
  collections: {
    content: defineCollection({
      type: 'page',
      source: '**'
    }),
    // 文档
    zhHans_docs: defineCollection({
      type: 'page',
      source: 'zhHans/docs/**',
      schema: navigationSchema
    }),
    en_docs: defineCollection({
      type: 'page',
      source: 'en/docs/**',
      schema: navigationSchema
    }),
    zhHant_docs: defineCollection({
      type: 'page',
      source: 'zhHant/docs/**',
      schema: navigationSchema
    }),
    // 条款和政策
    zhHans_terms: defineCollection({
      type: 'page',
      source: 'zhHans/terms/**'
    }),
    en_terms: defineCollection({
      type: 'page',
      source: 'en/terms/**'
    }),
    zhHant_terms: defineCollection({
      type: 'page',
      source: 'zhHant/terms/**'
    })
  }
})
