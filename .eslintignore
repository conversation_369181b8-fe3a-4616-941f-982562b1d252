# Dependencies
node_modules/

# Build outputs
.nuxt/
.output/
dist/
.nitro/

# Generated files
.nuxt
.output
.nitro

# Environment files
.env
.env.*

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary folders
tmp/
temp/

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Public assets that don't need linting
public/
static/

# Config files that might have different formatting
*.config.js
*.config.ts
tailwind.config.ts
nuxt.config.ts
app.config.ts
content.config.ts
nuxt.schema.ts
tsconfig.json
jsconfig.json
ecosystem.config.cjs