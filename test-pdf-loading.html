<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF.js Loading Test</title>
</head>
<body>
    <h1>PDF.js Loading Test</h1>
    <div id="status">Loading...</div>
    <div id="details"></div>

    <script type="module">
        async function testPdfLoading() {
            const statusDiv = document.getElementById('status');
            const detailsDiv = document.getElementById('details');

            try {
                statusDiv.textContent = 'Loading PDF.js...';

                // 模拟我们的新加载逻辑
                const pdfModule = await import('./app/components/FreePdf/assets/build/pdf.mjs');
                console.log('PDF module imported:', pdfModule);

                // 如果 globalThis.pdfjsLib 不存在，手动设置
                if (!globalThis.pdfjsLib) {
                    console.log('手动设置 globalThis.pdfjsLib');
                    globalThis.pdfjsLib = pdfModule;
                }

                // 验证关键函数
                if (globalThis.pdfjsLib && globalThis.pdfjsLib.getDocument) {
                    statusDiv.textContent = 'PDF.js loaded successfully!';
                    detailsDiv.innerHTML = `
                        <h3>Available exports:</h3>
                        <ul>
                            ${Object.keys(globalThis.pdfjsLib).slice(0, 10).map(key => `<li>${key}</li>`).join('')}
                            ${Object.keys(globalThis.pdfjsLib).length > 10 ? `<li>... and ${Object.keys(globalThis.pdfjsLib).length - 10} more</li>` : ''}
                        </ul>
                        <p>Total exports: ${Object.keys(globalThis.pdfjsLib).length}</p>
                        <p>getDocument function: ${typeof globalThis.pdfjsLib.getDocument}</p>
                    `;

                    // 测试重新导入
                    setTimeout(async () => {
                        console.log('Testing re-import...');
                        const pdfModule2 = await import('./app/components/FreePdf/assets/build/pdf.mjs');
                        console.log('Re-import successful, globalThis.pdfjsLib still exists:', !!globalThis.pdfjsLib);
                    }, 2000);
                } else {
                    statusDiv.textContent = 'Failed to load PDF.js';
                    detailsDiv.textContent = 'globalThis.pdfjsLib is missing getDocument function';
                }

            } catch (error) {
                statusDiv.textContent = 'Error loading PDF.js';
                detailsDiv.textContent = error.message;
                console.error('Error:', error);
            }
        }

        testPdfLoading();
    </script>
</body>
</html>
