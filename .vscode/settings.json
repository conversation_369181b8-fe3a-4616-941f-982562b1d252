{
  "[javascript][javascriptreact][typescript][typescriptreact][vue]": {
    "editor.formatOnSave": false,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 搜索时排除文件
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/static": true,
    "**/build": true,
    "**/*.min.js": true,
    "**/*.map": true,
    "**/*.log": true,
    "**/*.cache": true,
    "**/*.bak": true,
    "**/*.tmp": true
  },
  // 开启文件嵌套
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "tsconfig.json": "tsconfig.*.json, env.d.ts",
    "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*",
    "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig"
  }
}
