import { createConfigForNuxt } from '@nuxt/eslint-config/flat'

/**
 * ESLint 配置文件 - 基于 Nuxt 3 + TypeScript + Vue 3
 * 提供代码风格统一和错误检测功能
 */
export default createConfigForNuxt({
  // 启用功能特性
  features: {
    // 启用 TypeScript 支持，提供类型检查和 TS 特定规则
    typescript: true,

    // 代码风格配置 - 替代 Prettier 的格式化功能
    stylistic: {
      indent: 2, // 使用 2 个空格缩进
      quotes: 'single', // 使用单引号而非双引号
      semi: false, // 不使用分号结尾
      commaDangle: 'never' // 不使用尾随逗号
    }
  },

  // 指定源码目录，ESLint 将在这些目录中查找文件
  dirs: {
    src: ['./app'] // Nuxt 3 的应用目录
  }
})
  .append(
    // 自定义规则配置
    {
      rules: {
        // ==================== Vue 组件相关规则 ====================

        // 关闭组件名必须多单词的限制（允许 Index.vue 等单词组件名）
        'vue/multi-word-component-names': 'off',

        // 关闭模板根节点必须单一的限制（Vue 3 支持多根节点）
        'vue/no-multiple-template-root': 'off',

        // 强制 HTML 标签自闭合规则
        'vue/html-self-closing': ['error', {
          html: {
            void: 'always', // void 元素（如 <img>）必须自闭合
            normal: 'always', // 普通 HTML 元素必须自闭合
            component: 'always' // Vue 组件必须自闭合
          },
          svg: 'always', // SVG 元素必须自闭合
          math: 'always' // MathML 元素必须自闭合
        }],

        // 限制每行的属性数量，提高代码可读性
        'vue/max-attributes-per-line': ['error', {
          singleline: { max: 3 }, // 单行最多 3 个属性
          multiline: { max: 1 } // 多行时每行最多 1 个属性
        }],

        // 关闭 v-html 指令的 XSS 攻击警告
        'vue/no-v-html': 'off',

        // Vue 属性顺序排列规则
        'vue/attributes-order': ['error', {
          order: [
            'DEFINITION', // 如 'is', 'v-is'
            'LIST_RENDERING', // 如 'v-for'
            'CONDITIONALS', // 如 'v-if', 'v-else-if', 'v-else', 'v-show', 'v-cloak'
            'RENDER_MODIFIERS', // 如 'v-pre', 'v-once'
            'GLOBAL', // 如 'id'
            'UNIQUE', // 如 'ref', 'key'
            'SLOT', // 如 'v-slot', 'slot'
            'TWO_WAY_BINDING', // 如 'v-model'
            'OTHER_DIRECTIVES', // 如 'v-custom-directive'
            'OTHER_ATTR', // 如 'custom-prop="foo"', 'v-bind:prop="foo"', ':prop="foo"'
            'EVENTS', // 如 '@click="functionCall"', 'v-on="event"'
            'CONTENT' // 如 'v-text', 'v-html'
          ],
          alphabetical: false // 不按字母顺序排序，使用上面定义的逻辑顺序
        }],

        // ==================== TypeScript 相关规则 ====================

        // 检测未使用的变量，但允许以下划线开头的变量（约定俗成的忽略标记）
        '@typescript-eslint/no-unused-vars': ['error', {
          argsIgnorePattern: '^_', // 忽略以 _ 开头的参数（如 _unused）
          varsIgnorePattern: '^_' // 忽略以 _ 开头的变量（如 _temp）
        }],

        // 警告使用 any 类型，鼓励使用更具体的类型
        '@typescript-eslint/no-explicit-any': 'warn',

        // ==================== 通用代码质量规则 ====================

        // 禁止使用 var 声明，强制使用 let/const
        'no-var': 'error'
      }
    }
  )
