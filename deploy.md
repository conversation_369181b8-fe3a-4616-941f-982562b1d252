### ztsl-web部署：

生产访问网址：https://selecttranslate.com/

部署方法：
https://www.iwecore.cn/article/14
https://juejin.cn/post/7110115159695163406

### 检出代码

git clone https://zingsoft.zicp.fun/zing-translate/ztsl-web.git
cd /home/<USER>/ztsl-web

#pnpm 切换镜像源
(1)查看当前镜像源：pnpm config get registry
(2)设置为淘宝镜像源：pnpm config set registry https://registry.npmmirror.com
(3)切回原镜像源：pnpm config set registry https://registry.npmjs.org

### 安装依赖包

pnpm install

### 配置 NuxtUI Pro 密钥

vi /home/<USER>/.nuxtrc
增加：uiPro.license=6F38B90A-DC35-426D-A237-664819CEBD44

### 打包为静态资源 （会输出 .output 目录）

pnpm run build:pro

### 预览启动模式（非生产启动）

node .output/server/index.mjs

### PM2 启动 （生产启动模式）

(1) 在项目中添加PM2服务启动配置
请看工程目录下的 ecosystem.config.js
(2) 安装pm2 (要用 -g 全局安装)，pm2 是一个带有负载均衡功能的Node应用的进程管理器
pnpm setup
source /home/<USER>/.bashrc
pnpm install pm2 -g
(3) 启动服务
pm2 start /home/<USER>/ztsl-web/ecosystem.config.cjs
(4) 看一下启动的服务列表
pm2 list
(5) 设置服务开机启动
pm2 save // 保存设置
pm2 startup

### PM2常用命令

pm2 list # 查看启动的服务列表
pm2 restart nuxtjsDemo # 重启名为nuxtjsDemo的服务
pm2 restart all # 重启所有进程
pm2 stop nuxtjsDemo # 终止名为nuxtjsDemo的服务
pm2 stop all # 停掉所有应用
pm2 delete nuxtjsDemo # 删除名为nuxtjsDemo的服务
pm2 kill # 杀掉所有服务（全部）
pm2 logs nuxtjsDemo # 查看名为nuxtjsDemo的服务日志
