#!/bin/bash

## 开始时间
startTime=`date '+%Y-%m-%d %H:%M:%S'`
startTime_s=`date +%s`

## 接收参数
WEB_NAME=$1

## 生产启动脚本
source /etc/profile

## 进入工程目录
cd /home/<USER>/ztsl-web

## 关闭所有进程
/home/<USER>/.local/share/pnpm/pm2 kill

if [ -z "$WEB_NAME" ]; then
  echo "使用最新代码启动ztsl-api"

  ## 备份旧目录
  # 获取当前日期和时间，格式为 YYYY-MM-DD_HH-MM-SS
  timestamp=$(date +"%Y-%m-%d_%H-%M-%S")
  # 旧文件备份重命名
  /usr/bin/mv /home/<USER>/ztsl-web/.output /home/<USER>/ztsl-web/.output_bak-${timestamp}

  ## 删除旧目录
  /usr/bin/rm -rf /home/<USER>/ztsl-web/.output

  ## 解压新目录
  /usr/bin/tar -zxvf ztsl-web-output.tar.gz
else
  echo "启动指定ztsl-web版本: $WEB_NAME"

  # 判断备份文件是否存在
  if [ ! -d "/home/<USER>/ztsl-web/$WEB_NAME" ]; then
    echo "指定的备份目录不存在: /home/<USER>/ztsl-web/$WEB_NAME"
    exit 0
  fi

  ## 删除旧目录
  /usr/bin/rm -rf /home/<USER>/ztsl-web/.output

  ## 把备份目录替换到当前目录
  /usr/bin/cp -r /home/<USER>/ztsl-web/$WEB_NAME /home/<USER>/ztsl-web/.output
fi

## 重启所有进程
/home/<USER>/.local/share/pnpm/pm2 start ecosystem.config.cjs

# 结束时间
endTime=`date '+%Y-%m-%d %H:%M:%S'`
endTime_s=`date +%s`
# 计算时长
sumTime=$[ $endTime_s - $startTime_s ]
echo "开始：$startTime"
echo "结束：$endTime"
echo "用时：$sumTime 秒"

exit 0
