#!/bin/bash

## 此脚本仅适合用于 Linux 系统下执行（在MacOS下执行可能会导致文档访问有问题）

## 开始时间
startTime=`date '+%Y-%m-%d %H:%M:%S'`
startTime_s=`date +%s`

# 检查系统可用内存
TOTAL_MEM=$(free -m | awk 'NR==2{print $2}')
AVAILABLE_MEM=$(free -m | awk 'NR==2{print $7}')

echo "总内存: ${TOTAL_MEM}MB, 可用内存: ${AVAILABLE_MEM}MB"

# 根据可用内存设置 Node.js 堆大小（使用80%的可用内存）
NODE_HEAP_SIZE=$((AVAILABLE_MEM * 80 / 100))

if [ $NODE_HEAP_SIZE -lt 8192 ]; then
    NODE_HEAP_SIZE=8192  # 最小8GB
fi

echo "设置 Node.js 堆大小: ${NODE_HEAP_SIZE}MB"

# 设置 Node.js 内存限制和并行处理
export NODE_OPTIONS="--max-old-space-size=${NODE_HEAP_SIZE}"
export UV_THREADPOOL_SIZE=64

## 生产启动脚本
# source /etc/profile

# 获取当前用户目录
USER_HOME=$HOME
echo "USER_HOME: $USER_HOME"

# 获取当前脚本的绝对路径目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
echo "SCRIPT_DIR: $SCRIPT_DIR"

# 获取父目录(即工程目录)
PROJECT_DIR=$(dirname "$SCRIPT_DIR")
echo "PROJECT_DIR: $PROJECT_DIR"

## 进入工程目录
cd $PROJECT_DIR

# 删除本地打包文件和目录
rm -rf .output
rm -f ztsl-web-output.tar.gz

## 更新代码（用 which git 找到 git 的绝对路径 ）
echo "git pull, update code begin!"
git pull

# 安装依赖包
echo "pnpm install begin!"
pnpm install --no-frozen-lockfile

# 打包为静态资源 （会输出 .output 目录）
echo "pnpm run build:dev begin!"
# 设置并行构建进程数
export NUXT_ANALYZE=false  # 禁用分析以提升速度
export NUXT_TELEMETRY_DISABLED=1  # 禁用遥测
# 获取 CPU 核心数用于并行构建
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CPU_CORES=$(sysctl -n hw.ncpu)
else
    # Linux
    CPU_CORES=$(nproc)
fi
echo "CPU cores: $CPU_CORES"
build_start=`date +%s`
pnpm run build:dev --parallel=$CPU_CORES
build_end=`date +%s`

# 删除旧的打包文件
rm -f ztsl-web-output.tar.gz

# 将 .output 目录打包（--no-xattrs 选项：创建归档时不包含扩展属性）
tar --no-xattrs -zcvf ztsl-web-output.tar.gz .output

# 设置免密登录
# 在本地Ubuntu系统上生成SSH密钥对：ssh-keygen -t rsa
# 将公钥复制到远程服务器的authorized_keys文件中：ssh-copy-id -i ~/.ssh/id_rsa.pub ubuntu@**************

# 设置环境变量
REMOTE_USER="ubuntu"
REMOTE_HOST="**************"
REMOTE_DIR="/home/<USER>/ztsl-web"

# 将包上传到生产服务器(前提是本地已经配置了免密登录)
scp ecosystem.config.cjs ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR} || { echo "配置文件上传失败"; exit 1; }
scp ztsl-web-output.tar.gz ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR} || { echo "输出文件上传失败"; exit 1; }

# 进入脚本目录
cd $PROJECT_DIR/deploy
# 将启动脚本上传到服务器(前提是本地已经配置了免密登录)
scp deploy_ztsl_web_dev.sh ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR} || { echo "输出文件上传失败"; exit 1; }

# 等待2秒
sleep 2

# 登录远程开发环境服务器，并执行发布脚本
ssh ${REMOTE_USER}@${REMOTE_HOST} << EOF
cd ${REMOTE_DIR}
bash deploy_ztsl_web_dev.sh
echo "远程发布完成！"
EOF

# 结束时间
endTime=`date '+%Y-%m-%d %H:%M:%S'`
endTime_s=`date +%s`
# 计算时长
sumTime=$[ $endTime_s - $startTime_s ]
echo "开始：$startTime"
echo "结束：$endTime"
echo "用时：$sumTime 秒"
echo "构建耗时：$((build_end - build_start)) 秒"

exit 0
