#!/bin/bash

## 开始时间
startTime=`date '+%Y-%m-%d %H:%M:%S'`
startTime_s=`date +%s`

## 生产启动脚本
source /etc/profile

## 进入工程目录
cd /home/<USER>/build-dev/ztsl-web

## 更新代码（用 which git 找到 git 的绝对路径 ）
/usr/bin/git pull

# 安装依赖包
/usr/bin/pnpm install

# 打包为静态资源 （会输出 .output 目录）
/usr/bin/pnpm run build:dev

# 删除旧的打包文件
/usr/bin/rm -f ztsl-web-output.tar.gz

# 将 .output 目录打包
/usr/bin/tar -zcvf ztsl-web-output.tar.gz .output

# 设置免密登录
# 在本地Ubuntu系统上生成SSH密钥对：ssh-keygen -t rsa
# 将公钥复制到远程服务器的authorized_keys文件中：ssh-copy-id -i ~/.ssh/id_rsa.pub ubuntu@43.132.216.153

# 将包上传到生产服务器(前提是本地已经配置了免密登录)
/usr/bin/scp ecosystem.config.cjs ubuntu@122.51.218.139:/home/<USER>/ztsl-web
/usr/bin/scp ztsl-web-output.tar.gz ubuntu@122.51.218.139:/home/<USER>/ztsl-web

# 进入脚本目录
cd /home/<USER>/build-dev/ztsl-web/deploy
# 将启动脚本上传到生产服务器(前提是本地已经配置了免密登录)
/usr/bin/scp deploy_ztsl_web_dev.sh ubuntu@122.51.218.139:/home/<USER>/ztsl-web

# 结束时间
endTime=`date '+%Y-%m-%d %H:%M:%S'`
endTime_s=`date +%s`
# 计算时长
sumTime=$[ $endTime_s - $startTime_s ]
echo "开始：$startTime"
echo "结束：$endTime"
echo "用时：$sumTime 秒"

exit 0
