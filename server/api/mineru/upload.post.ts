export default defineEventHandler(async (event) => {
  try {
    // 获取请求数据
    const formData = await readMultipartFormData(event)

    if (!formData) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No form data provided'
      })
    }

    // 提取文件和上传URL
    let fileData: { data: Buffer, filename?: string, type?: string } | undefined
    let uploadUrl: string | undefined

    for (const field of formData) {
      if (field.name === 'file') {
        fileData = {
          data: field.data,
          filename: field.filename,
          type: field.type
        }
      }
      else if (field.name === 'uploadUrl') {
        uploadUrl = field.data.toString('utf-8')
      }
    }

    if (!fileData || !uploadUrl) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing file or uploadUrl'
      })
    }

    // 使用 fetch 发送 PUT 请求（替代 axios）
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: fileData.data, // 直接传递Buffer
      timeout: 60000 // 60秒超时
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return {
      success: true,
      status: response.status,
      message: 'File uploaded successfully'
    }
  }
  catch (error: any) {
    console.error('Upload error:', error)

    // 提供更详细的错误信息
    const errorMessage = error.cause?.message || error.message || 'Failed to upload file'

    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: `Upload failed: ${errorMessage}`
    })
  }
})
