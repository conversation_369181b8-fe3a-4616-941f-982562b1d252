import axios from 'axios'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { taskId, token } = body

  try {
    // 服务端请求 Mineru API
    const response = await axios.get(`https://mineru.net/api/v4/extract/task/${taskId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    })

    const result = response.data
    return result
  }
  catch (error) {
    console.error('服务端代理请求失败:', error)
    throw createError({
      statusCode: 500,
      message: '获取任务结果失败'
    })
  }
})
