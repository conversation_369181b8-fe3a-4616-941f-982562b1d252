import axios from 'axios'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { data, token } = body

  try {
    // 服务端请求 MinerU API
    const response = await axios.post('https://mineru.net/api/v4/file-urls/batch', data, {
      headers: {
        Authorization: `Bear<PERSON> ${token}`
      }
    })

    return response.data
  }
  catch (error) {
    console.error('服务端代理请求失败:', error)
    throw createError({
      statusCode: 500,
      message: '请求文件上传链接失败'
    })
  }
})
