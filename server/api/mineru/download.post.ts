import JSZip from 'jszip'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { url } = body

  try {
    // 1. 下载ZIP文件，获取ArrayBuffer
    const response = await fetch(url, {
      method: 'GET'
    })

    if (!response.ok) {
      return createError({
        statusCode: response.status,
        message: `下载失败: HTTP ${response.status}`
      })
    }

    const arrayBuffer = await response.arrayBuffer()

    if (arrayBuffer.byteLength > 0) {
      const firstBytes = new Uint8Array(
        arrayBuffer.slice(0, Math.min(16, arrayBuffer.byteLength))
      )
      // 检查是否为ZIP文件
      if (firstBytes[0] !== 0x50 || firstBytes[1] !== 0x4b) {
        // 非ZIP文件
        return createError({
          statusCode: 400,
          message: '非ZIP文件'
        })
      }
    }
    else {
      return createError({
        statusCode: 400,
        message: '下载的文件为空'
      })
    }

    // 2. 使用JSZip解析ZIP文件
    const zip = new JSZip()
    const zipData = await zip.loadAsync(arrayBuffer)

    // 3. 提取文件内容
    let docContent: string | null = null
    let docLayout: string | null = null
    let sourceContent: string | null = null

    // 读取full.md
    const fullMdFile = zipData.file('full.md')
    if (fullMdFile) {
      docContent = await fullMdFile.async('text')
    }

    // 读取layout.json
    const layoutFile = zipData.file('layout.json')
    if (layoutFile) {
      docLayout = await layoutFile.async('text')
    }

    // 读取源文件
    for (const [path, fileObj] of Object.entries(zipData.files)) {
      if (!fileObj.dir && path.toLowerCase().endsWith('_origin.pdf')) {
        const bytes = await fileObj.async('uint8array')
        const base64 = Buffer.from(bytes).toString('base64')
        sourceContent = `data:application/pdf;base64,${base64}`
        break
      }
    }


    // 基于布局修正图片与说明之间的空行（在替换为 data URL 之前执行）
    docContent = processImagesByLayout(docContent, docLayout || '')

    // 替换图片
    docContent = await replaceImagesWithDataUrls(docContent, zipData)

    return {
      docContent,
      docLayout,
      sourceContent
    }
  }
  catch (error) {
    console.error('文件下载失败:', error)
    throw createError({
      statusCode: 500,
      message: '文件下载失败'
    })
  }
})

/**
 * 将 images 目录中的图片转为 data URL 并替换 md 中的链接
 * @param docContent 文档内容
 * @param zipData ZIP 数据
 */
const replaceImagesWithDataUrls = async (
  docContent: string | null,
  zipData: JSZip
): Promise<string | null> => {
  if (!docContent) return docContent

  const extToMime: Record<string, string> = {
    png: 'image/png',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    gif: 'image/gif',
    svg: 'image/svg+xml',
    webp: 'image/webp',
    bmp: 'image/bmp',
    tif: 'image/tiff',
    tiff: 'image/tiff',
    avif: 'image/avif'
  }

  const imagesMap: Record<string, string> = {}

  for (const [path, fileObj] of Object.entries(zipData.files)) {
    if (!fileObj.dir && path.startsWith('images/')) {
      const bytes = await fileObj.async('uint8array')
      const ext = (path.split('.').pop() || '').toLowerCase()
      const mime = extToMime[ext] || 'application/octet-stream'
      const base64 = Buffer.from(bytes).toString('base64')
      imagesMap[path] = `data:${mime};base64,${base64}`
    }
  }

  const mdImgRE
    = /!\[([^\]]*)\]\((?:\.\/*)?images\/([^\s)]+)(?:\s+"[^"]*")?\)/g
  let replaced = docContent.replace(
    mdImgRE,
    (_m, alt: string, relPath: string) => {
      const key = `images/${relPath}`
      const dataUrl = imagesMap[key]
      return dataUrl ? `![${alt}](${dataUrl})` : _m
    }
  )

  // 替换 HTML 图片：<img src="(./)images/xxx">
  const htmlImgRE
    = /(<img\b[^>]*\bsrc=["'])(?:\.\/*)?images\/([^"']+)(["'][^>]*>)/gi
  replaced = replaced.replace(
    htmlImgRE,
    (_m, pre: string, relPath: string, post: string) => {
      const key = `images/${relPath}`
      const dataUrl = imagesMap[key]
      return dataUrl ? `${pre}${dataUrl}${post}` : _m
    }
  )

  return replaced
}

/**
 * 基于布局修正图片与说明之间的空行
 * @param docContent 文档内容
 * @param docLayout 布局内容
 */
const processImagesByLayout = (
  docContent: string | null,
  docLayout: string | null
): string | null => {
  if (!docContent || !docLayout) {
    return docContent
  }

  const layout = JSON.parse(docLayout)
  const pageLayoutList = layout.pdf_info
  for (let i = 0; i < pageLayoutList.length; i++) {
    const pageLayout = pageLayoutList[i]
    const blockList = pageLayout.para_blocks
    for (let j = 0; j < blockList.length; j++) {
      const block = blockList[j]
      if (block.type !== 'image') {
        continue
      }

      const subBlockList = block.blocks
      if (!subBlockList || subBlockList.length === 0) {
        continue
      }

      // 处理图片节点内容排序问题
      docContent = handleImageBlockContentSort(docContent, block)

      // 处理图片节点
      for (let k = 0; k < subBlockList.length; k++) {
        const subBlock = subBlockList[k]
        if (subBlock.type === 'image_body') {
          const lineList = subBlock.lines
          const line = lineList[lineList.length - 1]
          const spanList = line.spans
          const span = spanList[spanList.length - 1]
          if (span.type !== 'image') {
            continue
          }

          docContent = ensureEmptyLineAfterImage(docContent, span.image_path)
        }
        else if (subBlock.type === 'image_caption') {
          let content = handleBboxContent(subBlock.lines)
          // 删除尾部空格
          content = content.trim()

          docContent = ensureEmptyLineAfterText(docContent, content)
        }
      }
    }
  }

  // 分组处理
  const lineList = docContent.split('\n\n')

  let targetDocContent = ''
  for (let i = 0; i < lineList.length; i++) {
    let content = lineList[i]
    if (content.startsWith('\n')) {
      content = content.slice(1).trim()
    }
    if (content.startsWith('# ')) {
      content = `<h1>${content.slice(2)}</h1>`
    }
    else if (content.startsWith('## ')) {
      content = `<h2>${content.slice(3)}</h2>`
    }
    else if (content.startsWith('### ')) {
      content = `<h3>${content.slice(4)}</h3>`
    }
    else if (content.startsWith('#### ')) {
      content = `<h4>${content.slice(5)}</h4>`
    }
    else if (content.startsWith('##### ')) {
      content = `<h5>${content.slice(6)}</h5>`
    }
    else if (content.startsWith('###### ')) {
      content = `<h6>${content.slice(7)}</h6>`
    }
    targetDocContent += '<hgroup>' + content + '</hgroup>\n\n'
  }
  return targetDocContent
}

/**
 * 确保图片后有一个空行
 * @param markdownContent 文档内容
 * @param imageName 图片名称
 */
const ensureEmptyLineAfterImage = (
  markdownContent: string,
  imageName: string
): string => {
  // 允许在换行符前存在可选空白（如两个空格实现软换行），并保留这些空白
  const escapedImageName = imageName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

  const pattern = new RegExp(
    `(!\\[[^\\]]*\\]\\(\\s*(?:\\.\\/*)?images\\/${escapedImageName}(?:\\s+"[^"]*")?\\s*\\))(\\s*\\r?\\n)(?=\\S)`,
    'g'
  )

  // 将 `...jpg)  \n` 替换为 `...jpg)  \n\n`；如果已是 `\n\n` 或 `  \n\n` 则不会匹配，不会变动
  return markdownContent.replace(pattern, '$1$2\n')
}

/**
 * 确保文字后有一个空行
 * @param markdownContent 文档内容
 * @param text 文字
 */
const ensureEmptyLineAfterText = (
  markdownContent: string,
  text: string
): string => {
  if (!markdownContent || !text) return markdownContent

  // 转义待匹配文字，避免正则元字符干扰
  const escapedText = text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

  // 匹配：文字 + [可选空格] + 单个换行；且后面不是（可选空格 + 第二个换行）
  // 这样已是两个换行的情况不会匹配，不会重复插入
  const pattern = new RegExp(
    `(${escapedText})([ \\t]*\\r?\\n)(?![ \\t]*\\r?\\n)`,
    'g'
  )

  // 保留原有空格与第一个换行，只补一个换行
  return markdownContent.replace(pattern, '$1$2\n')
}

/**
 * 处理图片节点内容排序问题
 * @param docContent 文档内容
 * @param block 图片节点
 */
const handleImageBlockContentSort = (
  docContent: string,
  block: any
): string => {
  const subBlockList = block.blocks
  if (!subBlockList || subBlockList.length === 0) {
    return docContent
  }

  // 查找图片内容节点和图片说明节点
  let imageBodyBlock: any = null
  let imageCaptionBlock: any = null
  for (let k = 0; k < subBlockList.length; k++) {
    const subBlock = subBlockList[k]
    if (subBlock.type === 'image_body') {
      imageBodyBlock = subBlock
    }
    else if (subBlock.type === 'image_caption') {
      imageCaptionBlock = subBlock
    }
  }

  if (!imageBodyBlock || !imageCaptionBlock) {
    return docContent
  }

  // 找出图片内容节点和图片说明节点的索引
  const image_path = imageBodyBlock.lines[0].spans[0].image_path
  const imageCaptionContent = handleBboxContent(imageCaptionBlock.lines)

  // 查找图片路径所在行的内容
  const lines = docContent.split('\n')
  const imageIndex = lines.findIndex(line => line.includes(image_path))
  if (image_path === '3095ac9263865ac0fe6504a9fd570886eced49fa07e19b6b9af7d99760c7210f.jpg') {
    lines.forEach((line, index) => {
      console.log(index, line)
    })

    console.log('imageIndex', imageIndex)
    console.log('imageCaptionContent', imageCaptionContent)
  }

  if (imageBodyBlock.bbox[1] > imageCaptionBlock.bbox[1]) {
    // 图片内容节点在图片说明节点后面，如果在docContent中相反，则交换
    // 向上最多查找5行
    let captionIndex = queryCaptionContentIndex(lines, imageCaptionContent, imageIndex, -5)
    if (captionIndex !== -1) {
      // 如果存在判断未一致，不需要处理
      return docContent
    }
    else {
      // 如果找不到，向下找5行
      captionIndex = queryCaptionContentIndex(lines, imageCaptionContent, imageIndex, 5)
      if (image_path === '3095ac9263865ac0fe6504a9fd570886eced49fa07e19b6b9af7d99760c7210f.jpg') {
        console.log('向下查找captionIndex', captionIndex)
      }

      if (captionIndex !== -1) {
        // 交换
        const temp = lines[imageIndex]
        lines[imageIndex] = lines[captionIndex]
        lines[captionIndex] = temp
        // 重新组合文档内容
        docContent = lines.join('\n')
        return docContent
      }
    }
  }
  else {
    // 图片内容节点在图片说明节点前面，如果在docContent中相反，则交换
    // 向下最多查找5行
    let captionIndex = queryCaptionContentIndex(lines, imageCaptionContent, imageIndex, 5)
    if (captionIndex !== -1) {
      // 如果存在判断未一致，不需要处理
      return docContent
    }
    else {
      // 如果找不到，向上找5行
      captionIndex = queryCaptionContentIndex(lines, imageCaptionContent, imageIndex, -5)
      if (image_path === '3095ac9263865ac0fe6504a9fd570886eced49fa07e19b6b9af7d99760c7210f.jpg') {
        console.log('向上查找captionIndex', captionIndex)
      }
      if (captionIndex !== -1) {
        // 交换
        const temp = lines[imageIndex]
        lines[imageIndex] = lines[captionIndex]
        lines[captionIndex] = temp
        // 重新组合文档内容
        docContent = lines.join('\n')
        return docContent
      }
    }
  }

  return docContent
}

const queryCaptionContentIndex = (contentLines: string[], captionContent: string, fromIndex: number, offsetRow: number) => {
  let captionIndex = -1

  if (offsetRow > 0) {
    // 往下查找
    for (let i = fromIndex; i < Math.min(fromIndex + offsetRow, contentLines.length); i++) {
      const line = contentLines[i]
      if (line.trim() === captionContent.trim()) {
        captionIndex = i
        break
      }
    }
  }
  else if (offsetRow < 0) {
    // 往上查找
    for (let i = fromIndex; i > Math.max(0, fromIndex + offsetRow); i--) {
      const line = contentLines[i]
      if (line.trim() === captionContent.trim()) {
        captionIndex = i
        break
      }
    }
  }
  return captionIndex
}

/**
 * 处理图片说明节点内容
 * @param lines 图片说明节点内容
 * @returns 图片说明节点内容

 */
const handleBboxContent = (lines: any[]): string => {
  let content = ''
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const spans = line.spans
    for (let j = 0; j < spans.length; j++) {
      const span = spans[j]
      if (j > 0) {
        content += ' '
      }

      if (span.type == 'text') {
        content += span.content
      }
      else if (span.type == 'inline_equation') {
        content += `$${span.content}$`
      }
    }
    if (i < lines.length - 1) {
      content += '\n'
    }
  }
  return content
}
