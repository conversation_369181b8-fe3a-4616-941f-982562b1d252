{"name": "nuxt-ui-pro-template-saas", "private": true, "type": "module", "scripts": {"dev": "nuxt dev --dotenv .env.dev", "dev:test": "nuxt dev --dotenv .env.test", "dev:pro": "nuxt dev --dotenv .env.pro", "build:dev": "nuxt build --preset=node-server --dotenv .env.dev", "build:test": "nuxt build --preset=node-server --dotenv .env.test", "build:pro": "nuxt build --preset=node-server --dotenv .env.pro", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "preinstall": "npx only-allow pnpm", "typecheck": "nuxt typecheck", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@chenfengyuan/vue-countdown": "^2.1.3", "@nuxt/components": "^2.2.1", "@nuxt/content": "^3.6.3", "@nuxt/fonts": "^0.11.0", "@nuxt/image": "^1.10.0", "@nuxt/ui-pro": "^3.3.0", "@nuxtjs/fontaine": "^0.5.0", "@nuxtjs/i18n": "9.3.1", "@pinia/nuxt": "^0.11.2", "@resvg/resvg-js": "^2.6.2", "@types/katex": "^0.16.7", "@types/markdown-it": "^14.1.2", "@vueuse/nuxt": "^13.0.0", "ali-oss": "^6.22.0", "axios": "1.8.3", "better-sqlite3": "^12.2.0", "compromise": "^14.14.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "docx-preview": "^0.3.6", "html2canvas-oklch": "1.5.0-alpha.0", "js-base64": "^3.7.7", "jspdf": "^3.0.1", "jszip": "^3.10.1", "katex": "^0.16.22", "mammoth": "^1.10.0", "markdown-it": "^14.1.0", "nprogress": "^0.2.0", "nuxt": "^4.0.2", "nuxt-gtag": "^3.0.2", "nuxt-og-image": "^5.0.4", "pdf-lib": "^1.17.1", "pinia": "3.0.3", "pnpm": "^9.15.9", "qrcode.vue": "^3.6.0", "qs": "6.14.0", "shiki": "^1.29.1", "tesseract.js": "^6.0.0", "ua-parser-js": "^2.0.3", "ufo": "^1.5.4", "vue-pdf-embed": "^2.1.2", "vue3-print-nb": "^0.1.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@iconify-json/ant-design": "^1.2.5", "@iconify-json/bi": "^1.2.2", "@iconify-json/devicon": "^1.2.21", "@iconify-json/eos-icons": "^1.2.2", "@iconify-json/fluent": "^1.2.16", "@iconify-json/heroicons": "^1.2.2", "@iconify-json/hugeicons": "^1.2.4", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify-json/logos": "^1.2.4", "@iconify-json/lucide": "^1.2.29", "@iconify-json/material-symbols": "^1.2.16", "@iconify-json/mdi": "^1.2.3", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/ph": "^1.2.2", "@iconify-json/ri": "^1.2.5", "@iconify-json/rivet-icons": "^1.2.2", "@iconify-json/simple-icons": "^1.2.29", "@iconify-json/solar": "^1.2.2", "@iconify-json/tabler": "^1.2.17", "@iconify-json/vaadin": "^1.2.1", "@iconify-json/wpf": "^1.2.0", "@nuxt/devtools": "2.3.0", "@nuxt/eslint": "^1.8.0", "@styils/vue": "^1.1.7", "@types/crypto-js": "^4.2.2", "@types/node": "^22.13.10", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "eslint": "^9.33.0", "eslint-plugin-vue": "^10.4.0", "globals": "^16.0.0", "pinia-plugin-persistedstate": "^4.2.0", "typescript": "^5.9.0", "vue-tsc": "^2.2.2"}, "pnpm": {"onlyBuiltDependencies": ["better-sqlite3"]}, "packageManager": "pnpm@10.6.2"}