{
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "baseUrl": "./", // 基础路径
    "paths": {
      "@/*": ["./app/*"], // 别名
      "~/*": ["./app/*"] // 别名
    },
    "target": "ESNext", // 目标
    "module": "ESNext", // 模块
    "moduleResolution": "node", // 模块解析
    "allowJs": true, // 允许js
    "checkJs": true, // 检查js
    "resolveJsonModule": true, // 解析json
    "esModuleInterop": true, // 允许es模块互操作
    "sourceMap": true, // 源码映射
    "allowImportingTsExtensions": true // 允许导入 .ts 扩展名
  },
  "include": ["app/**/*.ts", "app/**/*.js", "app/**/*.d.ts", "app/**/*.tsx", "app/**/*.vue", "env.d.ts", ".nuxt/nuxt.d.ts", "**/*"],
  "exclude": ["node_modules"]
}
