// https://nuxt.com/docs/api/configuration/nuxt-config
// @ts-ignore
import i18nConfig from './i18n/vue_i18n.config'

// @ts-ignore
export default defineNuxtConfig({
  modules: [
    '@nuxt/ui-pro',
    '@nuxt/content',
    '@nuxt/eslint',
    '@nuxt/image',
    '@vueuse/nuxt',
    '@pinia/nuxt', // 重新启用，项目依赖此模块
    // https://prazdevs.github.io/pinia-plugin-persistedstate/zh/guide/
    'pinia-plugin-persistedstate/nuxt', // 重新启用，项目需要此模块
    'nuxt-og-image',
    // Google Analytics integration for Nuxt: https://nuxt.com/modules/gtag
    // https://analytics.google.com/
    'nuxt-gtag',
    '@nuxtjs/i18n', // https://nuxt.com/modules/i18n
    '@nuxt/icon' // 自定义图标
  ],

  // 开发者工具 - 根据环境配置
  devtools: {
    enabled: process.env.DEPLOY_ENV !== 'pro'
  },

  app: {
    head: {
      meta: [
        process.env.APP_HEAD_META_ROBOTS === 'true' ? { name: 'robots', content: 'noindex, nofollow' } : {} // 是否允许搜索引擎索引
      ]
    }
  },

  // 全局样式组件
  css: ['~/assets/css/main.css', 'katex/dist/katex.min.css'],

  colorMode: {
    disableTransition: true
  },
  content: {
    // 若用 @nuxt/content 的“文档驱动路由”，建议开启 documentDriven，或确保有 pages/[...slug].vue 捕获路由。
    documentDriven: true
  },
  // 获取环境变量
  runtimeConfig: {
    public: {
      env: process.env.DEPLOY_ENV,
      googleClientId: process.env.GOOGLE_LOGIN_CLIENT_ID,
      // 使用 Stripe 支付的最小金额(人民币)
      stripePaymentMinAmount: parseInt(process.env.STRIPE_PAYMENT_MIN_AMOUNT || '10')
    }
  },

  // 构建优化
  build: {
    transpile: [],
    // 启用分析（仅在需要时）
    analyze: false
  },

  // 这里是渲染content路径的配置
  routeRules: {
    // 首页预渲染
    '/': { prerender: true },

    // 文档路由 - 使用 SSR
    '/docs/**': { ssr: true },
    '/en/docs/**': { ssr: true },
    '/zhHans/docs/**': { ssr: true },
    '/zhHant/docs/**': { ssr: true },

    // 针对所有路由禁用缓存
    '/docs': { redirect: '/docs/start/introduce', prerender: false },
    '/en/docs': { redirect: '/en/docs/start/introduce', prerender: false },
    '/zhHans/docs': { redirect: '/zhHans/docs/start/introduce', prerender: false },
    '/zhHant/docs': { redirect: '/zhHant/docs/start/introduce', prerender: false },
    '/terms': { redirect: '/terms/terms-of-service' },
    '/en/terms': { redirect: '/en/terms/terms-of-service' },
    '/zhHans/terms': { redirect: '/zhHans/terms/terms-of-service' },
    '/zhHant/terms': { redirect: '/zhHant/terms/terms-of-service' },

    // 静态资源缓存优化
    '/images/**': { headers: { 'Cache-Control': 's-maxage=31536000' } },
    '/icons/**': { headers: { 'Cache-Control': 's-maxage=31536000' } },
    '/_nuxt/**': { headers: { 'Cache-Control': 's-maxage=31536000' } }
  },

  devServer: {
    host: '0.0.0.0',
    port: 3000
  },

  future: {
    compatibilityVersion: 4
  },
  compatibilityDate: '2025-03-24',

  nitro: {
    preset: 'node-server',
    // 开发环境代理配置
    devProxy: {
      '/apis': {
        target: process.env.BACKEND_SERVICE_API_URL, // 这里是后端服务接口地址
        changeOrigin: true,
        prependPath: true
      }
    },
    // 生产环境路由规则
    // 该配置用于服务端请求转发
    routeRules: {
      // SPA fallback for all routes  - 解决刷新 404 问题
      '/**': {
        ssr: true,
        prerender: false,
        headers: { 'cache-control': 's-maxage=0' }
      },
      // API 代理
      '/apis/**': {
        proxy: process.env.BACKEND_SERVICE_API_URL + '/**' // 这里是后端服务接口地址,如：http://localhost:9000/**
      }
    }
  },

  vite: {
    ssr: {
      noExternal: ['vue-pdf-embed', 'vue3-print-nb', 'katex'] // 确保这些包在 SSR 中正常工作
    },
    build: {
      // 启用多线程压缩
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: process.env.DEPLOY_ENV === 'pro', // 只在生产环境移除 console
          drop_debugger: process.env.DEPLOY_ENV === 'pro' // 只在生产环境移除 debugger
        }
      },
      // 增加内存限制
      rollupOptions: {
        maxParallelFileOps: 5 // 增加并行文件操作的最大数量
      }
    },
    // 优化依赖预构建
    optimizeDeps: {
      include: ['vue', 'vue-router', 'katex']
    }
  },

  // 启用TypeScript严格模式
  typescript: {
    strict: false
  },

  hooks: {
    // Define `@nuxt/ui` components as global to use them in `.md` (feel free to add those you need)
    'components:extend': (components) => {
      const globals = components.filter(c => ['UButton'].includes(c.pascalName))
      globals.forEach(c => (c.global = true))
    }
  },
  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  },

  googleSignIn: {
    clientId: process.env.GOOGLE_LOGIN_CLIENT_ID // Google客户端 ID
  },

  gtag: {
    id: 'G-S6C6V6008F' // Google Analytics ID
  },

  // 使用导入的i18nConfig
  i18n: i18nConfig,

  // ui: {
  //   icons: ['heroicons', 'simple-icons', 'ri', 'ant-design', 'ph', 'eos-icons', 'lucide']
  // },

  icon: {
    customCollections: [
      {
        prefix: 'custom-icon',
        dir: './app/assets/custom-icons'
      }
    ]
  },

  piniaPersistedstate: {
    // https://prazdevs.github.io/pinia-plugin-persistedstate/zh/guide/
    cookieOptions: {
      sameSite: 'none',
      maxAge: 86400 * 90
    },
    storage: 'localStorage'
  }
})
