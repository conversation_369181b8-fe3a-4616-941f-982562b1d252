<template>
  <div class="overlay flex items-center justify-center">
    <div class="gradient" />

    <div class="absolute top-4 flex w-96">
      <div class="flex w-56">
        <HeaderLogo />
      </div>
      <div class="flex w-24">
        <UButton
          icon="i-heroicons-home"
          :label="t('auth.common.home')"
          :to="'/' + locale"
          color="neutral"
          variant="subtle"
          class="items-center"
        />
      </div>
      <div class="mt-1 flex w-8">
        <!-- 界面语言切换 -->
        <LanguageSwitcher />
      </div>
      <div class="ml-1 flex w-8">
        <!-- 切换主题颜色 -->
        <UColorModeButton size="sm" class="items-center" />
      </div>
    </div>

    <slot />
  </div>
</template>

<!-- 用户认证（注册、登录、重置密码）的页面布局 -->
<script setup lang="ts">
const { t, locale } = useI18n()

useHead({
  bodyAttrs: {
    class: 'dark:bg-gray-950'
  }
})
</script>

<style scoped>
.gradient {
  position: absolute;
  inset: 0;
  pointer-events: none;
  /* var(--color-primary-500)  */
  background: radial-gradient(50% 50% at 50% 50%, rgb(14 165 233 / 0.25) 0, #fff 100%);
}

.dark {
  .gradient {
    /*  var(--color-primary-400) - var(--color-gray-950) */
    background: radial-gradient(50% 50% at 50% 50%, rgb(56 189 248 / 0.1) 0, rgb(8 47 73) 100%);
  }
}

.overlay {
  background-size: 100px 100px;
  /* var(--color-gray-200) - var(--color-gray-200)  */
  background-image: linear-gradient(to right, rgb(229 231 235) 0.5px, transparent 0.5px), linear-gradient(to bottom, rgb(229 231 235) 0.5px, transparent 0.5px);
}

.dark {
  .overlay {
    /* var(--color-gray-900) - var(--color-gray-900)  */
    background-image: linear-gradient(to right, rgb(17 24 39) 0.5px, transparent 0.5px), linear-gradient(to bottom, rgb(17 24 39) 0.5px, transparent 0.5px);
  }
}

.auth_footer {
  /* position: absolute;
  bottom: 20px;
  right: 300px; */
}
</style>
