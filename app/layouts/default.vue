<template>
  <div class="app-wrapper">
    <AppHeader />

    <UMain class="main-container">
      <slot />
    </UMain>

    <AppFooter />

    <!-- <ClientOnly>
      <LazyUContentSearch :files="files" :navigation="navigation" />
    </ClientOnly> -->
  </div>
</template>

<!-- 默认布局 -->
<script setup lang="ts">
// const { data: navigation } = await useAsyncData('navigation', () => fetchContentNavigation(), { default: () => [] });
// const { data: files } = useLazyFetch<ParsedContent[]>('/api/search.json', { default: () => [], server: false });

// provide('navigation', navigation);
</script>

<style scoped>
.app-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-container {
  flex: 1;
  min-height: auto;
}
</style>
