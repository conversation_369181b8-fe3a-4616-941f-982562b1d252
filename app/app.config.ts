export default defineAppConfig({
  ui: {
    checkbox: {
      slots: {
        base: 'cursor-pointer'
      }
    },
    // 颜色
    colors: {
      primary: 'sky',
      neutral: 'slate'
    },
    switch: {
      slots: {
        base: 'cursor-pointer'
      }
    },
    button: {
      slots: {
        base: 'font-medium rounded-full cursor-pointer'
      },
      defaultVariants: {
        size: 'md',
        color: 'primary'
      }
    },
    input: {
      default: {
        size: 'md'
      }
    },
    card: {
      rounded: 'rounded-xl'
    },
    footer: {
      top: {
        wrapper: 'border-t border-gray-200 dark:border-gray-800',
        container: 'py-8 lg:py-16'
      },
      bottom: {
        wrapper: 'border-t border-gray-200 dark:border-gray-800'
      }
    },
    page: {
      hero: {
        wrapper: 'lg:py-24'
      }
    },
    notifications: {
      wrapper: 'fixed flex flex-col justify-start z-[55]',
      position: 'left-1/2 -translate-x-1/2 top-0 bottom-auto',
      width: 'w-full sm:w-96',
      container: 'px-4 sm:px-6 py-6 space-y-3 overflow-y-auto'
    }
  },
  uiPro: {
    dashboardGroup: {
      base: 'relative'
    }
  }
})
