<template>
  <div>
    <AppHeader />

    <UMain>
      <UContainer>
        <UPage>
          <!-- 版本迁移使用（UError) https://ui.nuxt.com/components/error -->
          <UError
            :error="{
              statusCode: 404,
              statusMessage: t('common.page_does_not_exist'),
              message: t('common.page_does_not_exist_tip')
            }"
            :clear="{
              label: t('common.back_to_home_page'), // 返回首页
              color: 'primary',
              size: 'lg'
            }"
          />
        </UPage>
      </UContainer>
    </UMain>

    <AppFooter />

    <!-- <ClientOnly>
      <LazyUContentSearch :files="files" :navigation="navigation" />
    </ClientOnly> -->

    <UNotifications />
  </div>
</template>

<script setup lang="ts">
const { t, locale } = useI18n()

defineProps({
  error: {
    type: Object,
    required: true
  }
})

useSeoMeta({
  title: t('common.page_does_not_exist'), // 页面不存在
  description: t('common.page_does_not_exist_tip') // 抱歉！您要访问的页面不存在
})

useHead({
  htmlAttrs: {
    lang: locale.value === 'zhHans' ? 'zh-Hans' : locale.value === 'zhHant' ? 'zh-Hant' : 'en' // 页面语言随设置的语言变化而变化
  }
})

// const { data: navigation } = await useAsyncData('navigation', () => queryCollectionNavigation('docs'));
// const { data: files } = useLazyFetch<ParsedContent[]>('/api/search.json', { default: () => [], server: false });

// provide('navigation', navigation);
</script>
