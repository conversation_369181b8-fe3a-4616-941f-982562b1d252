<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="26"
    height="26"
    viewBox="0 0 512 512"
    class="translate-y-0.5"
  >
    <path
      fill="none"
      stroke="#0ea5e9"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="32"
      d="M48 112h288M192 64v48m80 336l96-224l96 224m-162.5-64h133M281.3 112S257 206 199 277S80 384 80 384"
    />
    <path
      fill="none"
      stroke="#0ea5e9"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="32"
      d="M256 336s-35-27-72-75s-56-85-56-85"
    />
  </svg>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'InterfaceLanguageIcon'
})
</script>
