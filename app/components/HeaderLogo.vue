<template>
  <div class="cursor-pointer" @click="() => router.push('/' + locale)">
    <!-- 根据不同的语言显示不同的logo -->
    <div v-if="locale === 'zhHans'" class="ztsl-logo-zhs dark:ztsl-logo-zhs h-8 w-32 lg:h-10 lg:w-56" />
    <div v-else-if="locale === 'zhHant'" class="ztsl-logo-zht dark:ztsl-logo-zht h-8 w-32 lg:h-10 lg:w-56" />
    <div v-else-if="locale === 'en'" class="ztsl-logo-en dark:ztsl-logo-en h-8 w-32 lg:h-10 lg:w-56" />
    <div v-else class="ztsl-logo-en dark:ztsl-logo-en h-8 w-32 lg:h-10 lg:w-56" />
  </div>
</template>

<script setup lang="ts">
const { locale } = useI18n()
const router = useRouter()
</script>

<style scoped>
.ztsl-logo-zhs {
  background-image: url(assets/images/logo_zhs.png);
  background-size: contain;
  background-repeat: no-repeat;
  /* width: 9.2rem !important; */
}
.ztsl-logo-zht {
  background-image: url(assets/images/logo_zht.png);
  background-size: contain;
  background-repeat: no-repeat;
  /* width: 9.2rem !important; */
}
.ztsl-logo-en {
  background-image: url(assets/images/logo_en.png);
  background-size: contain;
  background-repeat: no-repeat;
  /* width: 13.23rem !important; */
}

.dark {
  .ztsl-logo-zhs {
    background-image: url(assets/images/logo_zhs_white.png);
    background-size: contain;
    background-repeat: no-repeat;
    /* width: 9.2rem !important; */
  }
  .ztsl-logo-zht {
    background-image: url(assets/images/logo_zht_white.png);
    background-size: contain;
    background-repeat: no-repeat;
    /* width: 9.2rem !important; */
  }
  .ztsl-logo-en {
    background-image: url(assets/images/logo_en_white.png);
    background-size: contain;
    background-repeat: no-repeat;
    /* width: 13.23rem !important; */
  }
}
</style>
