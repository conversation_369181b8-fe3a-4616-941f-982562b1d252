<template>
  <!-- UHeader 中的 to 为设置logo的默认链接 -->
  <!-- https://ui.nuxt.com/pro/components/header -->
  <UHeader :ui="{ container: 'max-w-full px-4 sm:px-5 lg:px-5' }">
    <template #title>
      <HeaderLogo />
    </template>

    <!-- 导航菜单新版 -->
    <UNavigationMenu variant="link" :items="links" :ui="{ link: 'font-bold p-4' }" />

    <template #right>
      <div class="flex items-center gap-2 sm:gap-4">
        <!-- 消息通知：只有登录后才显示 -->
        <UnreadMessages v-if="member.member_id" />
        <!-- 首页 -->
        <UButton
          :label="t('header.home')"
          icon="i-heroicons-home"
          color="neutral"
          variant="subtle"
          class="hidden lg:flex"
          :to="'/' + locale"
        />
        <!-- 退出 -->
        <UButton
          :label="t('header.logout')"
          icon="i-heroicons-arrow-right-on-rectangle-20-solid"
          color="neutral"
          variant="subtle"
          class="lg:flex"
          @click="logOut"
        />
        <!-- 界面语言切换 -->
        <LanguageSwitcher class="mt-1" />
        <!-- 切换主题颜色 -->
        <UColorModeButton v-if="!isMobileDevice()" size="sm" class="hidden cursor-pointer lg:flex" />
      </div>
    </template>

    <!-- 侧边导航 -->
    <template #body>
      <UPageLinks :links="aside_links" />
      <!-- <UAsideLinks
        :links="aside_links"
        :ui="{
          wrapper: 'space-y-4 mb-3 mt-1 lg:mb-6 -mx-1 lg:mx-0',
          base: 'flex items-center gap-1.5 lg:gap-2 group',
          active: 'text-(--ui-primary) font-semibold',
          inactive: 'text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200 font-medium',
          icon: {
            wrapper: 'rounded-md p-1 inline-flex ring-inset ring-1',
            base: 'w-4 h-4 flex-shrink-0',
            active: 'bg-primary ring-primary text-background',
            inactive: 'bg-gray-100/50 dark:bg-(--ui-bg-inverted) ring-gray-300 dark:ring-gray-700 group-hover:bg-primary group-hover:ring-primary group-hover:text-background'
          },
          externalIcon: {
            name: 'i-heroicons-arrow-up-right-20-solid',
            base: 'w-3 h-3 absolute top-0.5 -right-3.5 text-neutral-400 dark:text-neutral-500'
          },
          label: 'text-sm/6 font-semibold relative'
        }"
      /> -->

      <!-- 分割线 -->
      <USeparator class="my-6" />

      <ThemeSelector />
    </template>
  </UHeader>
</template>

<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import UnreadMessages from '@/pages/unread_messages.vue'

const route = useRoute()
const ztsl_token = useCookie('ztsl_token')
const { t, locale } = useI18n()
const currentEnv = ref('')
const member = ref({})

onMounted(() => {
  // 通过判断cookie来判断用户是否在插件端执行退出操作，从而同步清除存储数据
  if (!ztsl_token || ztsl_token.value === null || ztsl_token.value === undefined) {
    const authStore = useAuthStoreWithOut()
    authStore.reset() // 清除存储数据
  }
  else {
    const authStore = useAuthStoreWithOut()
    member.value = authStore.getMember
  }
})

const links = computed<NavigationMenuItem[]>(() => [
  {
    label: t('header.account'), // 账户
    to: '/' + locale.value + '/account'
  },
  {
    label: t('header.billing'), // 账单
    to: '/' + locale.value + '/billing'
  },
  /*
  {
    label: t('header.pdf_plus'), // PDF Plus
    to: '/' + locale.value + '/pdf-plus-record'
  } */
  // {
  //   label: '问题反馈',
  //   to: {
  //     path: `/${locale.value}/feedback_list`
  //     // query: { layout: 'member' } // 传递参数，定向member布局
  //   }
  // }
  {
    label: t('header.feedback'), // 问题反馈
    to: '/' + locale.value + '/feedback_list'
  }
])

// 移动端侧边导航
const aside_links = computed<NavigationMenuItem[]>(() => [
  {
    icon: 'i-heroicons-home',
    label: t('header.home'),
    to: '/' + locale.value
  },
  {
    icon: 'i-heroicons-user-circle',
    label: t('header.account'), // 账户
    to: '/' + locale.value + '/account'
  },
  {
    icon: 'i-material-symbols-order-approve-outline-sharp',
    label: t('header.billing'), // 账单
    to: '/' + locale.value + '/billing'
  },
  {
    icon: 'i-mingcute-pdf-line',
    label: t('header.pdf_plus'), // PDF Plus
    to: '/' + locale.value + '/pdf-plus-record'
  },
  {
    icon: 'i-heroicons-chat-bubble-left-right',
    label: t('问题反馈'), // 问题反馈
    to: '/' + locale.value + '/feedback_list'
  }
])

/** 退出 */
const logOut = () => {
  const authStore = useAuthStoreWithOut() // 要在函数内部使用，不能放文件头部统一创建
  authStore.reset() // 清除存储数据
  ztsl_token.value = null // 清除cookie
  navigateTo({ path: '/' + locale.value + '/login' })
}
</script>

<style scoped></style>
