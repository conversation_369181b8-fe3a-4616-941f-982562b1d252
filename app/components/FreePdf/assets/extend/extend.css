#thumbnailView {
  width: calc(100% - 1px) !important;
}

#viewerContainer > #viewer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.page-area {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.page-area > .page {
  margin: 1px 10px -8px 10px;
}

/* 修复 Tailwind 默认 box-sizing:border-box 导致的 pdf.js 文字层与画布层错位问题 */
.pdfViewer .page,
.pdfViewer .canvasWrapper,
.pdfViewer .textLayer {
  box-sizing: content-box !important;
}
