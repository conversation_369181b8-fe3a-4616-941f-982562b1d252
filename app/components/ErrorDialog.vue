<!-- 系统错误提示对话框 -->
<template>
  <UModal
    v-if="errorState.visible"
    v-model:open="isOpen"
    :ui="{ width: 'sm:max-w-md', rounded: 'rounded-xl' }"
    prevent-close
  >
    <template #content>
      <div class="p-5">
        <!-- 标题区域 -->
        <div class="mb-4 flex items-center">
          <UIcon
            :name="errorState.type === 'error' ? 'i-heroicons-exclamation-circle' : 'i-heroicons-information-circle'"
            :class="errorState.type === 'error' ? 'text-red-500' : 'text-blue-500'"
            class="mr-2 text-xl"
          />
          <!-- 无论什么类型，目前都只是提示，暂时保留notice的代码，目前没做使用 -->
          <!--  <h3 class="text-lg font-medium">{{ errorState.title || (errorState.type === 'error' ? t('common.error_tip') : t('common.notice')) }}</h3> -->
          <h3 class="text-lg font-medium">
            {{ errorState.title !== '' ? errorState.title : t('common.error_tip') }}
          </h3>
        </div>

        <!-- 内容区域 -->
        <div class="mb-4 max-h-60 overflow-y-auto rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
          {{ formattedMessage }}
        </div>

        <!-- 按钮区域 -->
        <div class="flex justify-end">
          <UButton
            color="neutral"
            :variant="errorState.type === 'error' ? 'soft' : 'solid'"
            size="md"
            @click="closeDialog"
          >
            {{ t('common.close') }}
          </UButton>
        </div>
      </div>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { useErrorDialog } from '@/config/axios/useErrorDialog'

const { t } = useI18n()
const { state: errorState, close } = useErrorDialog()

const isOpen = computed({
  get: () => errorState.value.visible,
  set: (value) => {
    if (!value) closeDialog()
  }
})

/**
 * 关闭错误对话框
 */
function closeDialog() {
  close()
}

/**
 * 格式化错误消息
 */
const formattedMessage = computed(() => {
  const error = errorState.value.message

  let errorMsg = ''

  // 如果是错误类型
  if (errorState.value.type === 'error') {
    // 检查是否是对象类型（AxiosError）
    if (typeof error === 'object' && error !== null) {
      const status = (error as any).response?.status
      const errMsg = (error as any).response?.data?.message

      switch (status) {
        case 400:
          errorMsg = t('http_service.http_400_request_error')
          break
        case 401:
          errorMsg = errMsg || t('http_service.http_401_authentication_has_expired_please_login_again')
          break
        case 403:
          errorMsg = t('http_service.http_403_no_access_permission_please_log_in_first')
          break
        case 404:
          errorMsg = t('http_service.http_404_error_with_the_requested_url') + `${(error as any).response?.config?.url || ''}`
          break
        case 408:
          errorMsg = t('http_service.http_408_request_timeout')
          break
        case 500:
          errorMsg = t('http_service.http_500_internal_service_error')
          break
        case 501:
          errorMsg = t('http_service.http_501_service_not_implemented')
          break
        case 502:
          errorMsg = t('http_service.http_502_gateway_error')
          break
        case 503:
          errorMsg = t('http_service.http_503_service_unavailable')
          break
        case 504:
          errorMsg = t('http_service.http_504_gateway_timeout')
          break
        case 505:
          errorMsg = t('http_service.http_505_http_version_not_supported')
        default:
          errorMsg = String(error)
      }
    }
    else {
      // 如果不是对象，直接显示消息
      errorMsg = String(error)
    }
  }
  else if (errorState.value.type === 'msg') {
    // 处理普通消息
    if (error === 'http_no_content_returned_by_backend_api') {
      errorMsg = t('http_service.http_no_content_returned_by_backend_api')
    }
    else if (error === 'http_operation_failed_please_retry') {
      errorMsg = t('http_service.http_operation_failed_please_retry')
    }
    else {
      errorMsg = String(error)
    }
  }

  return errorMsg
})
</script>

<style scoped>
.ztsl-dialog-main {
  padding: 20px;
}
.ztsl-dialog-title {
  padding-top: 8px;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}
.ztsl-dialog-content {
  padding: 10px 20px;
  text-align: center;
  color: #333333;
  max-height: 40vh;
  overflow-y: auto;
  margin-bottom: 10px;
}
.ztsl-dialog-btn {
  position: relative;
  border: none;
  height: 30px;
  border-radius: 20px;
  cursor: pointer;
  background-color: #0ea5e9;
}
.ztsl-dialog-btn:active {
  opacity: 0.8;
  transition: all 0.3s;
}
.ztsl-closure {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 20px;
}
.ztsl-closure:hover {
  cursor: pointer;
  opacity: 0.8;
}
</style>
