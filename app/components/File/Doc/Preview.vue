<template>
  <div>
    <!-- 预览容器 -->
    <div id="docContainer" ref="containerRef" class="overflow-auto" />

    <!-- 文件输入 -->
    <input
      ref="fileInputRef"
      class="hidden"
      type="file"
      accept=".docx"
      @change="handleFileChange"
    />
  </div>
</template>

<script setup lang="ts">
import { renderAsync } from 'docx-preview'

// 定义组件的 emits
const emits = defineEmits(['rendered'])
// 文件输入框引用
const fileInputRef = ref<HTMLInputElement | null>(null)
// 预览容器引用
const containerRef = ref<HTMLDivElement | null>(null)

/**
 * 文档信息
 */
interface DocInfo {
  name: string
  pageCount: number
  pageCurrent: number
}
// 文档信息
const docInfo = reactive<DocInfo>({
  name: '',
  pageCount: 0,
  pageCurrent: 1
})

/**
 * 打开文件选择器
 */
const openFileSelector = () => {
  fileInputRef.value?.click()
}

/**
 * 文件选择变化处理函数
 */
const handleFileChange = (event: Event) => {
  // 获取文件对象
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) {
    return
  }

  // 检查文件类型
  if (file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    alert('请上传 .docx 文件')
    return
  }

  // 打开文件
  openFile(file)
}

/**
 * 打开文件
 * @param file  文件对象
 */
const openFile = async (file: File) => {
  // 更新文档名称
  docInfo.name = file.name

  // 停止监听
  stopObserve()

  // 渲染文档
  const renderRes = await renderAsync(file, containerRef.value as HTMLDivElement)

  await nextTick()
  // 启动监听
  startObserve()

  // 获取页数
  docInfo.pageCount = document.querySelectorAll('.docx-wrapper .docx').length

  // 页码变化处理函数
  pageChange(docInfo.pageCurrent)

  // 回调
  emits('rendered', renderRes)
}

/**
 * 页码变化处理函数
 * @param page  页码
 */
const pageChange = (page: number) => {
  const pageList = document.querySelectorAll('.docx-wrapper .docx')
  if (pageList.length > 0 && pageList.length >= page) {
    pageList[page - 1].scrollIntoView({ block: 'start' })
    docInfo.pageCurrent = page
  }
}

// 监听页面内容变化
let observer: MutationObserver | null = null
const startObserve = () => {
  const el = document.querySelector('#docContainer .docx-wrapper')
  if (!el || observer) return

  observer = new MutationObserver(() => {
    // 合并高频回调
    // if (!(updatePageInfo as any)._raf) {
    //   (updatePageInfo as any)._raf = requestAnimationFrame(() => {
    //     (updatePageInfo as any)._raf = 0;
    //     updatePageInfo();
    //   });
    // }
    console.log('页面内容变化')
  })

  observer.observe(el, {
    childList: true,
    subtree: true,
    characterData: false,
    attributes: false
  })

  // 初次统计
  // updatePageInfo();
}

const stopObserve = () => {
  observer?.disconnect()
  observer = null
}

/**
 * 组件挂载时启动监听
 */
onMounted(() => {
  startObserve()
})
/**
 * 组件卸载时停止监听
 */
onUnmounted(() => {
  stopObserve()
})

// 暴露给父组件的方法
defineExpose({ openFileSelector, openFile })
</script>

<style scoped></style>
