<template>
  <UModal v-model:open="openModel">
    <template #content>
      <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-base leading-6 font-semibold text-neutral-900 dark:text-white">
              <UIcon
                v-if="props.payWay === 'wechat'"
                name="i-ri-wechat-pay-fill"
                class="size-8 flex-shrink-0 align-middle dark:bg-gray-100"
                :style="{ color: '#00c250' }"
              />
              <UIcon
                v-else-if="props.payWay === 'alipay'"
                name="i-bi-alipay"
                class="size-8 flex-shrink-0 align-middle dark:bg-gray-100"
                :style="{ color: '#0f8dff' }"
              />
              <span class="ml-2 inline-block">{{ props.payWayName }}</span>
            </h3>
            <UButton
              color="neutral"
              variant="subtle"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="handleCloseModel"
            />
          </div>
        </template>
        <template #default>
          <div class="container">
            <p class="countdown">
              <!-- 倒计时插件 https://github.com/fengyuanchen/vue-countdown -->
              <!-- 剩余支付时间 -->
              <VueCountdown v-slot="{ hours, minutes, seconds }" :time="countdown_millisecond">
                {{ t('buy.remaining_payment_time') }} : {{ hours }} : {{ minutes }} : {{ seconds }}
              </VueCountdown>
            </p>
            <p class="mb-4 text-xl leading-6 font-semibold text-neutral-900 dark:text-white">
              <!-- 支付金额 -->
              {{ t('buy.payment_amount') }} ：
              <span class="text-2xl text-red-500">
                {{ formatCurrency(props.payAmount) }}
              </span>
            </p>
            <div class="pay_info">
              <QrcodeVue
                v-if="props.payWay == 'wechat'"
                :value="props.payUrl"
                :level="qrcodeLevel"
                :render-as="qrcodeRenderAs"
                :size="200"
              />
              <div v-else-if="props.payWay == 'alipay'" class="alipay-page">
                <iframe :src="props.payUrl" draggable="false" />
              </div>
            </div>
            <!-- 请使用微信扫码支付 -->
            <p v-if="props.payWay === 'wechat'" class="pay_tips">
              {{ t('buy.wechat_scan_qrcode_to_pay') }}
            </p>
            <!-- 请使用支付宝扫码支付 -->
            <p v-else-if="props.payWay === 'alipay'" class="pay_tips">
              {{ t('buy.alipay_scan_qrcode_to_pay') }}
            </p>
            <!-- 请及时支付 -->
            <p v-else class="pay_tips">
              {{ t('buy.please_pay_on_time') }}
            </p>
          </div>
        </template>
      </UCard>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import QrcodeVue, { type Level, type RenderAs } from 'qrcode.vue'
import VueCountdown from '@chenfengyuan/vue-countdown'
import { useAuthStoreWithOut } from '~/store/modules/auth'
import type { HeadersTokenType } from '~/api/login/types'
import type { MemberPayOrderType } from '~/api/types'
import { getMemberPayOrderStatusApi } from '~/api/product'

const { t, locale } = useI18n()

const openModel = ref(false)
const qrcodeLevel = ref<Level>('M')
const qrcodeRenderAs = ref<RenderAs>('svg')

/** 订单有效期倒计时毫秒数 */
const countdown_millisecond = ref(0)
const intervalId = ref(null)

const props = defineProps({
  payWay: {
    type: String
  },
  payWayName: {
    type: String
  },
  payAmount: {
    type: Number
  },
  payUrl: {
    type: String
  },
  expireTime: {
    type: String
  },
  orderNo: {
    type: String
  }
})

/** 侦听 openModel 改变事件 */
watch(openModel, (newVal) => {
  if (newVal) {
    countdown_millisecond.value = getCountdownMillisecond(props.expireTime)
    // 开启订单轮询
    intervalId.value = setInterval(handleQueryOrderStatus, 2000)
  }
})

/** 打开支付弹窗 */
const handleOpenModel = () => {
  openModel.value = true
}

/** 关闭支付弹窗 */
const handleCloseModel = () => {
  openModel.value = false
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
}

/** 获取倒计时毫秒数 */
const getCountdownMillisecond = (endTime: string) => {
  const currentTime = new Date()
  const endTimeDate = new Date(endTime)
  return Math.floor(endTimeDate.getTime() - currentTime.getTime())
}

/** 查询订单状态 */
const handleQueryOrderStatus = () => {
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  const queryParams: MemberPayOrderType = {
    order_no: props.orderNo
  }
  // 订单状态（create:已创建,pay:已支付,close:已关闭）
  getMemberPayOrderStatusApi(headers, queryParams).then((resp) => {
    const payOrderStatus = resp.message
    if (payOrderStatus === 'pay' || payOrderStatus === 'close') {
      // 支付成功

      // 清除定时器
      clearInterval(intervalId.value)

      // 关闭弹框
      handleCloseModel()

      // 跳转到服务订单页
      const router = useRouter()
      router.push({ path: '/' + locale.value + '/account', replace: true })
    }
  })
}

onBeforeUnmount(() => {
  if (intervalId.value != null) {
    clearInterval(intervalId.value)
  }
})

// 暴露父容器方法
defineExpose({ handleOpenModel })

/** 格式化货币 */
const formatCurrency = (val: string | number | bigint) => {
  if (typeof val !== 'number') {
    return val
  }
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 1,
    maximumFractionDigits: 2
  }).format(val)
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.countdown {
  color: #666;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 20px;
}

.pay_amount {
  color: #666;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 12px;
}

.pay_amount > span {
  color: red;
}

.pay_info {
  width: 200px;
  height: 200px;
  border: solid 1px #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.pay_tips {
  color: #999;
  font-size: 14px;
  margin-top: 12px;
}

.alipay-page {
  margin-top: 9px;
  margin-left: 17px;
  height: 150px;
  width: 150px;
  transform: scale(1.2);
}
</style>
