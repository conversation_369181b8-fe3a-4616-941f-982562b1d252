<template>
  <div class="loading-mask dark:bg-primary-800 dark:bg-opacity-50" :style="{ zIndex: zIndex }">
    <div class="loading-spinner">
      <svg class="circular" :class="size" viewBox="0 0 50 50">
        <circle
          class="path"
          cx="25"
          cy="25"
          r="20"
          fill="none"
        />
      </svg>
      <div class="loading-text">
        {{ loadingText }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  loadingText: {
    type: String,
    default: 'Loading...'
  },
  zIndex: {
    type: Number,
    default: 2000
  },
  size: {
    type: String as PropType<'default' | 'small' | 'large'>,
    default: 'default',
    validator: (val: string) => ['default', 'small', 'large'].includes(val)
  }
})
</script>

<style scoped>
.loading-mask {
  position: absolute;
  /* background-color: rgba(255, 255, 255, 0.9); */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: opacity 0.3s;
}
.loading-spinner {
  top: 50%;
  margin-top: calc((0px - 42px) / 2);
  width: 100%;
  text-align: center;
  position: absolute;
}
.loading-spinner .circular {
  display: inline;
  animation: loading-rotate 2s linear infinite;
}
/* 小号尺寸 */
.loading-spinner .circular.small {
  width: 24px;
  height: 24px;
}

/* 默认尺寸 */
.loading-spinner .circular.default {
  width: 32px;
  height: 32px;
}

/* 大号尺寸 */
.loading-spinner .circular.large {
  width: 42px;
  height: 42px;
}

.loading-spinner .path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: #409eff;
  stroke-linecap: round;
}
.loading-text {
  color: #409eff;
  font-size: 13px;
  margin-top: 8px;
}
@keyframes loading-rotate {
  to {
    transform: rotate(360deg);
  }
}
@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}
</style>
