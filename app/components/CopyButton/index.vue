<template>
  <!-- 复制按钮 -->
  <UTooltip :text="t('selected_trans.main.copy')" :popper="{ placement: 'top', strategy: 'fixed' }">
    <UButton
      active
      color="neutral"
      variant="ghost"
      active-color="neutral"
      active-variant="ghost"
      :size="props.size"
      class="rounded-full text-neutral-500"
      @click="handleCopy"
    >
      <template #leading>
        <Transition name="icon-fade" mode="out-in">
          <template v-if="!isCopied">
            <UIcon name="i-solar-copy-linear" :class="props.size === 'xs' ? 'size-4' : 'size-5'" />
          </template>
          <template v-else>
            <UIcon name="i-heroicons-check-20-solid" :class="props.size === 'xs' ? 'size-4' : 'size-5'" />
          </template>
        </Transition>
      </template>
    </UButton>
  </UTooltip>
</template>

<script setup>
const { t } = useI18n()

const props = defineProps({
  // 复制内容
  content: {
    type: [String, Number],
    required: true
  },
  // 按钮大小
  size: {
    type: String,
    default: 'sm'
  }
})

const emit = defineEmits(['copy-success', 'copy-error'])

// 添加图标状态
const isCopied = ref(false)

/**
 * 使用传统方法复制文本（兼容性方案）
 * @param {string} text - 要复制的文本
 * @returns {boolean} 是否复制成功
 */
function fallbackCopyToClipboard(text) {
  try {
    // 使用Range和Selection API实现复制
    const range = document.createRange()
    const selection = window.getSelection()

    // 创建一个临时的span元素来存放文本
    const tempSpan = document.createElement('span')
    tempSpan.textContent = text
    tempSpan.style.position = 'fixed'
    tempSpan.style.top = '0'
    tempSpan.style.left = '0'
    tempSpan.style.opacity = '0'
    tempSpan.style.whiteSpace = 'pre' // 保留空格和换行

    document.body.appendChild(tempSpan)

    // 选择文本
    range.selectNodeContents(tempSpan)
    selection.removeAllRanges()
    selection.addRange(range)

    // 执行复制命令
    const successful = document.execCommand('copy')

    // 清理
    selection.removeAllRanges()
    document.body.removeChild(tempSpan)

    return successful
  }
  catch (err) {
    console.log('Fallback: 复制失败', err)
    return false
  }
}

// 复制操作
async function handleCopy() {
  try {
    // 如果复制内容为空, 则不进行复制
    if (props.content === '') {
      console.log('复制内容为空')
      return
    }
    let success = false

    // 首先尝试使用 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(props.content.toString())
      success = true
    }
    else {
      // 如果 Clipboard API 不可用，使用后备方案
      success = fallbackCopyToClipboard(props.content.toString())
    }

    if (success) {
      // 修改为图标状态切换
      isCopied.value = true
      setTimeout(() => {
        isCopied.value = false
      }, 1500)
      emit('copy-success')
    }
    else {
      // 复制失败
      throw new Error('复制失败')
    }
  }
  catch (err) {
    // 复制失败
    console.error('复制失败:', err)
    emit('copy-error', err)
  }
}
</script>

<style scoped>
/* 添加图标过渡效果 */
.icon-fade-enter-active,
.icon-fade-leave-active {
  transition: all 0.2s ease;
}

.icon-fade-enter-from,
.icon-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>
