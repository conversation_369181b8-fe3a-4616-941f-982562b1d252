<template>
  <USeparator class="h-px" />
  <UFooter v-if="isShowFooter" :ui="{ root: 'bg-(--ui-bg-elevated)/50', top: 'border-b border-[var(--ui-border)]' }">
    <template #top>
      <UContainer>
        <UFooterColumns :columns="footer_links">
          <template #right>
            <div class="flex items-start gap-8 text-center">
              <div class="w-26">
                <img src="https://assets.selecttranslate.com/statics/ztsl_official_account_qrcode.jpg" alt="WeChat Official Account" class="size-26 rounded-md" />
                <!-- 微信公众号 -->
                <span class="text-sm">{{ t('footer.wechat_official_account') }}</span>
              </div>
              <div class="w-26">
                <img src="https://assets.selecttranslate.com/statics/ztsl_wecom_qrcode.jpg" alt="WeCom" class="size-26 rounded-md" />
                <!-- 微信交流群 -->
                <span class="text-sm">{{ t('footer.wechat_group') }}</span>
              </div>
            </div>
          </template>
        </UFooterColumns>
      </UContainer>
    </template>

    <template #left>
      <p class="text-sm text-neutral-500 dark:text-neutral-400">
        {{ t('footer.copyright') }}
        <br />
        <!-- 只在简体中文模式下显示备案号 -->
        <a v-if="locale === 'zhHans'" href="https://beian.miit.gov.cn/" target="_blank">粤ICP备**********号</a>
        <a
          v-if="locale === 'zhHans'"
          href="https://beian.mps.gov.cn/#/query/webSearch?code=**************"
          target="_blank"
          class="ml-4"
        >粤公网安备**************号</a>
        <a
          v-if="locale === 'zhHans'"
          href="https://dxzhgl.miit.gov.cn/dxxzsp/xkz/xkzgl/resource/qiyesearch.jsp?num=%25E7%25B2%25A4B2-20250164&type=xuke"
          target="_blank"
          class="ml-4"
        >粤B2-20250164</a>
      </p>
    </template>
  </UFooter>
</template>

<script setup lang="ts">
const { t, locale } = useI18n()
const route = useRoute()

// 根据路径来判断是否显示footer
const isShowFooter = computed(() => {
  return route.path !== '/' + locale.value + '/text'
})

// 页面底部链接集合 - 修改变量名以避免与之前的变量冲突
const footer_links = computed(() => [
  {
    label: t('footer.help_enter'),
    children: [
      {
        label: t('footer.use_doc'), // 使用文档
        to: '/' + locale.value + '/docs/start/introduce',
        target: '_blank'
      },
      {
        label: t('footer.faq'), // 常见问题
        to: '/' + locale.value + '/faq',
        target: '_blank'
      },
      {
        label: t('footer.feedback'), // 问题反馈
        to: '/' + locale.value + '/feedback'
      }
    ]
  },
  {
    label: t('footer.terms_policies'), // 条款和政策
    children: [
      {
        label: t('footer.terms_of_service'), // 服务条款
        to: '/' + locale.value + '/terms/terms-of-service',
        target: '_blank'
      },
      {
        label: t('footer.privacy_policy'), // 隐私政策
        to: '/' + locale.value + '/terms/privacy-policy',
        target: '_blank'
      }
    ]
  }
  // {
  //   label: t('footer.company'), // 公司
  //   children: [
  //     {
  //       label: t('footer.about') // 关于
  //     },
  //     {
  //       label: t('footer.information') // 资讯
  //     }
  //   ]
  // }
])

/* const links2 = [{
  label: '智应软件',
  icon: 'i-heroicons-rocket-launch',
  to: 'https://zingsw.com',
  target: '_blank'
}] */

// const toast = useToast();

// const email = ref('');
// const loading = ref(false);

// function onSubmit() {
//   loading.value = true;

//   setTimeout(() => {
//     toast.add({
//       title: t('footer.subscribe_success'),
//       description: t('footer.subscribe_success_desc')
//     });

//     loading.value = false;
//   }, 1000);
// }
</script>
