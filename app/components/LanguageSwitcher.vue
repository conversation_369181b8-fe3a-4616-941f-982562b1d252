<template>
  <div ref="dropdownButtonWrapper" class="relative" data-dropdown-button>
    <ULink @click.stop="toggleDropdown">
      <InterfaceLanguageIcon class="cursor-pointer" />
    </ULink>

    <transition name="fade">
      <ul v-show="isDropdownVisible" class="dropdown-menu absolute top-full right-0 z-50 w-max rounded-lg border border-gray-200 bg-white text-sm shadow-lg dark:border-gray-700 dark:bg-gray-800">
        <li
          v-for="locale in locales"
          :key="locale.code"
          class="cursor-pointer px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
          :class="{
            'text-(--ui-primary) dark:bg-gray-600': $i18n.locale === locale.code
          }"
          @click="changeLocaleAndCloseDropdown(locale.code)"
        >
          <a href="javascript:void(0);" class="flex items-center justify-center gap-2">
            {{ locale.name }}
          </a>
        </li>
      </ul>
    </transition>
  </div>
</template>

<script setup>
import InterfaceLanguageIcon from '~/components/icons/InterfaceLanguageIcon.vue'
import { useAuthStoreWithOut } from '@/store/modules/auth'

const switchLocalePath = useSwitchLocalePath()
const { locales, setLocale } = useI18n()

const authStore = useAuthStoreWithOut()

// 点击按钮事件，展开下拉菜单
const isDropdownVisible = ref(false)
function toggleDropdown() {
  isDropdownVisible.value = !isDropdownVisible.value
}

// 选择语言并且隐藏下拉菜单
function changeLocaleAndCloseDropdown(localeCode) {
  setLocale(localeCode)

  authStore.interfaceLanguage = localeCode

  isDropdownVisible.value = false

  // 获取新语言路径，并且重定向到该路径，刷新头部以及底部链接地址
  switchLocalePath(localeCode)

  // 发送请求同步插件的界面语言
  window.postMessage(
    {
      type: 'setInterfaceLanguage',
      language: localeCode
    },
    window.location.origin
  )
}

// 添加全局点击事件监听器
function handleOutsideClick(event) {
  const button = document.querySelector('[data-dropdown-button]')
  const menu = document.querySelector('.dropdown-menu')

  // 当点击空白区域则隐藏下拉菜单
  if (button && menu && !button.contains(event.target) && !menu.contains(event.target)) {
    isDropdownVisible.value = false
  }
}

let clickEvent

onMounted(() => {
  clickEvent = document.addEventListener('click', handleOutsideClick)

  /** 监听 插件端content.js 发送的更新界面语言消息 */
  document.addEventListener('updateInterfaceLanguageParams', (event) => {
    const { interfaceLanguage } = event.detail

    // 更新界面语言
    changeLocaleAndCloseDropdown(interfaceLanguage)
  })
})

onUnmounted(() => {
  document.removeEventListener('click', clickEvent)
})
</script>
