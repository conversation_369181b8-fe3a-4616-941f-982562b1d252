<!-- 移动端下的主题切换 -->
<template>
  <div class="flex items-center rounded-lg bg-gray-100 p-1 dark:bg-gray-800">
    <button
      v-for="option in options"
      :key="option.value"
      :class="[
        'relative flex w-full items-center justify-center rounded-md px-3 py-1.5 text-sm font-medium transition-all duration-200 ease-in-out',
        {
          'bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-white': colorMode.preference === option.value,
          'text-gray-600 hover:bg-white/50 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700/50 dark:hover:text-white': colorMode.preference !== option.value
        }
      ]"
      @click="colorMode.preference = option.value"
    >
      <UIcon
        :name="option.icon"
        :class="[
          'mr-1.5 h-4 w-4',
          {
            'text-gray-900 dark:text-white': colorMode.preference === option.value,
            'text-gray-500 dark:text-gray-400': colorMode.preference !== option.value
          }
        ]"
      />
      <span class="truncate">{{ option.label }}</span>
    </button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: 'xs' | 'sm' | 'md' | 'lg'
}

withDefaults(defineProps<Props>(), {
  size: 'sm'
})

const colorMode = useColorMode()
const { t } = useI18n()

// 主题选项配置
const options = computed(() => [
  {
    value: 'light',
    label: t('theme.light'),
    icon: 'i-lucide-sun'
  },
  {
    value: 'dark',
    label: t('theme.dark'),
    icon: 'i-lucide-moon'
  },
  {
    value: 'system',
    label: t('theme.system'),
    icon: 'i-lucide-monitor'
  }
])
</script>
