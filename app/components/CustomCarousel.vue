<template>
  <div
    class="relative w-full"
    :class="ui.container || ''"
    @touchstart="onTouchStart"
    @touchend="onTouchEnd"
  >
    <!-- 当前展示的幻灯片 -->
    <div :key="current" class="w-full" :class="ui.item || ''">
      <slot :item="items[current]" />
    </div>

    <!-- 上一张 / 下一张按钮（桌面端显示） -->
    <button
      v-if="arrows"
      type="button"
      class="absolute top-1/2 z-10 flex -translate-y-1/2 items-center justify-center rounded-full border border-gray-300 bg-white/80 p-1 text-gray-800 hover:bg-gray-100 focus:outline-none"
      :class="prev.class || 'left-4'"
      @click="prevSlide"
    >
      <UIcon :name="prev.icon || 'i-heroicons-arrow-left-20-solid'" class="h-5 w-5" />
    </button>

    <button
      v-if="arrows"
      type="button"
      class="absolute top-1/2 z-10 flex -translate-y-1/2 items-center justify-center rounded-full border border-gray-300 bg-white/80 p-1 text-gray-800 hover:bg-gray-100 focus:outline-none"
      :class="next.class || 'right-4'"
      @click="nextSlide"
    >
      <UIcon :name="next.icon || 'i-heroicons-arrow-right-20-solid'" class="h-5 w-5" />
    </button>

    <!-- 指示点 -->
    <div v-if="dots" class="mt-2 flex justify-center gap-2" :class="ui.dots?.wrapper || ''">
      <button
        v-for="(dot, index) in items.length"
        :key="index"
        type="button"
        class="mx-1 h-3 w-3 rounded-full transition-colors"
        :class="current === index ? 'bg-(--ui-primary)' : 'bg-gray-300 dark:bg-gray-600'"
        @click="goTo(index)"
      />
    </div>

    <!-- 移动端滑动提示 -->
    <div v-if="isMobileDevice()" class="pointer-events-none absolute inset-x-0 -bottom-8 flex justify-center select-none">
      <div class="rounded-full bg-black/50 px-3 py-1 text-xs text-white">
        <span>←</span>
        <!-- 左右滑动查看更多 -->
        <span class="mx-1">{{ t('common.swipe_to_see_more') }}</span>
        <span>→</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()

interface ArrowOptions {
  color?: string
  icon?: string
  class?: string
}

interface UiOptions {
  container?: string
  item?: string
  dots?: {
    wrapper?: string
  }
}

const props = withDefaults(
  defineProps<{
    items: any[]
    loop?: boolean
    arrows?: boolean
    dots?: boolean
    ui?: UiOptions
    prev?: ArrowOptions
    next?: ArrowOptions
  }>(),
  {
    loop: true,
    arrows: true,
    dots: true,
    ui: () => ({}),
    prev: () => ({}),
    next: () => ({})
  }
)

const current = ref(0)

function nextSlide() {
  if (current.value < props.items.length - 1) {
    current.value += 1
  }
  else if (props.loop) {
    current.value = 0
  }
}

function prevSlide() {
  if (current.value > 0) {
    current.value -= 1
  }
  else if (props.loop) {
    current.value = props.items.length - 1
  }
}

function goTo(index: number) {
  if (index >= 0 && index < props.items.length) {
    current.value = index
  }
}

// 触控滑动处理
let startX = 0
function onTouchStart(event: TouchEvent) {
  startX = event.touches[0].clientX
}

function onTouchEnd(event: TouchEvent) {
  const endX = event.changedTouches[0].clientX
  const diffX = endX - startX
  if (Math.abs(diffX) > 50) {
    // 向左滑动显示下一张
    if (diffX < 0) {
      nextSlide()
    }
    else {
      prevSlide()
    }
  }
}

// 为 TypeScript 提供插槽类型
// 这样在使用 <CustomCarousel v-slot="{ item }"> 时，item 的属性可以被正确推断

interface SlotProps {
  item: any
}

declare function defineSlots<Slots>(): void

defineSlots<{
  default(props: SlotProps): any
}>()
</script>

<style scoped>
/* 这里可以根据需要添加额外样式 */
</style>
