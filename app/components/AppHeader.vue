<template>
  <UHeader :ui="{ container: 'max-w-full px-4 sm:px-5 lg:px-5' }">
    <template #title>
      <HeaderLogo />
    </template>

    <!-- 自定义导航菜单 -->
    <nav v-if="!isDisabledNav" class="flex gap-4">
      <NuxtLink
        v-for="link in header_links"
        :key="link.to"
        :to="link.to"
        :class="['p-4 text-sm font-bold', isActive(link.to) || isChildActive(link.children) ? 'text-primary-500 dark:text-primary-400' : '']"
      >
        {{ link.label }}
      </NuxtLink>
    </nav>
    <!-- <UNavigationMenu :items="header_links" /> -->

    <template #right>
      <div class="flex items-center gap-2 sm:gap-4">
        <!-- 消息通知：只有登录后才显示 -->
        <UnreadMessages v-if="member.member_id" />
        <!-- 账户 -->
        <UButton
          v-if="member.member_id"
          :label="t('header.account')"
          :to="'/' + locale + '/account'"
          class="lg:flex"
          color="neutral"
          variant="subtle"
        >
          <template #leading>
            <UAvatar
              v-if="member.signup_mode === 'wechat'"
              :src="member.avatar ? member.avatar : avatar_svg"
              :alt="member.member_id"
              size="2xs"
            />
            <UAvatar
              v-else
              :src="avatar_svg"
              :alt="member.member_id"
              size="2xs"
            />
          </template>
        </UButton>
        <!-- 退出 -->
        <UButton
          v-if="member.member_id"
          :label="t('header.logout')"
          icon="i-heroicons-arrow-right-on-rectangle-20-solid"
          class="hidden lg:flex"
          color="neutral"
          variant="subtle"
          @click="logOut"
        />
        <!-- 登录 -->
        <UButton
          v-if="!member.member_id"
          :label="t('header.login')"
          :to="'/' + locale + '/login'"
          icon="i-heroicons-arrow-right-end-on-rectangle-20-solid"
          color="neutral"
          variant="subtle"
        />
        <!-- 界面语言切换 -->
        <LanguageSwitcher class="mt-1" />
        <!-- 切换主题颜色 -->
        <UColorModeButton v-if="!isMobileDevice()" size="sm" class="hidden cursor-pointer lg:flex" />
      </div>
    </template>

    <!-- 移动端侧边导航 -->
    <template #body>
      <!-- 侧边导航 -->
      <UPageLinks :links="header_aside_links" />

      <!-- 分割线 -->
      <USeparator class="my-6" />

      <!-- 文档导航 -->
      <UContentNavigation :navigation="docs_tree_links" highlight default-open />

      <!-- 分割线 -->
      <USeparator class="my-6" />

      <!-- 移动端主题切换 -->
      <ThemeSelector />
    </template>
  </UHeader>
</template>

<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui'
import avatar from '@/assets/images/avatar.svg'
import avatar_white from '@/assets/images/avatar_white.svg'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import UnreadMessages from '@/pages/unread_messages.vue'
import { isMobileDevice } from '@/utils/utils'

const colorMode = useColorMode() // dark or white

defineProps({
  // 是否禁用导航
  isDisabledNav: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()

// 扩展 NavigationMenuItem 类型以支持子路由
interface ExtendedNavigationMenuItem extends NavigationMenuItem {
  children?: ExtendedNavigationMenuItem[]
  to: string // 确保 to 属性是必需的
}

// 判断是否高亮
const isActive = (path: string) => {
  // 特殊处理首页路径
  if (path === `/${locale.value}` || path === '/') {
    // 首页只在精确匹配时高亮
    return route.path === path || route.path === `/${locale.value}`
  }

  // 其他页面支持子路径匹配
  return route.path.startsWith(path)
}

// 判断子路由是否激活
const isChildActive = (children: ExtendedNavigationMenuItem[]) => {
  if (!children) return false
  return children.some(child => isActive(child.to))
}

// 获取当前发布环境变量
const currentEnv = ref('')

/** 按颜色模式显示不同的头像图片 */
const avatar_svg = computed(() => {
  return colorMode.value === 'dark' ? avatar_white : avatar
})

const { t, locale } = useI18n()

// const authStore = useAuthStoreWithOut()
// const member = computed(() => authStore.getMember)
const member = ref({})
const ztsl_token = useCookie('ztsl_token')

onMounted(() => {
  const authStore = useAuthStoreWithOut()
  member.value = authStore.getMember

  // 通过判断cookie来判断用户是否在插件端执行退出操作，从而同步清除存储数据
  if (!ztsl_token || ztsl_token.value === null || ztsl_token.value === undefined) {
    const authStore = useAuthStoreWithOut()
    authStore.reset() // 清除存储数据
  }

  currentEnv.value = useRuntimeConfig().public.env // 获取当前发布环境变量
})

const header_links = computed<ExtendedNavigationMenuItem[]>(() => [
  {
    label: t('header.home'),
    to: '/' + locale.value,
    // 添加额外标识方便调试
    isHome: true
  },
  {
    label: t('header.selected_translation'), // AI 择优翻译
    to: '/' + locale.value + '/text'
  },
  {
    label: t('header.file_translation'), // 文件翻译
    to: '/' + locale.value + '/file',
    children: [
      {
        label: t('translate_type.file.title'), // 免费 PDF 翻译
        to: '/' + locale.value + '/file'
      },
      {
        label: t('translate_type.miner_u.title'), // MinerU
        to: '/' + locale.value + '/file/miner-u'
      },
      {
        label: t('translate_type.pdf_plus.title'), // PDF Plus
        to: '/' + locale.value + '/pdf-plus'
      }
    ]
  },
  {
    label: t('header.pricing'), // 价格
    to: '/' + locale.value + '/pricing'
  },
  {
    label: t('header.docs'),
    to: '/' + locale.value + '/docs' // 直接指向第一个教程页面
  }
])

// 侧边导航

// 移动端导航 links
const header_aside_links = computed<NavigationMenuItem[]>(() => [
  {
    icon: 'i-heroicons-home',
    label: t('header.home'), // 首页
    to: '/' + locale.value
  },
  {
    icon: 'i-material-symbols-fact-check-outline',
    label: t('header.selected_translation'), // AI 择优翻译
    to: '/' + locale.value + '/text'
  },
  {
    icon: 'i-fluent-document-pdf-24-regular',
    label: t('header.file_translation'), // 文档翻译
    to: '/' + locale.value + '/file'
  },
  {
    icon: 'i-ant-design-dollar-circle-outlined',
    label: t('header.pricing'), // 价格
    to: '/' + locale.value + '/pricing'
  }
])

/**
 * 获取当前文档内容
 */

// 调整导航树结构
const docs_tree_links = computed(() => [
  {
    icon: 'i-heroicons-book-open',
    title: t('header.docs'),
    path: `/${locale.value}/docs`,
    children: docs_links.value
  }
])

// 参考地址 https://ui.nuxt.com/getting-started/content#findpageheadline
const { data: navigation } = await useAsyncData('navigation', () => queryCollectionNavigation(`${locale.value}_docs`))

/**
 * 文档导航子目录
 * 根据组件需要的字段遍历出相应的数据
 */
const docs_links = computed(() => {
  // 根据 locale 查到对应语言的数据
  const languageNode = navigation.value.find(item => item.stem === locale.value)

  if (!languageNode) return []

  // 根据 languageNode 查到 docs 节点
  const docsNode = languageNode.children?.find(item => item.path?.includes('/docs'))
  if (!docsNode) return []

  return docsNode.children
})

/** 退出 */
const logOut = () => {
  const authStore = useAuthStoreWithOut() // 要在函数内部使用，不能放文件头部统一创建
  authStore.reset() // 清除存储数据
  ztsl_token.value = null // 清除cookie
  navigateTo({ path: '/' + locale.value + '/login' })
}
</script>
