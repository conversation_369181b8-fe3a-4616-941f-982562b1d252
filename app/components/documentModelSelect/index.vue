<!-- 文档翻译通用模型选择组件 -->
<template>
  <USelectMenu
    :size="isMobile ? 'lg' : 'xl'"
    :model-value="translateEngineConfig"
    :items="enabledEnginesList"
    :search-input="false"
    :variant="variant"
    class="flex w-full truncate"
    :ui="{
      content: 'w-full',
      value: 'truncate',
      trailing: 'flex-shrink-0'
    }"
    @update:model-value="onSelectionChangeTranslateEngineConfig"
  >
    <template #item="{ item }">
      <div class="flex items-center gap-2 truncate w-full">
        <img
          v-if="translateEngineConfig.value && translateModeIcon[translateEngineConfig.value]"
          :src="translateModeIcon[item.value]"
          :alt="item.label"
          class="h-5 w-5 rounded-xs bg-white p-0.5"
        />
        <span class="min-w-0 flex-1 truncate text-sm">{{ item.label }}</span>
        <!-- 模型版本名称 -->
        <span v-if="item.modelName !== ''" class="text-xs text-neutral-500 dark:text-neutral-400">({{ item.modelName }})</span>
        <span v-if="item.serviceMode === 'plus'" class="text-sm text-[#f29a02]">-&nbsp;{{ item.serviceModeName }}</span>
        <span v-if="item.serviceMode === 'custom'" class="text-sm text-[#909399]">&nbsp;-&nbsp;{{ item.serviceModeName }}</span>
        <span v-if="item.serviceMode === 'free'" class="text-sm text-[#04ae82]">&nbsp;-&nbsp;{{ item.serviceModeName }}</span>
      </div>
    </template>
    <template #default>
      <div class="flex items-center gap-2 truncate">
        <img
          v-if="translateEngineConfig.value && translateModeIcon[translateEngineConfig.value]"
          :src="translateModeIcon[translateEngineConfig.value]"
          :alt="translateEngineConfig.label"
          class="h-5 w-5 rounded-xs bg-white p-0.5"
        />
        <!-- 模型名称 -->
        <span class="text-sm">{{ translateEngineConfig.label }}</span>
        <!-- 模型版本名称 -->
        <span v-if="translateEngineConfig.modelName !== ''" class="text-xs text-neutral-500 dark:text-neutral-400">({{ translateEngineConfig.modelName }})</span>
        <span v-if="translateEngineConfig.serviceMode === 'plus'" class="text-sm text-[#f29a02]">-&nbsp;{{ translateEngineConfig.serviceModeName }}</span>
        <span v-if="translateEngineConfig.serviceMode === 'custom'" class="text-sm text-[#909399]">&nbsp;-&nbsp;{{ translateEngineConfig.serviceModeName }}</span>
        <span v-if="translateEngineConfig.serviceMode === 'free'" class="text-sm text-[#04ae82]">&nbsp;-&nbsp;{{ translateEngineConfig.serviceModeName }}</span>
      </div>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
import { translateModeIcon } from '@/utils/translateModeIcon'

const isMobile = ref(isMobileDevice())

interface EngineModel {
  label: string
  value: string
  img?: string
  modelVersion: string // 模型版本
  modelName: string // 模型名称
  serviceModeName: string // 服务模式名称 （custom 自定义API Key， free 免费 , plus 会员）
  serviceMode: string // 服务模式 （custom 自定义API Key， free 免费 , plus 会员）
}

defineProps({
  translateEngineConfig: {
    type: Object as () => EngineModel,
    default: () => ({})
  },
  // 模型列表
  enabledEnginesList: {
    type: Array as () => EngineModel[],
    default: () => []
  },
  // 选择器类型
  variant: {
    type: String,
    default: 'none'
  }
})

const emit = defineEmits(['update:translateEngineConfig'])

const onSelectionChangeTranslateEngineConfig = (val: any) => {
  emit('update:translateEngineConfig', val)
}
</script>
