import type { InternalAxiosRequestConfig, AxiosResponse, AxiosRequestConfig, AxiosInstance, AxiosRequestHeaders, AxiosError } from 'axios'

interface RequestInterceptors<T> {
  // 请求拦截
  requestInterceptors?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig
  requestInterceptorsCatch?: (err: any) => any
  // 响应拦截
  responseInterceptors?: (config: T) => T
  responseInterceptorsCatch?: (err: any) => any
}
interface AxiosConfig<T = AxiosResponse> {
  baseUrl: {
    dev: string
    pro: string
  }
  code: number
  unauthorized_code: number
  defaultHeaders: AxiosHeaders
  timeout: number
  interceptors: RequestInterceptors<T>
}

interface RequestConfig<T = AxiosResponse> extends AxiosRequestConfig {
  interceptors?: RequestInterceptors<T>
}

export { AxiosResponse, RequestInterceptors, RequestConfig, AxiosConfig, AxiosInstance, InternalAxiosRequestConfig, AxiosRequestHeaders, AxiosError }
