// 系统错误提示状态共享

interface ErrorState {
  visible: boolean
  message: string | unknown
  title: string
  type: 'error' | 'msg'
}

// 创建一个全局单例状态
const state = ref<ErrorState>({
  visible: false,
  message: '',
  title: '提示（Alert messages）',
  type: 'error'
})

export function useErrorDialog() {
  /**
   * 显示错误对话框
   * @param message 错误信息
   * @param type 类型
   * @param title 标题
   */
  function show(message: any, type: 'error' | 'msg' = 'error', title: string = '') {
    state.value = {
      visible: true,
      message,
      title,
      type
    }

    return new Promise<void>((resolve) => {
      // 当对话框关闭时解析 promise
      const unwatch = watch(
        () => state.value.visible,
        (newVisible) => {
          if (!newVisible) {
            unwatch()
            resolve()
          }
        }
      )
    })
  }

  /**
   * 关闭错误对话框
   */
  function close() {
    state.value.visible = false
  }

  return {
    state: readonly(state),
    show,
    close
  }
}
