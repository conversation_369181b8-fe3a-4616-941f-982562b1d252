import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import axios from 'axios'
// import qs from 'qs';
import { config } from './config'
import request from '@/config/axios'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import { useErrorDialog } from '@/config/axios/useErrorDialog'

const { result_code, unauthorized_code, request_timeout } = config

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: '/apis', // api 的 base_url
  timeout: request_timeout, // 请求超时时间
  headers: {} // 请求头信息
})

// request拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const authStore = useAuthStoreWithOut()

    const token = authStore.getToken
    if (token !== '') {
      (config.headers as any)[authStore.getTokenKey ?? 'Authorization'] = token // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    if (config.method === 'post' && (config.headers as any)['Content-Type'] === 'application/x-www-form-urlencoded') {
      // config.data = qs.stringify(config.data);
      config.data = new URLSearchParams(config.data).toString()
      console.log('config.data', config.data)
    }
    // post put 参数处理
    if ((config.method === 'post' || config.method === 'put') && (config.headers as any)['Content-Type'] === 'application/json') {
      for (const key in config.data) {
        // 参数处理
        if (config.data[key] === '') {
          config.data[key] = null
        }
      }
    }
    // get参数编码
    if (config.method === 'get' && config.params) {
      let url = config.url as string
      url += '?'
      const keys = Object.keys(config.params)
      for (const key of keys) {
        if (
          // 禁止提交的get参数类型
          config.params[key] !== void 0
          && config.params[key] !== null
          && config.params[key] !== ''
        ) {
          url += `${key}=${encodeURIComponent(config.params[key])}&`
        }
      }
      url = url.substring(0, url.length - 1)
      config.params = {}
      config.url = url
    }
    return config
  },
  (error: AxiosError) => {
    // Do something with request error
    console.log('request error:', error) // for debug
    Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  (response: AxiosResponse<any>) => {
    // 这个状态码是和后端约定好的
    const code = response.data.code
    const message = response.data.message
    const refresh = response.headers['if-refresh']
    const { show } = useErrorDialog()

    if (response.config.responseType === 'blob') {
      // 如果是文件流，直接过
      return response
    }
    else if (code === null || code === undefined) {
      // 如因乱码等原因导致无法获取code，当后端无返回数据处理（如 Cloudflare 代理等可能会导致乱码）
      show('http_no_content_returned_by_backend_api', 'msg')
    }
    else if (code === result_code) {
      if (refresh === '1') {
        // 因token快过期，刷新token
        refreshToken().then((res) => {
          const authStore = useAuthStoreWithOut()
          authStore.setToken(`${res.data.token_type} ${res.data.access_token}`)
          authStore.setRefreshToken(res.data.refresh_token)
        })
      }
      return response.data
    }
    else if (code === unauthorized_code) {
      // 因token无效，token过期导致
      refreshToken().then((res) => {
        const authStore = useAuthStoreWithOut()
        authStore.setToken(`${res.data.token_type} ${res.data.access_token}`)
        authStore.setRefreshToken(res.data.refresh_token)

        show('http_operation_failed_please_retry', 'msg')
      })
    }
    else {
      show(message, 'error')
    }
  },
  (error: AxiosError) => {
    console.log('err', error)
    const authStore = useAuthStoreWithOut()
    const { show } = useErrorDialog()

    // 获取状态码
    const status = error.response?.status

    // 处理特定状态码
    if (status === 401) {
      // 强制要求重新登录
      authStore.logout()
    }
    else if (status === 403) {
      // 强制要求重新登录，因无系统权限
      authStore.logout()
    }

    // 直接将错误对象传递给 show 方法，错误处理逻辑将在 ErrorDialog 组件中统一处理
    show(error, 'error')
    return Promise.reject(error)
  }
)

// 刷新Token
const refreshToken = (): Promise<IResponse> => {
  const authStore = useAuthStoreWithOut()
  const data = authStore.getRefreshToken
  return request.post({ url: '/member/auth/token/refresh', data })
}

export { service }
