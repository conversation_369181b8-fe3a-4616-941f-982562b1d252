import { service } from './service'

import { config } from './config'

const { default_headers } = config

const request = (option: any) => {
  const { url, method, params, data, headers, responseType } = option

  const headersData = headers || {}
  if (!headersData['Content-Type']) {
    headersData['Content-Type'] = default_headers
  }

  // console.log('post请求，headersData：', headersData)
  // console.log('post请求，responseType类型：', responseType)

  return service({
    url: url,
    method,
    params,
    data,
    responseType: 'json', // 默认为 "json"
    headers: headersData
  })
}
export default {
  get: <T = any>(option: any) => {
    return request({ method: 'get', ...option }) as unknown as T
  },
  post: <T = any>(option: any) => {
    return request({ method: 'post', ...option }) as unknown as T
  },
  delete: <T = any>(option: any) => {
    return request({ method: 'delete', ...option }) as unknown as T
  },
  put: <T = any>(option: any) => {
    return request({ method: 'put', ...option }) as unknown as T
  }
}
