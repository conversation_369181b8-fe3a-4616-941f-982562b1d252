import { defineNuxtRouteMiddleware, navigateTo } from 'nuxt/app'
import { useAuthStoreWithOut } from '@/store/modules/auth'

// 路由中间件，放置在middleware/目录中，当在页面中使用时，会通过异步导入自动加载。
// 全局路由中间件，放置在middleware/目录中（使用.global后缀），将在每次路由更改时自动运行。
// https://cloud.tencent.com/developer/article/2351950
export default defineNuxtRouteMiddleware(async (to, from) => {
  // 使用页面路由守卫，判断是否登录，未登录则跳转到登录页
  const authStore = useAuthStoreWithOut()
  // console.log('===> authStore.getIsMember:', authStore.getIsMember);
  if (authStore.getIsMember) {
    // 如果已登录，返回 undefined 或空对象，继续导航
    // 如果不需要重定向或中断导航，可以返回 undefined 或空对象
    return
  }
  else {
    // 未登录，跳转到登录页
    let lang = 'zhHans'
    let page = 'account'
    const pathParts = to.path.split('/') // 从路径中截取当前界面语言（/zhHans/account，中的 zhHans）
    if (pathParts.length > 2) {
      lang = pathParts[1] // 当前语言:zhHans、en、zhHant
      page = pathParts[2] // 当前页面:account 、billing
      // console.log('===> interface lang:', lang)
      // console.log('===> page:', page)
    }
    // 一定要写return
    // 修改：保留所有查询参数
    return navigateTo({
      path: '/' + lang + '/loading',
      query: {
        fromPage: page,
        ...to.query // 保留原始路由的所有查询参数
      }
    })
  }
})
