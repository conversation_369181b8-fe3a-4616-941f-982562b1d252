export default defineNuxtPlugin(() => {
  const config = useRuntimeConfig()

  // 检查Google客户端ID是否配置
  if (!config.public.googleClientId) {
    console.error('Google客户端ID未配置，请检查环境变量GOOGLE_LOGIN_CLIENT_ID')
  }

  return {
    provide: {
      googleSignIn: {
        init: () => {
          return new Promise((resolve) => {
            if (typeof window !== 'undefined' && !window.google) {
              const script = document.createElement('script')
              script.src = 'https://accounts.google.com/gsi/client'
              script.async = true
              script.defer = true
              script.onload = () => {
                resolve(window.google)
              }
              document.head.appendChild(script)
            }
            else {
              resolve(window.google)
            }
          })
        },

        renderButton: (elementId: string, options: any) => {
          return new Promise((resolve) => {
            const checkGoogle = setInterval(() => {
              if (window.google && window.google.accounts) {
                clearInterval(checkGoogle)
                window.google.accounts.id.initialize({
                  client_id: config.public.googleClientId,
                  callback: options.onSuccess,
                  ...options
                })

                window.google.accounts.id.renderButton(document.getElementById(elementId), {
                  theme: 'outline',
                  size: 'large',
                  text: 'signin_with',
                  ...options.buttonConfig
                })
                resolve(null)
              }
            }, 100)
          })
        },

        requestCode: (options: any) => {
          return new Promise((resolve, reject) => {
            const checkGoogle = setInterval(() => {
              if (window.google && window.google.accounts) {
                clearInterval(checkGoogle)
                window.google.accounts.oauth2
                  .initCodeClient({
                    client_id: config.public.googleClientId,
                    scope: 'email profile openid',
                    ux_mode: 'popup',
                    callback: (response: any) => {
                      if (response.error) {
                        reject(response)
                      }
                      else {
                        resolve(response)
                      }
                    },
                    error_callback: (error: any) => {
                      reject(error)
                    },
                    ...options
                  })
                  .requestCode()
              }
            }, 100)
          })
        }
      }
    }
  }
})

// Window.google 类型声明已在 types/google-signin.d.ts 中定义
