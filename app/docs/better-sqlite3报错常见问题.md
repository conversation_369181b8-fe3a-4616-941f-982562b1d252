### 当出现这个错误的时候

```
PS E:\Vscode\ztsl-web> pnpm rebuild better-sqlite3
node_modules/.pnpm/better-sqlite3@12.2.0/node_modules/better-sqlite3: Running install snode_modules/.pnpm/better-sqlite3@12.2.0/node_modules/better-sqlite3: Running install script, failed in 13.9s
.../node_modules/better-sqlite3 install$ prebuild-install || node-gyp rebuild --rele…
│ prebuild-install warn install Request timed out
│ E:\Vscode\ztsl-web\node_modules\.pnpm\better-sqlite3@12.2.0\node_modules\better-sq…
│ gyp info it worked if it ends with ok
│ gyp info using node-gyp@11.1.0
│ gyp info using node@22.16.0 | win32 | x64
│ gyp info find Python using Python version 3.11.4 found at "C:\Users\<USER>\AppData\Lo…
│ gyp ERR! find VS
│ gyp ERR! find VS msvs_version not set from command line or npm config
│ gyp ERR! find VS VCINSTALLDIR not set, not running in VS Command Prompt
│ gyp ERR! find VS could not use PowerShell to find Visual Studio 2017 or newer, try…
│ gyp ERR! find VS
│ gyp ERR! find VS Failure details: undefined
│ gyp ERR! find VS could not use PowerShell to find Visual Studio 2017 or newer, try…
│ gyp ERR! find VS
│ gyp ERR! find VS Failure details: undefined
│ gyp ERR! find VS not looking for VS2017 as it is only supported up to Node.js 21
│ gyp ERR! find VS not looking for VS2017 as it is only supported up to Node.js 21
│ gyp ERR! find VS not looking for VS2017 as it is only supported up to Node.js 21
│ gyp ERR! find VS not looking for VS2015 as it is only supported up to Node.js 18
│ gyp ERR! find VS not looking for VS2013 as it is only supported up to Node.js 8
│ gyp ERR! find VS
│ gyp ERR! find VS ******************************\*\*******************************
│ gyp ERR! find VS You need to install the latest version of Visual Studio
│ gyp ERR! find VS including the "Desktop development with C++" workload.
│ gyp ERR! find VS For more information consult the documentation at:
│ gyp ERR! find VS https://github.com/nodejs/node-gyp#on-windows
│ gyp ERR! find VS ******************************\*\*******************************
│ gyp ERR! find VS
│ gyp ERR! configure error
│ gyp ERR! stack Error: Could not find any Visual Studio installation to use
│ gyp ERR! stack at VisualStudioFinder.fail (C:\Users\<USER>\AppData\Local\pnpm\.tools\…
│ gyp ERR! stack at VisualStudioFinder.findVisualStudio (C:\Users\<USER>\AppData\Local\…
│ gyp ERR! stack at process.processTicksAndRejections (node:internal/process/task_qu…
│ gyp ERR! stack at async createBuildDir (C:\Users\<USER>\AppData\Local\pnpm\.tools\pnp…
│ gyp ERR! stack at async run (C:\Users\<USER>\AppData\Local\pnpm\.tools\pnpm\10.6.2\no…
│ gyp ERR! System Windows_NT 10.0.26100
│ gyp ERR! command "E:\\nvm\\nodejs\\node.exe" "C:\\Users\\<USER>\\AppData\\Local\\pnpm…
│ gyp ERR! cwd E:\Vscode\ztsl-web\node_modules\.pnpm\better-sqlite3@12.2.0\node_modu…
│ gyp ERR! node -v v22.16.0
│ gyp ERR! node-gyp -v v11.1.0
│ gyp ERR! not ok
└─ Failed in 13.9s at E:\Vscode\ztsl-web\node_modules\.pnpm\better-sqlite3@12.2.0\node_modules\better-sqlite3
```

#### 方案 2： 安装 VS Build Tools

1. 下载 Visual Studio 2022 Build Tools
   👉 https://visualstudio.microsoft.com/visual-cpp-build-tools/

2. 安装时勾选：
   Desktop development with C++
   Windows 10/11 SDK（默认勾上即可）

3. 安装完成后，重新运行：

   ```
   pnpm rebuild better-sqlite3
   ```

#### 方案 2：改用 Node.js LTS 版本

因为 Node.js 22 还很新，better-sqlite3@12.2.0 可能没有对应的预编译包。
建议用 Node.js 20 LTS（长期支持，兼容最好）。

如果你用 nvm：

nvm install 20
nvm use 20

然后重新安装依赖：

pnpm install

#### 方案 3：强制下载预编译二进制（如果有）

尝试：

pnpm rebuild better-sqlite3 --build-from-source=false

但这个前提是作者已经发布了 Node.js 22 的预编译包（目前可能没有）。

⚡ 建议：
我推荐你先试 方案 2：换成 Node.js 20 LTS，这样基本能直接用预编译包，不需要装 VS。
如果必须用 Node.js 22，再去装 VS Build Tools 编译。
