import type { TranslationEngineState, TranslationEngine, ErrorInfo, RationaleInfo, ScoreInfo, TranslationResult, SortIndexInfo, AiSelectTranslationStatus } from '@/types/translateOptions'
import { TranslationStatus } from '@/types/translateOptions'

/**
 * 创建翻译引擎状态实例
 * @param engine 基础翻译引擎配置
 * @returns 带有状态的翻译引擎实例
 */
export const createTranslationEngineState = (engine: TranslationEngine): TranslationEngineState => ({
  ...engine,
  translationsResult: [],
  error: [],
  score: [],
  rationale: [], // 择优分析
  status: TranslationStatus.NotTranslated, // 翻译状态
  sortIndexArray: [], // 排序索引数组
  selectedTransStatus: [] // 择优翻译状态
})

/**
 * 根据可用引擎列表生成翻译引擎配置
 * @param enabledEngines 启用的翻译引擎列表
 * @returns 翻译引擎状态配置列表
 */
export const getTranslationEnginesConfig = (enabledEngines: TranslationEngine[]): TranslationEngineState[] => {
  return enabledEngines.map(engine => createTranslationEngineState(engine))
}
