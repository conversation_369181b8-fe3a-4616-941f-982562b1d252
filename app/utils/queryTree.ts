// 查询目录

/**
 * 查询目录并映射数据
 * @param locale - 语言代码
 * @param collection - 集合名称
 * @returns 映射后的目录数据
 */
export const queryAndMapTree = async (locale: string, collection: string) => {
  const { data } = await useAsyncData(`${locale}_${collection}`, () => queryCollection(`${locale}_${collection}`).first())

  // 定位到目录下的直接子项
  const items = data.value?.navigation?.items

  // 确保每个子项的路径正确
  return items
    ?.map(item => ({
      ...item,
      // 规范化路径，确保格式一致
      path: item.path,
      // 保留原始子项结构
      children: item.children?.map(child => ({
        ...child,
        path: child.path
      }))
    }))
    .sort((a, b) => (a.order || 0) - (b.order || 0))
}
