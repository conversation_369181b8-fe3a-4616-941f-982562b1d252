import { useStorage } from '@/hooks/web/useStorage'
import { UAParser } from 'ua-parser-js'
// import { Device } from '~/utils/device';
const { setStorage, getStorage } = useStorage('localStorage')
/**
 *
 * 怎么获取User-Agent？ https://blog.browserscan.net/zh/docs/useragent
 * AppleWebKit/537.36讲解： https://blog.csdn.net/qq_45656077/article/details/113619553
 * 存储 localStorage 中获取系统信息
 * 获取系统信息
 */

/**
 * 如果需要获取User-Agent 请使用 getResult().ua 获取
 * @returns
 */
export const getSystemInfoFromSession = function () {
  const parser = new UAParser()
  const result = parser.getResult()

  const systemInfo = {
    device_type: result.device.type, // 设备类型:PC
    os_name: result.os.name, // 操作系统名称
    os_version: result.os.version, // 操作系统版本
    browser_name: result.browser.name, // 浏览器名称
    browser_version: result.browser.version, // 浏览器版本
    browser_engine: result.engine.name // 浏览器内核
  }

  return systemInfo
}

/**
 * 拆分多语言句子
 * @param text 文本
 * @returns 句子数组
 */
export const splitMultilingualSentences = (text: string): string[] => {
  if (typeof text !== 'string') {
    throw new TypeError('输入必须是字符串')
  }
  try {
    // 保护英文缩写和版本号中的句点，避免错误拆分，例如 "Node.js" 或 "22.13.11"
    const dotPlaceholder = '__DOT__'
    const protectedText = text.replace(/([A-Za-z]+\.[A-Za-z]+|(?:\d+\.)+\d+)/g, match => match.replace(/\./g, dotPlaceholder))

    // 判断文本中是否包含 CJK（中文、日文、韩文）字符
    const containsCJK = /[\u4e00-\u9FFF\u3040-\u30FF\uAC00-\uD7AF]/.test(text)

    // 根据是否包含 CJK 字符选择拆分策略：
    // - CJK 文本通常无空格，因此允许分隔符后零个或多个空白，
    //   但如果接续的内容以字母或数字开头，则认为不在句末边界（避免拆分英文缩写或版本号）。
    // - 非 CJK 语言要求分隔符后至少有一个空格，并且下一个字符不能是数字。
    const pattern = containsCJK ? /(?<=[.。！？!?؟।॥︕︖﹗﹖｡]+)(?=\s*(?![A-Za-z0-9])[^.。！？!?؟।॥︕︖﹗﹖｡]|$)/u : /(?<=[.。！？!?؟。॥︕︖﹗﹖｡]+)(?=\s+(?!\d)[^.。！？!?؟।॥︕︖﹗﹖｡]|$)/u

    // 分割经过保护的文本但保留分隔符
    const sentences = protectedText.split(pattern)

    // 过滤空字符串，同时将预处理的占位符恢复为句点
    return sentences.filter(sentence => sentence.trim().length > 0).map(sentence => sentence.replace(new RegExp(dotPlaceholder, 'g'), '.'))
  }
  catch (error) {
    console.log('句子分割出错:', error)
    return [text] // 降级处理
  }
}

/**
 *
 * 存储 localStorage 翻译服务列表
 * 获取翻译服务列表
 */
export function getTranslateServiceList(list) {
  let USER_AGENT = getStorage('ztsl_web_translate_service_list')
  if (!USER_AGENT || USER_AGENT.os_name === 'unknown') {
    USER_AGENT = list
    setStorage('ztsl_web_translate_service_list', USER_AGENT)
  }
  return USER_AGENT
}

/**
 * 存储 localStorage 翻译服务列表
 */
export function setTranslateServiceList(systemInfo) {
  // console.log('===>WEB_TRANSLATE_SERVICE_LIST:', systemInfo);
  setStorage('ztsl_web_translate_service_list', systemInfo)
}

/**
 * 判断是否是微信浏览器
 */
export function isWechatBrowser() {
  // const ua = navigator.userAgent.toLowerCase();
  // return ua.indexOf("micromessenger") !== -1;
  return /(micromessenger)/i.test(navigator.userAgent)
}

/**
 * 检测字符串是否为空或只包含不可见字符
 * @param {string} text - 要检查的字符串
 * @returns {boolean} - 如果字符串为空或只包含不可见字符，则返回true
 */
export const isEmptyContent = (text: string): boolean => {
  if (!text || text.trim() === '') return true

  // 检测字符串是否只包含不可见字符
  // \u00A0 - 不间断空格
  return /^[\u00A0\s]+$/.test(text)
}

/**
 * 解析 html DOM 字符串
 * @param {string} str - 要解析的HTML字符串
 * @returns {Node|DocumentFragment|null} - 返回解析后的DOM节点或DocumentFragment
 */
export const parseStringToDom = (str: string) => {
  if (!str) return null

  try {
    const tempContainer = document.createElement('div')
    tempContainer.innerHTML = str // 直接注入原始字符串

    // 如果只有一个子节点，直接返回它
    if (tempContainer.childNodes.length === 1) {
      return tempContainer.firstChild
    }
    // 如果有多个子节点，创建一个DocumentFragment来包含所有节点
    else if (tempContainer.childNodes.length > 1) {
      const fragment = document.createDocumentFragment()
      // 将所有子节点移动到DocumentFragment中
      while (tempContainer.firstChild) {
        fragment.appendChild(tempContainer.firstChild)
      }
      return fragment
    }

    return null
  }
  catch (error) {
    console.error('解析DOM字符串错误:', error)
    return null
  }
}

/**
 * 检测当前设备是否为移动端（包括平板设备）
 * @returns {boolean} - 如果是移动端或平板设备返回true，否则返回false
 */
export const isMobileDevice = (): boolean => {
  // 使用正则表达式检测常见移动设备和平板设备的User-Agent特征
  const mobileRegex = /(android|iphone|ipad|ipod|blackberry|windows phone|opera mini|silk|kindle|mobile|tablet)/i

  // 检查navigator.userAgent
  if (typeof navigator !== 'undefined' && navigator.userAgent) {
    if (mobileRegex.test(navigator.userAgent)) {
      return true
    }
  }

  // 检查屏幕宽度（通常移动设备和平板屏幕较窄）
  if (typeof window !== 'undefined' && window.innerWidth) {
    // 使用更大的宽度阈值以包含平板设备（通常平板设备宽度小于1024px）
    if (window.innerWidth < 1024) {
      return true
    }
  }

  return false
}
