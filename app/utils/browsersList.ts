import iconCrxPuzzle from '@/assets/icon/icon-crx-puzzle.svg'
import iconZip from '@/assets/images/icon-zip.svg'
import icon360 from '@/assets/images/logo-360.svg'

// 扩展插件地址
const chrome_extensions_url = '#' // https://chromewebstore.google.com/category/extensions?utm_source=ext_sidebar&hl=zh-CN
const edge_extensions_url = '#' // https://microsoftedge.microsoft.com/addons/Microsoft-Edge-Extensions-Home
const the_360browser_extensions_url = '#' // https://ext.se.360.cn/#/home
const crx_install_url = 'extensions/crx'
const zip_install_url = 'extensions/zip'
const safari_extensions_url = '#' // https://www.selecttranslate.com/extensions/safari
const firefox_extensions_url = '#' // https://www.selecttranslate.com/extensions/firefox

interface BrowserButton {
  id: string
  type: 'icon' | 'image'
  url: string
  label: string
  icon?: string // 图标类
  imgSrc?: string // 图片类
}

// 浏览器按钮列表工厂函数
export const getBrowsersList = (): BrowserButton[] => {
  // 注意useNuxtApp 要在函数类使用
  const { $i18n } = useNuxtApp()

  return [
    {
      id: 'Chrome',
      type: 'icon',
      url: chrome_extensions_url,
      label: $i18n.t('home.hero.extension_pc_chrome_label'),
      icon: 'i-logos-chrome'
    },
    {
      id: 'Edge',
      type: 'icon',
      url: edge_extensions_url,
      label: $i18n.t('home.hero.extension_pc_edge_label'),
      icon: 'i-logos-microsoft-edge'
    },
    {
      id: 'Safari',
      type: 'icon',
      url: safari_extensions_url,
      label: $i18n.t('home.hero.extension_pc_safari_label'),
      icon: 'i-logos-safari'
    },
    {
      id: 'Firefox',
      type: 'icon',
      url: firefox_extensions_url,
      label: $i18n.t('home.hero.extension_pc_firefox_label'),
      icon: 'i-logos-firefox'
    }
    // {
    //   id: '360',
    //   type: 'image',
    //   url: the_360browser_extensions_url,
    //   label: $i18n.t('home.hero.extension_pc_360browser_label'),
    //   imgSrc: icon360
    // },
    // {
    //   id: 'Zip',
    //   type: 'image',
    //   url: zip_install_url,
    //   label: $i18n.t('home.hero.extension_pc_zip_label'),
    //   imgSrc: iconZip
    // },
    // {
    //   id: 'Crx',
    //   type: 'image',
    //   url: crx_install_url,
    //   label: $i18n.t('home.hero.extension_pc_crx_label'),
    //   imgSrc: iconCrxPuzzle
    // }
  ]
}
