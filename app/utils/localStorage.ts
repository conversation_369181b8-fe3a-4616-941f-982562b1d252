import { useStorage } from '@/hooks/web/useStorage'

const { getStorage } = useStorage('localStorage')

export enum LocalStorageKeyEnum {
  ZTSL_WEB_SYSTEMINFOFROMSESSION = 'ztsl_web_systeminfofromsession', // 系统信息
  ZTSL_WEB_TRANSLATE_SERVICE_LIST = 'ztsl_web_translate_service_list', // 翻译服务列表
  ZTSL_WEB_TRANSLATE_SERVICE = 'ztsl_web_translate_service', // 翻译服务
  ZTSL_WEB_TARGET_LANGUAGE = 'ztsl_web_target_language', // 目标语言
  ZTSL_WEB_TRANS_DISPLAY_MODE = 'ztsl_web_trans_display_mode' // 翻译显示模式
}
/**
 *
 * 获取 localStorage 内容
 * @param key 存储key
 * @returns 返回存储内容
 */
export function getLocalStorage(key: string) {
  return window['localStorage'].getItem(key)
}

/**
 * 存储 localStorage 内容
 * @param key 存储key
 * @param value 存储内容
 */
export function setLocalStorage(key: string, value: string) {
  // setStorage(key, value)
  window['localStorage'].setItem(key, value)
}
