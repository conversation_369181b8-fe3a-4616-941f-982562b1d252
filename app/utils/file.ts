import { PDFDocument } from 'pdf-lib'
import J<PERSON><PERSON><PERSON> from 'jszip'
import { renderAsync } from 'docx-preview'

type FileType = 'pdf' | 'ppt' | 'word' | 'image' | 'epub' | 'html' | 'markdown' | 'text' | 'subtitle' | 'other'

/**
 * 文件信息接口
 * 用于存储文件的基本信息和解析状态
 * 包含文件类型、大小、页数等属性
 */
interface FileInfo {
    parse_success: boolean // 是否解析成功
    file: File // 文件
    fileType: FileType // 文件类型
    fileName: string // 文件名
    fileSize: number // 文件大小（字节）
    fileSizeMb: number // 文件大小（MB）
    pageCount: number // 页数
    isEncrypted: boolean // 是否加密
}

/**
 * 解析文件
 * @param file 文件对象
 * @returns 文件信息
 */
export const parseFile = async (file: File): Promise<FileInfo> => {
    // 获取文件类型
    const fileType = getFileType(file)

    // 计算文件页数
    const pageCount = await getFilePageCount(file)

    // 处理加密文件
    let isEncrypted = false
    if (fileType === 'pdf') {
        // 读取文件内容
        const arrayBuffer = await file.arrayBuffer()
        const pdf = await PDFDocument.load(arrayBuffer, {
            ignoreEncryption: true,
            throwOnInvalidObject: false
        })
        isEncrypted = pdf.isEncrypted
    }

    // 返回文件信息
    const fileInfo: FileInfo = {
        parse_success: true,
        file,
        fileType: fileType,
        fileName: file.name,
        fileSize: file.size,
        fileSizeMb: file.size / 1024 / 1024,
        pageCount: pageCount,
        isEncrypted: isEncrypted
    }
    return fileInfo
}

/**
 * 获取文件类型
 * @param file 文件对象
 * @returns 文件类型
 */
export const getFileType = (file: File): FileType => {
    const fileName = file.name.toLowerCase()
    if (fileName.endsWith('.pdf')) {
        return 'pdf'
    }
    if (fileName.endsWith('.ppt') || fileName.endsWith('.pptx')) {
        return 'ppt'
    }
    if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
        return 'word'
    }
    if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') || fileName.endsWith('.gif')) {
        return 'image'
    }
    if (fileName.endsWith('.epub')) {
        return 'epub'
    }
    if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
        return 'html'
    }
    if (fileName.endsWith('.md')) {
        return 'markdown'
    }
    if (fileName.endsWith('.txt')) {
        return 'text'
    }
    if (fileName.endsWith('.srt') || fileName.endsWith('.vtt') || fileName.endsWith('.ass') || fileName.endsWith('.ssa') || fileName.endsWith('.sbv') || fileName.endsWith('.lrc')) {
        return 'subtitle'
    }
    return 'other'
}

/**
 * 获取文件页数
 * @param file 文件对象
 * @returns 文件页数
 */
export const getFilePageCount = async (file: File): Promise<number> => {
    // 获取文件类型
    const fileType = getFileType(file)

    // 处理 PDF 文件
    if (fileType === 'pdf') {
        // 读取文件内容
        const arrayBuffer = await file.arrayBuffer()
        const pdf = await PDFDocument.load(arrayBuffer, {
            ignoreEncryption: true,
            throwOnInvalidObject: false
        })
        return pdf.getPageCount()
    }

    // 处理 PPT 文件
    if (fileType === 'ppt') {
        // 读取文件内容
        const zip = await JSZip.loadAsync(file)
        // 过滤出所有 slide 文件
        const slides = Object.keys(zip.files).filter(name =>
            name.startsWith('ppt/slides/slide')
        )
        return slides.length
    }

    // 处理 Word 文件
    if (fileType === 'word') {
        // 读取文件内容
        const container = document.createElement('div')
        document.body.appendChild(container)

        await renderAsync(file, container)

        const pageHeightPx = 1122 // A4 高度对应的像素值（需根据 DPI 调整）
        const totalHeight = container.scrollHeight
        const pageCount = Math.ceil(totalHeight / pageHeightPx)

        document.body.removeChild(container)
        return pageCount
    }

    // 处理图片文件
    if (fileType === 'image') {
        return 1
    }

    // 处理 EPUB 文件
    if (fileType === 'epub') {
        try {
            const zip = await JSZip.loadAsync(file)
            // EPUB 文件结构：OEBPS 目录下的 xhtml 文件通常是章节内容
            const contentFiles = Object.keys(zip.files).filter(name =>
                (name.includes('OEBPS') || name.includes('EPUB')) && 
                (name.endsWith('.xhtml') || name.endsWith('.html'))
            )
            return Math.max(1, contentFiles.length) // 至少返回1页
        } catch (error) {
            console.error('解析 EPUB 文件失败:', error)
            return 1
        }
    }

    // 处理 HTML 文件
    if (fileType === 'html') {
        try {
            const text = await file.text()
            // 简单按内容长度估算页数，假设每页约3000字符
            const estimatedPages = Math.max(1, Math.ceil(text.length / 3000))
            return estimatedPages
        } catch (error) {
            console.error('解析 HTML 文件失败:', error)
            return 1
        }
    }

    // 处理 Markdown 文件
    if (fileType === 'markdown') {
        try {
            const text = await file.text()
            // 按 markdown 标题分页，或按内容长度估算
            const headingMatches = text.match(/^#+\s/gm) || []
            const pagesByHeadings = headingMatches.length
            const pagesByLength = Math.ceil(text.length / 3000)
            return Math.max(1, Math.max(pagesByHeadings, pagesByLength))
        } catch (error) {
            console.error('解析 Markdown 文件失败:', error)
            return 1
        }
    }

    // 处理 TXT 文件
    if (fileType === 'text') {
        try {
            const text = await file.text()
            // 按行数估算页数，假设每页约50行
            const lines = text.split('\n').length
            return Math.max(1, Math.ceil(lines / 50))
        } catch (error) {
            console.error('解析 TXT 文件失败:', error)
            return 1
        }
    }

    // 处理字幕文件
    if (fileType === 'subtitle') {
        try {
            const text = await file.text()
            // SRT 字幕按字幕条数估算，VTT 按 cue 数量估算
            if (file.name.toLowerCase().endsWith('.srt')) {
                const subtitleBlocks = text.split('\n\n').filter(block => block.trim())
                return Math.max(1, Math.ceil(subtitleBlocks.length / 20)) // 假设每页20条字幕
            } else {
                // VTT、ASS 等格式按行数估算
                const lines = text.split('\n').filter(line => line.trim())
                return Math.max(1, Math.ceil(lines.length / 30))
            }
        } catch (error) {
            console.error('解析字幕文件失败:', error)
            return 1
        }
    }

    return 0
}
