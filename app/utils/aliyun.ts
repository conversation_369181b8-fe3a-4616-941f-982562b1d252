import OSS from 'ali-oss'
import { getOSSToken } from '@/api/oss'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import type { HeadersTokenType } from '@/api/login/types'
import type { OssType } from '@/api/types'

interface FileObj {
  progress: number
  status: number
}

interface UploadCallbackData {
  ossUrl: string // 文件在 OSS 上的 URL
  status: number // 上传状态
  storage_path: string // 存储路径
  storage_bucket: string // 存储桶名称
  storage_cloud: string // 存储云服务商
  name: string // 文件名
}

interface Credentials {
  region: string // 存储区域
  accessKeyId: string // 访问密钥ID
  accessKeySecret: string // 访问密钥
  securityToken: string // 临时访问密钥
  bucket: string // 存储桶名称
}

// 定义一个全局对象来存储每个文件的上传进度
const uploadProgressMap = new Map<string, number>()

/**
 * 上传文件
 * @param file 上传的文件
 * @param fileObj 上传的文件对象
 * @param folder 上传的文件夹
 * @param callback 回调函数
 * @returns 上传结果的Promise
 */
export async function uploadFile(file: File, fileObj: FileObj, folder: string = '', callback?: (data: UploadCallbackData) => void): Promise<UploadCallbackData> {
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const paramData: OssType = {
    platform: 'ztsl',
    storage_cloud: 'aliyun'
  }
  const res = await getOSSToken(headers, paramData)
  console.log('RoleSessionName-------', res)

  const client = new OSS({
    region: res.data.region,
    accessKeyId: res.data.accessKeyId,
    accessKeySecret: res.data.accessKeySecret,
    stsToken: res.data.securityToken,
    bucket: res.data.bucket,
    secure: true // Force HTTPS
  })

  let fileName = folder ? `${folder}/` : ''
  fileName += fileRandomString() + '.' + file.type.substr(file.type.indexOf('/') + 1)

  // 使用文件名称作为键来存储上传进度
  uploadProgressMap.set(fileName, 0)

  try {
    const result = await client.multipartUpload(fileName, file, {
      progress: (p: number) => {
        const progress = parseInt(p * 100)
        fileObj.progress = progress
        fileObj.status = 2
        // 更新上传进度
        uploadProgressMap.set(fileName, progress)
      },
      meta: { year: 2020, people: 'test' },
      mime: file.type
    })
    fileObj.status = 3
    client.putACL(result.name, 'private')
    console.log('fileUrl---------', result.name)
    const fileUrl = `https://${res.data.baseUrl}/${result.name}`
    console.log('fileUrl---------', fileUrl)
    const _data: UploadCallbackData = {
      ossUrl: fileUrl,
      status: 3,
      storage_path: folder,
      storage_bucket: res.data.bucket,
      storage_cloud: 'aliyun',
      name: file.name
    }
    if (callback) callback(_data)
    return _data
  }
  catch (error) {
    console.error('-----------', error)
    const errorStatus = error instanceof Error && 'status' in error ? (error as any).status : 0
    fileObj.status = errorStatus === 0 ? 5 : 4
    if (callback)
      callback({
        ossUrl: '',
        status: fileObj.status,
        storage_path: folder,
        storage_bucket: res.data.bucket,
        storage_cloud: 'aliyun',
        name: file.name
      })
    return {
      ossUrl: '',
      status: fileObj.status,
      storage_path: folder,
      storage_bucket: res.data.bucket,
      storage_cloud: 'aliyun',
      name: file.name
    }
  }
  finally {
    // 上传完成后，移除该文件的进度记录
    uploadProgressMap.delete(fileName)
  }
}

/**
 * 获取文件的上传进度
 * @param fileName 文件名称
 * @returns 上传进度百分比
 */
export function getUploadProgress(fileName: string): number {
  return uploadProgressMap.get(fileName) || 0
}

function fileRandomString(len: number = 32): string {
  const $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789'
  const maxPos = $chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}
