/**
 * 窗口拖拽工具函数
 * 提供窗口拖拽功能，支持自定义拖拽起始位置、结束回调等
 */

import { ref } from 'vue'

/**
 * 窗口位置类型
 */
export interface WindowPosition {
  x: number
  y: number
}

/**
 * 拖拽配置选项
 */
export interface DragOptions {
  /**
   * 初始窗口位置
   */
  initialPosition?: WindowPosition
  /**
   * 拖拽开始回调
   */
  onDragStart?: (position: WindowPosition) => void
  /**
   * 拖拽进行中回调
   */
  onDragging?: (position: WindowPosition) => void
  /**
   * 拖拽结束回调
   */
  onDragEnd?: (position: WindowPosition) => void
  /**
   * 边界限制
   */
  boundary?: {
    minX?: number
    maxX?: number
    minY?: number
    maxY?: number
  }
}

/**
 * 使用拖拽功能
 * @param options 拖拽配置选项
 * @returns 拖拽相关的状态和方法
 */
export function useDrag(options: DragOptions = {}) {
  // 窗口位置
  const position = ref<WindowPosition>(options.initialPosition || { x: 0, y: 0 })
  // 是否正在拖拽
  const isDragging = ref(false)
  // 鼠标开始拖拽时的位置
  let startMousePosition = { x: 0, y: 0 }
  // 开始拖拽时窗口的位置
  let startWindowPosition = { x: 0, y: 0 }

  /**
   * 开始拖拽
   * @param event 鼠标事件
   */
  const startDrag = (event: MouseEvent) => {
    // 记录开始拖拽时的鼠标位置
    startMousePosition = {
      x: event.clientX,
      y: event.clientY
    }

    // 记录开始拖拽时窗口的位置
    startWindowPosition = { ...position.value }

    // 设置正在拖拽状态
    isDragging.value = true

    // 添加鼠标移动和松开事件监听
    document.addEventListener('mousemove', onDrag)
    document.addEventListener('mouseup', endDrag)

    // 调用拖拽开始回调
    if (options.onDragStart) {
      options.onDragStart(position.value)
    }
  }

  /**
   * 拖拽中
   * @param event 鼠标事件
   */
  const onDrag = (event: MouseEvent) => {
    if (!isDragging.value) return

    // 计算鼠标移动的距离
    const dx = event.clientX - startMousePosition.x
    const dy = event.clientY - startMousePosition.y

    // 更新窗口位置
    let newX = startWindowPosition.x + dx
    let newY = startWindowPosition.y + dy

    // 应用边界限制
    if (options.boundary) {
      const { minX, maxX, minY, maxY } = options.boundary

      if (minX !== undefined) newX = Math.max(minX, newX)
      if (maxX !== undefined) newX = Math.min(maxX, newX)
      if (minY !== undefined) newY = Math.max(minY, newY)
      if (maxY !== undefined) newY = Math.min(maxY, newY)
    }

    // 更新位置状态
    position.value = {
      x: newX,
      y: newY
    }

    // 调用拖拽进行中回调
    if (options.onDragging) {
      options.onDragging(position.value)
    }
  }

  /**
   * 结束拖拽
   */
  const endDrag = () => {
    if (!isDragging.value) return

    // 设置拖拽状态为结束
    isDragging.value = false

    // 移除事件监听器
    document.removeEventListener('mousemove', onDrag)
    document.removeEventListener('mouseup', endDrag)

    // 调用拖拽结束回调
    if (options.onDragEnd) {
      options.onDragEnd(position.value)
    }
  }

  return {
    position,
    isDragging,
    startDrag,
    endDrag
  }
}
