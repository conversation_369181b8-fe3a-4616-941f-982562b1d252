/**
 * 根据平台编码、资源包类型，获取资源信息的请求参数
 */
export type GetResourcePackType = {
  platform?: string
  resource_pack_type?: string
}

/**
 * 根据产品位置编号和平台编码，获取产品信息的请求参数
 */
export type GetProductType = {
  platform?: string
  position_no?: string
}

/**
 * 根据问题编号和平台编码，获取反馈回复信息的请求参数
 */
export type GetFeedbackType = {
  platform?: string
  feedback_no?: string
}

/**
 * 根据产品编号和购买方式，创建初始化支付订单的请求参数。
 */
export type CreatePayOrderType = {
  /** 平台编码 */
  platform?: string
  /** 平台终端 */
  platform_endpoint?: string
  /** 会员Token */
  token?: string
  /** 订单类型（plus:Plus会员、general_token:通用token流量、pdf_plus:PDF_PLUS页数） */
  order_type: string
  /** 产品编号 */
  product_no?: string
  /** 购买数量 */
  buy_count?: number
  /** 支付模式(1:直接购买、2:订阅模式) */
  pay_mode?: number
  /** 购买方式 */
  buy_mode?: string
  /** 支付方式 */
  pay_way?: string
  /** 支付类型 */
  pay_type?: string
  /** 支付回调地址 */
  pay_return_url?: string
}

/**
 * 会员支付订单的请求参数
 */
export type MemberPayOrderType = {
  order_no?: string
}

/**
 * 免费试用的请求参数
 */
export type FreeTrialType = {
  /** 平台编码 */
  platform?: string
  /** 会员Token */
  token?: string
}

/**
 * 创建反馈请求参数
 */
export type CreateFeedback = {
  /** 平台编码 */
  platform?: string
  /** 问题内容 */
  feedback_content?: string
  /** 邮箱 */
  email?: string
  /** 会员Token */
  token?: string
  /** 问题编号 */
  // feedback_no?: string;
  /** 反馈类型 */
  feedback_type?: number
  /** 会员编号 */
  member_id?: string
}

/**
 * 创建回复请求参数
 */
export type CreateReply = {
  /** 平台编码 */
  platform?: string
  /** 回复内容 */
  content?: string
  /** 问题编号 */
  feedback_no?: string
  /** 会员Token */
  token?: string
  /** 回复类型 */
  reply_user_type?: number
  /** 父ID（引用主键ID、回复编号） */
  parent_id?: string
}

/**
 * 解析PDF内容的请求参数
 * platform: str  # 产品所属平台（当前默认ztsl:精挑翻译）
 * url: str  # PDF文件地址
 * total_pages: str  # PDF总页数
 * page_ranges: str  # PDF解析页码范围
 * storage_bucket: str  # PDF文件存储桶
 * storage_path: str  # PDF文件文件夹
 * storage_cloud: str  # PDF文件存储云
 * coded_text: str  # PDF文件MD5文本
 * name: str  # PDF文件名称
 */

export type ParsePdfPdf = {
  platform?: string
  url?: string
  total_pages?: string
  page_ranges?: string
  storage_bucket?: string
  storage_path?: string
  storage_cloud?: string
  coded_text?: string
  name?: string
}
/**
 * 查询PDF转换状态的请求参数
 */
export type PdfStatus = {
  file_id?: string
}
/**
 * 删除PDF转换状态的请求参数
 */
export type Pdfdelete = {
  id?: number
}

/**
 * 应用下载的请求参数
 */
export type GetAppDownloadUrl = {
  /** 应用编号 */
  application_no?: string
  /** 版本编号 */
  version_no?: string
}

/**
 * 根据平台编码和存储云，获取产品信息的请求参数
 */
export type OssType = {
  platform?: string
  storage_cloud?: string
}
