export type HeadersTokenType = {
  token: string
}

export type MemberSingupType = {
  email: string
  password: string
  method: string
  platform?: string // 用户在注册时使用的入口平台（web、app等）
  interface_language?: string // 用户在注册界面端选择的界面语言（zhHans、zhHant、en等）
}

export type SendEmailVerifyCodeType = {
  email: string
  platform?: string // 用户在注册时使用的入口平台（web、app等）
  interface_language?: string // 用户在注册界面端选择的界面语言（zhHans、zhHant、en等）
}

export type EmailLoginType = {
  email: string
  password: string
  method: string // 登录方式，email：邮箱登录，phone：手机号登录
  platform?: string
}

export type MemberType = {
  email: string
  nickname: string
  member_id: string
  avatar: string
}

export type EmailVerifyCodeType = {
  email: string
  verify_code: string
  method: string
  platform?: string
}

export type ResetPassType = {
  email: string
  verify_code: string
  new_pass: string
  method: string
  platform?: string
}

export type WechatLoginType = {
  code: string
  state: string
  method: string
  platform?: string
}

export type GoogleLoginType = {
  code: string
  state: string
  method: string
  platform?: string
}

export type MemberProductType = {
  platform: string
}

export type MemberProductTokenUsageType = {
  platform: string
}

export type ProductOrderType = {
  platform: string
  page: number
  limit: number
}

export type FeedbackType = {
  platform: string
  page: number
  limit: number
}
