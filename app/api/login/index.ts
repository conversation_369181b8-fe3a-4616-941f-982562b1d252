import type {
  EmailLoginType,
  EmailVerifyCodeType,
  GoogleLoginType,
  HeadersTokenType,
  MemberProductTokenUsageType,
  MemberProductType,
  MemberSingupType,
  ProductOrderType,
  ResetPassType,
  SendEmailVerifyCodeType,
  WechatLoginType
} from './types'

import request from '@/config/axios'

export const signupByEmailApi = (data: MemberSingupType): Promise<IResponse> => {
  return request.post({ url: '/member/auth/signup/email', data })
}

export const sendEmailVerifyCodeApi = (data: SendEmailVerifyCodeType): Promise<IResponse> => {
  return request.post({ url: '/member/auth/sendEmailVerifyCode', data })
}

export const verifyEmailApi = (data: EmailVerifyCodeType): Promise<IResponse> => {
  return request.post({ url: '/member/auth/signup/verify/email', data })
}

export const getCurrentMemberInfo = (): Promise<IResponse> => {
  return request.get({ url: '/member/auth/current/member/info' })
}

export const getMemberProductInfo = (headersToken: HeadersTokenType, paramData: MemberProductType): Promise<IResponse> => {
  return request.get({ url: '/product/member/product', headers: headersToken, params: paramData })
}

export const memberProductSubscriptionCancel = (headersToken: HeadersTokenType, platform: string): Promise<IResponse> => {
  return request.put({
    url: '/product/member/product/subscription/cancel',
    headers: headersToken,
    data: { platform: platform }
  })
}

export const getMemberProductTokenUsage = (headersToken: HeadersTokenType, paramData: MemberProductTokenUsageType): Promise<IResponse> => {
  return request.get({ url: '/product/member/token/usage', headers: headersToken, params: paramData })
}

// 获取会员资源包账户
export const getMemberResourceAccount = (headersToken: HeadersTokenType, paramData: MemberProductType): Promise<IResponse> => {
  return request.get({ url: '/product/member/resource/account', headers: headersToken, params: paramData })
}

export const getMemberProductOrderListApi = (headersToken: HeadersTokenType, paramData: ProductOrderType): Promise<IResponse> => {
  return request.get({ url: '/product/member/orders', headers: headersToken, params: paramData })
}

export const resetPassApi = (data: ResetPassType): Promise<IResponse> => {
  return request.post({ url: '/member/auth/resetpass', data })
}

export const emailLoginApi = (data: EmailLoginType): Promise<IResponse> => {
  return request.post({ url: '/member/auth/email_login', data })
}

export const wechatLoginApi = (data: WechatLoginType): Promise<IResponse> => {
  return request.post({ url: '/member/auth/wechat_login', data })
}

export const googleLoginApi = (data: GoogleLoginType): Promise<IResponse> => {
  return request.post({ url: '/member/auth/google_login', data })
}
