import type { ParsePdfPdf, PdfStatus, Pdfdelete } from './types.ts'
import type { HeadersTokenType, ProductOrderType } from '@/api/login/types'
import request from '@/config/axios'

/**
 * 根据PDF文件，解析PDF中的内容
 * @param data url
 * @returns product
 */
export const parsePdfByPdfUrl = (headersToken: HeadersTokenType, data: ParsePdfPdf): Promise<IResponse> => {
  return request.post({ url: '/file/parse/record/pix_file', headers: headersToken, data })
}
/**
 * 查询PDF转换状态
 * @param data url
 * @returns product message, 状态（processing:处理中, completed:已完成, error:错误）
 */
export const pixPdfStatus = (headersToken: HeadersTokenType, paramData: PdfStatus): Promise<IResponse> => {
  return request.get({ url: '/file/parse/record/file_status', headers: headersToken, params: paramData })
}
/**
 * 查询PDF记录解析PDF记录
 * @param data url
 * @returns product message, 状态（processing:处理中, completed:已完成, error:错误）
 */
export const pixPdfSave = (headersToken: HeadersTokenType, paramData: PdfStatus): Promise<IResponse> => {
  return request.get({ url: '/file/parse/record/save', headers: headersToken, params: paramData })
}
/**
 * 分页查询PDF解析记录
 *
 * @param
 * @returns
 */
export const getMemberFileRecordListApi = (headersToken: HeadersTokenType, paramData: ProductOrderType): Promise<IResponse> => {
  return request.get({ url: '/file/member/file/parse/record/page', headers: headersToken, params: paramData })
}
/**
 * 删除PDF解析记录
 *
 * @param
 * @returns
 */
export const deleteMemberFileRecordApi = (headersToken: HeadersTokenType, paramData: Pdfdelete): Promise<IResponse> => {
  return request.delete({ url: '/file/member/record/delete', headers: headersToken, params: paramData })
}
