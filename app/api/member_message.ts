import type { HeadersTokenType } from '@/api/login/types'
import request from '@/config/axios'

/**
 * 获取会员消息列表
 * @param headersToken token令牌
 * @param queryParams 查询参数
 */
export const getMemberMessageList = (headersToken: HeadersTokenType, queryParams: any): Promise<IResponse> => {
  return request.get({ url: '/member/auth/user/member_message/list', headers: headersToken, params: queryParams })
}

/**
 * 更新会员消息阅读状态
 * @param data_id 选择更新的id
 */
export const updateMemberMessageReadStatus = (ids: any): Promise<IResponse> => {
  return request.put({ url: '/member/auth/user/member_message/read', data: { ids } })
}

/**
 * 创建会员问题反馈表
 * @param data 请求参数
 */
export const createMemberMessage = (data: any): Promise<IResponse> => {
  return request.post({ url: '/member/auth/user/member_message/create', data })
}

/**
 * 获取消息详情
 * @param headers token令牌
 * @param id 消息ID
 */
export const getMemberMessageDetail = (headers: HeadersTokenType, id: string | string[]) => {
  return request.get<MemberMessageItem>({
    url: '/member/auth/user/member_message/detail',
    headers,
    params: { id }
  })
}

/**
 * 删除会员消息表
 * @param ids 消息ID列表
 */
export const deleteMemberMessage = (data: any): Promise<IResponse> => {
  return request.delete({ url: '/member/auth/user/member_message/delete', data })
}
