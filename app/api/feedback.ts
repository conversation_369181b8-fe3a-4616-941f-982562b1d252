import type { GetFeedbackType, CreateFeedback, CreateReply } from './types'
import type { HeadersTokenType, FeedbackType } from '@/api/login/types'
import request from '@/config/axios'

// 获取反馈信息
export const getFeedbackListApi = (headersToken: HeadersTokenType, paramData: FeedbackType): Promise<IResponse> => {
  return request.get({ url: '/feedback/feedbacks', headers: headersToken, params: paramData })
}

// 获取反馈回复信息
export const getReplyByFeedbackNoApi = (headersToken: HeadersTokenType, paramData: GetFeedbackType): Promise<IResponse> => {
  return request.get({ url: '/feedback/feedback_replys', headers: headersToken, params: paramData })
}

// 创建反馈
export const createFeedback = (headersToken: HeadersTokenType, data: CreateFeedback): Promise<IResponse> => {
  return request.post({ url: '/feedback/feedback', headers: headersToken, data })
}

// 创建反馈回复
export const createReply = (headersToken: HeadersTokenType, data: CreateReply): Promise<IResponse> => {
  // console.log('data1111', data);
  return request.post({ url: '/feedback/feedback_reply', headers: headersToken, data })
}

// // 上传图片
// export const uploadFeedbackImage = (headersToken: HeadersTokenType, file: File, feedbackId?: number, replyId?: number): Promise<IResponse> => {
//   const formData = new FormData();
//   formData.append('file', file);

//   if (feedbackId) {
//     formData.append('feedback_id', feedbackId.toString());
//   }

//   if (replyId) {
//     formData.append('feedback_reply_id', replyId.toString());
//   }

//   return request.post({
//     url: '/feedback/images',
//     headers: {
//       ...headersToken,
//       'Content-Type': 'multipart/form-data'
//     },
//     data: formData
//   });
// };

// 上传图片
export const uploadFeedbackImage = (headersToken: HeadersTokenType, file: File, feedbackId?: number, replyId?: number, memberId?: string): Promise<IResponse> => {
  const formData = new FormData()
  formData.append('file', file)
  if (feedbackId) {
    formData.append('feedback_id', feedbackId.toString())
  }
  if (replyId) {
    formData.append('feedback_reply_id', replyId.toString())
  }
  if (memberId) {
    formData.append('member_id', memberId)
  }
  return request.post({
    url: '/feedback/images',
    headers: {
      ...headersToken,
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  })
}

// 获取图片列表
export const getFeedbackImages = (
  headersToken: HeadersTokenType,
  params: {
    page?: number
    limit?: number
    feedback_id?: number
    feedback_reply_id?: number
  }
): Promise<IResponse> => {
  return request.get({
    url: '/feedback/member/images',
    headers: headersToken,
    params
  })
}

// 批量更新问题处理状态
export const updateFeedbackStatusBatch = (headersToken: HeadersTokenType, data: { id: number, feedback_status: string | number }): Promise<IResponse> => {
  return request.put({
    url: '/feedback/feedback/batch/update/feedback_status',
    headers: headersToken,
    data
  })
}
