import type { CreatePayOrderType, MemberPayOrderType, FreeTrialType, GetResourcePackType } from './types'
import type { HeadersTokenType } from '@/api/login/types'
import request from '@/config/axios'

/**
 * 根据平台编码、资源包类型，获取资源信息
 *
 * @param data GetResourcePackType
 * @returns product
 */
export const getResourceByResourceApi = (paramData: GetResourcePackType): Promise<IResponse> => {
  return request.get({ url: '/resource/resource/pack', params: paramData })
}

/**
 * 根据产品编号和购买方式，创建初始化微信支付订单
 * @param data CreatePayOrderType
 * @returns payOrder
 */
export const createPayOrderApi = (data: CreatePayOrderType): Promise<IResponse> => {
  return request.post({ url: '/pay/order/create', data })
}

/**
 * 根据产品编号和购买方式，创建支付宝支付订单
 * @param data
 */
export const createAliPayOrderApi = (data: CreatePayOrderType): Promise<IResponse> => {
  return request.post({ url: '/pay/alipay/order/create', data })
}

export const createTradeOrder = (data: CreatePayOrderType): Promise<IResponse> => {
  return request.post({ url: '/pay/trade/order/create', data })
}

/**
 * 根据token和订单编号，获取会员支付订单状态
 * @param data MemberPayOrderType
 * @returns payOrder
 */
export const getMemberPayOrderStatusApi = (headersToken: HeadersTokenType, queryParams: MemberPayOrderType): Promise<IResponse> => {
  return request.get({ url: '/pay/order/status', headers: headersToken, params: queryParams })
}

export const freeTrial = (data: FreeTrialType): Promise<IResponse> => {
  return request.post({ url: '/product/debuts/trial', data })
}

/**
 * 优惠券查看
 * @param coupon_id 优惠券信息
 */
export const couponView = (coupon_id: number): Promise<IResponse> => {
  return request.post({ url: `/pay/coupon/view/${coupon_id}` })
}

/**
 * 优惠券领取
 * @param coupon_id 优惠券ID
 */
export const couponReceive = (coupon_id: number): Promise<IResponse> => {
  return request.post({ url: '/pay/coupon/receive', data: { id: coupon_id } })
}

/**
 * 优惠券码会员列表
 * @param headersToken token令牌
 * @param queryParams 查询参数
 */
export const couponCodeMemberList = (headersToken: HeadersTokenType, queryParams: any): Promise<IResponse> => {
  return request.get({ url: '/pay/coupon/code/member/list', headers: headersToken, params: queryParams })
}

/**
 * 优惠券码兑换
 * @param code_value 优惠券码
 */
export const couponCodeRedeem = (code_value: string): Promise<IResponse> => {
  return request.post({ url: '/pay/coupon/code/exchange', data: { code_value } })
}
