import axios from 'axios'

interface MineruFileParam {
  name: string // 文件名，支持.pdf、.doc、.docx、.ppt、.pptx、.png、.jpg、.jpeg多种格式
  is_ocr?: boolean // 是否启动 ocr 功能，默认 false
  data_id?: string // 解析对象对应的数据 ID。由大小写英文字母、数字、下划线（_）、短划线（-）、英文句号（.）组成，不超过 128 个字符，可以用于唯一标识您的业务数据。
  page_ranges?: string // 指定页码范围，格式为逗号分隔的字符串。例如："2,4-6"：表示选取第2页、第4页至第6页（包含4和6，结果为 [2,4,5,6]）；"2--2"：表示从第2页一直选取到倒数第二页（其中"-2"表示倒数第二页）。
}

export interface MineruFileUrlsBatchParam {
  enable_formula?: boolean // 是否开启公式识别，默认 true
  enable_table?: boolean // 是否开启表格识别，默认 true
  language?: string // 指定文档语言，默认 ch，可以设置为auto，当为auto时模型会自动识别文档语言
  callback?: string // 解析结果回调通知您的 URL，支持使用 HTTP 和 HTTPS 协议的地址
  seed?: string // 随机字符串，该值用于回调通知请求中的签名。由英文字母、数字、下划线（_）组成，不超过 64 个字符。
  extra_formats?: string[] // markdown、json为默认导出格式，无须设置，该参数仅支持docx、html、latex三种格式中的一个或多个
  model_version?: string // mineru模型版本，两个选项:v1、v2，默认v1。
  files: MineruFileParam[]
}

/**
 * 批量获取文件下载链接
 */
export interface MineruFileUrlsBatchResponse {
  code: number // 接口状态码，成功：0
  data: {
    batch_id: string // 批量任务 ID
    file_urls: string[] // 文件上传链接
  }
  msg: string // 接口返回信息
  trace_id: string // 请求 ID
}

export interface MineruExtractTaskResponse {
  code: number // 状态码
  data: {
    task_id: string // 任务 ID
    data_id: string // 解析对象对应的数据 ID。说明：如果在解析请求参数中传入了 data_id，则此处返回对应的 data_id。
    state: string // 任务处理状态，完成:done，pending: 排队中，running: 正在解析，failed：解析失败，converting：格式转换中
    err_msg: string // 文件解析结果压缩包
    layout_url: string // 布局文件
    full_md_link: string // markdown文件
    file_name: string // 文件名
    url: string // 文件下载链接
    type: string // 文件类型
    file_info: {
      pages: number // 总页数
      width: number // 宽度
      height: string // 高度
    }
    model_version: string // 模型版本
    is_chem: boolean // 是否为化学文件
  }
  msg: string // 状态信息
  trace_id: string // 请求 ID
}

/**
 * 批量任务结果
 */
interface BatchExtractResult {
  file_name: string // 文件名
  state: string // 任务处理状态，完成:done，waiting-file: 等待文件上传排队提交解析任务中，pending: 排队中，running: 正在解析，failed：解析失败，converting：格式转换中
  full_zip_url?: string // 文件解析结果压缩包
  err_msg?: string // 解析失败原因，当 state=failed 时，有效
  data_id?: string // 解析对象对应的数据 ID。说明：如果在解析请求参数中传入了 data_id，则此处返回对应的 data_id。
  extract_progress?: {
    extracted_pages?: number // 文档已解析页数，当state=running时有效
    start_time?: string // 文档解析开始时间，当state=running时有效
    total_pages?: number // 文档总页数，当state=running时有效
  }
}

/**
 * 批量获取任务结果
 */
export interface MineruExtractTaskBatchResponse {
  code: number // 状态码
  data: {
    batch_id: string // 批量任务 ID
    extract_result: BatchExtractResult[] // 批量任务结果
  }
  msg: string // 状态信息
  trace_id: string // 请求 ID
}

/**
 * 批量申请文件上传链接
 * @param data 申请参数
 * @param token 访问令牌
 */
export const fileUrlsBatch = async (data: MineruFileUrlsBatchParam, token: string): Promise<MineruFileUrlsBatchResponse> => {
  try {
    const response = await axios.post('/api/mineru/file-urls-batch', {
      data,
      token
    })
    return response.data
  }
  catch (error) {
    console.error('请求失败:', error)
    throw new Error('请求文件上传链接失败')
  }
}

/**
 * 上传文件
 * @param file  文件
 * @param uploadUrl 上传链接
 */
export const uploadFile = async (file: File, uploadUrl: string): Promise<any> => {
  try {
    // 改为服务端上传，避免CORS问题
    const formData = new FormData()
    formData.append('file', file)
    formData.append('uploadUrl', uploadUrl)

    const response = await axios.post('/api/mineru/upload', formData)
    return response.data
  }
  catch (error) {
    console.error('文件上传失败:', error)
    throw new Error('文件上传失败')
  }
}

/**
 * 获取任务结果
 * @param taskId 任务ID
 * @param token 访问令牌
 */
export const extractTask = async (taskId: string, token: string): Promise<MineruExtractTaskResponse> => {
  try {
    const response = await axios.post('/api/mineru/extract-task', {
      taskId,
      token
    })
    return response.data
  }
  catch (error) {
    console.error('请求失败:', error)
    throw new Error('获取任务结果失败')
  }
}

/**
 * 获取任务结果
 * @param batchId 批次ID
 * @param token 访问令牌
 */
export const extractTaskBatch = async (batchId: string, token: string): Promise<MineruExtractTaskBatchResponse> => {
  try {
    const response = await axios.post('/api/mineru/extract-task-batch', {
      batchId,
      token
    })
    return response.data
  }
  catch (error) {
    console.error('请求失败:', error)
    throw new Error('获取任务结果失败')
  }
}

export const downloadFileContent = async (url: string): Promise<{ docContent: string | null, docLayout: string | null, sourceContent: string | null }> => {
  try {
    const response = await axios.post('/api/mineru/download', { url })
    return response.data
  }
  catch (error) {
    console.error('文件下载失败:', error)
    throw new Error('文件下载失败')
  }
}
