<template>
  <div class="flex min-h-screen items-center justify-center">
    <div class="text-center">
      <UIcon name="i-heroicons-arrow-path" class="text-primary-500 mx-auto h-10 w-10 animate-spin" />
      <p class="mt-4 text-lg font-medium">
        Loading...
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStoreWithOut } from '@/store/modules/auth'

// definePageMeta 定义页面的布局(此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'member'
})

const router = useRouter()
const route = useRoute()
const { locale } = useI18n()

useSeoMeta({
  title: 'Loading' // 加载中
})

/*
对于像 account 和 billing 等需要会员登录才能访问的页面，
通过 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
已登录的中转缓冲后跳回原页面。（解决在原页面刷新时，路由跳转至登录页的问题）
*/

// 页面挂载时加载,判断用户Token是否存在，存在则跳转到对应的页面
onMounted(() => {
  const toPage = route.query.fromPage as string // 来源页面
  const authStore = useAuthStoreWithOut()
  // console.log('Loading页面 - authStore.getToken:', authStore.getToken);
  // console.log('Loading页面 - toPage:', toPage);
  // console.log('Loading页面 - route.query:', route.query);
  // console.log('Loading页面 - locale.value:', locale.value);

  if (authStore.getToken && authStore.getToken !== '') {
    if (toPage === 'account') {
      router.push({ path: '/' + locale.value + '/account', replace: true }) // 跳转到账户页面
    }
    else if (toPage === 'billing') {
      router.push({ path: '/' + locale.value + '/billing', replace: true }) // 跳转到账单页面
    }
    else if (toPage === 'pdf-plus-record') {
      router.push({ path: '/' + locale.value + '/pdf-plus-record', replace: true }) // 跳转到PDF记录页面
    }
    else if (toPage === 'member_message') {
      router.push({ path: '/' + locale.value + '/member_message', replace: true }) // 跳转到消息页面
    }
    else if (toPage === 'feedback') {
      router.push({ path: '/' + locale.value + '/feedback', replace: true }) // 跳转到问题反馈表单页面
    }
    else if (toPage === 'feedback_list') {
      router.push({ path: '/' + locale.value + '/feedback_list', replace: true }) // 跳转到问题反馈列表页面
    }
    else if (toPage === 'reply_list') {
      // 如果是reply_list页面，需要保留问题编号参数
      const feedbackNo = route.query.feedback_no as string
      const feedbackId = route.query.feedback_id as string

      // console.log('跳转到reply_list - problemNo:', problemNo, 'problemId:', problemId);
      console.log('跳转到reply_list - feedbackNo:', feedbackNo, 'feedbackId:', feedbackId)

      if (feedbackNo) {
        const query = { feedback_no: feedbackNo }
        if (feedbackId) query['feedback_id'] = feedbackId

        router.push({
          path: '/' + locale.value + '/reply_list',
          query: query,
          replace: true
        }) // 跳转到回复列表页面并保留参数
      }
      else {
        router.push({ path: '/' + locale.value + '/feedback_list', replace: true }) // 如果没有问题编号参数，跳转到反馈列表
      }
    }
    else {
      router.push({ path: '/' + locale.value, replace: true }) // 默认跳转到首页
    }
  }
  else {
    // 如果没有会员信息
    authStore.reset() // 清除存储数据
    router.push({ path: '/' + locale.value + '/login', replace: true })
  }
})
</script>
