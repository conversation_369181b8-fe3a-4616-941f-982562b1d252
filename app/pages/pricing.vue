<template>
  <div>
    <UContainer>
      <!-- 产品价格套餐 -->
      <PricingPlan :skip-to-uri="'/' + locale + '/account'" :product="product" />
    </UContainer>

    <!-- 使用兑换码 -->
    <div class="flex justify-center py-4 mt-5">
      <NuxtLink
        :to="'/' + locale + '/coupon_code_list'"
        class="inline-flex items-center gap-1 text-primary dark:hover:text-primary-400 transition-colors"
      >
        <UIcon name="i-material-symbols-redeem" class="h-7 w-7 align-bottom text-amber-500 lg:h-7 lg:w-7" />
        <span class="text-lg">{{ t('coupon.code.use_title') }}</span>
      </NuxtLink>
    </div>

    <!-- 为什么要使用精挑翻译 -->
    <UPageSection
      :title="t('selected_tsl_desc.title')"
      :ui="{
        container: 'py-16 sm:py-16 md:py-24 lg:py-32 xl:py-32 mt-10 gap-4 sm:gap-y-4 px-2 sm:px-2 md:px-4 lg:px-4 xl:px-4 flex flex-col',
        title: 'text-2xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #description>
        <div>
          <span class="text-sm tracking-tight lg:text-lg">{{ t('selected_tsl_desc.description_1') }}</span>
          <br />
          <span class="text-sm tracking-tight lg:text-lg">{{ t('selected_tsl_desc.description_2') }}</span>
          <br />
          <span class="text-sm tracking-tight lg:text-lg">{{ t('selected_tsl_desc.description_3') }}</span>
          <br />
          <br />
          <span class="text-lg leading-8 font-bold tracking-tight text-neutral-700 lg:text-xl dark:text-neutral-200">{{ t('selected_tsl_desc.description_4') }}</span>
        </div>
      </template>
      <CustomCarousel
        v-slot="{ item }"
        dots
        :arrows="!isMobileDevice()"
        loop
        :items="selectedTransDemoItems"
        class="mx-auto w-full max-w-[1230px] rounded-lg"
        :ui="{
          item: 'basis-full',
          container: 'px-5',
          dots: {
            wrapper: 'relative bottom-0 mt-2'
          }
        }"
        :prev="{
          color: 'primary',
          icon: 'i-heroicons-arrow-left-20-solid',
          class: '-start-4 sm:-start-4 md:-start-4 lg:-start-4 xl:-start-4'
        }"
        :next="{
          color: 'primary',
          icon: 'i-heroicons-arrow-right-20-solid',
          class: '-end-4 sm:-end-4 md:-end-4 lg:-end-4 xl:-end-4'
        }"
      >
        <!-- 统一卡片式布局 -->
        <div class="my-2 w-full space-y-4 text-sm tracking-tight lg:text-base">
          <!-- 原文 & 最佳译文卡片 -->
          <UCard class="bg-gray-100 shadow-none ring-0 inset-shadow-none dark:bg-gray-800">
            <template #header>
              <span class="inline-block leading-8 font-semibold text-zinc-500 dark:text-zinc-400">{{ t('selected_tsl_desc.examples.original_text') }}:&nbsp;{{ item.original_text }}</span>
              <br />
              <span class="inline-block leading-8 font-semibold text-zinc-500 dark:text-zinc-400">{{ t('selected_tsl_desc.examples.best_translation') }}:&nbsp;{{ item.selected_result }}</span>
              <br />
              <span class="inline-block leading-8 font-semibold text-zinc-500 dark:text-zinc-400">
                {{ t('selected_tsl_desc.examples.selected_model') }}:
                <span class="mx-1 inline-block align-middle">
                  <span class="flex items-center justify-center rounded-xs dark:bg-gray-100" :class="trans_engine_img_bg_class">
                    <img :src="translateModeIcon[item.selected_model]" alt="translateModeIcon" :class="trans_engine_img_class" />
                  </span>
                </span>
                <span class="text-l inline-block leading-6 font-semibold">{{ item.selected_model_version }}</span>
              </span>
            </template>
          </UCard>

          <!-- 各模型翻译对比卡片列表 -->
          <UCard v-for="row in item.items" :key="row.engine + row.engine_version">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <span class="flex items-center justify-center rounded-xs dark:bg-gray-100" :class="trans_engine_img_bg_class">
                  <img :src="translateModeIcon[row.engine]" :alt="row.engine" :class="trans_engine_img_class" />
                </span>
                <span :class="trans_engine_text_class">{{ row.engine_version || row.engine }}</span>
              </div>
              <UBadge
                color="neutral"
                variant="soft"
                size="xl"
                :class="row.score.class"
              >
                <span class="text-xs font-semibold tracking-tight text-zinc-500 lg:text-sm dark:text-zinc-400">{{ row.score.value }}</span>
              </UBadge>
            </div>
            <UBadge class="mt-3" :class="row.translation.class">
              <span class="text-xs tracking-tight text-zinc-500 lg:text-sm dark:text-zinc-400">{{ row.translation.value }}</span>
            </UBadge>
            <p class="mt-1 text-xs tracking-tight text-zinc-500 lg:text-xs dark:text-zinc-400">
              {{ row.analysis }}
            </p>
          </UCard>
        </div>
      </CustomCarousel>
      <!-- 样例数据说明 -->
      <span class="mt-6 px-2 text-sm tracking-tight text-zinc-500 sm:text-sm md:text-sm lg:px-5 lg:text-base xl:text-base">
        <!-- 说明：以上样例采用同价位层级的不同模型进行翻译，并对翻译结果进行评分得到的数据。 -->
        <span>{{ t('selected_tsl_desc.examples.note_01') }}</span>
        <!-- AI 模型翻译及评分存在一定的随机性，重现测试时结果可能略有差异，但总体相近。 -->
        <span>{{ t('selected_tsl_desc.examples.note_02') }}</span>
        <!-- 以上数据仅供参考。强烈建议您直接体验测试。 -->
        <span>{{ t('selected_tsl_desc.examples.note_03') }}</span>
        <!-- AI 择优翻译 -->
        <NuxtLink :to="'/' + locale + '/text'" target="_blank" class="font-medium text-(--ui-primary)">{{ t('header.selected_translation') }}</NuxtLink>
      </span>
    </UPageSection>

    <!-- FAQ 常见问题 -->
    <FAQ />
  </div>
</template>

<script setup lang="ts">
/* @ts-nocheck */
import { resolveComponent } from 'vue'
import PricingPlan from './components/PricingPlan.vue'
import FAQ from './components/FAQ.vue'
import CustomCarousel from '@/components/CustomCarousel.vue'
import { getProductByPositionNoApi } from '@/api/product'
import type { GetProductType } from '@/api/types'

import { translateModeIcon } from '@/utils/translateModeIcon'

const { t, locale } = useI18n()

const UBadge = resolveComponent('UBadge')

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('header.pricing') + ' - ' + t('common.site_name'),
  ogTitle: t('header.pricing') + ' - ' + t('common.site_name'),
  description: t('common.description'),
  ogDescription: t('common.description')
})

// defineOgImageComponent('Saas');

// const isYearly = ref(false)

/** 平台在售产品信息 */
const product = ref({})

onMounted(async () => {
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const paramData: GetProductType = {
    platform: 'ztsl',
    position_no: 'ztsl-plus'
  }
  product.value = (await getProductByPositionNoApi(paramData)).data
})

/**
 * 精挑翻译样例数据.
 */
const selectedTransDemoItems = [
  {
    original_language: '英语',
    target_language: '简体中文',
    original_text: 'We can not be too careful in doing such experiments.',
    selected_model: 'openai',
    selected_model_version: 'GPT-4.1-mini',
    selected_result: '在进行此类实验时，我们再怎么小心也不为过。', // "can not……too…… "句式表示"无论怎样也不过分"、"越……越好"
    items: [
      {
        engine: 'deepseek',
        engine_version: 'DeepSeek-V3-0324',
        translation: { value: '在进行此类实验时，我们再怎么小心也不为过。', class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis: '翻译为"再怎么小心也不为过"，准确传达了原文"cannot be too careful"的强调谨慎的含义，语言流畅，符合中文表达习惯。'
      },
      {
        engine: 'qwen',
        engine_version: 'Qwen-Plus',
        translation: { value: '做这样的实验我们再怎么小心都不为过。', class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis: '"再怎么小心也不为过"准确传达了原文强调谨慎的含义，语言自然流畅。'
      },
      {
        engine: 'gemini',
        engine_version: 'Gemini-2.5-Flash',
        translation: { value: '做这样的实验，我们再怎么小心也不为过。', class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis: '"再怎么小心也不为过"准确传达了原文强调谨慎的含义，语言简洁自然，符合中文习惯。'
      },
      {
        engine: 'doubao',
        engine_version: 'Doubao-1.5-pro-32k',
        translation: { value: '我们做这样的实验越小心越好。', class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        score: { value: 90, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis: '"越小心越好"表达了原文的核心意思，但语气较口语化，缺少"再小心也不为过"的强调语气，稍显简单。'
      },
      {
        engine: 'transmart',
        engine_version: '',
        translation: { value: '做这样的实验，我们越仔细越好.', class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        score: { value: 90, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis: '"越仔细越好"表达了原文的核心意思，语言简洁，但"仔细"较"谨慎"语气弱，稍有差距。'
      },
      {
        engine: 'claude',
        engine_version: 'Claude-3.5-Haiku',
        translation: { value: '在进行这样的实验时，我们不能不谨慎。', class: 'bg-gray-300/50 dark:bg-gray-400/50' },
        score: { value: 85, class: 'bg-gray-300/50 dark:bg-gray-400/50' },
        analysis: '"不能不谨慎"表达了必须谨慎的意思，接近原文含义，但语气不如"再小心也不为过"强烈，稍显委婉。'
      },
      {
        engine: 'openai',
        engine_version: 'GPT-4.1-mini',
        translation: { value: '我们在做这样的实验时不能过于小心。', class: 'bg-orange-300/50 dark:bg-orange-400/50' },
        score: { value: 70, class: 'bg-orange-300/50 dark:bg-orange-400/50' },
        analysis: '"不能过于小心"表达上接近原文，但"不能过于"有否定过度谨慎的意味，略有偏差，未完全体现"越小心越好"的含义。'
      },
      {
        engine: 'deepl',
        engine_version: '',
        translation: { value: '在进行此类实验时，我们不能过于谨慎。', class: 'bg-red-200/50 dark:bg-red-500/50' },
        score: { value: 60, class: 'bg-red-200/50 dark:bg-red-500/50' },
        analysis: '"不能过于谨慎"表达了否定过度谨慎，含义与原文相反，未体现"越小心越好"的意思。'
      },
      {
        engine: 'google',
        engine_version: '',
        translation: { value: '在做这样的实验时我们不能太小心。', class: 'bg-red-200/50 dark:bg-red-500/50' },
        score: { value: 40, class: 'bg-red-200/50 dark:bg-red-500/50' },
        analysis: '同样表达"不能太小心"，语序不同但含义错误，未体现原文强调谨慎的重要性。'
      },
      {
        engine: 'zhipu',
        engine_version: 'GLM-4-Plus',
        translation: { value: '我们在进行这样的实验时不能太小心。', class: 'bg-red-200/50 dark:bg-red-500/50' },
        score: { value: 40, class: 'bg-red-200/50 dark:bg-red-500/50' },
        analysis: '"不能太小心"，含义与原文相反，虽"进行"用词较好，但整体错误。'
      },
      {
        engine: 'microsoft',
        engine_version: '',
        translation: { value: '我们在做这样的实验时不能太小心。', class: 'bg-red-200/50 dark:bg-red-500/50' },
        score: { value: 40, class: 'bg-red-200/50 dark:bg-red-500/50' },
        analysis: '翻译为"不能太小心"，与原文"cannot be too careful"含义相反，原文强调越小心越好，翻译表达了不能小心，意思错误。'
      }
    ]
  },
  {
    original_language: '英语',
    target_language: '简体中文',
    original_text: 'The horse raced past the barn fell.',
    selected_model: 'openai',
    selected_model_version: 'GPT-4.1-mini',
    selected_result: '那匹跑过谷仓的马摔倒了。',
    items: [
      {
        engine: 'doubao',
        engine_version: 'Doubao-1.5-pro-32k',
        translation: { value: '那匹跑过谷仓的马摔倒了。', class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis: '翻译准确且完整，表达了"那匹跑过谷仓的马摔倒了"，符合原句含义。'
      },
      {
        engine: 'gemini',
        engine_version: 'Gemini-2.5-Flash',
        translation: { value: '那匹跑过谷仓的马摔倒了。', class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis: '翻译准确，语义完整。'
      },
      {
        engine: 'openai',
        engine_version: 'GPT-4.1-mini',
        translation: { value: '跑过谷仓的马摔倒了。', class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis: '翻译简洁明了，准确表达了"跑过谷仓的马摔倒了"，符合原句的语法和语义。'
      },
      {
        engine: 'zhipu',
        engine_version: 'GLM-4-Plus',
        translation: { value: '那匹马跑过谷仓后摔倒了。', class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        score: { value: 90, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis: '翻译准确传达了"那匹马跑过谷仓后摔倒了"的意思，语义连贯，符合原句含义。'
      },
      {
        engine: 'deepl',
        engine_version: '',
        translation: { value: '马儿飞快地跑过谷仓，摔倒了。', class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        score: { value: 90, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis: '翻译表达了马儿飞快跑过谷仓并摔倒，语义准确，符合原句含义。'
      },
      {
        engine: 'google',
        engine_version: '',
        translation: { value: '马跑过谷仓时摔倒了。', class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        score: { value: 85, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis: '翻译准确表达了"马跑过谷仓时摔倒了"的意思，符合原句的歧义结构，语义清晰。'
      },
      {
        engine: 'deepseek',
        engine_version: 'DeepSeek-V3-0324',
        translation: { value: '马儿疾驰过谷仓，倒下了。', class: 'bg-gray-300/50 dark:bg-gray-400/50' },
        score: { value: 80, class: 'bg-gray-300/50 dark:bg-gray-400/50' },
        analysis: '翻译表达了马儿疾驰过谷仓并倒下，语义基本准确，但"倒下了"未明确指马，略有歧义。'
      },
      {
        engine: 'claude',
        engine_version: 'Claude-3.5-Haiku',
        translation: { value: '那匹马在赛过谷仓后倒下了。', class: 'bg-orange-300/50 dark:bg-orange-400/50' },
        score: { value: 70, class: 'bg-orange-300/50 dark:bg-orange-400/50' },
        analysis: '翻译表达了马在赛过谷仓后倒下，但"赛过"用词不当，且句子不够通顺，影响理解。'
      },
      {
        engine: 'qwen',
        engine_version: 'Qwen-Plus',
        translation: { value: '马匹从 barn 边飞驰而过摔倒了。', class: 'bg-red-300/50 dark:bg-red-500/50' },
        score: { value: 65, class: 'bg-red-300/50 dark:bg-red-500/50' },
        analysis: '翻译中"barn"未翻译成"谷仓"，且句子结构不够通顺，影响理解。'
      },
      {
        engine: 'transmart',
        engine_version: '',
        translation: { value: '那匹马跑过谷仓.', class: 'bg-red-300/50 dark:bg-red-500/50' },
        score: { value: 40, class: 'bg-red-300/50 dark:bg-red-500/50' },
        analysis: '翻译只表达了"那匹马跑过谷仓"，遗漏了"摔倒了"的信息，信息不完整。'
      },
      {
        engine: 'microsoft',
        engine_version: '',
        translation: { value: '马匹飞驰而过，谷仓倒下了。', class: 'bg-red-300/50 dark:bg-red-500/50' },
        score: { value: 20, class: 'bg-red-300/50 dark:bg-red-500/50' },
        analysis: '翻译将原句理解为"谷仓倒下了"，而原句中"fell"是指"马"倒下，误解了句子结构，导致意思错误。'
      }
    ]
  },
  {
    original_language: '简体中文',
    target_language: '英语',
    original_text: '这份情谊如茶，初尝清香，久品醇厚，愿我们细水长流。',
    selected_model: 'gemini',
    selected_model_version: 'Gemini-2.5-Flash',
    selected_result: 'This friendship is like tea, fragrant at first sip and mellow upon longer savoring. May our bond flow gently and enduringly like a steady stream. ',
    items: [
      {
        engine: 'deepseek',
        engine_version: 'DeepSeek-V3-0324',
        translation: {
          value: 'This friendship is like tea, fragrant at first sip and mellow upon longer savoring. May our bond flow gently and enduringly like a steady stream.',
          class: 'bg-sky-500/50 dark:bg-sky-500/50'
        },
        score: { value: 98, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis:
          '这是一个非常优秀的翻译。准确捕捉了原文的意境和比喻。使用了"fragrant at first sip and mellow upon longer savoring"来描绘品茶的过程，非常形象。"May our bond flow gently and enduringly like a steady stream"完美地翻译了"细水长流"。'
      },
      {
        engine: 'zhipu',
        engine_version: 'GLM-4-Plus',
        translation: {
          value: 'This friendship is like tea, initially tasting refreshing, becoming rich and mellow over time, may our bond last like a gentle stream.',
          class: 'bg-sky-500/50 dark:bg-sky-500/50'
        },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis:
          '翻译准确且生动地传达了原文的含义。使用了更贴切的词语如"initially tasting refreshing"和"becoming rich and mellow over time"。"may our bond last like a gentle stream"完美地捕捉了"细水长流"的比喻意义。'
      },
      {
        engine: 'gemini',
        engine_version: 'Gemini-2.5-flash',
        translation: {
          value: 'This friendship is like tea, initially fragrant when tasted, rich and mellow when savored over time. May our relationship flow like a gentle stream.',
          class: 'bg-sky-500/50 dark:bg-sky-500/50'
        },
        score: { value: 95, class: 'bg-sky-500/50 dark:bg-sky-500/50' },
        analysis:
          '翻译准确且流畅。使用了"initially fragrant when tasted, rich and mellow when savored over time"来描述茶的特点，表达自然。"May our relationship flow like a gentle stream"也很好地传达了"细水长流"的比喻意义。'
      },
      {
        engine: 'claude',
        engine_version: 'Claude-3.5-haiku',
        translation: {
          value: 'This friendship is like tea, initially experiencing its delicate fragrance, deeply savoring its richness over time, may we flow gently like a long, steady stream.',
          class: 'bg-lime-500/50 dark:bg-lime-500/50'
        },
        score: { value: 93, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis:
          '翻译准确且表达生动。"initially experiencing its delicate fragrance, deeply savoring its richness over time"描绘了品茶的细腻感受。"may we flow gently like a long, steady stream"很好地传达了"细水长流"的比喻意义。'
      },
      {
        engine: 'qwen',
        engine_version: 'Qwen-Plus',
        translation: {
          value: 'This friendship is like tea, with a fresh fragrance when first tasted and a rich flavor when savored over time. May our bond last long and flow gently like a stream.',
          class: 'bg-lime-500/50 dark:bg-lime-500/50'
        },
        score: { value: 92, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis:
          '翻译准确。使用了"with a fresh fragrance when first tasted and a rich flavor when savored over time"来描述茶的特点，表达自然。"May our bond last long and flow gently like a stream"很好地传达了"细水长流"的比喻意义。'
      },
      {
        engine: 'doubao',
        engine_version: 'Doubao-1.5-pro-32k',
        translation: {
          value: 'This friendship is like tea. At first taste, it is fragrant; after long savoring, it is mellow. May our friendship last as gently flowing water.',
          class: 'bg-lime-500/50 dark:bg-lime-500/50'
        },
        score: { value: 90, class: 'bg-lime-500/50 dark:bg-lime-500/50' },
        analysis:
          '翻译准确且清晰。将原文拆分成更短的句子，使表达更易理解。"At first taste, it is fragrant; after long savoring, it is mellow"表达清晰。"May our friendship last as gently flowing water"传达了"细水长流"的比喻，但不如"stream"更常用。'
      },
      {
        engine: 'google',
        engine_version: '',
        translation: { value: 'This friendship is like tea, which is fragrant at first and becomes mellow over time. May our friendship last forever.', class: 'bg-gray-300/50 dark:bg-gray-400/50' },
        score: { value: 85, class: 'bg-gray-300/50 dark:bg-gray-400/50' },
        analysis:
          '翻译准确传达了原文的意思，并且表达相对流畅。"fragrant at first and becomes mellow over time"表达自然。"May our friendship last forever"传达了"细水长流"的含义，但不如直接比喻更贴切。'
      },
      {
        engine: 'openai',
        engine_version: 'GPT-4.1-mini',
        translation: {
          value: 'This friendship is like tea, initially tasting fresh and fragrant, and savoring it long brings out its mellow richness. May our bond last forever.',
          class: 'bg-gray-300/50 dark:bg-gray-400/50'
        },
        score: { value: 80, class: 'bg-gray-300/50 dark:bg-gray-400/50' },
        analysis: '翻译基本准确，但后半句"May our bond last forever"未能完全捕捉"细水长流"的比喻意义，只表达了"长久"的意思。'
      },
      {
        engine: 'microsoft',
        engine_version: '',
        translation: { value: 'This friendship is like tea, the first taste of fragrance, long-term mellow, may we be fine water for a long time.', class: 'bg-orange-300/50 dark:bg-orange-400/50' },
        score: { value: 60, class: 'bg-orange-300/50 dark:bg-orange-400/50' },
        analysis:
          '翻译基本传达了原文的意思，但表达不够自然流畅。"first taste of fragrance"和"long-term mellow"的表达方式在英文中不够地道。"fine water for a long time"未能准确传达"细水长流"的比喻意义。'
      },
      {
        engine: 'deepl',
        engine_version: '',
        translation: { value: 'This friendship is like tea, the first taste of the fragrance, long taste mellow, may we be long flowing.', class: 'bg-red-300/50 dark:bg-red-500/50' },
        score: { value: 55, class: 'bg-red-300/50 dark:bg-red-500/50' },
        analysis: '翻译较为直白，部分表达不够自然。"the first taste of the fragrance"和"long taste mellow"表达生硬。"may we be long flowing"未能准确传达"细水长流"的比喻，且语法不够完整。'
      },
      {
        engine: 'transmart',
        engine_version: '',
        translation: { value: 'This friendship is like tea, first taste fragrance, long mellow, may we flow.', class: 'bg-red-300/50 dark:bg-red-500/50' },
        score: { value: 50, class: 'bg-red-300/50 dark:bg-red-500/50' },
        analysis: '翻译过于简化，部分表达不完整且不够自然。"first taste fragrance, long mellow"语法不完整。"may we flow"未能准确传达"细水长流"的比喻意义。'
      }
    ]
  }
]

// CSS class param
const trans_engine_class = 'inline-flex w-32 lg:w-40 align-bottom'
const trans_engine_img_bg_class = 'size-5 sm:size-5 md:size-6 lg:size-7 xl:size-7 flex-shrink-0'
const trans_engine_img_class = 'size-5 sm:size-5 md:size-5 lg:size-6 xl:size-6 flex-shrink-0'
const trans_engine_text_class = 'ml-1 text-zinc-500 text-sm lg:text-base leading-5 lg:leading-7'
</script>

<style scoped></style>
