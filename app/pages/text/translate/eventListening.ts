import { sharedState } from '@/pages/text/useData/sharedState'

const { highlightId } = sharedState

/**
 * 事件监听
 * 这些方法会被挂载到 text/SelectLanguage.vue, 入口文件中
 * 为了解决恢复历史记录的时候也可以实现点击输入框原文的时候也能高亮、切换择优结果等类点击事件
 */

export const eventListening = () => {
  // 保存点击事件处理函数的引用，以便移除
  let clickEventHandler: ((event: MouseEvent) => void) | null = null

  /**
   * 处理文本高亮功能
   * 为段落添加点击事件，点击时移除其他高亮并添加当前段落高亮
   * 使用事件委托方式处理，以支持动态添加的元素
   */
  const handleSentenceClickHighlight = () => {
    // 尝试获取输入框
    const textareaDom = document.querySelector('.editable-textarea')
    // 检查DOM元素是否存在
    if (!textareaDom) {
      return
    }

    // 如果已有事件处理函数，先移除它，防止重复添加
    if (clickEventHandler) {
      textareaDom.removeEventListener('click', clickEventHandler)
    }

    // 创建新的事件处理函数
    clickEventHandler = (event: MouseEvent) => {
      // 检查点击的元素或其父元素是否有selected-translate-paragraph属性
      const targetElement = (event.target as HTMLElement).closest('span.container-source')

      if (targetElement) {
        // 移除所有现有高亮
        const allHighlightedNodes = textareaDom.querySelectorAll('.sentence-selected-highlight')

        for (const highlightedNode of allHighlightedNodes) {
          highlightedNode.classList.remove('sentence-selected-highlight')
        }

        // 更新高亮id
        highlightId.value = targetElement.getAttribute('selected-translate-id')
        // 添加高亮类
        targetElement.classList.add('sentence-selected-highlight')
      }
    }

    // 使用事件委托，在父元素上监听点击事件
    textareaDom.addEventListener('click', clickEventHandler)
  }

  // 清理函数，用于组件卸载时移除事件监听器
  const cleanup = () => {
    const textareaDom = document.querySelector('.editable-textarea')
    if (textareaDom && clickEventHandler) {
      textareaDom.removeEventListener('click', clickEventHandler)
      clickEventHandler = null
    }
  }

  return {
    handleSentenceClickHighlight,
    cleanup
  }
}
