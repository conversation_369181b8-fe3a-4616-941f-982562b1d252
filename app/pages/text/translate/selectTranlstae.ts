/**
 * TODO 择优翻译-择优翻译请求
 */

/** 翻译数据管理 */
import { useDomControl } from '@/pages/text/translate/domControl'

import { translationState } from '@/pages/text/useData/useTranslationState'
import { useInputTranslate } from '@/store/inputTranslate'
import { storeToRefs } from 'pinia'
import { useTranslationHistoryState } from '@/pages/text/useData/useHistoryState.ts'
// 获取到输入译本地存储
import { sharedState } from '@/pages/text/useData/sharedState'
import { useAuthStoreWithOut } from '@/store/modules/auth'
// 导入模型权重获取函数
import { getModelWeight } from '@/pages/text/js/modelWeights'
import { TranslationStatus } from '@/types/translateOptions'

// 获取插件翻译请求
import { useGetPluginTranslate } from '@/pages/text/js/sendExtension'

const { clearAiSelectTranslateResults, translationEngineOptions, updateTranslationEngineStatus } = translationState

/**
 * 注意事项
 * 1. 以下遍历要使用 for 方法遍历， 不要使用forEach 遍历，因为性能没有 for 好
 * 2. !如果在这里使用 pinia 存储：Pinia可能还没有初始化导致出错, 改成getStore里面使用
 * 3. 不要在这里直接使用Pinia存储, 因为需要初始化之后才能调用 pinia， 如果在这是使用变成初始化前调用了, 改为在函数内部使用
 */

/**
 * 择优翻译
 */
export const aiSelectTranslate = () => {
  /**
   * 获取存储中的状态
   * 这样只有在函数被调用时才会尝试访问Pinia
   * 在函数内部获取Pinia存储
   * 并且每次调用都是最新值
   * @returns {Object} 包含所有需要的状态和存储实例
   */
  const getStore = () => {
    const useInputTransStore = useInputTranslate()
    const authStore = useAuthStoreWithOut()
    return {
      useInputTransStore,
      authStore,
      ...storeToRefs(useInputTransStore)
    }
  }

  // 获取插件择优翻译请求
  const { useAiSelectTranslate } = useGetPluginTranslate

  // 获取页面语言
  const getLocale = () => {
    const { authStore } = getStore()
    return authStore.interfaceLanguage
  }

  // 使用历史记录状态
  const { updateAiSelectTranslationHistoryData, updateAiSelectSetting, clearHistoryAiSelectData } = useTranslationHistoryState()

  // 获取共享状态数据
  const { isTranslateStatus, originalTextList, aiSelectTransList, messageEvent, customResult, aiSelectFinallyResultText, historyKey, reSelect, currentTranslateStatus, selectedModelsStatus }
    = sharedState

  // 获取dom控制函数
  const { clearAiSelectErrorDom, handleAiSelectError, removeAllEventListeners, originalLoading, handleSelectedResult, handleHighlightFirstParagraph, handleBreaks } = useDomControl

  const invalidTexts = ['\n', '<n0></n0>']

  /**
   * 择优翻译请求方法
   */
  const aiSelectTranslateReq = async () => {
    if (originalTextList.value.combinedText.length === 0) return

    // 获取 Pinia 存储
    const { enabledEnginesList, aiSelectModel, isAiSelectAnalysis } = getStore()

    // 清除现有状态（择优翻译）
    await cleanupExistingState()

    // 等待进行中翻译完成
    await waitForTranslationComplete()

    // 重置选中状态
    selectedModelsStatus.value = {}

    // 设置择优翻译 loading 状态（开启）
    sharedState.isAiSelectLoading.value = true

    /**
     * 用户如果已经精挑翻译完成了一次， 这时候开启单句翻译是没有高亮任何一个句子的所以需要手动判断一下有没有高亮句子
     * 如果没有高亮句子就默认高亮第一句子，否则右侧模型翻译结果区域就会变空白
     */
    if (currentTranslateStatus.value.isSingleSentence) {
      handleHighlightFirstParagraph()
    }

    // 移除事件监听
    window.removeEventListener('message', messageEvent.value.aiSelectTranslateMessage)

    // 更新历史记录中的择优状态
    updateAiSelectSetting(historyKey.value, {
      isSingleSentenceAiSelect: currentTranslateStatus.value.isSingleSentence,
      aiSelectModel: aiSelectModel.value,
      isAiSelectAnalysis: currentTranslateStatus.value.isAiSelectAnalysis
    })

    /**
     * 构建一个原文（文本+id）、翻译结果（文本+id+engine）
     * 基于 originalTextList + translationEngineOptions 构建一个对象数组
     * 过滤掉翻译失败模型
     * 用于精挑翻译请求
     */
    const buildRequestData = prepareTranslationResults()

    // console.log('调试日志-构建择优翻译请求数据', buildRequestData);

    if (buildRequestData.length === 0) return

    // 过滤掉换行符的原文数量， 用户判断是否全部翻译完成， 当originalArrayLength等于 0 说明全部翻译完成关闭 loading
    const originalArrayLength = buildRequestData.filter((item) => {
      return item.originalText !== '<n0></n0>'
    }).length

    // 设置每一句原文的翻译引擎状态（择优翻译状态）
    setEnginesLoadingState(buildRequestData)

    // 设置当前选中的择优翻译模型
    currentTranslateStatus.value.currentSelectedAiSelectModel = aiSelectModel.value.value

    // 设置当前是否开启分析
    currentTranslateStatus.value.isAiSelectAnalysis = isAiSelectAnalysis.value

    /** 发送翻译请求 */
    sendSingleSentenceRequests(buildRequestData)

    /**
     * 设置结果处理函数
     * 通过 window.addEventListener 接收到插件返回的数据
     */
    setupResultHandler(enabledEnginesList, originalArrayLength)
  }

  /**
   * 等待翻译完成
   */
  const waitForTranslationComplete = async () => {
    if (isTranslateStatus.value) {
      await new Promise((resolve) => {
        const checkStatus = () => {
          if (!isTranslateStatus.value) {
            resolve({})
          }
          else {
            setTimeout(checkStatus, 100)
          }
        }
        checkStatus()
      })
    }
  }

  /**
   * 按分数和模型权重排序并分配sortIndex
   * @param scoreMap 分数映射Map
   * @param statusNodeGlobalId 状态节点ID
   * */
  const sortEnginesByScoreAndWeight = (
    scoreMap: Map<number, Array<{ id: number, score: number, rationale: string, engine: string }>>,
    statusNodeGlobalId: string
  ): Array<{ engine: string, sortIndex: number }> => {
    // 将分数从高到低排序
    const sortedScores = Array.from(scoreMap.keys()).sort((a, b) => b - a)

    // 2. 收集所有引擎数据，包括分数和权重
    const allEngines: Array<{
      engine: string
      translationEngine: any
      score: number
      weight: number
    }> = []

    for (const score of sortedScores) {
      const enginesWithScore = scoreMap.get(score)
      if (enginesWithScore) {
        for (const engineData of enginesWithScore) {
          // 找出相同评分的
          const translationEngine = translationEngineOptions.value.find(option => option.value === engineData.engine)
          if (translationEngine) {
            // 获取模型权重
            const weight = getModelWeight(translationEngine.modelVersion, translationEngine.value)
            allEngines.push({
              engine: engineData.engine,
              translationEngine,
              score,
              weight
            })
          }
        }
      }
    }

    // 3. 按分数和权重排序：首先按分数排序，分数相同时按权重排序
    allEngines.sort((a, b) => {
      // 分数降序排列
      const scoreDiff = b.score - a.score
      if (scoreDiff !== 0) return scoreDiff
      // 权重降序排列
      return b.weight - a.weight
    })

    // 4. 分配sortIndex，并构建返回数组
    let currentIndex = 1
    const result = []
    for (const engineData of allEngines) {
      // 记录到 translationEngine 的 sortIndexArray（兼容原有逻辑）
      engineData.translationEngine.sortIndexArray.push({
        sortIndex: currentIndex,
        statusNodeGlobalId
      })
      result.push({
        engine: engineData.engine,
        sortIndex: currentIndex
      })
      currentIndex++
    }
    return result
  }

  /**
   * 清理现有状态
   * 择优翻译
   */
  const cleanupExistingState = async () => {
    // 移除所有事件监听
    removeAllEventListeners()
    // 清理精挑翻译结果
    clearAiSelectTranslateResults()
    // 清除历史记录中的择优翻译结果
    clearHistoryAiSelectData()
    // 清理精挑翻译错误
    clearAiSelectErrorDom()
    // 清空精挑翻译结果文本
    aiSelectFinallyResultText.value = []
    // 清空用户自定义翻译结果
    customResult.value = []
  }

  /**
   * !设置每一句原文的翻译引擎状态（择优翻译状态）
   * @param {Array} buildRequestData - 构建的请求数据数组，包含原文和各引擎翻译结果
   * @param {Array} invalidTexts - 无效文本数组 （<n0></n0>、\n）
   * @description 将有效翻译结果的引擎状态设置为评分中
   */
  const setEnginesLoadingState = (buildRequestData) => {
    for (const { translateReslut, statusNodeGlobalId } of buildRequestData) {
      // 跳过空结果
      if (!translateReslut?.length) continue

      // 检查是否有有效的翻译内容
      const hasValidTranslations = translateReslut.some(result => result.text && !invalidTexts.includes(result.text))

      if (hasValidTranslations && statusNodeGlobalId) {
        // 对所有有效翻译结果设置评分状态
        for (const { engine } of translateReslut) {
          const engineOption = translationEngineOptions.value.find(option => option.value === engine)

          if (engineOption) {
            // 将等待择优状态设置为空
            engineOption.status = TranslationStatus.Empty

            /**
             *  切换到 使用 selectedTransStatus 来管理状态
             *  因为择优翻译可能会有逐句择优翻译, 所以要每个句子都有一个择优翻译状态
             */

            // 检查是否已存在相同 statusNodeGlobalId 的状态
            const statusExists = engineOption.selectedTransStatus && engineOption.selectedTransStatus.some(item => item.statusNodeGlobalId === statusNodeGlobalId)

            // 只有在不存在相同 statusNodeGlobalId 的状态时才添加
            if (!statusExists) {
              // 设置翻译引擎状态为评分中
              engineOption.selectedTransStatus.push({
                status: TranslationStatus.Scoring,
                statusNodeGlobalId
              })
            }

            // console.log('调试日志-设置翻译引擎状态', engineOption);
          }
        }
      }
    }
  }

  /**
   * 构建请求
   * 构建后的格式：
   * {
   *  originalText,
   *  statusNodeGlobalId,
   *  translateReslut:[
   *  {
   *    engine,
   *    statusNodeGlobalId,
   *    text,
   * }
   * ]
   * }
   */
  const prepareTranslationResults = () => {
    // 获取已经启动的模型列表
    const { enabledEnginesList, isSingleSentenceAiSelect } = getStore()

    /** 全文翻译 */
    if (!isSingleSentenceAiSelect.value) {
      const mergedTranslationResult = {
        originalText: originalTextList.value.combinedText,
        statusNodeGlobalId: 'combined', // 合并翻译结果的statusNodeGlobalId
        translateReslut: []
      }

      // 只处理已启用的翻译引擎
      for (const engineId of enabledEnginesList.value) {
        // 查找对应引擎的翻译选项
        const engineOption = translationEngineOptions.value.find(option => option.value === engineId)

        if (engineOption.translationsResult.length === 0) continue

        if (engineOption) {
          // 合并该引擎下的所有翻译结果
          const mergedText = engineOption.translationsResult[0].text

          if (mergedText) {
            mergedTranslationResult.translateReslut.push({
              engine: engineId,
              statusNodeGlobalId: mergedTranslationResult.statusNodeGlobalId,
              text: mergedText
            })
          }
        }
      }

      return [mergedTranslationResult]
    }
    /** 逐句翻译 */
    const translationResultsArray = []

    for (const item of aiSelectTransList.value) {
      const { text, statusNodeGlobalId, paragraphId } = item

      // 创建结果对象
      const resultItem = {
        originalText: text,
        statusNodeGlobalId,
        paragraphId,
        translateReslut: [] // 注意这里与注释中的拼写一致
      }

      // 只处理已启用的翻译引擎
      for (const engineId of enabledEnginesList.value) {
        // 查找对应引擎的翻译选项
        const engineOption = translationEngineOptions.value.find(option => option.value === engineId)

        if (engineOption) {
          // 检查该引擎是否存在与当前statusNodeGlobalId匹配的错误
          const hasError = engineOption.error && Array.isArray(engineOption.error) && engineOption.error.some(err => err.statusNodeGlobalId === statusNodeGlobalId)

          // 如果存在错误，跳过当前引擎的翻译结果
          if (hasError) {
            continue
          }

          // 查找该引擎对应此文本的翻译结果
          const translationResult = engineOption.translationsResult.find(result => result.statusNodeGlobalId === statusNodeGlobalId)

          // 找到原始翻译结果的索引
          const resultIndex = engineOption.translationsResult.findIndex(result => result.statusNodeGlobalId === statusNodeGlobalId)

          // 如果存在翻译结果且有对应的原始翻译文本
          if (translationResult && resultIndex !== -1 && engineOption.translationsResult[resultIndex]) {
            resultItem.translateReslut.push({
              engine: engineId,
              statusNodeGlobalId,
              text: translationResult.text
            })
          }
        }
      }

      translationResultsArray.push(resultItem)
    }

    return translationResultsArray
  }

  /**
   * !发起择优翻译请求
   * 在原文中标记为翻译中（loading 线）
   */
  const sendSingleSentenceRequests = (buildRequestData) => {
    console.log('getLocale()', getLocale())

    const { isSingleSentenceAiSelect, currentTranslateMode, isAiSelectAnalysis, targetLanguage } = getStore()
    const isSingleSentence = currentTranslateMode.value === 'aiSelect' && isSingleSentenceAiSelect.value

    for (let index = 0; index < buildRequestData.length; index++) {
      const { originalText, statusNodeGlobalId, translateReslut, paragraphId } = buildRequestData[index]
      // console.log(`发送单句精挑请求-${statusNodeGlobalId}`, buildRequestData);

      // 标记为翻译中
      if (isSingleSentence) originalLoading('i', statusNodeGlobalId)

      // 发送请求
      useAiSelectTranslate({
        multiTranslationEngineRequestResults: translateReslut,
        statusNodeGlobalId,
        isAiSelectAnalysis: isAiSelectAnalysis.value,
        toLanguage: targetLanguage.value,
        interfaceLanguage: getLocale(),
        originalContent: originalText,
        paragraphId
      })
    }
  }

  /**
   * 设置结果处理函数
   */
  const setupResultHandler = (enabledEnginesList, originalArrayLength) => {
    const { isSingleSentenceAiSelect, aiSelectModel, isAiSelectAnalysis } = getStore()
    // 结果处理函数实现...
    messageEvent.value.aiSelectTranslateMessage = async (event) => {
      if (event.data.type === 'AI_SELECT_TRANSLATE') {
        try {
          const { data: aiSelectTranslateResults, statusNodeGlobalId } = event.data

          console.log(`接收到插件返回的数据-${statusNodeGlobalId}`, aiSelectTranslateResults)
          // 存储本次择优翻译的 评分 分析 排序id
          const aiSelectTranslationData = []

          // 将原文节点标记为精挑翻译完成
          originalLoading('y', statusNodeGlobalId)

          /** 精挑翻译错误处理 */
          if (aiSelectTranslateResults.status !== 200) {
            /** 发生错误显示重新择优翻译 */
            reSelect.value = true
            originalArrayLength--

            // 只重置当前有翻译结果的引擎状态
            updateTranslationEngineStatus(enabledEnginesList.value, 1)
            // 调用错误处理函数
            handleAiSelectError(statusNodeGlobalId, aiSelectTranslateResults.message || '择优翻译失败')

            /**
             * 如果原文节点全部翻译完成，则关闭loading状态
             */
            if (originalArrayLength === 0) {
              sharedState.isAiSelectLoading.value = false
            }

            /**
             * 择优发生错误也要更新择优历史记录数据
             * 因为前面添加了择优翻译 loading 线
             */
            await updateAiSelectTranslationHistoryData(historyKey.value, aiSelectTranslationData)
            return
          }

          // 处理每个精挑结果
          if (aiSelectTranslateResults.text !== '<n0></n0>') {
            // 创建一个Map来存储每个score对应的引擎列表
            const scoreMap = new Map()
            originalArrayLength--

            // 2. 然后在循环中使用这个稳定数组
            for (const { id, score, rationale, engine } of aiSelectTranslateResults.data) {
              if (!scoreMap.has(score)) {
                scoreMap.set(score, [])
              }
              scoreMap.get(score).push({
                engine,
                id,
                score,
                rationale
              })
            }

            // 按分数和模型权重排序并分配sortIndex
            const sortResult = sortEnginesByScoreAndWeight(scoreMap, statusNodeGlobalId)

            for (const SelectResults of aiSelectTranslateResults.data) {
              const { score, rationale, engine } = SelectResults
              // 获取对应的翻译引擎
              const translationEngine = translationEngineOptions.value.find(option => option.value === engine)

              // 如果翻译引擎不存在，则跳过
              if (!translationEngine) continue

              // 标记状态
              translationEngine.status = 6

              // 精挑评分
              translationEngine.score.push({
                score: score,
                statusNodeGlobalId
              })

              // 精挑分析
              translationEngine.rationale.push({
                rationale: rationale,
                statusNodeGlobalId
              })

              /**
               * 设置翻译引擎状态为空
               */
              const selectedTransStatus = translationEngine.selectedTransStatus.find(item => item.statusNodeGlobalId === statusNodeGlobalId)
              if (selectedTransStatus) selectedTransStatus.status = TranslationStatus.Empty

              // console.log('调试日志-设置翻译引擎状态为空', translationEngine.selectedTransStatus);

              /**
               *  存储本次择优翻译的 评分 分析 排序id
               * 通过深度拷贝解决数据引用问题, 获取原始数据
               */
              aiSelectTranslationData.push({
                value: translationEngine.value,
                score: JSON.parse(JSON.stringify(translationEngine.score)),
                rationale: JSON.parse(JSON.stringify(translationEngine.rationale)),
                sortIndexArray: JSON.parse(JSON.stringify(translationEngine.sortIndexArray))
              })
            }

            // 如果原文节点全部翻译完成，则关闭loading状态
            if (originalArrayLength === 0) {
              sharedState.isAiSelectLoading.value = false
            }

            /**
             * 添加择优翻译结果
             */
            await handleSelectedResult(statusNodeGlobalId, sortResult, translationEngineOptions.value)

            updateAiSelectSetting(historyKey.value, {
              isSingleSentenceAiSelect: isSingleSentenceAiSelect.value,
              aiSelectModel: aiSelectModel.value,
              isAiSelectAnalysis: isAiSelectAnalysis.value
            })
          }
          else {
            // aiSelectTranslateResults.text等于'<n0></n0>' 说明当这次返回的是换行符，直接添加换行符无需处理
            handleBreaks(aiSelectTranslateResults, statusNodeGlobalId)
          }

          /**
           * TODO 向历史记录添加择优翻译结果
           * 1. 评分， 2. 分析， 3. 排序id
           * 这里不要使用异步， 因为历史记录的数据不需要实时更新的，确保不会影响后面的处理
           */

          await updateAiSelectTranslationHistoryData(historyKey.value, aiSelectTranslationData)
        }
        catch (error) {
          // console.error('Error processing AI select results:', error);
          updateTranslationEngineStatus(enabledEnginesList.value, 4)
        }
      }
    }

    // 移除事件监听器
    window.removeEventListener('message', messageEvent.value.aiSelectTranslateMessage)

    // 添加事件监听器
    window.addEventListener('message', messageEvent.value.aiSelectTranslateMessage)
  }

  /**
   * 重置翻译引擎状态
   * 重置所有的selectedTransStatus
   */
  const resetTranslationEngineStatus = () => {
    translationEngineOptions.value.forEach((item) => {
      item.selectedTransStatus = []
    })
  }

  return {
    aiSelectTranslateReq
  }
}

export const useAiSelectTranslate = aiSelectTranslate()
