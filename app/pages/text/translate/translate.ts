/**
 * TODO 择优翻译-处理多模型翻译
 */

import { translationState } from '@/pages/text/useData/useTranslationState'
import { useInputTranslate } from '@/store/inputTranslate'
import { storeToRefs } from 'pinia'
import { useTranslationHistoryState } from '@/pages/text/useData/useHistoryState.ts'
import { buildOriginalTextList, storeProcessedTextData } from '@/pages/text/js/text-processor'
import { storeEditorState } from '@/pages/text/components/Textarea/js/dom-serializer'
import { cancelRequests } from '../js/cancelRequests' // 取消请求
import { sendDataToBackground } from '@/pages/text/js/dataAnalysis'

// 模型支持状态
import { modelSupportState } from '@/pages/text/useData/useModelSupport'
// 导入共享状态
import { sharedState } from '@/pages/text/useData/sharedState'

// 精挑翻译
import { useAiSelectTranslate } from '@/pages/text/translate/selectTranlstae'

// 获取插件翻译请求
import { useGetPluginTranslate } from '@/pages/text/js/sendExtension'

// 操作dom的工具函数
import { useDomControl } from '@/pages/text/translate/domControl'

const { isEngineSupported } = modelSupportState

// 翻译状态管理
const { clearTranslationsContent, translationEngineOptions, updateTranslationEngineStatus, clearSpecifiedTranslationResults } = translationState
const { aiSelectTranslateReq } = useAiSelectTranslate
const { useTextTranslate } = useGetPluginTranslate
// 将直接解构改为调用函数后解构

const { handleHighlightFirstParagraph, removeAllHighlightedSentences } = useDomControl

// 不要在这里直接使用Pinia存储
// 因为需要初始化之后才能调用 pinia， 如果在这是使用变成初始化前调用了
// !如果在这里使用 pinia 存储：Pinia可能还没有初始化导致出错, 改成getStore里面使用
// 改为在函数内部使用

/**
 * 翻译
 * 1. 构建原文文本数组
 * 2. 发送翻译请求
 * 3. 清理事件，清理完成执行精挑翻译
 * 4. 用户关闭择优翻译的处
 * 5. 检测语言
 * 6. 处理筛选推荐语言
 * 7. 返回翻译方法
 */

export const translate = () => {
  // 使用共享状态
  const { highlightId, originalTextList, aiSelectTransList, isTranslateStatus, messageEvent, sourceDOMStructure, historyKey, customResult, aiSelectFinallyResultText, currentTranslateStatus }
    = sharedState

  // 使用历史记录状态
  const { startNewTranslation, updateMultiModelTranslationResult, clearHistoryTransData, removeHistory } = useTranslationHistoryState()

  // 将activeRequestIds从Set改为Map，键是模型名称，值是该模型的请求ID
  const activeRequestIds = new Map<string, string>() // 记录每个模型的活跃请求ID

  /**
   * 获取存储中的状态
   * 这样只有在函数被调用时才会尝试访问Pinia
   * 在函数内部获取Pinia存储
   * 并且每次调用都是最新值
   * @returns {Object} 包含所有需要的状态和存储实例
   */
  const getStore = () => {
    const useInputTransStore = useInputTranslate()
    return {
      useInputTransStore,
      ...storeToRefs(useInputTransStore)
    }
  }

  /**
   * 注意事项
   * 1. 以下遍历要使用 for 方法遍历， 不要使用forEach 遍历，因为性能没有 for 好
   */

  /**
   * 构建原文文本数组
   * @param data 原文文本数组
   * @param type 翻译类型 1. Compare 对比翻译，2.classic 传统翻译
   * originalTextList.value {
   *  {
   *    text: string;
   *    statusNodeGlobalId: string;
   *  }[]
   * }
   */
  const buildOriginalTextListSendRequest = (executeTranslateModelList = [], isAutoSelecctTrans = true) => {
    const { currentTranslateMode, originalLanguage, targetLanguage } = getStore()

    // 检查原文语言与目标语言是否相同
    if (originalLanguage.value === targetLanguage.value) {
      return
    }

    // 构建原文文本数组
    originalTextList.value = buildOriginalTextList()

    // 构建择优翻译专属数组
    aiSelectTransList.value = storeProcessedTextData()

    const editorElement = document.querySelector('.editable-textarea') as HTMLElement

    // 序列化编辑器DOM结构
    const editorDOMStructure = storeEditorState(editorElement)

    sourceDOMStructure.value = editorDOMStructure

    // 如果原文文本数组有值，并且combinedText有值，则执行翻译
    if (originalTextList.value.paragraphs.length > 0 && originalTextList.value.combinedText) {
      // 触发翻译之前清理之前的数据跟事件监听
      window.removeEventListener('message', messageEvent.value.translateMessage)
      window.removeEventListener('message', messageEvent.value.aiSelectTranslateMessage)
      // 执行翻译
      handleExpandRequestDebounce(executeTranslateModelList, isAutoSelecctTrans)
    }
  }

  /**
   * 1. 先清除翻译数据
   * 2. 延迟执行翻译(防抖)
   * @param {Array} executeTranslateModelList 执行翻译的模型列表
   * @param {Boolean} 是否需要翻译完成之后自动择优翻译(默认开启， 有些情况是不要自动择优翻译的，比如： 某个模型发送报错之后不会自动重新择优翻译)
   */
  function handleExpandRequestDebounce(executeTranslateModelList = [], isAutoSelecctTrans = true) {
    const { currentTranslateMode, enabledEnginesList, traditionalEngines } = getStore()

    /** 执行翻译前只取消指定模型的请求 */
    if (executeTranslateModelList.length > 0) {
      // 只取消需要重新翻译的模型请求
      for (const engine of executeTranslateModelList) {
        if (activeRequestIds.has(engine)) {
          cancelRequests([activeRequestIds.get(engine)])
          activeRequestIds.delete(engine)
        }
      }
    }
    else {
      // 全部重新翻译时，取消所有请求
      cancelRequests(Array.from(activeRequestIds.values()))
      activeRequestIds.clear()
    }

    let engineList = []
    // 对比翻译跟择优翻译
    if (currentTranslateMode.value === 'aiSelect' || currentTranslateMode.value === 'compare') {
      engineList = enabledEnginesList.value
    }
    else {
      engineList = [traditionalEngines.value.value]
    }

    // console.log('调试日志-handleExpandRequestDebounce', engineList);

    /**
     * 如果executeTranslateModelList 数组有值说明是用户手动添加新的模型并且开启了自动翻译、不是择优翻译tab
     */
    if (executeTranslateModelList.length > 0) {
      // console.log('清理指定模型的翻译数据配置');
      // 清除指定模型的翻译数据配置
      clearSpecifiedTranslationResults(executeTranslateModelList)
      engineList = executeTranslateModelList
    }
    else {
      // console.log('清除所有模型的翻译数据配置');
      // 清除所有模型的翻译数据配置
      clearTranslationsContent()
      // 清除multiModelTranslationOptions
      clearHistoryTransData()
    }

    // 设置翻译状态
    translationState.setTranslating(engineList)

    // 2. 延迟执行翻译(防抖)
    debouncedTranslate(engineList, isAutoSelecctTrans)
  }

  // 延迟执行翻译
  const debouncedTranslate = useDebounceFn((engineList, isAutoSelecctTrans) => {
    sendExpandRequest(engineList, isAutoSelecctTrans)
  }, 300)

  // 发送翻译请求
  function sendExpandRequest(engineList = [], isAutoSelecctTrans = true) {
    try {
      // 获取最新的Pinia存储状态
      const { targetLanguage, originalLanguage, isSingleSentenceAiSelect, currentTranslateMode, isAiSelectAnalysis, aiSelectModel } = getStore()

      // 1. 基础检查
      if (!originalTextList.value.paragraphs?.length || !translationEngineOptions.value?.length) return

      // console.log('调试日志-sendExpandRequest', originalTextList.value.paragraphs, translationEngineOptions.value);
      // console.log('engineList', engineList);

      /**
       * 标记为已开启翻译（即使后续所有模型被过滤掉也需要显示“目标语言不支持”提示）
       */
      originalTextList.value.isTranslated = true

      // 过滤掉不支持语言的模型
      engineList = engineList.filter(engine => isEngineSupported(engine))

      if (engineList.length === 0) return

      // 如果当前历史记录不存在，则创建新的历史记录项
      if (!historyKey.value) {
        if (currentTranslateMode.value === 'traditional') {
          // 传统翻译传递模型值，确保为字符串类型且非空
          let modelValue = ''
          if (Array.isArray(engineList) && engineList.length > 0) {
            if (typeof engineList[0] === 'string') {
              modelValue = engineList[0]
            }
            else if (engineList[0] && typeof engineList[0].value === 'string') {
              modelValue = engineList[0].value
            }
          }
          startNewTranslation(sourceDOMStructure.value, originalTextList.value.combinedText, currentTranslateMode.value, modelValue)
        }
        else {
          startNewTranslation(sourceDOMStructure.value, originalTextList.value.combinedText, currentTranslateMode.value)
        }
      }

      // 如果是择优翻译模式，设置loading状态为true, 并且是自动择优翻译
      if (currentTranslateMode.value === 'aiSelect' && isAutoSelecctTrans) {
        sharedState.isAiSelectLoading.value = true
      }

      // 记录本次翻译的目标语言-原文语言
      currentTranslateStatus.value.currentTranslateTargetLanguage = {
        targetLanguage: targetLanguage.value,
        originalLanguage: originalLanguage.value
      }

      currentTranslateStatus.value.currentModelList = engineList

      // 记录本次翻译使用的是什么模式（aiSelect/classic/compare）
      currentTranslateStatus.value.translateType = currentTranslateMode.value

      /**
       * 记录本次翻译使用的是什么模式（全文/逐句）
       * 判断是否需要全文翻译
       * 1. 如果 isSingleSentenceAiSelect 是开启的 但是 currentTranslateMode等于'aiSelect'是关闭的就 = 全文翻译
       * 2. 如果 isSingleSentenceAiSelect 是关闭的 但是 currentTranslateMode不等于'aiSelect'， electTranslate是开启的就 = 全文翻译
       * 3. 如果 isSingleSentenceAiSelect 、 currentTranslateMode等于其他 都是关闭的 = 全文
       * 只有当精挑翻译和单句精挑都开启时才是单句翻译，其他情况都是全文翻译
       * isSingleSentence 是 true 为逐句翻译，false 为全文翻译
       */
      currentTranslateStatus.value.isSingleSentence = currentTranslateMode.value === 'aiSelect' && isSingleSentenceAiSelect.value

      // 开启翻译- 确保在多模型翻译请求的过程中不会进行精挑翻译
      isTranslateStatus.value = true

      // 4. 标记翻译状态
      updateTranslationEngineStatus(engineList, 2)

      let expectedResultsCount = engineList.length

      // 7. 创建新的消息处理器
      messageEvent.value.translateMessage = (event) => {
        if (event.data.type !== 'TEXT_INPUT_TRANSLATE') return

        try {
          const { isSingleSentence, translateEngine, response } = event.data.message
          console.log('调试日志-接收到插件返回的翻译结果', event.data.message)

          // 检查当前模型的请求ID是否匹配
          if (activeRequestIds.get(translateEngine) !== event.data.requestId) {
            // console.log('请求ID不匹配，返回', {
            // activeRequestIds: activeRequestIds.get(translateEngine),
            // eventRequestIds: event.data.requestId,
            // engine: translateEngine
            // });
            return
          }

          // 处理单句精挑翻译的高亮
          if (currentTranslateMode.value === 'aiSelect' && isSingleSentenceAiSelect.value) {
            // 高亮第一个段落
            handleHighlightFirstParagraph()
          }
          else {
            // 移除所有高亮
            removeAllHighlightedSentences()
            // 如果当前不是单句精挑翻译，则高亮ID为全文
            highlightId.value = 'combined'
          }

          // 处理翻译结果
          const engineConfig = translationEngineOptions.value.find(person => person.value === translateEngine)

          if (!engineConfig) return

          for (const item of response) {
            const {
              statusNodeGlobalId,
              paragraphId,
              data: { status, text, message }
            } = item

            if (status === 200) {
              const newText = text === '<n0></n0>' ? '\n' : text.replace(/<n0><\/n0>/g, '\n')

              // 检查是否存在相同 状态 id 的翻译结果
              const isExist = engineConfig.translationsResult.some(translation => translation.statusNodeGlobalId === statusNodeGlobalId)

              if (isExist) {
                // 如果存在相同 状态 id 的翻译结果，则替换
                engineConfig.translationsResult = engineConfig.translationsResult.map((translation) => {
                  if (translation.statusNodeGlobalId === statusNodeGlobalId) {
                    return { ...translation, text: newText }
                  }
                  return translation
                })
              }

              if (isSingleSentence) {
                // 单句翻译
                // const newText = text === '<n0></n0>' ? '\n' : text.replace(/<n0><\/n0>/g, '\n');
                // 添加翻译结果
                engineConfig.translationsResult.push({
                  text: newText,
                  statusNodeGlobalId,
                  engine: translateEngine,
                  paragraphId
                })
              }
              else {
                // 构建翻译结果数组
                engineConfig.translationsResult.push({
                  text: newText,
                  statusNodeGlobalId,
                  engine: translateEngine
                })
              }

              // 设置翻译状态
              if (currentTranslateMode.value === 'aiSelect' && isAutoSelecctTrans) {
                // 等待择优
                engineConfig.status = 7
              }
              else {
                // 翻译完成
                engineConfig.status = 3
              }
            }
            else {
              // 添加错误标记和错误信息
              engineConfig.error.push({
                statusNodeGlobalId,
                message
              })
              engineConfig.status = 4
            }
          }

          /**
           * 获取发送请求的模型属性，每次返回一个翻译结果就减一
           * 当减到0的时候，说明所有翻译请求都返回了，就可以关闭正在翻译的状态
           */
          expectedResultsCount--

          // 该模型翻译已完成，移除请求ID
          activeRequestIds.delete(translateEngine)

          if (expectedResultsCount === 0) {
            // 关闭正在翻译的状态
            isTranslateStatus.value = false

            // 移除翻译事件监听器
            if (messageEvent.value.translateMessage) {
              window.removeEventListener('message', messageEvent.value.translateMessage)
            }

            // 执行精挑翻译
            if (currentTranslateStatus.value.translateType === 'aiSelect' && currentTranslateMode.value === 'aiSelect') {
              if (isAutoSelecctTrans) {
                // 发送精挑翻译请求
                aiSelectTranslateReq()
              }
              else {
                // 如果自动择优翻译关闭，则不发送精挑翻译请求但是开启允许用户重新择优翻译的状态
                sharedState.reSelect.value = true
              }
            }

            // console.log('translationEngineOptions.value', translationEngineOptions.value);

            /**
             * TODO 向历史记录添加翻译结果
             */
            updateMultiModelTranslationResult(historyKey.value, translationEngineOptions.value, engineList, currentTranslateStatus.value)

            // 翻译模型
            const trans_engine = engineList.map((engine) => {
              const engineOption = translationEngineOptions.value.find(item => item.value === engine)
              const result = {
                modelValue: engine,
                serviceMode: engineOption?.serviceMode
              }

              return result
            })

            /**
             * 发送数据到 background.js 中做数据分析
             */
            sendDataToBackground({
              trans_engine: trans_engine, // 翻译模型
              aiSelect_engine: aiSelectModel.value.value, // 择优翻译模型
              aiSelect_model_version: aiSelectModel.value.modelVersion, // 择优翻译模型版本
              target_language: targetLanguage.value, // 目标语言
              original_language: originalLanguage.value, // 原文语言
              translateMode: currentTranslateMode.value, // 翻译模式(traditional: 传统翻译, compare: 对比翻译, aiSelect: AI择优翻译)
              isAutoTranslate: isAutoSelecctTrans, // 是否自动翻译
              isAiSelectAnalysis: isAiSelectAnalysis.value, // 是否是择优翻译分析
              sentenceMode: currentTranslateStatus.value.isSingleSentence ? 'sentencePairs' : 'fullText' // 逐句择优：fullText(全文) 或 sentencePairs(逐句)
            })
          }
        }
        catch (error) {
          // 获取最新的Pinia存储状态
          const { enabledEnginesList } = getStore()

          console.error('Translation result processing error:', error)

          // 这个 4 现在是没有用上的
          updateTranslationEngineStatus(enabledEnginesList.value, 4)
          isTranslateStatus.value = false
        }
      }

      // 移除事件监听器
      window.removeEventListener('message', messageEvent.value.translateMessage)

      // 8. 添加事件监听器
      window.addEventListener('message', messageEvent.value.translateMessage)

      // 调试日志
      // console.log('engineList', engineList);
      // console.log('currentTranslateMode', currentTranslateMode.value);
      // console.log('isSingleSentenceAiSelect', isSingleSentenceAiSelect.value);

      for (const engine of engineList) {
        // 为每个模型生成唯一请求ID
        const requestId = crypto.randomUUID()
        // 存储每个模型的请求ID
        activeRequestIds.set(engine, requestId)

        // 发送翻译请求 使用的是插件的翻译请求（window.postMessage）
        useTextTranslate({
          originalText: originalTextList.value,
          translateEngine: engine,
          toLanguage: targetLanguage.value,
          fromLanguage: originalLanguage.value,
          isSingleSentence: currentTranslateStatus.value.isSingleSentence,
          requestId
        })
      }
    }
    catch (error) {
      // 获取最新的Pinia存储状态
      const { enabledEnginesList } = getStore()

      console.error('Translation request error:', error)
      updateTranslationEngineStatus(enabledEnginesList.value, 4)
      isTranslateStatus.value = false
    }
  }

  return {
    sendExpandRequest,
    buildOriginalTextListSendRequest,
    handleExpandRequestDebounce
  }
}

export const useTranslate = translate()
