/**
 * 操作dom的工具函数
 */
import { useTranslationHistoryState } from '@/pages/text/useData/useHistoryState.ts'
import { storeToRefs } from 'pinia'

// 翻译数据管理
import { translationState } from '@/pages/text/useData/useTranslationState'
// 导入共享状态
import { sharedState } from '@/pages/text/useData/sharedState'
import { useInputTranslate } from '@/store/inputTranslate'
import { isEmptyContent } from '@/utils/utils'

const { clearTranslationsContent } = translationState

// 不要在这里直接使用Pinia存储
// 因为需要初始化之后才能调用 pinia， 如果在这是使用变成初始化前调用了
// !如果在这里使用 pinia 存储：Pinia可能还没有初始化导致出错, 改成getStore里面使用
// 改为在函数内部使用

/**
 * 操作dom的工具函数
 */
const domControl = () => {
  // 使用历史记录状态
  const { updateAiSelectTranslationResult } = useTranslationHistoryState()

  const getStore = () => {
    const useInputTransStore = useInputTranslate()
    return {
      useInputTransStore,
      ...storeToRefs(useInputTransStore)
    }
  }

  /**
   * 注意事项
   * 1. 以下遍历要使用 for 方法遍历， 不要使用forEach 遍历，因为性能没有 for 好
   */

  // 使用共享状态
  const { highlightId, aiSelectFinallyResultText, messageEvent, historyKey, editSelectedTransResult, selectedModelsStatus } = sharedState

  // 初始化翻译数据
  function initializeTranslateData() {
    // 不需要注释，直接调用共享状态的重置方法
    sharedState.resetAllState()
  }

  // 移除全部的事件监听
  function removeAllEventListeners() {
    // 移除精挑翻译事件监听
    window.removeEventListener('message', messageEvent.value.aiSelectTranslateMessage, false)
    messageEvent.value.aiSelectTranslateMessage = null

    // 移除翻译事件监听
    window.removeEventListener('message', messageEvent.value.translateMessage, false)
    messageEvent.value.translateMessage = null
  }

  // 重置所有翻译数据- 重新翻译让数据初始化
  function resetAllTranslateData() {
    // 移除全部的事件监听
    removeAllEventListeners()

    // 使用共享状态的重置方法
    sharedState.resetAllState()

    // 重置翻译数据
    clearTranslationsContent()

    // 清除精挑翻译的错误dom
    clearAiSelectErrorDom()

    // 移除所有高亮的句子
    removeAllHighlightedSentences()

    // 移除原文节点的 所有的loading线
    removeOriginalLoadingLine()
  }

  /**
   * 处理精挑翻译最终结果
   * @param {Object} data - 精挑翻译结果数据
   */

  // 创建一个更新队列以确保按顺序处理
  let updateQueue = Promise.resolve()

  function handleFinallyResult(data) {
    // 将当前数据添加到内存中
    aiSelectFinallyResultText.value.push(data)

    // 将更新任务添加到队列中
    updateQueue = updateQueue.then(async () => {
      try {
        // 更新历史记录
        await updateAiSelectTranslationResult(historyKey.value, data)
      }
      catch (error) {
        // console.error('更新精挑翻译结果失败:', error);
      }
    })

    // 返回当前队列的Promise
    return updateQueue
  }

  /**
   * 更新当前择优翻译区域选中模型的翻译结果
   * 区分开究竟是 更新择优的翻译结果，还是模型的翻译结果
   * !1. selected-result-engine是择优翻译结果的模型 2. selected-result-id是择优翻译结果的id
   * 3. selected-translate-result-id 是更新模型的翻译结果
   * @param statusNodeGlobalId 当前点击的句子节点id
   * @param engine 当前点击的句子节点引擎
   * @param selectedResultModel 当前择优翻译结果区域选中的模型
   */
  function changeSelectedResult(statusNodeGlobalId, engine, selectedResultModel, updateText) {
    // console.log('changeSelectedResult', {
    //   statusNodeGlobalId,
    //   engine,
    //   selectedResultModel
    // });

    // 清空当前选中的模型
    editSelectedTransResult.value = {
      engine: '',
      statusNodeGlobalId: '',
      text: '',
      modifiedModel: '',
      paragraphId: ''
    }

    // 找到当前点击的翻译结果 dom
    const currentParagraph = document.querySelector(
      `span[selected-translate-result-id="${statusNodeGlobalId}"][selected-translate-result-engine="${engine}"][selected-translate-result-node="y"]`
    ) as HTMLSpanElement
    //  向上寻找段落id
    const paragraphId = currentParagraph.closest('div')?.getAttribute('data-paragraph-id')

    // 更新选中状态
    selectedModelsStatus.value[statusNodeGlobalId] = engine

    // 更新当前选中的模型
    editSelectedTransResult.value = {
      engine,
      statusNodeGlobalId,
      text: updateText || currentParagraph.innerText,
      modifiedModel: selectedResultModel.value,
      paragraphId
    }
  }

  // 清除全部的错误
  function clearAiSelectErrorDom() {
    // 确保能够获取到输入框 dom
    const inputDom = document.querySelector('.editable-textarea')

    // 添加空值检查- 防止空值报错
    if (!inputDom) {
      return
    }

    const originalNode = inputDom.querySelectorAll(`span[selected-translate-id]`) as NodeListOf<HTMLSpanElement>

    if (originalNode) {
      for (const node of originalNode) {
        if (node.classList.contains('ai-select-error')) {
          node.classList.remove('ai-select-error')
          const errorIndicator = node.querySelector('.error-indicator')
          if (errorIndicator) {
            errorIndicator.remove()
          }
        }
      }
    }
  }

  /**
   * 输入框内的节点全部都没有高亮则高亮第一个
   */
  function handleHighlightFirstParagraph() {
    // 尝试获取输入框
    const textareaDom = document.querySelector('.editable-textarea')

    // 获取所有段落节点
    const paragraphs = textareaDom.querySelectorAll('div[data-paragraph-id]')

    // 检查是否有任何段落已经被高亮
    const hasHighlighted = Array.from(paragraphs).some((paragraph) => {
      const spans = paragraph.querySelectorAll('.container-source')
      return Array.from(spans).some(span => span.classList.contains('sentence-selected-highlight'))
    })

    // 如果没有任何段落被高亮，则找到第一个非空的段落进行高亮
    if (!hasHighlighted && paragraphs.length > 0) {
      // 遍历所有段落，找到第一个有实际内容的段落
      for (const paragraph of paragraphs) {
        const spans = paragraph.querySelectorAll('.container-source')

        // 找到第一个有内容的span
        for (const span of spans) {
          const content = span.textContent?.trim()
          // 检查span是否有实际内容（不是空或只有零宽空格）
          if (content && !isEmptyContent(content)) {
            // 高亮这个span而不是整个段落
            span.classList.add('sentence-selected-highlight')
            // 更新高亮id
            highlightId.value = span.getAttribute('selected-translate-id')
            return // 找到并高亮后立即返回
          }
        }
      }
    }
  }

  /**
   * 移除所有高亮的句子
   */
  function removeAllHighlightedSentences() {
    // 尝试获取输入框
    const textareaDom = document.querySelector('.editable-textarea')

    // 添加空值检查-防止空值报错
    if (!textareaDom) {
      return
    }

    // 获取所有段落节点
    const paragraphs = textareaDom.querySelectorAll('div[data-paragraph-id]')

    /**
     * 移除所有段落里面的 span 标签中的高亮句子
     */
    for (const paragraph of paragraphs) {
      const spans = paragraph.querySelectorAll('.container-source')
      for (const span of spans) {
        span.classList.remove('sentence-selected-highlight')
      }
    }
  }

  /**
   * 标记原文节点-显示精挑中的loading(线)
   * 如果原文在翻译中则标记为翻译中（i）
   * 如果原文翻译完成则标记为翻译完成（y）
   */
  function originalLoading(status, statusNodeGlobalId) {
    // 尝试获取输入框
    const textareaDom = document.querySelector('.editable-textarea')

    const originalNodes = textareaDom.querySelectorAll(`span[selected-translate-id="${statusNodeGlobalId}"]`)

    if (originalNodes) {
      // 将原文节点标记为翻译中
      for (const node of originalNodes) {
        node.setAttribute('selected-translate-paragraph', status) // 标记状态

        if (status === 'i') {
          node.classList.add('ai-select-loading-line')
        }
        else if (status === 'y') {
          node.classList.remove('ai-select-loading-line')
        }
      }
    }
  }

  /**
   * 移除原文节点的 所有的loading线
   */
  function removeOriginalLoadingLine() {
    // 尝试获取输入框
    const textareaDom = document.querySelector('.editable-textarea')

    // 添加空值检查-防止空值报错
    if (!textareaDom) {
      return
    }

    // 获取所有带有 loading 线的节点
    const loadingNodes = textareaDom.querySelectorAll('.ai-select-loading-line')

    // 移除所有节点的 loading 线样式
    for (const node of loadingNodes) {
      node.classList.remove('ai-select-loading-line')
      // 重置状态标记
      node.setAttribute('selected-translate-paragraph', 'y')
    }
  }

  /**
   * 处理精挑翻译错误
   * @param {string} statusNodeGlobalId - 错误节点的全局ID
   * @param {string} errorMessage - 错误信息
   */
  function handleAiSelectError(statusNodeGlobalId, errorMessage) {
    // 尝试获取输入框
    const textareaDom = document.querySelector('.editable-textarea')

    // 创建工具提示元素
    const createTooltip = () => {
      const tooltip = document.createElement('div')
      tooltip.id = 'custom-error-tooltip'
      tooltip.className = 'custom-error-tooltip'
      return tooltip
    }

    // 创建工具提示箭头
    const createTooltipArrow = () => {
      const arrow = document.createElement('div')
      arrow.className = 'custom-error-tooltip-arrow'
      return arrow
    }

    // 创建工具提示内容
    const createTooltipContent = () => {
      const content = document.createElement('div')
      content.className = 'custom-error-tooltip-content'
      return content
    }

    // 处理视口边界
    const handleViewportBoundaries = (tooltip, rect) => {
      const tooltipRect = tooltip.getBoundingClientRect()
      const TOOLTIP_OFFSET = 8
      const VIEWPORT_PADDING = 10

      let left
      let top

      // 处理水平边界
      if (tooltipRect.right > window.innerWidth - VIEWPORT_PADDING) {
        left = `${window.innerWidth - tooltipRect.width - VIEWPORT_PADDING}px`
      }
      else if (tooltipRect.left < VIEWPORT_PADDING) {
        left = `${VIEWPORT_PADDING}px`
      }

      // 处理垂直边界
      if (tooltipRect.bottom > window.innerHeight - VIEWPORT_PADDING) {
        const spaceAbove = rect.top
        const spaceBelow = window.innerHeight - rect.bottom

        if (spaceAbove > spaceBelow && spaceAbove > tooltipRect.height + VIEWPORT_PADDING) {
          top = `${rect.top - tooltipRect.height - TOOLTIP_OFFSET}px`
          tooltip.classList.replace('position-bottom', 'position-top')
          tooltip.querySelector('.custom-error-tooltip-arrow')?.classList.replace('arrow-top', 'arrow-bottom')
        }
      }

      // 应用位置
      tooltip.style.left = left
      tooltip.style.top = top
    }

    // 如果是combined，则为所有span添加错误指示器
    if (statusNodeGlobalId === 'combined') {
      const allSpans = textareaDom.querySelectorAll('span[selected-translate-id]')

      for (const span of allSpans) {
        // 检查节点是否有文本内容
        if ((span as HTMLElement).textContent?.trim()) {
          addErrorIndicator(span as HTMLElement, errorMessage)
        }
      }
      return
    }

    // 处理单个节点的情况
    const originalNode = textareaDom.querySelector(`span[selected-translate-id="${statusNodeGlobalId}"]`) as HTMLElement
    if (!originalNode || !originalNode.textContent?.trim()) return

    addErrorIndicator(originalNode, errorMessage)

    // 添加错误指示器的辅助函数
    function addErrorIndicator(node: HTMLElement, message: string) {
      // 确保节点有文本内容
      if (!node.textContent?.trim()) return

      // 更新节点样式
      node.classList.add('ai-select-error')
      node.classList.remove('ai-select-loading-line')

      // 检查是否已存在错误指示器，防止重复添加
      const existingErrorNode = node.querySelector('.error-indicator')
      if (existingErrorNode) {
        // 如果已存在错误指示器，只更新错误信息
        const errorIcon = existingErrorNode.querySelector('.error-icon')
        if (errorIcon) {
          (errorIcon as HTMLElement).dataset.tooltip = message
        }
        return
      }

      // 创建错误指示器
      const errorNode = document.createElement('span')
      errorNode.className = 'error-indicator'

      const errorIcon = document.createElement('span')
      errorIcon.className = 'error-icon'
      errorIcon.dataset.tooltip = message

      // 工具提示事件处理
      const handleMouseEnter = (e) => {
        const target = e.target
        // 确保只有一个工具提示存在
        const existingTooltip = document.getElementById('custom-error-tooltip')
        if (existingTooltip) {
          document.body.removeChild(existingTooltip)
        }

        const tooltip = createTooltip()
        tooltip.append(createTooltipArrow(), createTooltipContent())
        document.body.appendChild(tooltip)

        // 设置工具提示内容
        const content = tooltip.querySelector('.custom-error-tooltip-content')
        content && (content.textContent = message)

        // 计算位置
        const rect = target.getBoundingClientRect()
        tooltip.style.cssText = `
        display: block;
        top: ${rect.bottom + 8}px;
        left: ${rect.left + rect.width / 2}px;
      `

        // 使用requestAnimationFrame优化性能
        requestAnimationFrame(() => {
          handleViewportBoundaries(tooltip, rect)
        })
      }

      const handleMouseLeave = () => {
        const tooltip = document.getElementById('custom-error-tooltip')
        tooltip && (tooltip.style.display = 'none')
      }

      // 绑定事件监听
      errorIcon.addEventListener('mouseenter', handleMouseEnter)
      errorIcon.addEventListener('mouseleave', handleMouseLeave)

      // 组装DOM元素
      errorNode.appendChild(errorIcon)
      node.appendChild(errorNode)
    }
  }

  /**
   * 更新当前选中的模型
   * @param {string} statusNodeGlobalId 原文节点id
   * @param {Array} sortResult 精挑翻译结果
   * @param {Array} translationEngineOptions 翻译引擎配置
   */
  function handleSelectedResult(statusNodeGlobalId, sortResult, translationEngineOptions) {
    // console.log('statusNodeGlobalId', statusNodeGlobalId);

    // 确保有排序结果
    if (!sortResult || !sortResult.length) return

    // 按sortIndex排序，找出排序索引最小的结果（最佳结果）
    const bestResult = sortResult[0]

    // 更新当前选中的模型
    selectedModelsStatus.value[statusNodeGlobalId] = bestResult.engine
    // console.log('selectedModelsStatus', selectedModelsStatus.value);

    // 从translationEngineOptions中找到对应引擎的翻译结果
    const engineResult = translationEngineOptions.find(engine => engine.value === bestResult.engine)
    // console.log('engineResult', engineResult);

    if (engineResult) {
      // 从翻译结果中找到对应statusNodeGlobalId的结果
      const translation = engineResult.translationsResult.find(result => result.statusNodeGlobalId === statusNodeGlobalId || result.statusNodeGlobalId === 'combined')
      // console.log('translation', translation);

      if (translation) {
        // 将结果添加到最终结果中
        handleFinallyResult({
          engine: bestResult.engine,
          statusNodeGlobalId,
          paragraphId: translation.paragraphId,
          text: translation.text // 翻译文本
        })
      }
    }
  }

  /**
   * 处理换行符
   * aiSelectTranslateResults.text等于'<n0></n0>' 说明当这次返回的是换行符，直接添加换行符无需处理
   */
  function handleBreaks(aiSelectTranslateResults, statusNodeGlobalId) {
    if (aiSelectTranslateResults.text === '<n0></n0>') {
      handleFinallyResult({
        statusNodeGlobalId,
        paragraphId: aiSelectTranslateResults.paragraphId,
        text: aiSelectTranslateResults.text
      })
    }
  }

  return {
    initializeTranslateData,
    removeAllEventListeners,
    resetAllTranslateData,
    changeSelectedResult,
    handleHighlightFirstParagraph,
    removeAllHighlightedSentences,
    originalLoading,
    clearAiSelectErrorDom,
    handleAiSelectError,
    handleSelectedResult,
    handleBreaks
  }
}

export const useDomControl = domControl()
