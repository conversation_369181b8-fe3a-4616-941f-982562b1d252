<!-- AI 择优翻译 -->
<template>
  <!-- 这个高度减去 1px 是因为头部增加了 1px 的 border -->
  <div class="h-full pt-3 md:pt-6">
    <!-- 检测是否安装浏览器插件 -->
    <div class="min-h-full">
      <ExtensionInstalled
        translate-type="text"
        loading-type="icon"
        :is-show-product="true"
        size="large"
      >
        <!-- 主内容区域 -->
        <!-- 翻译类型 (如: 择优翻译、 文档翻译) -->
        <TranslateType type="text" class="max-sm:hidden" />

        <div class="relative flex flex-col px-4 md:flex-row md:px-8 lg:pr-10 lg:pl-14">
          <!-- 主内容区域  -->
          <div class="flex-1">
            <!-- 头部 -->
            <HeaderMenus class="relative z-10" />

            <!-- 左侧内容区域：输入框顶部操作 -->
            <div class="relative flex w-full flex-col rounded-t-lg rounded-b-lg bg-white md:min-h-0 md:rounded-tl-lg md:rounded-tr-lg dark:bg-neutral-800">
              <!-- 输入框头部操作区域 -->
              <TopOperation class="z-5" />

              <!-- 左侧内容区域：输入框和精挑翻译结果显示 -->
              <div class="relative z-10 flex w-full rounded-b-lg border border-t-0 border-neutral-200 bg-white max-md:rounded-tr-none max-sm:flex-col dark:border-neutral-700 dark:bg-neutral-800">
                <!-- 原文/结果 -->
                <!-- 左侧输入框 -->
                <Textarea
                  ref="textareaRef"
                  :class="{ 'w-1/2': currentTranslateMode === 'aiSelect', 'w-full': currentTranslateMode !== 'aiSelect' }"
                  class="max-sm:w-full"
                  @content-cleared="clearUrlParams"
                />
                <!-- 分割线 -->
                <div v-if="currentTranslateMode === 'aiSelect'" class="absolute top-0 left-1/2 h-full w-px bg-neutral-200 max-sm:hidden dark:bg-neutral-700" />
                <!-- 右侧结果显示 小于 640px 时，宽度为 100% -->
                <div v-if="currentTranslateMode === 'aiSelect'" class="flex w-1/2 rounded-br-lg max-sm:w-full max-sm:border-t-1 max-sm:border-neutral-200 max-sm:dark:border-neutral-700">
                  <!-- 择优翻译结果 -->
                  <SelectResults />
                </div>
              </div>
              <!-- 拖拽条 - 只在大屏幕显示 -->
              <div class="drag-handle absolute top-1/2 -right-5 z-10 hidden flex-shrink-0 cursor-col-resize md:block" @mousedown="handleExternalDrag($event)">
                <UIcon name="i-mdi-drag-vertical" class="size-6 text-neutral-400 hover:text-neutral-500" />
              </div>
            </div>
          </div>

          <!-- 滚动区域 -->

          <!-- 拖拽组件, 注意这里需要设置 pt-10 是因为头部（HeaderMenus）的高度是 h-10，两个值需要一直否则无法跟左侧区域平行 -->
          <SidebarResizer
            ref="sidebarResizerRef"
            class="custom-scrollbar mt-4 w-full rounded-none pt-0 pr-0 pb-0 md:mt-0 md:h-full md:w-auto md:rounded-l-lg md:pt-10 md:pr-2 md:pb-5"
            :class="{ 'md:max-h-[calc(100vh-12rem)]': isTabBarExpanded, 'md:max-h-screen': !isTabBarExpanded }"
          >
            <!-- 多模型翻译结果 -->
            <MultiModelTransResult />
          </SidebarResizer>
        </div>
      </ExtensionInstalled>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
// 初始化数据
import { sendMessageToChromeExtension } from '@/pages/text/js/initData'
// 组件相关
import SelectResults from '@/pages/text/components/SelectResults/index.vue'
import HeaderMenus from '@/pages/text/components/HeaderMenus/index.vue'
import TopOperation from '@/pages/text/components/TopOperation/index.vue'
import Textarea from '@/pages/text/components/Textarea/index.vue'
import MultiModelTransResult from '@/pages/text/components/MultiModelTransResult/index.vue'
import SidebarResizer from '@/pages/text/components/SidebarResizer/index.vue'
import { useInputTranslate } from '@/store/inputTranslate'
import ExtensionInstalled from '@/pages/components/ExtensionInstalled/index.vue'

// 翻译类型
import TranslateType from '@/pages/components/TranslateType.vue'
import { useDomControl } from '@/pages/text/translate/domControl'

import { useTranslateTabShow } from '@/store/translateTabShow'

const useTranslateTabShowStore = useTranslateTabShow()
const { isTabBarExpanded } = storeToRefs(useTranslateTabShowStore)

const { resetAllTranslateData } = useDomControl

const useInputTransStore = useInputTranslate()
const { currentTranslateMode } = storeToRefs(useInputTransStore)

const { t, locale } = useI18n()
const isComponentActive = ref(true)
const textareaRef = ref(null)
const route = useRoute()
const router = useRouter()
const sidebarResizerRef = ref(null) // 添加对SidebarResizer组件的引用

// 处理外部拖拽条的拖拽
const handleExternalDrag = (event: MouseEvent) => {
  if (sidebarResizerRef.value) {
    sidebarResizerRef.value.handleMouseDown(event)
  }
}

// 组件重新加载方法
const reloadComponent = () => {
  isComponentActive.value = false
  nextTick(() => {
    isComponentActive.value = true
  })
}

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('selected_trans.title'), // AI 择优翻译
  ogTitle: t('selected_trans.title'), // AI 择优翻译
  description: t('common.description'),
  ogDescription: t('common.description')
})

/**
 * 在页面加载时发送消息给浏览器插件
 * 获取初始化数据
 */
onBeforeMount(async () => {
  await sendMessageToChromeExtension(locale.value)
})

onMounted(() => {
  // 组件挂载时重新加载组件
  reloadComponent()

  /* 设置 main-container 背景色为 elevated/50, 当前页面无法撑满 所以需要 给父级 main 添加 bg-elevated/50
   */
  const mainContainer = document.querySelector('.main-container') as HTMLElement

  if (mainContainer) {
    mainContainer.classList.add('bg-elevated/50', 'dark:bg-neutral-900')
  }
})

onBeforeUnmount(() => {
  // 组件卸载时移除事件监听
  resetAllTranslateData()

  // !注意离开组件的时候需要移除 背景色样式, 否则会影响其他页面
  const mainContainer = document.querySelector('.main-container') as HTMLElement
  if (mainContainer) {
    // 移除 main-container 的背景色样式
    mainContainer.classList.remove('bg-elevated/50', 'dark:bg-neutral-900')
  }
})

const clearUrlParams = () => {
  // // 使用路由导航清除URL参数，但保留当前路径
  const { path } = route
  // 提取需要保留的查询参数（如果有的话）
  const newQuery = { ...route.query }
  // 删除不需要的参数
  delete newQuery.content
  delete newQuery.targetLang
  // 保留mode参数如果它不是从划词翻译过来的aiSelect模式
  if (route.query.mode === 'aiSelect' && route.query.content) {
    delete newQuery.mode
  }
  // 使用路由导航更新URL，不触发完整页面刷新
  router.replace({ path, query: newQuery })
}
</script>

<!-- 这里不加 scoped 是因为需要覆盖全局样式 -->
<style>
@import '@/pages/text/style/index.css';
</style>
