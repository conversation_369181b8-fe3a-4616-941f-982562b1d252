/* 择优翻译公共样式 */

.loading-layout {
  width: 100%;
  height: 0.625rem;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.3125rem;
}

.loading-text {
  color: #409eff;
  font-size: 0.8125rem;
}

.loading {
  width: 0.9375rem;
  height: 0.9375rem;
  border: 0.1875rem solid rgba(0, 0, 0, 0.1);
  border-top-color: #8ec6e3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg) translateZ(0);
  }
}

/** 删除滚动条 */
.delete-scrollbar {
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
}

.delete-scrollbar::-webkit-scrollbar {
  display: none;
}

.delete-scrollbar::-webkit-scrollbar-thumb {
  display: none;
}

.delete-scrollbar::-webkit-scrollbar-track {
  display: none;
}

.delete-scrollbar::-webkit-scrollbar-button {
  display: none;
}

.delete-scrollbar::-webkit-scrollbar-corner {
  display: none;
}

/**
 * @description 自定义滚动条样式
 * @supports 支持Webkit(Chrome/Safari/Edge)和Firefox浏览器
 * @usage 在需要自定义滚动条的元素上添加 .custom-scrollbar 类名
 */
.custom-scrollbar {
  /* 对于Firefox */
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

/* 对于Webkit浏览器(Chrome/Safari/Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 10px;
  transition: all 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/**
 * @description 隐藏滚动条但保留滚动功能
 * @usage 在需要隐藏滚动条但保留滚动功能的元素上添加 .hide-scrollbar 类名
 */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.sentence-selected-highlight {
  /* border: 1px solid #409eff; */
  /* background-color: #fff; */
  transition: background-color 0.2s ease;
  background-color: #e1f0f5;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
  /* 确保行内元素正确换行 */
  white-space: pre-wrap;
  padding: 4px 0;
  border-radius: 2px;
}

.dark .sentence-selected-highlight {
  background-color: #10507a;
}

/* 悬停在句子上高亮 */
.highlight-on-hover:hover {
  background-color: #f3f4f6;
  transition: background-color 0.2s ease;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
  /* 确保行内元素正确换行 */
  white-space: pre-wrap;
  padding: 4px 0;
  border-radius: 2px;
}

.dark .highlight-on-hover:hover {
  background-color: #1e3a5f;
}
/* 精挑翻译loading 线 */
.ai-select-loading-line {
  position: relative;
  background-image: linear-gradient(to right, #00c6ff 0%, #ff5e62 50%, #8a2be2 100%);
  background-repeat: repeat-x;
  background-size: 200% 0.0938rem;
  background-position: 0 100%;
  padding-bottom: 0.0938rem;
  animation: loadingAnimation 2s linear infinite;
}

@keyframes loadingAnimation {
  0% {
    background-position: 200% 100%;
  }
  100% {
    background-position: -200% 100%;
  }
}

/* 精挑翻译错误 */
.ai-select-error {
  border-bottom: 2px dashed #ff4d4f;
}

.error-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  position: relative;
}

.error-icon {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ff4d4f"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/></svg>');
  background-size: contain;
  cursor: pointer;
}

/* 自定义tooltip样式 */
.custom-error-tooltip {
  position: fixed;
  z-index: 100000;
  display: none;
  /* transform: translateX(-50%); */
  max-width: 300px;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.custom-error-tooltip-content {
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dark .custom-error-tooltip-content {
  background-color: rgb(31, 41, 55);
  color: #f3f4f6;
}

.custom-error-tooltip-arrow {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

.custom-error-tooltip-arrow.arrow-bottom {
  bottom: -12px;
  border-top-color: rgba(0, 0, 0, 0.75);
}

.dark .custom-error-tooltip-arrow.arrow-bottom {
  border-top-color: rgb(31, 41, 55);
}

.custom-error-tooltip-arrow.arrow-top {
  top: -12px;
  border-bottom-color: rgba(0, 0, 0, 0.75);
}

.dark .custom-error-tooltip-arrow.arrow-top {
  border-bottom-color: rgb(31, 41, 55);
}

/* 错误提示动画 */
@keyframes errorShake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

.ai-select-error .error-indicator {
  animation: errorShake 0.5s ease-in-out;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .error-icon:hover::after {
    color: rgba(0, 0, 0, 0.85);
  }
}

/* 小屏幕优化的浮动工具栏 */
@media (max-width: 768px) {
  .floating-toolbar {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 1000;
  }

  .floating-toolbar-button {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .dark .floating-toolbar-button {
    background-color: rgb(31, 41, 55);
    color: #f3f4f6;
  }

  /* 优化输入框在小屏幕的高度 */
  .textarea-container {
    min-height: 200px !important;
    max-height: 60vh !important;
  }

  /* 隐藏拖拽条 */
  .drag-handle {
    display: none !important;
  }

  /* 优化头部菜单高度 */
  .header-menus-compact {
    height: 2rem !important;
  }
}
