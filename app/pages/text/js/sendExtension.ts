/**
 * 向插件发送信息
 * window.postMessage 发送方式
 * 通过 window.addEventListener 监听到插件返回的数据
 */

/**
 * 封装获取插件翻译数据的方法
 */
export const getPluginTransData = () => {}

/**
 * 封装获取插件翻译请求
 */
export const getPluginTranslate = () => {
  /**
   * 模型翻译请求
   */
  const useTextTranslate = ({ originalText, translateEngine, toLanguage, fromLanguage, isSingleSentence, requestId }) => {
    window.postMessage(
      {
        type: 'textTranslate',
        originalText: toRaw(originalText),
        translateEngine: toRaw(translateEngine),
        toLanguage: toRaw(toLanguage),
        fromLanguage: toRaw(fromLanguage),
        isSingleSentence: toRaw(isSingleSentence),
        requestId: toRaw(requestId)
      },
      window.location.origin
    )
  }
  /**
   * 择优翻译请求
   */
  const useAiSelectTranslate = ({ multiTranslationEngineRequestResults, statusNodeGlobalId, isAiSelectAnalysis, toLanguage, interfaceLanguage, originalContent, paragraphId }) => {
    window.postMessage(
      {
        type: 'aiSelectTranslate',
        multiTranslationEngineRequestResults: toRaw(multiTranslationEngineRequestResults),
        statusNodeGlobalId: toRaw(statusNodeGlobalId),
        isAiSelectAnalysis: toRaw(isAiSelectAnalysis),
        toLanguage: toRaw(toLanguage),
        interfaceLanguage: toRaw(interfaceLanguage),
        originalContent: toRaw(originalContent),
        paragraphId: toRaw(paragraphId)
      },
      window.location.origin
    )
  }

  return {
    useTextTranslate,
    useAiSelectTranslate
  }
}

/**
 * 封装更新插件数据的方法
 */

const updatePluginData = () => {
  /**
   * 处理选择精挑翻译模型
   * 直接通知插件更新精挑翻译模型, 不需要返回数据
   * @param data 精挑翻译模型 {
   *  value: string;
   *  label: string;
   *  img: string;
   *  modelName: string;
   *  serviceMode: string;
   * }
   */
  const aiSelectTranslateModel = (data: { value: string }) => {
    window.postMessage(
      {
        type: 'updateAiSelectEngine',
        aiSelectModel: data.value
      },
      window.location.origin
    )
  }

  return {
    aiSelectTranslateModel
  }
}

/**
 * 封装获取插件翻译数据的方法
 */

export const useGetPluginTransData = getPluginTransData()

/**
 * 使用获取插件翻译数据的方法
 */
export const useGetPluginTranslate = getPluginTranslate()

/**
 * 使用更新插件数据的方法
 */
export const useUpdatePluginData = updatePluginData()
