/**
 * 择优翻译数据分析
 * 使用 window.postMessage 发送数据到 content.js 然后转发到 background.js 中做数据分析
 *
 * 统计配置项包括：
 * 1. 翻译模型：trans_engine
 * 2. 择优模型：aiSelectEngine
 * 3. 目标语言：target_language
 * 4. 原文语言：original_language
 * 5. 逐句择优：fullText(全文) 或 sentencePairs(逐句)
 * 6. 择优分析：isAiSelectAnalysis
 * 7. 翻译模式：translateMode (traditional: 传统翻译, compare: 对比翻译, aiSelect: AI择优翻译)
 * 8. 自动翻译：isAutoTranslate
 */

/**
 * 翻译引擎信息
 */
export interface TranslationEngine {
  /** 模型值 */
  modelValue: string
  /** 服务模式 */
  serviceMode: 'custom' | 'free' | 'plus'
}

/**
 * google 分析数据
 */
export interface AnalysisData {
  trans_engine?: TranslationEngine[] // 翻译模型
  aiSelect_engine?: string // 择优翻译模型
  target_language?: string // 目标语言
  original_language?: string // 原文语言
  sentenceMode?: 'fullText' | 'sentencePairs' // 逐句择优：fullText(全文) 或 sentencePairs(逐句)
  isAiSelectAnalysis?: boolean // 是否是择优翻译分析
  translateMode?: 'traditional' | 'compare' | 'aiSelect' // 翻译模式
  isAutoTranslate?: boolean // 是否自动翻译
  aiSelect_model_version: string // 择优翻译模型版本
}

export const sendDataToBackground = (data: AnalysisData) => {
  window.postMessage({
    type: 'dataAnalysis',
    name: 'Web_Translate_Text',
    data: {
      ...data
    }
  })
}
