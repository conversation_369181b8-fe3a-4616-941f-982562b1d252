// 检测语言
export const detectLanguage = (content, autoDetectText, locale, originalLanguageList) => {
  try {
    window.postMessage({ type: 'detectLanguage', text: content }, window.location.origin)

    // 监听消息
    const getDetectLanguage = (event) => {
      if (event.data.type === 'DETECT_LANGUAGE') {
        // 根据页面语言获得检测的语言国际化
        let lang = ''
        const data = event.data.data
        if (data === null) {
          return
        }

        if (locale === 'zhHans') {
          lang = data.zhs_name
        }
        else if (locale === 'zhHant') {
          lang = data.zht_name
        }
        else {
          lang = data.en_name
        }

        originalLanguageList.value[0].label = `${lang} (${autoDetectText})` // 修改这里,使用固定的格式
      }
      // 移除事件监听
      window.removeEventListener('message', getDetectLanguage)
    }

    // 监听消息(onmessage 只监听移除不需要移除)
    window.addEventListener('message', getDetectLanguage)
  }
  catch (error) {
    console.error('detect language error:', error)
  }
}
