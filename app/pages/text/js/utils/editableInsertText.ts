/**
 * !重要
 * 向可编辑元素中插入文本
 * @param {HTMLElement} element - 目标可编辑元素
 * @param {string} text - 要插入的文本内容
 * @param {boolean} isCursorInsertText - 是否在光标位置插入文本
 * @description
 * 该函数通过多种方法尝试向contenteditable元素中插入文本，
 * 特别针对富文本编辑器进行了优化处理：
 * 1. 正确处理光标位置和选区
 * 2. 支持在光标位置插入或替换选中内容
 * 3. 兼容各种富文本编辑器（如TinyMCE、CKEditor、Quill等）
 * 4. 触发必要的事件以确保编辑器状态同步
 */
export const contenteditableInsertText = (element, text, isCursorInsertText = false) => {
  if (!element || !text) {
    return false
  }

  // 确保元素获得焦点
  element.focus()

  // 获取当前选区
  const selection = window.getSelection()
  let range = null

  // 如果存在选区，使用当前选区；否则创建新的选区
  if (selection.rangeCount > 0) {
    range = selection.getRangeAt(0)
    // 确保选区在目标元素内
    if (!element.contains(range.commonAncestorContainer)) {
      range = createRangeAtEnd(element)
    }
  }
  else {
    range = createRangeAtEnd(element)
  }

  // 如果不是光标插入模式且没有选中文本，则选中所有内容
  if (!isCursorInsertText && selection.toString().trim() === '') {
    range.selectNodeContents(element)
    selection.removeAllRanges()
    selection.addRange(range)
  }

  // 定义多种插入方法，按优先级排序
  const methods = [
    // 方法1：使用现代的 insertText 命令（推荐）
    () => {
      if (document.queryCommandSupported && document.queryCommandSupported('insertText')) {
        const success = document.execCommand('insertText', false, text)
        if (success) {
          triggerInputEvents(element, text)
          return true
        }
      }
      return false
    },

    // 方法2：使用 Selection API 手动插入
    () => {
      if (!range) return false

      // 删除选中的内容
      range.deleteContents()

      // 创建文本节点
      const textNode = document.createTextNode(text)

      // 插入文本节点
      range.insertNode(textNode)

      // 将光标移动到插入文本的末尾
      range.setStartAfter(textNode)
      range.setEndAfter(textNode)
      selection.removeAllRanges()
      selection.addRange(range)

      triggerInputEvents(element, text)

      return true
    },

    // 方法3：使用 ClipboardEvent 模拟粘贴
    () => {
      try {
        const clipboard = new DataTransfer()
        clipboard.setData('text/plain', text)
        const pasteEvent = new ClipboardEvent('paste', {
          clipboardData: clipboard,
          bubbles: true,
          cancelable: true
        })

        const handled = element.dispatchEvent(pasteEvent)
        if (handled) {
          triggerInputEvents(element, text)
          return true
        }
      }
      catch (e) {
        console.log('ClipboardEvent 方法失败:', e)
      }
      return false
    },

    // 方法4：使用 InputEvent
    () => {
      try {
        const inputEvent = new InputEvent('beforeinput', {
          inputType: 'insertText',
          data: text,
          bubbles: true,
          cancelable: true
        })

        const beforeInputHandled = element.dispatchEvent(inputEvent)
        if (beforeInputHandled) {
          // 如果 beforeinput 事件被处理，触发 input 事件
          triggerInputEvents(element, text)
          return true
        }
      }
      catch (e) {
        console.log('InputEvent 方法失败:', e)
      }
      return false
    },

    // 方法5：直接操作 DOM（最后的备选方案）
    () => {
      try {
        if (!range) return false

        // 删除选中内容
        range.deleteContents()

        // 创建包含文本的文档片段
        const fragment = document.createDocumentFragment()
        const lines = text.split('\n')

        lines.forEach((line, index) => {
          if (index > 0) {
            fragment.appendChild(document.createElement('br'))
          }
          if (line) {
            fragment.appendChild(document.createTextNode(line))
          }
        })

        // 插入片段
        range.insertNode(fragment)

        // 移动光标到末尾
        range.collapse(false)
        selection.removeAllRanges()
        selection.addRange(range)

        triggerInputEvents(element, text)
        return true
      }
      catch (e) {
        console.log('DOM 操作方法失败:', e)
        return false
      }
    }
  ]

  // 依次尝试每种方法
  for (let i = 0; i < methods.length; i++) {
    try {
      const success = methods[i]()
      if (success) {
        console.log(`文本插入成功，使用方法 ${i + 1}`)
        return true
      }
    }
    catch (e) {
      console.log(`方法 ${i + 1} 执行失败:`, e)
    }
  }

  console.log('所有插入方法都失败了')
  return false
}

/**
 * 在元素末尾创建一个范围
 * @param {HTMLElement} element
 * @returns {Range}
 */
function createRangeAtEnd(element) {
  const range = document.createRange()
  range.selectNodeContents(element)
  range.collapse(false) // 折叠到末尾
  return range
}

/**
 * 触发必要的输入事件
 * @param {HTMLElement} element
 */
function triggerInputEvents(element: HTMLElement, insertedText: string = '') {
  const inputEvent = new InputEvent('input', {
    bubbles: true,
    cancelable: true,
    data: insertedText,
    inputType: 'insertText'
  })

  const event = new Event('change', { bubbles: true })

  element.dispatchEvent(inputEvent)
  element.dispatchEvent(event)
}
