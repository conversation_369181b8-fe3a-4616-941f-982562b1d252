// 监听悬停事件

class DynamicHighlighter {
  private roots: HTMLElement[]
  private observer: MutationObserver
  private attrNames: string[] // 存储属性名的数组
  private isActive: boolean = true // 跟踪是否激活
  private eventHandlers: { element: HTMLElement, type: string, handler: EventListener }[] = [] // 存储事件处理器

  // 构造函数
  constructor(elements: HTMLElement[]) {
    this.roots = elements // 存储多个根元素
    this.attrNames = ['selected-translate-id', 'selected-translate-result-id'] // 初始化属性名数组
    this.initObserver()
    this.bindGlobalEvents()
  }

  // 初始化DOM变化观察器
  initObserver() {
    this.observer = new MutationObserver((mutations) => {
      this.handleMutations(mutations)
    })

    // 为每个根元素添加观察器
    this.roots.forEach((root) => {
      this.observer.observe(root, {
        childList: true,
        subtree: true,
        attributes: false,
        characterData: false
      })
    })
  }

  // 全局事件绑定
  bindGlobalEvents() {
    // 为每个根元素绑定事件
    this.roots.forEach((root) => {
      // 鼠标悬停事件处理函数
      const handleMouseover = (e: Event) => {
        if (!this.isActive) return

        const target = e.target as HTMLElement

        for (const attrName of this.attrNames) {
          const element = target.closest(`[${attrName}]`) as HTMLElement | null
          if (element) this.highlightGroup(element, attrName)
        }
      }

      // 鼠标移出事件处理函数
      const handleMouseout = (e: Event) => {
        if (!this.isActive) return

        const target = e.target as HTMLElement

        for (const attrName of this.attrNames) {
          const element = target.closest(`[${attrName}]`) as HTMLElement | null
          if (element) this.clearHighlight(element, attrName)
        }
      }

      // 绑定事件并保存引用
      root.addEventListener('mouseover', handleMouseover)
      root.addEventListener('mouseout', handleMouseout)

      // 存储事件处理器信息，以便后续可以移除
      this.eventHandlers.push({ element: root, type: 'mouseover', handler: handleMouseover }, { element: root, type: 'mouseout', handler: handleMouseout })
    })
  }

  // 高亮处理逻辑
  highlightGroup(target: HTMLElement, attrName: string) {
    const id = target.getAttribute(attrName)

    // 查找匹配的ID，忽略属性名差异
    if (attrName === 'selected-translate-id') {
      // 当悬停在textarea元素上时，同时高亮results容器中的对应元素
      document.querySelectorAll(`[selected-translate-id="${id}"], [selected-translate-result-id="${id}"]`).forEach((el: Element) => {
        (el as HTMLElement).classList.add('highlight-on-hover')
      })
    }
    else {
      // 当悬停在results容器元素上时，同时高亮textarea中的对应元素
      document.querySelectorAll(`[selected-translate-result-id="${id}"], [selected-translate-id="${id}"]`).forEach((el: Element) => {
        (el as HTMLElement).classList.add('highlight-on-hover')
      })
    }
  }

  // 清除高亮
  clearHighlight(target: HTMLElement, attrName: string) {
    const id = target.getAttribute(attrName)

    // 清除所有匹配ID的元素高亮，忽略属性名差异
    if (attrName === 'selected-translate-id') {
      document.querySelectorAll(`[selected-translate-id="${id}"], [selected-translate-result-id="${id}"]`).forEach((el: Element) => {
        (el as HTMLElement).classList.remove('highlight-on-hover')
      })
    }
    else {
      document.querySelectorAll(`[selected-translate-result-id="${id}"], [selected-translate-id="${id}"]`).forEach((el: Element) => {
        (el as HTMLElement).classList.remove('highlight-on-hover')
      })
    }
  }

  // DOM变化处理（可选优化）
  handleMutations(mutations: MutationRecord[]) {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // 这里可以添加对新节点的特殊处理
      }
    })
  }

  // 停止悬停高亮功能
  stop() {
    if (!this.isActive) return

    // 停止观察DOM变化
    this.observer.disconnect()

    // 移除所有事件监听器
    this.eventHandlers.forEach(({ element, type, handler }) => {
      element.removeEventListener(type, handler)
    })

    // 清除所有现有高亮
    document.querySelectorAll('.highlight-on-hover').forEach((el: Element) => {
      (el as HTMLElement).classList.remove('highlight-on-hover')
    })

    this.isActive = false
  }

  // 重新启动悬停高亮功能
  start() {
    if (this.isActive) return

    // 重新初始化观察器
    this.initObserver()

    // 重新绑定事件
    this.bindGlobalEvents()

    this.isActive = true
  }
}

export const observerHoverHighlight = (elements: HTMLElement | HTMLElement[]) => {
  // 支持单个元素或数组
  const elementsArray = Array.isArray(elements) ? elements : [elements]
  return new DynamicHighlighter(elementsArray)
}
