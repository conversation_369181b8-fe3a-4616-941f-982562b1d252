/**
 * 文本处理工具 - 负责构建原文文本数组
 */
import type { TextProcessResult, ParagraphData, OriginalArray } from '@/types/translate'

/**
 * 构建原文文本列表
 * @param editor 编辑器元素
 * @returns 包含文本列表和是否已翻译的对象
 */
export function buildOriginalTextList(): TextProcessResult {
  const editorElement = document.querySelector('.editable-textarea')
  // console.log('调试日志-editorElement', editorElement);
  if (!editorElement) return { paragraphs: [], isTranslated: false, combinedText: '' }

  // 获取所有段落
  const paragraphs = editorElement.querySelectorAll('div[data-paragraph-id]')

  // 存储所有段落数据
  const paragraphDataList: ParagraphData[] = []
  // 存储所有文本合并后的内容
  let allTexts = ''

  // 遍历每个段落
  for (let i = 0; i < paragraphs.length; i++) {
    const paragraph = paragraphs[i] as HTMLDivElement
    const paragraphId = paragraph.getAttribute('data-paragraph-id') || ''
    const textItems: OriginalArray[] = []

    // 获取段落内所有span
    const spans = paragraph.querySelectorAll('span.container-source')

    // 处理有内容的span，无论段落是否为空
    if (spans.length > 0) {
      // 为每个span创建一条数据
      for (const span of spans) {
        const text = span.textContent || ''
        console.log('text', text)
        const id = span.getAttribute('selected-translate-id') || ''
        const hasBr = span.querySelector('br') !== null

        // 如果只有br标签没有文本内容
        if (hasBr && !text.trim()) {
          textItems.push({
            text: '<n0></n0>',
            statusNodeGlobalId: id,
            paragraphId
          })
          allTexts += '\n'
        }
        else if (text.trim()) {
          // 有文本内容的span，只取文本内容，忽略br标签
          textItems.push({
            text: text.trim(),
            statusNodeGlobalId: id,
            paragraphId
          })

          // 添加文本到合并内容中
          allTexts += text.trim()
        }
      }
    }

    // 只有当段落有内容时才添加到结果中
    if (textItems.length > 0) {
      paragraphDataList.push({
        items: textItems,
        paragraphId
      })

      // !如果段落内容不包含换行符，且不是最后一个段落，则添加换行符
      // 因为我们输入框已经重构成 使用 自动构建 dom 结构的，所有 一个 div 代表着 一个段落（占满一行的）
      // 所有这里需要判断 如果段落内容不包含换行符，且不是最后一个段落，则添加换行符
      if (!paragraph.innerText?.includes('\n') && i < paragraphs.length - 1) {
        allTexts += '\n'
      }
    }
  }

  return {
    paragraphs: paragraphDataList,
    isTranslated: false,
    combinedText: allTexts
  }
}

/**
 * 存储处理后的文本数据
 * @returns 处理后的文本数组
 */
export function storeProcessedTextData(): OriginalArray[] {
  const editorElement = document.querySelector('.editable-textarea')
  if (!editorElement) return []

  // 获取所有段落
  const paragraphs = editorElement.querySelectorAll('div[data-paragraph-id]')

  // 存储所有文本项
  const textItems: OriginalArray[] = []

  // 遍历每个段落
  for (let i = 0; i < paragraphs.length; i++) {
    const paragraph = paragraphs[i]
    const paragraphId = paragraph.getAttribute('data-paragraph-id') || ''

    // 获取段落内所有span
    const spans = paragraph.querySelectorAll('span.container-source')

    // 处理有内容的span
    if (spans.length > 0) {
      // 为每个span创建一条数据
      for (const span of spans) {
        const id = span.getAttribute('selected-translate-id') || ''
        const text = span.textContent || ''

        // 检查span是否包含br标签
        const hasBr = span.querySelector('br') !== null

        // 如果只有br标签没有文本内容
        if (hasBr && !text.trim()) {
          textItems.push({
            text: '<n0></n0>',
            statusNodeGlobalId: id,
            paragraphId
          })
        }
        else if (text.trim()) {
          // 有文本内容的span，只取文本内容，忽略br标签
          textItems.push({
            text: text.trim(),
            statusNodeGlobalId: id,
            paragraphId
          })
        }
      }
    }
  }

  return textItems
}
