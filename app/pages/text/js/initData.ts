import { useInputTranslate } from '@/store/inputTranslate'
import { translationState } from '@/pages/text/useData/useTranslationState'

// 共享状态
import { sharedState } from '@/pages/text/useData/sharedState'

import { useAiSelectModelList, useLanguageList, useModelList } from '@/composables/usePluginData'

// 翻译引擎状态管理
const { initTranslationEngines, translationEngineOptions } = translationState

// 发送消息给chrome端获取输入翻译数据
export const sendMessageToChromeExtension = async (locale) => {
  // 获取输入翻译数据
  const [modelList, languagesList, { aiSelectModel, aiSelectModelList }] = await Promise.all([useModelList(locale), useLanguageList(locale, 'microsoft'), useAiSelectModelList(locale)])

  // 使用输入翻译数据
  const useInputTransStore = useInputTranslate()

  // 择优模型列表
  const tmList = aiSelectModelList.map(item => ({
    label: item.label,
    value: item.value,
    modelVersion: item.modelVersion,
    modelName: item.modelName,
    serviceMode: item.serviceMode,
    serviceModeName: item.serviceModeName,
    isAvailable: true // 默认设置为可用
  }))

  // 传统翻译模型列表
  useInputTransStore.traditionalEngines = modelList.find(item => item.value === useInputTransStore.traditionalEngines.value)

  useInputTransStore.aiSelectModelList = tmList

  // 择优模型
  let selectedModel = tmList.find(item => item.value === aiSelectModel)

  // 如果在模型列表中没有找到当前选择的模型，则设置isAvailable为true
  if (!selectedModel && aiSelectModel) {
    selectedModel = {
      value: aiSelectModel,
      label: '',
      modelVersion: '',
      modelName: '',
      serviceMode: '',
      serviceModeName: '',
      isAvailable: false
    }
  }

  useInputTransStore.aiSelectModel = selectedModel

  // 原文语言集
  useInputTransStore.originalLanguageList = languagesList
  // 目标语言集- 去掉数组中的第一个就是目标语言集
  useInputTransStore.targetLanguageList = languagesList.slice(1)

  // 翻译引擎列表
  initTranslationEngines(modelList)

  // 获取当前可用的引擎ID列表
  const availableEngineIds = new Set(translationEngineOptions.value.map(engine => engine.value))

  // 如果可用引擎为空，则不进行过滤- 防止没有默认选中模型
  if (!availableEngineIds.size) {
    // 过滤掉不可用的翻译引擎
    useInputTransStore.enabledEnginesList = useInputTransStore.enabledEnginesList.filter(engine => availableEngineIds.has(engine))
  }

  // 设置共享状态
  sharedState.resetAllState()
}
