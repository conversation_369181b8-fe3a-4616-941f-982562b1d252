/**
 * 翻译模型权重配置
 * 权重范围: 0-1
 * 权重越高，在评分相同时排序越靠前
 * 权重值根据模型的实际能力进行差异化设置
 *
 * 顶级模型（9-10）
 * 次级模型（7-9）
 * 中端模型（5-7）
 * 低端模型（3-5）
 * 低级模型（0-3）
 */

// 翻译引擎权重配置- 根据模型版本和性能差异设置不同权重
export const engineWeights = {
  // 豆包系列
  'doubao-1.5-pro-32k': 0.85,
  'doubao-pro-32k': 0.81,
  'doubao-1.5-lite-32k': 0.82,
  'doubao-1.5-pro-256k': 0.87,

  // 智谱AI系列
  'glm-4-air': 0.7,
  'glm-4-airx': 0.7,
  'glm-4.5-flash': 0.7,

  // DeepSeek系列
  'deepseek-v3': 0.95,

  // OpenAI系列
  'ztsl-us-est-gpt-4o-mini': 0.9,
  'gpt-4o-mini': 0.9,
  'gpt-4o': 0.91,
  'gpt-4-turbo': 0.83,

  // Claude系列
  'Claude 3.5 Haiku': 0.89,
  'claude-3-5-sonnet-20241022': 0.89,
  'claude-3-haiku-20240307': 0.77,
  'claude-3-opus-20240229': 0.77,

  // gemini系列
  'gemini-2.0-flash': 0.92,
  'gemini-2.0-flash-lite': 0.92,
  'gemini-1.5-flash': 0.92,

  // 通义千问系列
  'qwen-plus': 0.88,
  'qwen-turbo': 0.88,
  'qwen-max': 0.93,

  // 零一万物系列
  'yi-lightning': 0.73
}

// 定义非大模型的llm 类型的权重
export const llmWeights = {
  // 其他翻译引擎 - 根据实际效果设置权重
  deepl: 0.8,
  google: 0.6,
  transmart: 0.66, // 腾讯交互翻译
  microsoft: 0.3 // 微软翻译
}

/**
 * 获取模型权重值
 * @param modelVersion 模型版本（如果有）
 * @param engineValue 翻译引擎值（用于免费模型）
 * @returns 对应的权重值，如果未找到则返回0.1作为默认值
 */
export function getModelWeight(modelVersion: string | null | undefined, engineValue: string): number {
  // TypeScript 类型安全检查
  const engineWeightKey = modelVersion as keyof typeof engineWeights
  const llmWeightKey = engineValue as keyof typeof llmWeights

  // 检查 modelVersion 权重
  if (modelVersion && engineWeights[engineWeightKey] !== undefined) {
    return engineWeights[engineWeightKey]
  }

  // 检查 engineValue 权重
  if (!modelVersion) {
    return llmWeights[llmWeightKey]
  }

  // 返回中性默认值（根据业务需求调整）
  return 0.5
}
