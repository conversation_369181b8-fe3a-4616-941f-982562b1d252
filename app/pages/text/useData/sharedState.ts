/**
 * 共享状态管理
 * 用于解决各组件之间的循环依赖问题
 */
import type { OriginalArray, AiSelectFinallyResultText, TextProcessResult } from '@/types/translate'
import type { HistoryModelOptions } from '@/types/history'

/**
 * 当前翻译的目标语言-原文语言
 */
type CurrentTranslateTargetLanguage = {
  targetLanguage: string
  originalLanguage: string
}

export type CurrentTranslateStatus = {
  currentTranslateTargetLanguage: CurrentTranslateTargetLanguage
  translateType: string
  currentModelList: string[]
  currentSelectedAiSelectModel: string
  isSingleSentence: boolean
  isAiSelectAnalysis: boolean
}
/**
 * 用户修改了择优翻译结果区域的选中模型，并且用户点击选中了其他模型的翻译结果就存储起来， 在SelectResults.vue 组件中使用
 */
type EditSelectedTransResult = {
  engine: string
  statusNodeGlobalId: string
  text: string
  modifiedModel: string
  paragraphId: string
}
/**
 * 共享状态对象
 * 包含所有需要在组件间共享的数据
 * 注意事项：1. 不要使用 reactive 响应式函数，只能使用 ref 作为响应式数据，不然无法做到多组件数据同步
 */
export const sharedState = {
  // 高亮句子的ID( 如果高亮的是全文, 那么就设置为 'combined', 如果高亮的是单句, 那么就设置为单句的ID)
  highlightId: ref<string>(''),

  // 原始文本
  sourceDOMStructure: ref<string>(''),

  // 原文文本数组
  originalTextList: ref<TextProcessResult>({
    paragraphs: [],
    combinedText: '',
    isTranslated: false
  }),

  // 择优翻译数据列表（原文数组）
  aiSelectTransList: ref<OriginalArray[]>([]),

  // 翻译状态-确保在多模型翻译请求的过程中不会进行精挑翻译
  isTranslateStatus: ref<boolean>(false),

  // 历史记录的 key 值, 通过这个找到对应历史记录条目的位置
  historyKey: ref<string>(''),

  /**
   * 择优翻译 loading
   * 开启翻译并且是择优翻译就开启loading
   */
  isAiSelectLoading: ref<boolean>(false),
  /**
   * 每个模型的展开状态
   * 这个数据结构是 { label: string; value: string }
   */
  modelCollapsed: ref<{ [key: string]: boolean }>({}),

  /**
   * 择优翻译结果的选中模型
   * 其他组件可以查看/监听这个 用户在择优翻译结果选中的模型
   * 这个数据结构是 { label: string; value: string }
   */
  selectedResultModel: ref<{ label: string, value: string }>({ label: '', value: '' }),

  /**
   * 重新择优不会进行翻译（不会重新翻译）
   */
  reSelect: ref<boolean>(false),

  /**
   * 重新翻译状态（会进行重新翻译）
   */
  reTrans: ref<boolean>(false),

  /**
   * 创建一个给历史记录存储的多模型配置项
   * 用户点击恢复历史的翻译记录的时候, 就可以用来存储数据,
   * 用于显示这次的翻译结果, 不会因为插件的模型配置来显示, 而是根据用户的历史记录来显示
   * 注意这个 historyModelOptions 跟 translationEngineOptions 是两个不同的数据结构
   * translationEngineOptions会根据插件的模型的配置来显示, 而 historyModelOptions 是根据用户的历史记录来显示
   */
  historyModelOptions: ref<HistoryModelOptions[]>([]),

  // 精挑翻译结果
  aiSelectFinallyResultText: ref<AiSelectFinallyResultText[]>([]),

  // 用户自定义翻译结果
  customResult: ref<AiSelectFinallyResultText[]>([]),

  // 添加响应式状态对象用于跟踪每个句子的选中模型
  selectedModelsStatus: ref<{ [key: string]: string }>({}),

  // 消息事件监听器
  messageEvent: ref({
    translateMessage: null as any,
    aiSelectTranslateMessage: null as any
  }),

  /**
   * 用户修改了择优翻译结果区域的选中模型，并且用户点击选中了其他模型的翻译结果就存储起来， 在SelectResults.vue 组件中使用
   */
  editSelectedTransResult: ref<EditSelectedTransResult>({
    engine: '',
    statusNodeGlobalId: '',
    text: '',
    modifiedModel: '',
    paragraphId: ''
  }),

  /**
   * 记录当前执行翻译的总状态
   * 执行一次翻译会记录本次翻译的基本翻译参数: 目标语言, 原文语言, 翻译类型, 翻译模型列表, 逐句翻译模式, 是否是精挑翻译分析
   */
  currentTranslateStatus: ref<CurrentTranslateStatus>({
    // 当前翻译的目标语言-原文语言
    currentTranslateTargetLanguage: {
      targetLanguage: '',
      originalLanguage: ''
    },
    // 记录当前执行的是什么模型(aiSelect/classic/compare)
    translateType: '',
    /**
     * 记录本次翻译的模型列表, 应用场景是： 用于切换到其他模型的时候， 会根据 currentModelList 来跟 当前已选中的模型做对比
     * 如果 currentModelList 跟 当前已选中的模型不一致, 那么就显示重新择优翻译/ 重新翻译
     */
    currentModelList: [], // 当前翻译的模型列表
    currentSelectedAiSelectModel: '', // 记录当前择优翻译使用的模型
    /**
     * 记录本次翻译使用的是什么模式（全文/逐句）
     * 全文翻译（false）, 单句翻译（true）
     */
    isSingleSentence: false,
    isAiSelectAnalysis: false
  }),

  /**
   * 使用一个响应式数据来存储当前翻译选中的择优翻译模型， 如果切换了就显示重新择优翻译
   * 点击逐句择优开关的时候如果这时候判断到 **当前翻译选中的择优模型** 跟 **当前存储中的模型** 不一致 **并且**
   * **当前翻译的的逐句翻译的状态**  跟 **当前存储的逐句翻译状态不一致；** 那么就显示重新择优翻译
   * 字符串类型
   */
  // 重置所有状态
  resetAllState() {
    this.highlightId.value = ''
    this.originalTextList.value = {
      paragraphs: [],
      combinedText: '',
      isTranslated: false
    }
    this.aiSelectTransList.value = []
    this.isTranslateStatus.value = false
    this.isAiSelectLoading.value = false
    this.historyModelOptions.value = []
    this.historyKey.value = ''
    this.aiSelectFinallyResultText.value = []
    this.customResult.value = []
    this.selectedModelsStatus.value = {}
    this.messageEvent.value.translateMessage = null
    this.messageEvent.value.aiSelectTranslateMessage = null
    this.reSelect.value = false
    this.reTrans.value = false
    this.modelCollapsed.value = {}
    this.currentTranslateStatus.value = {
      currentTranslateTargetLanguage: {
        targetLanguage: '',
        originalLanguage: ''
      },
      translateType: '',
      currentModelList: [],
      currentSelectedAiSelectModel: '',
      isSingleSentence: false,
      isAiSelectAnalysis: false
    }
    this.editSelectedTransResult.value = {
      engine: '',
      statusNodeGlobalId: '',
      text: '',
      modifiedModel: '',
      paragraphId: ''
    }
  }
}
