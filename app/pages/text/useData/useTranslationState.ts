import type { TranslationEngineState, TranslationEngine, ErrorInfo, RationaleInfo, ScoreInfo, SortIndexInfo, AiSelectTranslationStatus } from '@/types/translateOptions'
import { getTranslationEnginesConfig } from '@/utils/translationEnginesConfig'
import { TranslationStatus } from '@/types/translateOptions'
/**
 * !翻译状态管理组合式函数
 * !用于在多个组件之间共享翻译引擎状态
 * 其他组件直接调用就可以获取到模型数据， 也可以调用方法直接修改数据，因为使用了 ref 响应式原理
 */
export function useTranslationState() {
  // 使用 ref 创建一个响应式引用，可以在多个组件间共享
  const translationEngineOptions = ref<TranslationEngineState[]>([])

  // 初始化翻译引擎配置
  function initTranslationEngines(enabledEngines: TranslationEngine[]) {
    // 根据模型列表生成翻译引擎配置
    translationEngineOptions.value = getTranslationEnginesConfig(enabledEngines)
  }
  /**
   * 注意事项
   * 1. 以下遍历要使用 for 方法遍历， 不要使用forEach 遍历，因为性能没有 for 好
   */

  /**
   * 获取重置配置对象
   * 每次调用返回新的对象，避免引用问题
   */
  function getResetConfig() {
    return {
      translationsResult: [], // 翻译结果
      error: [], // 翻译错误信息
      score: [], // 择优评分
      rationale: [], // 择优分析
      status: TranslationStatus.NotTranslated, // 翻译状态
      sortIndexArray: [] // 排序数组
    }
  }

  // 翻译擎列表 翻译引擎选择
  const originalTranslationEngineList = computed(() => {
    const list = []
    for (const item of translationEngineOptions.value) {
      list.push({
        value: item.value,
        label: item.label,
        target: '_blank',
        modelName: item.modelName,
        serviceModeName: item.serviceModeName,
        serviceMode: item.serviceMode
      })
    }
    return list
  })

  /**
   * 更新翻译引擎状态
   * 该方法需要跟踪 方法sendExpandRequest ----> aiSelectTranslateReq方法的处理
   * status的值: 1: 未翻译 2: 翻译中 3: 翻译完成 4: 翻译失败, 5. 评分
   * 根据这个状态来显示是否需要loading显示、择优分析loading
   * @param engineList 翻译引擎列表
   * @param status 翻译状态
   */
  function updateTranslationEngineStatus(engineList, status: TranslationStatus) {
    for (const engine of engineList) {
      const translationEngine = translationEngineOptions.value.find(item => item.value === engine)
      if (translationEngine) {
        translationEngine.status = status
      }
    }
  }

  /**
   * 简化的翻译状态更新函数
   * 更方便地设置翻译状态，减少重复代码
   * @param status 翻译状态
   */
  function setStatus(status: TranslationStatus) {
    return engineList => updateTranslationEngineStatus(engineList, status)
  }

  // 预定义常用状态更新函数
  const setNotTranslated = setStatus(TranslationStatus.NotTranslated) // 未翻译
  const setTranslating = setStatus(TranslationStatus.Translating) // 翻译中
  const setCompleted = setStatus(TranslationStatus.Completed) // 翻译完成
  const setFailed = setStatus(TranslationStatus.Failed) // 翻译失败
  const setScoring = setStatus(TranslationStatus.Scoring) // 评分
  const setEmpty = setStatus(TranslationStatus.Empty) // 空

  // 清空精挑翻译结果
  function clearAiSelectTranslateResults() {
    for (const item of translationEngineOptions.value) {
      item.score = [] as ScoreInfo[] // 清除精挑评分
      item.rationale = [] as RationaleInfo[] // 清除精挑分析
      item.sortIndexArray = [] as SortIndexInfo[] // 添加这行，清除排序数组
      item.selectedTransStatus = [] as AiSelectTranslationStatus[] // 清除精挑翻译状态
    }
  }

  // 清空指定模型的择优翻译结果
  function clearSpecifiedAiSelectTranslateResults(engineValue: string) {
    for (const item of translationEngineOptions.value) {
      if (item.value === engineValue) {
        item.score = [] as ScoreInfo[] // 清除精挑评分
        item.rationale = [] as RationaleInfo[] // 清除精挑分析
        item.sortIndexArray = [] as SortIndexInfo[] // 添加这行，清除排序数组
      }
    }
  }

  /**
   * 清空翻译结果
   * 主要用于用户重新需要翻译的时候清楚所有模型的数据
   */
  function clearTranslationsContent() {
    for (const item of translationEngineOptions.value) {
      Object.assign(item, getResetConfig())
    }
  }

  /**
   * 清空指定的模型的所有数据
   * @param engineList 翻译引擎列表
   */
  function clearSpecifiedTranslationResults(engineList: string[]) {
    for (const engine of engineList) {
      const translationEngine = translationEngineOptions.value.find(item => item.value === engine)
      if (translationEngine) {
        Object.assign(translationEngine, getResetConfig())
      }
    }
  }

  return {
    translationEngineOptions,
    initTranslationEngines,
    originalTranslationEngineList,
    updateTranslationEngineStatus,
    clearTranslationsContent,
    clearAiSelectTranslateResults,
    clearSpecifiedAiSelectTranslateResults,
    clearSpecifiedTranslationResults,
    setStatus,
    setNotTranslated,
    setTranslating,
    setCompleted,
    setFailed,
    setScoring,
    setEmpty
  }
}

// 创建一个全局单例实例，确保在整个应用中共享相同的状态
export const translationState = useTranslationState()
