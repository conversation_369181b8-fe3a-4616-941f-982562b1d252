/**
 * 模型支持状态管理
 * 用于管理翻译引擎的语言支持状态
 * !其他组件直接调用就可以获取到模型数据， 也可以调用方法直接修改数据，因为使用了 ref 响应式原理
 */

const modelSupport = () => {
  const engineSupport = ref(new Map<string, boolean>()) // 存储每个引擎的语言支持状态
  const engineSupportCache = ref(new Map<string, string[]>()) // 用于缓存引擎支持的语言列表
  const showUnsupportedTip = ref(new Map<string, boolean>())

  /**
   * 更新引擎支持状态
   * @param engineList 启用的引擎列表
   * @param targetLang 目标语言
   */
  const updateEngineSupport = (engineList: string[], targetLang: string) => {
    for (const engine of engineList) {
      const supportedLanguages = engineSupportCache.value.get(engine)
      if (supportedLanguages) {
        engineSupport.value.set(engine, supportedLanguages.includes(targetLang))
      }
    }
  }

  /**
   * 设置引擎支持的语言列表
   * @param engineSupports 引擎支持的语言映射
   * @param targetLang 当前目标语言
   */
  const setEngineSupportCache = (engineSupports: Record<string, string[]>, targetLang: string) => {
    for (const [engineId, supportedLanguages] of Object.entries(engineSupports)) {
      engineSupportCache.value.set(engineId, supportedLanguages)
      engineSupport.value.set(engineId, supportedLanguages.includes(targetLang))
    }
  }

  /**
   * 显示不支持的提示
   * @param engineId 引擎ID
   */
  const showUnsupportedMessage = (engineId: string) => {
    showUnsupportedTip.value.set(engineId, true)
    setTimeout(() => {
      showUnsupportedTip.value.delete(engineId)
    }, 2000)
  }

  /**
   * 检查引擎是否支持目标语言
   * @param engineId 引擎ID
   */
  const isEngineSupported = (engineId: string): boolean => {
    return engineSupport.value.get(engineId) || false
  }

  /**
   * 清理引擎缓存
   * @param currentEngineIds 当前有效的引擎ID集合
   */
  const cleanupEngineCache = (currentEngineIds: Set<string>) => {
    // 清理所有缓存映射
    for (const map of [engineSupport.value, engineSupportCache.value, showUnsupportedTip.value]) {
      for (const key of Array.from(map.keys())) {
        if (!currentEngineIds.has(key)) map.delete(key)
      }
    }
  }

  return {
    engineSupport,
    engineSupportCache,
    showUnsupportedTip,
    updateEngineSupport,
    setEngineSupportCache,
    showUnsupportedMessage,
    isEngineSupported,
    cleanupEngineCache
  }
}

export const modelSupportState = modelSupport()
