/**
 * TODO 择优翻译-更新历史记录状态
 * !注意 indexedDB 不能直接存入 dom 元素节点， 需要转成outerHTML
 * 择优翻译历史记录状态管理
 */

import { useTranslationHistory } from '@/store/indexedDB/selectTransHistory'
import type { TranslationHistory, AiSelectSetting, InputDom } from '@/types/history'
import type { AiSelectFinallyResultText, TranslateMode } from '@/types/translate'
import { sharedState, type CurrentTranslateStatus } from './sharedState'

export const useTranslationHistoryState = () => {
  const { initDB, addHistory, updateHistoryFields, getHistoryByKey, getAllHistory, deleteHistory, clearAllHistory, generateHistoryKey, getHistoryByPage, getHistoryByTypeAndPage }
    = useTranslationHistory()

  const { historyKey, historyModelOptions } = sharedState

  // 是否正在保存历史记录
  const isHistorySaving = ref(false)
  // 历史记录列表
  const historyList = ref<TranslationHistory[]>([])
  // 是否已加载历史记录
  const isHistoryLoaded = ref(false)
  // 当前加载的翻译类型
  const currentTranslateType = ref<string | null>(null)

  // 针对单个模型进行清除数据
  function clearSingleModelHistoryData(value: string) {
    historyModelOptions.value = historyModelOptions.value.filter(item => item.value !== value)
  }

  // 内容标准化函数
  function normalizeText(text: string): string {
    return (text || '').replace(/\s+/g, '').trim()
  }
  // 模型标准化函数
  function normalizeModel(value: any): string {
    return String(value || '').trim()
  }

  // 初始化历史记录
  async function initHistory() {
    try {
      await initDB()
      await loadAllHistory()
      return true
    }
    catch (error) {
      console.error('初始化历史记录失败:', error)
      return false
    }
  }

  /**
   * 如果重新翻译的话清除一次 1. multiModelTranslationOptions
   */
  async function clearHistoryTransData() {
    const history = await getHistoryByKey(historyKey.value)
    // console.log('清除之前的历史记录数据', history);
    if (history) {
      history.multiModelTranslationOptions = []
      await updateHistoryFields(historyKey.value, { multiModelTranslationOptions: [] })
    }
  }

  /**
   * 如果重新翻译的话清除一次 1. aiSelectTranslationResult
   */
  async function clearHistoryAiSelectData() {
    const history = await getHistoryByKey(historyKey.value)
    if (history) {
      history.aiSelectTranslationResult = []
      await updateHistoryFields(historyKey.value, { aiSelectTranslationResult: [] })
    }
  }

  // 加载所有历史记录
  async function loadAllHistory() {
    try {
      historyList.value = await getAllHistory()
      isHistoryLoaded.value = true
      return historyList.value
    }
    catch (error) {
      console.error('加载历史记录失败:', error)
      return []
    }
  }

  /**
   * 分页加载历史记录
   * @param page 页码，从1开始
   * @param pageSize 每页数量
   * @returns 当前页历史记录和是否有更多数据
   */
  async function loadHistoryByPage(page: number, pageSize: number) {
    try {
      const { data, total } = await getHistoryByPage(page, pageSize)

      // 如果是第一页，先清空历史记录列表
      if (page === 1) {
        historyList.value = data
      }
      else {
        // 追加到现有列表
        historyList.value = [...historyList.value, ...data]
      }

      // 计算是否还有更多数据
      const hasMore = historyList.value.length < total

      // 标记历史记录已加载
      if (page === 1) {
        isHistoryLoaded.value = true
      }

      return {
        data,
        hasMore,
        total
      }
    }
    catch (error) {
      console.error('分页加载历史记录失败:', error)
      return {
        data: [],
        hasMore: false,
        total: 0
      }
    }
  }

  /**
   * 根据翻译类型分页加载历史记录
   * @param page 页码，从1开始
   * @param pageSize 每页数量
   * @param translateType 翻译类型
   * @returns 当前页历史记录和是否有更多数据
   */
  async function loadHistoryByTypeAndPage(page: number, pageSize: number, translateType: string) {
    try {
      currentTranslateType.value = translateType

      const { data, total } = await getHistoryByTypeAndPage(page, pageSize, translateType)

      // 如果是第一页，先清空历史记录列表
      if (page === 1) {
        historyList.value = data
      }
      else {
        // 追加到现有列表
        historyList.value = [...historyList.value, ...data]
      }

      // 计算是否还有更多数据
      const hasMore = historyList.value.length < total

      // 标记历史记录已加载
      if (page === 1) {
        isHistoryLoaded.value = true
      }

      return {
        data,
        hasMore,
        total
      }
    }
    catch (error) {
      console.error('按翻译类型分页加载历史记录失败:', error)
      return {
        data: [],
        hasMore: false,
        total: 0
      }
    }
  }

  // 开始新的翻译会话
  async function startNewTranslation(sourceDOMStructure: string, sourceText: string, translateType: TranslateMode, modelValue?: string) {
    // 只对传统翻译做去重
    if (translateType === 'traditional') {
      // 加载所有历史记录
      const allHistory = await getAllHistory()
      const normText = normalizeText(sourceText)
      const normModel = normalizeModel(modelValue)
      // 查找是否有相同内容和模型的记录
      const exists = allHistory.some(
        item => item.translateType === 'traditional' && normalizeText(item.sourceText) === normText && normalizeModel(item.multiModelTranslationOptions?.[0]?.value) === normModel
      )
      if (exists) {
        // 已存在则不再存储，直接返回空字符串
        return ''
      }
    }
    // 生成唯一的历史记录 key
    historyKey.value = generateHistoryKey()

    // 创建初始历史记录
    const initialHistory: TranslationHistory = {
      key: historyKey.value, // 历史记录 key
      sourceDOMStructure, // 原始文本
      sourceText, // 原始文本
      timestamp: Date.now(), // 时间戳
      translateType, // 翻译类型
      multiModelTranslationOptions: modelValue ? [{ value: modelValue, label: '', modelVersion: '', modelName: '', translationsResult: [] }] : [], // 多模型翻译选项
      showModelList: [] // 显示模型列表
    }

    try {
      await addHistory(initialHistory)
      return historyKey.value
    }
    catch (error) {
      console.error('创建翻译历史记录失败:', error)
      return ''
    }
  }
  /**
   * 更新多模型翻译结果
   * !注意 indexedDB 不能直接存入 dom 元素节点， 需要转成outerHTML
   * @param {Array} translationEngineOptions - 翻译引擎选项数组
   * @param {string[]} enabledEnginesList - 启用的翻译引擎列表
   * @returns {Promise<boolean>} - 更新成功返回true，失败返回false
   */
  async function updateMultiModelTranslationResult(key: string, translationEngineOptions, enabledEnginesList: string[], currentTranslateStatus: CurrentTranslateStatus) {
    // console.log('更新多模型翻译结果', {
    //   key,
    //   translationEngineOptions,
    //   enabledEnginesList,
    //   showModelList
    // });
    try {
      const history = await getHistoryByKey(key)
      if (!history) return false

      // 确保 multiModelTranslationOptions 数组已初始化
      if (!history.multiModelTranslationOptions) {
        history.multiModelTranslationOptions = []
      }

      for (const item of translationEngineOptions) {
        // 只有在enabledEnginesList中存在的引擎才会被添加到历史记录中
        if (!enabledEnginesList.includes(item.value)) continue

        // 构建存储MultiModelTranslationOptions 数据
        const multiModelTranslationOptions = {
          label: item.label,
          value: item.value,
          modelVersion: item.modelVersion || '',
          modelName: item.modelName || '',
          translationsResult: toRaw(item.translationsResult) // 翻译结果
        }

        // 检查历史记录中是否已存在该模型
        const existingModelIndex = history.multiModelTranslationOptions.findIndex(option => option.value === item.value)
        if (existingModelIndex === -1) {
          // 如果不存在,则添加新的模型
          history.multiModelTranslationOptions.push(multiModelTranslationOptions)
        }
        else {
          // 如果已存在,则更新现有模型的数据
          history.multiModelTranslationOptions[existingModelIndex] = multiModelTranslationOptions
        }
      }

      // 检查并更新showModelList
      for (const model of currentTranslateStatus.currentModelList) {
        const existingShowModelIndex = history.showModelList.findIndex(m => m.value === model.value)
        if (existingShowModelIndex === -1) {
          // 如果不存在,则添加新的显示模型
          history.showModelList.push(toRaw(model))
        }
        else {
          // 如果已存在,则更新现有显示模型数据
          history.showModelList[existingShowModelIndex] = toRaw(model)
        }
      }

      /**
       * 1.如果翻译类型不是择优翻译
       * 2.如果是否所有的翻译结果为空 就清空历史记录
       */
      if (currentTranslateStatus.translateType !== 'aiSelect' && history.multiModelTranslationOptions.every(option => option.translationsResult.length === 0)) {
        await removeHistory(key)
        return false
      }

      await updateHistoryFields(key, {
        showModelList: history.showModelList,
        multiModelTranslationOptions: history.multiModelTranslationOptions
      })

      return true
    }
    catch (error) {
      return false
    }
  }

  /**
   * 更新择优翻译的相关数据：1. 评分、分析、排序 id、输入框的 dom 树
   * @param {string} key - 历史记录的唯一标识
   * @returns {Promise<boolean>} - 更新成功返回true，失败返回false
   */
  async function updateAiSelectTranslationHistoryData(key: string, data) {
    try {
      // console.log('择优翻译历史记录数据', data);

      const history = await getHistoryByKey(key)

      if (!history) return false

      if (data.length > 0) {
        for (const { score, rationale, sortIndexArray, value } of data) {
          const scoreData = toRaw(score)
          const rationaleData = toRaw(rationale)
          const sortIndexArrayData = toRaw(sortIndexArray)

          // 找到匹配的模型选项并更新数据
          for (const item of history.multiModelTranslationOptions) {
            if (item.value === value) {
              // 确保所有属性存在后再设置值
              item.score = scoreData
              item.rationale = rationaleData
              item.sortIndexArray = sortIndexArrayData
            }
          }
        }
      }

      // console.log('更新择优翻译历史记录数据', history.multiModelTranslationOptions);

      await updateHistoryFields(key, {
        multiModelTranslationOptions: history.multiModelTranslationOptions
      })
      return true
    }
    catch (error) {
      console.error('更新择优翻译结果失败:', error)
      return false
    }
  }

  /**
   * 更新择优翻译结果
   * @param {string} key - 历史记录键
   * @param {AiSelectFinallyResultText} data - 择优翻译结果数据
   * @returns {Promise<boolean>} - 操作成功返回true，失败返回false
   */
  async function updateAiSelectTranslationResult(key: string, data: AiSelectFinallyResultText) {
    return new Promise(async (resolve, reject) => {
      try {
        const history = await getHistoryByKey(key)

        if (!history) return false

        // 构建要存储的数据对象
        const resultItem = {
          statusNodeGlobalId: toRaw(data.statusNodeGlobalId),
          text: toRaw(data.text),
          engine: toRaw(data.engine),
          paragraphId: toRaw(data.paragraphId)
        }

        // 确保aiSelectTranslationResult数组已初始化
        if (!history.aiSelectTranslationResult) {
          history.aiSelectTranslationResult = []
        }

        // 检查是否已存在相同statusNodeGlobalId的记录
        const existingIndex = history.aiSelectTranslationResult.findIndex(item => item.statusNodeGlobalId === data.statusNodeGlobalId)

        if (existingIndex !== -1) {
          // 如果已存在，则更新该记录
          history.aiSelectTranslationResult[existingIndex] = resultItem
        }
        else {
          // 如果不存在，则添加新记录
          history.aiSelectTranslationResult.push(resultItem)
        }

        // 更新历史记录
        await updateHistoryFields(key, {
          aiSelectTranslationResult: toRaw(history.aiSelectTranslationResult)
        })
        resolve(true)
      }
      catch (error) {
        console.error('更新择优翻译结果失败:', error)
        reject(false)
      }
    })
  }

  // 更新用户自定义翻译结果
  async function updateUserCustomTranslationResult(key: string, data: AiSelectFinallyResultText[]) {
    return new Promise(async (resolve, reject) => {
      try {
        const history = await getHistoryByKey(key)

        if (!history) return false

        if (!history.userCustomTranslationResult) {
          history.userCustomTranslationResult = []
        }
        const userCustomTranslationResult = []

        for (const item of data) {
          // 构建要存储的数据对象
          const resultItem = {
            statusNodeGlobalId: toRaw(item.statusNodeGlobalId),
            text: toRaw(item.text),
            engine: toRaw(item.engine),
            paragraphId: toRaw(item.paragraphId)
          }

          userCustomTranslationResult.push(resultItem)
        }

        await updateHistoryFields(key, {
          userCustomTranslationResult: toRaw(userCustomTranslationResult)
        })

        resolve(true)
      }
      catch (error) {
        console.error('更新用户自定义翻译结果失败:', error)
        reject(false)
      }
    })
  }

  /**
   * 更新择优设置
   * @param key
   * @param data
   */
  async function updateAiSelectSetting(key: string, data: AiSelectSetting) {
    try {
      const history = await getHistoryByKey(key)
      if (!history) return false

      history.aiSelectSetting = data
      await updateHistoryFields(key, {
        aiSelectSetting: {
          isSingleSentenceAiSelect: data.isSingleSentenceAiSelect,
          aiSelectModel: toRaw(data.aiSelectModel),
          isAiSelectAnalysis: data.isAiSelectAnalysis
        }
      })
      return true
    }
    catch (error) {
      console.error('更新择优设置失败:', error)
      return false
    }
  }
  // 删除单个历史记录
  async function removeHistory(key: string) {
    try {
      await deleteHistory(key)

      if (historyKey.value === key) {
        historyKey.value = ''
      }

      await loadAllHistory()
      return true
    }
    catch (error) {
      console.error('删除历史记录失败:', error)
      return false
    }
  }

  // 清空所有历史记录
  async function clearHistory() {
    try {
      await clearAllHistory()
      historyList.value = []
      historyKey.value = ''
      return true
    }
    catch (error) {
      console.error('清空历史记录失败:', error)
      return false
    }
  }

  return {
    // 状态
    isHistorySaving,
    historyList,
    isHistoryLoaded,
    currentTranslateType,

    // 方法
    clearSingleModelHistoryData,
    initHistory,
    clearHistoryTransData,
    clearHistoryAiSelectData,
    loadAllHistory,
    loadHistoryByPage,
    loadHistoryByTypeAndPage,
    startNewTranslation,
    updateMultiModelTranslationResult,
    updateAiSelectTranslationHistoryData,
    updateAiSelectSetting,
    updateAiSelectTranslationResult,
    updateUserCustomTranslationResult,
    removeHistory,
    clearHistory
  }
}
