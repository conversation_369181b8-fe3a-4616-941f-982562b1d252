<template>
  <div class="flex w-full items-center justify-between gap-2">
    <!-- 左侧操作区域 -->
    <div class="flex min-w-0 flex-1 items-center">
      <!-- 原文语言选择 -->
      <USelectMenu
        v-model="originalLanguage"
        value-key="zing_code"
        searchable
        size="md"
        variant="ghost"
        :ui="{ content: 'w-full' }"
        :items="originalLanguageList"
        class="w-42 min-w-0 flex-shrink cursor-pointer text-sm text-neutral-500"
        @update:model-value="handleOriginalHandoff"
      />

      <!-- 语言切换按钮 -->
      <UButton
        color="neutral"
        variant="ghost"
        icon="i-icon-park-outline-switch"
        size="sm"
        class="mx-2 flex-shrink-0 text-neutral-500"
        :disabled="originalLanguage === 'auto'"
        @click="swapLanguages"
      />

      <!-- 目标语言选择 -->
      <USelectMenu
        v-model="targetLanguage"
        value-key="zing_code"
        searchable
        size="md"
        variant="ghost"
        :ui="{ content: 'w-full' }"
        :items="targetLanguageList"
        class="w-42 min-w-0 flex-shrink cursor-pointer text-sm text-neutral-500"
        @update:model-value="handoffTargetHandoff"
      />
    </div>
    <!-- 右侧操作区域 -->
    <div v-show="!isEmpty" class="flex items-center justify-end">
      <!-- 输入框右侧操作区域 -->
      <UTooltip :text="t('selected_trans.operation.restore')" :popper="{ placement: 'right', strategy: 'fixed' }">
        <!-- 恢复原始内容 -->
        <UButton
          v-if="sourceDOMStructure"
          color="neutral"
          variant="ghost"
          active-color="neutral"
          size="sm"
          icon="i-mdi-restore"
          active-variant="ghost"
          class="rounded-full text-neutral-500"
          :ui="{
            leadingIcon: 'size-5'
          }"
          @click="$emit('restore')"
        />
      </UTooltip>

      <!-- 清空输入框 -->
      <UButton
        active
        color="neutral"
        variant="outline"
        active-color="neutral"
        active-variant="ghost"
        size="sm"
        icon="i-solar-close-circle-linear"
        class="rounded-full text-neutral-500"
        :ui="{
          leadingIcon: 'size-5'
        }"
        @click="$emit('clear')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInputTranslate } from '@/store/inputTranslate'

// 获取翻译方法
import { sharedState } from '@/pages/text/useData/sharedState'
import { useTranslate } from '@/pages/text/translate/translate'

const { originalTextList, reTrans, currentTranslateStatus, sourceDOMStructure } = sharedState

const { buildOriginalTextListSendRequest } = useTranslate

// 国际化
const { t } = useI18n()

// 获取翻译状态
const useInputTransStore = useInputTranslate()
const { originalLanguage, targetLanguage, originalLanguageList, targetLanguageList, currentTranslateMode, isAutoTranslate } = storeToRefs(useInputTransStore)

const emit = defineEmits(['restore', 'clear'])

defineProps({
  isEmpty: {
    type: Boolean,
    default: false
  }
})

/**
 * 切换原文语言
 * @param value 原文语言编码
 */
function handleOriginalHandoff(value: string) {
  useInputTransStore.originalLanguage = value
  if (originalLanguage.value !== 'auto') {
    // 填充当前自动检测出的原文语言 - (自动检测)
    useInputTransStore.originalLanguageList[0].label = t('selected_trans.auto_detect')
  }

  // 切换语言的时候重新翻译
  if (originalTextList.value.isTranslated && currentTranslateMode.value === 'traditional' && isAutoTranslate.value) {
    buildOriginalTextListSendRequest()
    return
  }

  // 重新翻译
  if (originalTextList.value.isTranslated && !isAutoTranslate.value) {
    reTrans.value = true
  }

  // 重新翻译
  if (originalTextList.value.isTranslated && currentTranslateStatus.value.currentTranslateTargetLanguage.originalLanguage === originalLanguage.value) {
    reTrans.value = false
  }
  else if (originalTextList.value.isTranslated && currentTranslateStatus.value.currentTranslateTargetLanguage.originalLanguage !== originalLanguage.value) {
    reTrans.value = true
  }
}

/**
 * 切换目标语言
 * @param value 目标语言
 */
function handoffTargetHandoff(value: string) {
  // 更新本地存储目标语言
  useInputTransStore.targetLanguage = value

  targetLanguage.value = value

  // 切换语言的时候重新翻译
  if (originalTextList.value.isTranslated && currentTranslateMode.value === 'traditional' && isAutoTranslate.value) {
    buildOriginalTextListSendRequest()
    return
  }

  if (originalTextList.value.isTranslated && currentTranslateMode.value !== 'aiSelect' && !isAutoTranslate.value) {
    reTrans.value = true
  }

  // 重新翻译
  if (originalTextList.value.isTranslated && currentTranslateStatus.value.currentTranslateTargetLanguage.targetLanguage === targetLanguage.value) {
    reTrans.value = false
  }
  else if (originalTextList.value.isTranslated && currentTranslateStatus.value.currentTranslateTargetLanguage.targetLanguage !== targetLanguage.value) {
    reTrans.value = true
  }
}

// 交换源语言和目标语言
const swapLanguages = () => {
  // 如果源语言是自动检测，则不进行交换
  if (originalLanguage.value === 'auto') return

  const temp = originalLanguage.value
  useInputTransStore.setOriginalLanguage(targetLanguage.value)
  useInputTransStore.setTargetLanguage(temp)
}
</script>

<style scoped>
.setting-select {
  position: relative;
  z-index: 10;
}
.flex-shrink {
  flex-shrink: 1;
  min-width: 0;
}
.min-w-\[120px\] {
  min-width: 120px;
}
</style>
