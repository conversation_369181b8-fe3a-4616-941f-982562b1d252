/**
 * 句子拆分和文本分析工具
 */
import { splitMultilingualSentences } from '@/utils/utils'
import { createTextSpan, createStyledTextSpan } from './dom-utils'

/**
 * 分析文本内容，检测是否包含特殊样式
 * @param text 要分析的文本
 * @returns 包含分析结果的对象
 */
export function analyzeTextContent(text: string): { isSpecial: boolean, text: string } {
  // 检测是否为版本号、代码片段或其他特殊内容
  const isSpecial
    = /^v\d+\.\d+\.\d+/.test(text) // 版本号，如 v24.0.11
      || /^\w+\.\w+/.test(text) // 代码片段，如 Node.js
      || /^[A-Z][\w-]+\s*\([^)]*\)/.test(text) // API/函数调用格式

  return {
    isSpecial,
    text
  }
}

/**
 * 拆分段落中的句子
 * @param paragraphText 段落文本
 * @returns 文档片段，包含多个span元素
 */
export function splitParagraphToSentences(paragraphText: string): DocumentFragment {
  const fragment = document.createDocumentFragment()

  if (!paragraphText.trim()) {
    return fragment
  }

  try {
    // 使用工具函数拆分句子
    const sentences = splitMultilingualSentences(paragraphText)

    sentences.forEach((sentence, index) => {
      // 分析文本内容
      const analysis = analyzeTextContent(sentence.trim())

      // 创建span
      let span
      if (analysis.isSpecial) {
        // 特殊内容，添加背景色
        span = createStyledTextSpan(analysis.text, '')
      }
      else {
        span = createTextSpan(analysis.text)
      }

      // 添加到片段
      fragment.appendChild(span)

      // 如果不是最后一个句子，添加空格
      if (index < sentences.length - 1) {
        fragment.appendChild(document.createTextNode(' '))
      }
    })
  }
  catch (error) {
    console.error('句子拆分错误:', error)
    // 降级处理，返回单个span
    const span = createTextSpan(paragraphText)
    fragment.appendChild(span)
  }

  return fragment
}

/**
 * 标准化粘贴内容的换行符
 * @param content 要处理的文本
 * @returns 分割后的段落数组
 */
export function normalizeLineBreaks(content: string): string[] {
  const lines = content
    .replace(/(\r\n|\r)/g, '\n') // 统一换行符
    .split('\n')
    .map(paragraph => paragraph.trim() || '')

  // 去除首部空行，但保留尾部空行
  let startIndex = 0
  const endIndex = lines.length - 1

  // 找到第一个非空行
  while (startIndex <= endIndex && lines[startIndex] === '') {
    startIndex++
  }

  // 提取有效内容行
  const result = startIndex <= endIndex ? lines.slice(startIndex, endIndex + 1) : []

  // 确保至少返回一个空字符串项，以便创建至少一个段落
  if (result.length === 0) {
    return ['']
  }

  return result
}
