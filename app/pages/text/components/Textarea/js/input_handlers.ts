/**
 * 输入事件处理
 */
import { undoManager } from './render-dom'
import { insertNewParagraph } from './insert_new_paragraph'
import { ensureEditorHasContent, isLastElementNotDeletable, placeCursorInParagraph } from './dom-utils'
import { paragraphIdManager } from './paragraph-id-manager'

// 添加标志来跟踪撤销/重做操作状态
let isUndoRedoOperation = false

/**
 * 设置输入事件处理程序
 */
export function setupInputHandlers(editor: HTMLElement): void {
  // 处理按键事件
  editor.addEventListener(
    'keydown',
    (e: KeyboardEvent) => {
      // shift + enter 换行
      if (e.shiftKey && e.key === 'Enter') {
        // 检查是否正在使用输入法输入
        const isComposing = e.isComposing || (e as any).keyCode === 229

        // 如果正在使用输入法输入，则不执行后续操作
        if (isComposing) {
          return
        }

        // 阻止默认的换行行为，因为我们自定义了段落拆分逻辑
        e.preventDefault()

        // 记录操作前状态
        // undoManager.captureState(editor, false);

        // Shift + Enter：在段落中间拆分段落

        // 插入新段落
        const activeParagraph = insertNewParagraph(editor)

        // 记录操作后状态
        requestAnimationFrame(() => {
          // !重要：将光标滚动到视图中，否则光标会停留在原段落,超长文本光标消失
          if (activeParagraph) {
            activeParagraph.scrollIntoView({ block: 'nearest' })
          }

          // undoManager.captureState(editor, false);
        })

        return // 添加 return 避免继续执行后面的 Enter 处理逻辑
      }

      // enter 换行
      if (e.key === 'Enter') {
        // 检查是否正在使用输入法输入
        const isComposing = (e as any).isComposing === true

        // 如果正在使用输入法输入，则不执行后续操作
        if (isComposing) {
          return
        }

        // 阻止默认的段落插入行为，因为我们有自定义的逻辑
        e.preventDefault()

        // 记录操作前状态
        // undoManager.captureState(editor, false);

        const activeParagraph = insertNewParagraph(editor)

        // 记录操作后状态
        requestAnimationFrame(() => {
          // !重要：将光标滚动到视图中，否则光标会停留在原段落,超长文本光标消失
          if (activeParagraph) {
            activeParagraph.scrollIntoView({ block: 'nearest' })
          }

          // undoManager.captureState(editor, false);
        })
      }

      // 撤销操作 Ctrl+Z (Windows) 或 Cmd+Z (Mac)
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault()
        isUndoRedoOperation = true
        undoManager.undo(editor)
        setTimeout(() => {
          isUndoRedoOperation = false
        }, 100)

        // 撤销后ID已在undo-manager中强制重排序，无需再次调用
      }

      // 重做操作 Ctrl+Shift+Z (Windows) 或 Cmd+Shift+Z (Mac)
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && e.shiftKey) {
        e.preventDefault()
        isUndoRedoOperation = true
        undoManager.redo(editor)
        setTimeout(() => {
          isUndoRedoOperation = false
        }, 100)

        // 重做后ID已在undo-manager中强制重排序，无需再次调用
      }

      // 重做操作的另一种快捷键 Ctrl+Y (Windows)
      if (e.ctrlKey && e.key === 'y') {
        e.preventDefault()
        isUndoRedoOperation = true
        undoManager.redo(editor)
        setTimeout(() => {
          isUndoRedoOperation = false
        }, 100)

        // 重做后ID已在undo-manager中强制重排序，无需再次调用
      }
    },
    true
  ) // 使用capture确保事件被优先处理

  // 输入前拦截
  editor.addEventListener('beforeinput', (e: InputEvent) => {
    // 检查删除操作是否会删除最后一个元素节点
    if (e.inputType === 'deleteContentBackward' || e.inputType === 'deleteContentForward' || e.inputType === 'deleteByCut' || e.inputType === 'deleteByDrag') {
      // 检查是否为不可删除的最后一个元素节点
      if (isLastElementNotDeletable(editor)) {
        e.preventDefault() // 阻止默认删除行为
        console.log('阻止删除最后一个元素节点')
        return
      }
    }

    if (e.inputType === 'historyUndo') {
      // 拦截浏览器原生的撤销操作
      isUndoRedoOperation = true
      undoManager.undo(editor)
      setTimeout(() => {
        isUndoRedoOperation = false
      }, 100)

      // 撤销后ID已在undo-manager中强制重排序，无需再次调用
    }
    else if (e.inputType === 'historyRedo') {
      // 拦截浏览器原生的重做操作
      isUndoRedoOperation = true
      undoManager.redo(editor)
      setTimeout(() => {
        isUndoRedoOperation = false
      }, 100)

      // 重做后ID已在undo-manager中强制重排序，无需再次调用
    }
    else if (e.inputType === 'insertText' || e.inputType === 'deleteContentBackward' || e.inputType === 'deleteContentForward') {
      // 对于删除操作，立即记录操作前状态（不使用节流）
      if (e.inputType === 'deleteContentBackward' || e.inputType === 'deleteContentForward') {
        // undoManager.captureState(editor, false);
      }
    }
  })

  // 监听输入法开始事件
  editor.addEventListener('compositionstart', (_) => {
    editor.setAttribute('data-composing', 'true')
  })

  // 输入法结束事件
  editor.addEventListener('compositionend', (_) => {
    setTimeout(() => {
      editor.setAttribute('data-composing', 'false')

      // 保存当前光标位置信息
      const selection = window.getSelection()
      let targetParagraph: HTMLElement | null = null

      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        // 找到光标所在的段落
        let node = range.startContainer
        while ((node && node.nodeType !== Node.ELEMENT_NODE) || !(node as HTMLElement).hasAttribute('data-paragraph-id')) {
          node = node.parentNode
        }
        targetParagraph = node as HTMLElement
      }

      // 如果没有找到目标段落，尝试从事件目标获取
      if (!targetParagraph) {
        let node = event.target as Node
        while (node && (node.nodeType !== Node.ELEMENT_NODE || !(node as HTMLElement).hasAttribute('data-paragraph-id'))) {
          node = node.parentNode
        }
        targetParagraph = node as HTMLElement
      }

      // 调用 ensureEditorHasContent 确保文本在段落节点内
      ensureEditorHasContent(editor)

      // 重新定位光标
      if (targetParagraph && targetParagraph.parentNode) {
        // 如果目标段落仍然存在，将光标定位到段落末尾
        placeCursorInParagraph(targetParagraph, true)
      }
      else {
        // 如果目标段落不存在，找到最后一个段落并定位到末尾
        const paragraphs = editor.querySelectorAll('div[data-paragraph-id]')
        if (paragraphs.length > 0) {
          const lastParagraph = paragraphs[paragraphs.length - 1] as HTMLElement
          placeCursorInParagraph(lastParagraph, true)
        }
      }
    }, 20)
  })

  // 添加MutationObserver监听DOM变化
  const observer = new MutationObserver((mutations) => {
    let needsReorder = false

    // 检查是否有段落被删除或添加
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // 检查是否有段落元素被添加
        const hasAddedParagraphs = Array.from(mutation.addedNodes).some(node => node.nodeType === Node.ELEMENT_NODE && (node as HTMLElement).hasAttribute('data-paragraph-id'))

        // 检查是否有段落元素被删除
        const hasRemovedParagraphs = Array.from(mutation.removedNodes).some(node => node.nodeType === Node.ELEMENT_NODE && (node as HTMLElement).hasAttribute('data-paragraph-id'))

        if (hasAddedParagraphs || hasRemovedParagraphs) {
          needsReorder = true
        }
      }
    })

    if (needsReorder) {
      paragraphIdManager.markDirty(editor, 150)
    }
    // 在输入法输入期间跳过 ensureEditorHasContent
    // !检查是否正在进行输入法输入, 防止在输入法的时候会出现光标跳到前几位的问题
    const isComposing = editor.hasAttribute('data-composing')
    if (!isComposing) {
      ensureEditorHasContent(editor)
    }
  })

  // 配置观察选项
  observer.observe(editor, {
    childList: true,
    subtree: true
  })
}
