/**
 * DOM元素创建和基础操作工具
 */

// 保存当前段落ID计数器
let paragraphIdCounter = 1

/**
 * 生成翻译ID
 */
export function generateTranslateId(paragraphId: number | string, segmentIndex: number): string {
  return `${paragraphId}-${segmentIndex}`
}

/**
 * 创建新段落元素
 */
export function createParagraphElement(): HTMLElement {
  const paragraph = document.createElement('div')
  const paragraphId = paragraphIdCounter++

  paragraph.setAttribute('data-paragraph-id', paragraphId.toString())
  paragraph.setAttribute('selected-translate-paragraph', 'y')

  return paragraph
}

/**
 * 创建文本容器
 */
export function createTextSpan(text: string = ''): HTMLElement {
  const span = document.createElement('span')
  span.classList.add('container-source')

  // 这里只创建span，实际ID会在添加到段落时设置
  if (text) {
    // 清理文本
    span.textContent = cleanupText(text)
  }
  else {
    // 使用br标签替代零宽空格，并添加标记
    const br = document.createElement('br')
    span.appendChild(br)
  }

  return span
}

/**
 * 设置或更新span的ID
 */
export function updateSpanId(span: HTMLElement, paragraphElement: HTMLElement): void {
  // 获取段落ID
  const paragraphId = paragraphElement.getAttribute('data-paragraph-id') || '1'

  // 计算此段落中的span序号
  const spans = paragraphElement.querySelectorAll('span')
  let segmentIndex = 0

  // 查找当前span在段落中的位置
  for (let i = 0; i < spans.length; i++) {
    if (spans[i] === span) {
      segmentIndex = i
      break
    }
  }

  // 设置ID
  span.setAttribute('selected-translate-id', generateTranslateId(paragraphId, segmentIndex))
}

/**
 * 创建带有特殊样式的文本容器
 */
export function createStyledTextSpan(text: string, className: string = ''): HTMLElement {
  const outerSpan = createTextSpan()

  if (className) {
    const innerSpan = document.createElement('span')
    innerSpan.classList.add(className)
    innerSpan.textContent = text
    outerSpan.appendChild(innerSpan)
  }
  else {
    outerSpan.textContent = text
  }

  return outerSpan
}

/**
 * 检查段落是否为空
 * @param paragraph 段落元素
 * @returns 是否为空
 */
export function isParagraphEmpty(paragraph: HTMLElement): boolean {
  return isEmptyContent(paragraph.textContent?.trim() || '')
}

/**
 * 查找最近的段落元素
 */
export function findClosestParagraph(node: Node): HTMLElement | null {
  let current: Node | null = node

  while (current && current.nodeType !== Node.ELEMENT_NODE) {
    current = current.parentNode
  }

  while (current && current instanceof HTMLElement) {
    if (current.tagName.toLowerCase() === 'div' && current.hasAttribute('data-paragraph-id')) {
      return current as HTMLElement
    }
    current = current.parentNode
  }

  return null
}

/**
 * 查找最近的span元素
 */
export function findClosestSpan(node: Node): HTMLElement | null {
  let current: Node | null = node

  while (current && current.nodeType !== Node.ELEMENT_NODE) {
    current = current.parentNode
  }

  while (current && current instanceof HTMLElement) {
    if (current.tagName.toLowerCase() === 'span') {
      return current as HTMLElement
    }
    current = current.parentNode
  }

  return null
}

/**
 * 将光标定位到段落中
 */
export function placeCursorInParagraph(paragraph: HTMLElement, atEnd: boolean = false): void {
  // 确保段落有内容可以放置光标
  if (!paragraph.hasChildNodes()) {
    const span = createTextSpan()
    paragraph.appendChild(span)
    updateSpanId(span, paragraph)
  }

  const range = document.createRange()
  const selection = window.getSelection()

  // 查找第一个或最后一个可聚焦的节点
  let targetNode: Node
  let offset: number

  if (atEnd) {
    // 在末尾定位光标
    const lastChild = paragraph.lastChild
    if (lastChild.nodeType === Node.TEXT_NODE) {
      // 如果是文本节点，定位到文本末尾
      targetNode = lastChild
      offset = lastChild.textContent.length
    }
    else if (lastChild.nodeType === Node.ELEMENT_NODE) {
      // 如果是元素节点，查找其最后一个文本子节点
      const lastTextNode = findLastTextNode(lastChild as HTMLElement)
      if (lastTextNode) {
        targetNode = lastTextNode
        offset = lastTextNode.textContent.length
      }
      else {
        // 如果没有文本节点，则定位到元素末尾
        targetNode = lastChild
        offset = lastChild.childNodes.length
      }
    }
    else {
      // 默认定位到段落末尾
      targetNode = paragraph
      offset = paragraph.childNodes.length
    }
  }
  else {
    // 在起始位置定位光标
    const firstChild = paragraph.firstChild
    if (firstChild.nodeType === Node.TEXT_NODE) {
      // 如果是文本节点，定位到文本开头
      targetNode = firstChild
      offset = 0
    }
    else if (firstChild.nodeType === Node.ELEMENT_NODE) {
      // 如果是元素节点，查找其第一个文本子节点
      const firstTextNode = findFirstTextNode(firstChild as HTMLElement)
      if (firstTextNode) {
        targetNode = firstTextNode
        offset = 0
      }
      else {
        // 如果没有文本节点，则定位到元素开头
        targetNode = firstChild
        offset = 0
      }
    }
    else {
      // 默认定位到段落开头
      targetNode = paragraph
      offset = 0
    }
  }

  // 设置范围并应用选择
  range.setStart(targetNode, offset)
  range.collapse(true)

  selection?.removeAllRanges()
  selection?.addRange(range)

  // 确保元素获得焦点
  paragraph.focus()
}

/**
 * 查找元素中的第一个文本节点
 */
export function findFirstTextNode(element: HTMLElement): Text | null {
  if (element.nodeType === Node.TEXT_NODE) {
    return element as unknown as Text
  }

  for (let i = 0; i < element.childNodes.length; i++) {
    const node = element.childNodes[i]
    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
      return node as Text
    }
    else if (node.nodeType === Node.ELEMENT_NODE) {
      const result = findFirstTextNode(node as HTMLElement)
      if (result) {
        return result
      }
    }
  }

  return null
}

/**
 * 查找元素中的最后一个文本节点
 */
export function findLastTextNode(element: HTMLElement): Text | null {
  if (element.nodeType === Node.TEXT_NODE) {
    return element as unknown as Text
  }

  for (let i = element.childNodes.length - 1; i >= 0; i--) {
    const node = element.childNodes[i]
    if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
      return node as Text
    }
    else if (node.nodeType === Node.ELEMENT_NODE) {
      const result = findLastTextNode(node as HTMLElement)
      if (result) {
        return result
      }
    }
  }

  return null
}

/**
 * 处理段落空状态
 */
export function handleEmptyState(paragraph: HTMLElement): void {
  // 获取段落的实际文本内容，排除HTML标签
  const actualTextContent = paragraph.textContent?.trim() || ''

  // 只有在段落真正为空时才处理
  if (!actualTextContent || isEmptyContent(actualTextContent)) {
    // 移除段落中可能存在的br标签（除了span内部的）
    const directBrElements = Array.from(paragraph.children).filter(child => child.tagName.toLowerCase() === 'br')
    directBrElements.forEach(br => paragraph.removeChild(br))

    // 检查是否已有span标签
    const existingSpans = paragraph.querySelectorAll('span')

    if (existingSpans.length > 0) {
      // 重用第一个现有的span，清空其内容但保持结构
      const span = existingSpans[0] as HTMLElement

      // 只有在真正为空时才设置br标签
      if (isEmptyContent(span.textContent)) {
        // 清空span内容
        span.innerHTML = ''

        // 添加br标签代替零宽空格
        const br = document.createElement('br')
        span.appendChild(br)
      }

      // 如果有多余的span，移除它们
      for (let i = 1; i < existingSpans.length; i++) {
        paragraph.removeChild(existingSpans[i])
      }

      // 更新ID
      const paragraphId = paragraph.getAttribute('data-paragraph-id') || '1'
      span.setAttribute('selected-translate-id', `${paragraphId}-0`)
    }
    else {
      // 创建带有br标签的span替代零宽空格
      const span = createTextSpan()
      const paragraphId = paragraph.getAttribute('data-paragraph-id') || '1'
      span.setAttribute('selected-translate-id', `${paragraphId}-0`)
      paragraph.appendChild(span)
    }
  }
}

/**
 * 更新段落中所有span的ID
 */
export function updateAllSpanIds(paragraph: HTMLElement): void {
  const spans = paragraph.querySelectorAll('span')
  spans.forEach((span, index) => {
    const paragraphId = paragraph.getAttribute('data-paragraph-id') || '1'
    span.setAttribute('selected-translate-id', generateTranslateId(paragraphId, index))
  })
}

/**
 * 清理文本
 * 如果文本为空，返回空字符串；如果文本中有内容，则移除零宽空格等不可见字符
 */
export function cleanupText(text: string): string {
  // 如果文本为空，返回空字符串
  if (!text || isEmptyContent(text)) {
    return ''
  }

  // 如果文本有实际内容，移除所有零宽空格
  return text.replace(/\u200B/g, '')
}

/**
 * 检查内容是否为空
 * 判断文本是否为空或只包含空白字符、零宽空格等
 */
export function isEmptyContent(text: string | null | undefined): boolean {
  if (!text) return true

  // 删除零宽空格和其他不可见字符后检查是否仅包含空白
  const cleanText = text.replace(/\u200B|\u200C|\u200D|\uFEFF/g, '')
  return cleanText.trim().length === 0
}

/**
 * 检测光标是否在段落开头
 * @param paragraph 段落元素
 * @param range 当前选区范围
 * @returns 是否在段落开头
 */
export function isCursorAtParagraphStart(paragraph: HTMLElement, range: Range): boolean {
  // 如果段落为空，认为光标在开头
  if (isEmptyContent(paragraph.innerText)) {
    return true
  }

  // 检查光标是否在段落的第一个文本节点的开头
  const firstChild = paragraph.firstChild
  if (!firstChild) return true

  // 如果光标在段落元素本身，且偏移量为0
  if (range.startContainer === paragraph && range.startOffset === 0) {
    return true
  }

  // 如果光标在第一个子元素中
  if (range.startContainer === firstChild) {
    if (firstChild.nodeType === Node.TEXT_NODE) {
      // 文本节点，检查偏移量是否为0
      return range.startOffset === 0
    }
    else if (firstChild.nodeType === Node.ELEMENT_NODE) {
      // 元素节点，检查偏移量是否为0
      return range.startOffset === 0
    }
  }

  // 如果光标在第一个子元素的第一个文本节点中
  if (firstChild.nodeType === Node.ELEMENT_NODE) {
    // 使用dom-utils中的findFirstTextNode函数
    const firstTextNode = findFirstTextNode(firstChild as HTMLElement)
    if (firstTextNode && range.startContainer === firstTextNode && range.startOffset === 0) {
      return true
    }
  }

  // 处理span嵌套的情况
  const firstSpan = paragraph.querySelector('span')
  if (firstSpan) {
    // 如果光标在span元素上且偏移量为0
    if (range.startContainer === firstSpan && range.startOffset === 0) {
      return true
    }

    // 检查span的第一个子节点
    const spanFirstChild = firstSpan.firstChild
    if (spanFirstChild && range.startContainer === spanFirstChild && range.startOffset === 0) {
      return true
    }

    // 如果是BR标签的特殊情况
    if (spanFirstChild && spanFirstChild.nodeName === 'BR' && range.startContainer === firstSpan && range.startOffset === 0) {
      return true
    }
  }

  return false
}

/**
 * 检测并包装段落中的孤立文本节点
 * 删除操作后可能会产生直接在段落中的文本节点，需要用span包装
 * @param paragraph 段落元素
 * @returns 是否进行了修复
 */
export function wrapOrphanedTextNodes(paragraph: HTMLElement): boolean {
  let hasChanges = false
  const nodesToProcess: Node[] = []

  // 收集所有直接子文本节点
  for (let i = 0; i < paragraph.childNodes.length; i++) {
    const node = paragraph.childNodes[i]
    if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
      nodesToProcess.push(node)
    }
  }

  // 处理每个孤立的文本节点
  nodesToProcess.forEach((node) => {
    const textContent = node.textContent || ''
    if (textContent.trim()) {
      // 创建新的span包装文本
      const span = createTextSpan(textContent)

      // 替换原文本节点
      paragraph.replaceChild(span, node)
      hasChanges = true
    }
  })

  return hasChanges
}

/**
 * 修复删除操作后的段落结构
 * 确保段落中的所有文本都被适当的span标签包装
 * @param paragraph 段落元素
 */
export function fixParagraphStructureAfterDeletion(paragraph: HTMLElement): void {
  // 1. 首先处理孤立的文本节点
  const hasOrphanedNodes = wrapOrphanedTextNodes(paragraph)

  // 2. 清理空的span标签
  const spans = Array.from(paragraph.querySelectorAll('span'))
  spans.forEach((span) => {
    if (isEmptyContent(span.textContent)) {
      // 如果span为空且不是段落中唯一的span，则删除它
      if (spans.length > 1) {
        paragraph.removeChild(span)
      }
      else {
        // 如果是唯一的span且为空，用br标签填充
        span.innerHTML = ''
        const br = document.createElement('br')
        span.appendChild(br)
      }
    }
  })

  // 3. 确保段落至少有一个span
  const remainingSpans = paragraph.querySelectorAll('span')
  if (remainingSpans.length === 0) {
    const span = createTextSpan()
    paragraph.appendChild(span)
  }

  // 4. 更新所有span的ID
  updateAllSpanIds(paragraph)

  // 5. 处理空段落状态
  handleEmptyState(paragraph)
}

/**
 * 创建默认段落
 * 统一创建默认段落的方法，确保所有地方创建的默认段落结构一致
 */
export function createDefaultParagraph(): HTMLElement {
  // 创建一个新段落
  const paragraph = createParagraphElement()

  // 创建一个默认的span标签，仅在必要时添加
  const span = createTextSpan()
  console.log('创建默认段落', span.outerHTML)

  paragraph.appendChild(span)
  console.log('添加span到段落', paragraph.outerHTML)

  // 更新span的ID - 设置为段落ID-0格式
  const paragraphId = paragraph.getAttribute('data-paragraph-id') || '1'
  span.setAttribute('selected-translate-id', `${paragraphId}-0`)

  return paragraph
}

/**
 * 在光标位置插入文本
 * @param text 要插入的文本
 * @param range 当前光标范围
 */
export function insertTextAtCursor(text: string, range: Range): void {
  const textNode = document.createTextNode(text)
  range.insertNode(textNode)

  // 将光标放在插入的文本之后
  range.setStartAfter(textNode)
  range.collapse(true)

  const selection = window.getSelection()
  if (selection) {
    selection.removeAllRanges()
    selection.addRange(range)
  }

  // 插入文本后，检查是否需要合并相邻的文本节点
  // 这有助于保持DOM结构的清洁
  const parentNode = textNode.parentNode
  if (parentNode) {
    parentNode.normalize()
  }
}

/**
 * 确保编辑器至少有一个段落
 */
export function ensureEditorHasContent(editor: HTMLElement): void {
  // 检查是否有段落标签
  const paragraphs = editor.querySelectorAll('div[data-paragraph-id]')

  if (paragraphs.length === 0) {
    // 清理编辑器内容- 防止意外在段落外面添加 br标签
    editor.innerHTML = ''

    // 如果没有段落，则创建一个默认段落
    const paragraph = createDefaultParagraph()
    editor.appendChild(paragraph)

    // Firefox特殊处理：确保光标定位到段落内部， 注意：Firefox 会将光标聚焦到段落外面， 所以要延迟将光标聚焦到段落内
    const isFirefox = navigator.userAgent.toLowerCase().includes('firefox')

    if (isFirefox) {
      // 延迟执行以确保DOM更新完成
      setTimeout(() => {
        placeCursorInParagraph(paragraph, false)
      }, 0)
    }
  }
  else {
    // 检查每个段落内部是否至少有一个span标签
    paragraphs.forEach((p) => {
      const paragraph = p as HTMLElement

      // 先移除段落直接子级的br标签（span内的br保留）
      const directBrElements = Array.from(paragraph.children).filter(child => child.tagName.toLowerCase() === 'br')

      directBrElements.forEach(br => paragraph.removeChild(br))

      const spans = paragraph.querySelectorAll('span')

      if (spans.length === 0) {
        // 检查是否有文本节点，如果有则包装为span而不是创建新的
        let hasTextNode = false
        for (let i = 0; i < paragraph.childNodes.length; i++) {
          const node = paragraph.childNodes[i]
          if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
            // 如果有文本节点，将其包装到span中
            const span = createTextSpan()
            span.textContent = node.textContent
            paragraph.replaceChild(span, node)
            hasTextNode = true
            break
          }
        }

        // 如果没有文本节点，才添加一个空span
        if (!hasTextNode) {
          const span = createTextSpan()
          paragraph.appendChild(span)
        }

        // 更新span的ID
        updateAllSpanIds(paragraph)
      }
    })
  }
}

/**
 * 检查是否为不可删除的最后一个元素节点
 * @param editor 编辑器元素
 * @returns 如果是最后一个不可删除的元素节点则返回true
 */
export function isLastElementNotDeletable(editor: HTMLElement): boolean {
  const paragraphs = editor.querySelectorAll('div[data-paragraph-id]')

  // 如果只有一个段落，检查该段落是否只有一个span且为空或只有一个字符
  if (paragraphs.length === 1) {
    const paragraph = paragraphs[0] as HTMLElement

    const spans = paragraph.querySelectorAll('span')

    // 如果只有一个span
    if (spans.length === 1) {
      const span = spans[0] as HTMLElement

      // 如果span中只有一个br元素，则不允许删除
      const children = Array.from(span.children)
      if (children.length === 1 && children[0].tagName.toLowerCase() === 'br') {
        return true
      }
    }

    // 如果段落没有span，也不允许删除
    if (spans.length === 0) {
      return true
    }
  }

  return false
}

/**
 * 清除编辑器内容
 * @param editor 编辑器元素
 */
export function clearEditorContent(): void {
  const editorElement = document.querySelector('.editable-textarea') as HTMLElement

  if (!editorElement) return

  // 清空编辑器内容
  editorElement.innerHTML = ''

  // 使用统一的默认段落创建方法
  const emptyParagraph = createDefaultParagraph()

  // 将空段落添加到编辑器
  editorElement.appendChild(emptyParagraph)

  // 正确设置光标位置到新创建的段落中
  setTimeout(() => {
    placeCursorInParagraph(emptyParagraph, false) // false表示放置到段落开头
  }, 0)
}
