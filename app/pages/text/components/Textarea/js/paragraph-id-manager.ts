/**
 * 段落ID管理器 - 性能优化版本
 * 实现按需重排序、防抖处理和局部更新
 */

import { updateAllSpanIds } from './dom-utils'

export class ParagraphIdManager {
  private reorderTimer: ReturnType<typeof setTimeout> | null = null
  private isDirty = false
  private static instance: ParagraphIdManager | null = null

  // 单例模式
  static getInstance(): ParagraphIdManager {
    if (!this.instance) {
      this.instance = new ParagraphIdManager()
    }
    return this.instance
  }

  /**
   * 标记需要重排序，使用防抖机制
   * @param editor 编辑器元素
   * @param delay 防抖延迟时间（毫秒）
   */
  markDirty(editor: HTMLElement, delay: number = 100): void {
    this.isDirty = true
    this.scheduleReorder(editor, delay)
  }

  /**
   * 从指定段落索引开始进行局部重排序
   * @param editor 编辑器元素
   * @param startIndex 开始重排序的段落索引
   */
  reorderFromIndex(editor: HTMLElement, startIndex: number): void {
    const paragraphs = editor.querySelectorAll('div[data-paragraph-id]')

    if (startIndex >= paragraphs.length) return

    // 只重排序从startIndex开始的段落
    for (let i = startIndex; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i] as HTMLElement
      const newId = (i + 1).toString()

      // 更新段落ID
      paragraph.setAttribute('data-paragraph-id', newId)

      // 更新段落内所有span的ID
      updateAllSpanIds(paragraph)
    }
  }

  /**
   * 立即执行完整重排序（用于关键时刻）
   * @param editor 编辑器元素
   */
  forceReorder(editor: HTMLElement): void {
    if (this.reorderTimer) {
      clearTimeout(this.reorderTimer)
      this.reorderTimer = null
    }

    this.reorderNow(editor)
    this.isDirty = false
  }

  /**
   * 检查是否需要重排序
   * @param editor 编辑器元素
   * @returns 是否需要重排序
   */
  needsReorder(editor: HTMLElement): boolean {
    const paragraphs = editor.querySelectorAll('div[data-paragraph-id]')

    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i] as HTMLElement
      const currentId = parseInt(paragraph.getAttribute('data-paragraph-id') || '0')
      const expectedId = i + 1

      if (currentId !== expectedId) {
        return true
      }
    }

    return false
  }

  /**
   * 获取段落在DOM中的实际索引
   * @param paragraph 段落元素
   * @returns 段落索引，如果未找到返回-1
   */
  getParagraphIndex(paragraph: HTMLElement): number {
    const editor = paragraph.closest('[data-ztsl-input]') as HTMLElement
    if (!editor) return -1

    const paragraphs = editor.querySelectorAll('div[data-paragraph-id]')
    return Array.from(paragraphs).indexOf(paragraph)
  }

  /**
   * 在段落插入后标记需要重排序
   * @param editor 编辑器元素
   * @param insertedParagraph 新插入的段落
   */
  onParagraphInserted(editor: HTMLElement, insertedParagraph: HTMLElement): void {
    // 使用防抖机制，避免频繁的立即重排序
    // 插入操作使用较短的延迟，确保用户体验
    this.markDirty(editor, 30)
  }

  /**
   * 在段落删除后标记需要重排序
   * @param editor 编辑器元素
   * @param deletedIndex 被删除段落的原索引
   */
  onParagraphDeleted(editor: HTMLElement, deletedIndex: number): void {
    // 从删除位置开始重排序
    this.reorderFromIndex(editor, deletedIndex)
  }

  /**
   * 防抖重排序调度
   * @param editor 编辑器元素
   * @param delay 延迟时间
   */
  private scheduleReorder(editor: HTMLElement, delay: number): void {
    if (this.reorderTimer) {
      clearTimeout(this.reorderTimer)
    }

    this.reorderTimer = setTimeout(() => {
      if (this.isDirty) {
        this.reorderNow(editor)
        this.isDirty = false
      }
      this.reorderTimer = null
    }, delay)
  }

  /**
   * 执行重排序操作
   * @param editor 编辑器元素
   */
  private reorderNow(editor: HTMLElement): void {
    const start = performance.now()
    const paragraphs = editor.querySelectorAll('div[data-paragraph-id]')

    // 检查是否真的需要重排序
    if (!this.needsReorder(editor)) {
      return
    }

    // 保存当前光标位置
    const selection = window.getSelection()
    let savedRange: Range | null = null
    if (selection && selection.rangeCount > 0) {
      savedRange = selection.getRangeAt(0).cloneRange()
    }

    // 执行重排序
    paragraphs.forEach((p, index) => {
      const paragraph = p as HTMLElement
      const newId = (index + 1).toString()

      // 更新段落ID
      paragraph.setAttribute('data-paragraph-id', newId)

      // 更新段落内所有span的ID
      updateAllSpanIds(paragraph)
    })

    // 恢复光标位置
    if (savedRange && selection) {
      try {
        selection.removeAllRanges()
        selection.addRange(savedRange)
      }
      catch (e) {
        // 如果恢复失败，忽略错误
        console.warn('无法恢复光标位置:', e)
      }
    }

    // 性能监控
    const duration = performance.now() - start
    if (duration > 16) {
      // 超过一帧时间(16ms)
      console.warn(`段落ID重排序耗时过长: ${duration.toFixed(2)}ms, 段落数: ${paragraphs.length}`)
    }
  }

  /**
   * 清理定时器（用于组件销毁时）
   */
  cleanup(): void {
    if (this.reorderTimer) {
      clearTimeout(this.reorderTimer)
      this.reorderTimer = null
    }
    this.isDirty = false
  }
}

// 导出单例实例
export const paragraphIdManager = ParagraphIdManager.getInstance()
