/**
 * 序列化编辑器DOM结构
 * 将编辑器DOM结构序列化为可存储的数据结构
 */

/**
 * 表示DOM节点的属性
 */
export interface NodeAttributes {
  [key: string]: string
}

/**
 * 表示文本节点
 */
export interface TextNodeData {
  type: 'text'
  content: string
}

/**
 * 表示元素节点
 */
export interface ElementNodeData {
  type: 'element'
  tagName: string
  attributes: NodeAttributes
  children: (ElementNodeData | TextNodeData)[]
}

/**
 * 表示整个编辑器的DOM结构
 */
export interface EditorDOMStructure {
  version: string // 数据格式版本，便于后续升级
  paragraphs: ElementNodeData[] // 段落列表
}

/**
 * 将DOM元素序列化为可存储的数据结构
 * @param editorElement 富文本编辑器元素
 * @returns 序列化后的数据结构
 */
export function serializeEditorDOM(editorElement: HTMLElement): EditorDOMStructure {
  const paragraphs: ElementNodeData[] = []

  // 获取所有段落元素
  const paragraphElements = editorElement.querySelectorAll('div[data-paragraph-id]')

  // 遍历段落元素并序列化
  paragraphElements.forEach((paragraphElement) => {
    paragraphs.push(serializeElement(paragraphElement as HTMLElement))
  })

  return {
    version: '1.0',
    paragraphs
  }
}

/**
 * 序列化单个元素
 * @param element DOM元素
 * @returns 序列化后的元素数据
 */
function serializeElement(element: HTMLElement): ElementNodeData {
  const attributes: NodeAttributes = {}

  // 获取元素的所有属性
  Array.from(element.attributes).forEach((attr) => {
    attributes[attr.name] = attr.value
  })

  const children: (ElementNodeData | TextNodeData)[] = []

  // 处理子节点
  element.childNodes.forEach((childNode) => {
    if (childNode.nodeType === Node.ELEMENT_NODE) {
      // 元素节点
      children.push(serializeElement(childNode as HTMLElement))
    }
    else if (childNode.nodeType === Node.TEXT_NODE && childNode.textContent) {
      // 文本节点（忽略空白文本节点）
      if (childNode.textContent.trim()) {
        children.push({
          type: 'text',
          content: childNode.textContent
        })
      }
    }
  })

  return {
    type: 'element',
    tagName: element.tagName.toLowerCase(),
    attributes,
    children
  }
}

/**
 * 将序列化的DOM结构还原为实际DOM元素
 * @param structure 序列化的DOM结构
 * @returns 还原的DOM元素
 */
export function deserializeEditorDOM(structure: EditorDOMStructure): DocumentFragment {
  const fragment = document.createDocumentFragment()

  // 还原所有段落
  structure.paragraphs.forEach((paragraph) => {
    const paragraphElement = deserializeElement(paragraph)
    fragment.appendChild(paragraphElement)
  })

  return fragment
}

/**
 * 还原单个元素
 * @param nodeData 序列化的元素数据
 * @returns 还原的DOM元素
 */
function deserializeElement(nodeData: ElementNodeData): HTMLElement {
  // 创建元素
  const element = document.createElement(nodeData.tagName)

  // 设置所有属性
  Object.entries(nodeData.attributes).forEach(([name, value]) => {
    element.setAttribute(name, value)
  })

  // 添加所有子节点
  nodeData.children.forEach((childData) => {
    if (childData.type === 'element') {
      // 元素子节点
      const childElement = deserializeElement(childData)
      element.appendChild(childElement)
    }
    else if (childData.type === 'text') {
      // 文本子节点
      const textNode = document.createTextNode(childData.content)
      element.appendChild(textNode)
    }
  })

  return element
}

/**
 * 存储编辑器DOM结构到历史记录
 * @param editorElement 编辑器元素
 * @returns 可存储的数据结构
 */
export function storeEditorState(editorElement: HTMLElement): string {
  const structure = serializeEditorDOM(editorElement)
  // console.log('序列化编辑器状态', structure);
  return JSON.stringify(structure)
}

/**
 * 从历史记录还原编辑器DOM结构
 * @param editorElement 目标编辑器元素
 * @param storedState 存储的编辑器状态
 */
export function restoreEditorState(editorElement: HTMLElement, storedState: string): void {
  try {
    // 解析存储的状态
    const structure: EditorDOMStructure = JSON.parse(storedState)

    // 清空编辑器
    editorElement.innerHTML = ''

    // 还原DOM结构
    const fragment = deserializeEditorDOM(structure)
    editorElement.appendChild(fragment)

    // 触发input事件，确保处理器能够正确处理
    const event = new Event('input', { bubbles: true })
    editorElement.dispatchEvent(event)
  }
  catch (error) {
    console.error('还原编辑器状态失败:', error)
  }
}
