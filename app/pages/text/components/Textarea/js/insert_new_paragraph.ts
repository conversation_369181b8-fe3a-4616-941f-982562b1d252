/**
 * 插入新段落
 */
import { createParagraphElement, findClosestParagraph, placeCursorInParagraph, handleEmptyState, updateAllSpanIds, isCursorAtParagraphStart } from './dom-utils'
import { paragraphIdManager } from './paragraph-id-manager'

/**
 * 在当前段落前面插入新段落，光标保持在原段落开头
 * @param editor 编辑器元素
 * @param currentParagraph 当前段落
 */
function insertNewParagraphBefore(editor: HTMLElement, currentParagraph: HTMLElement): void {
  // 保存当前光标位置
  const selection = window.getSelection()
  if (!selection) return

  const range = selection.getRangeAt(0)
  const savedRange = range.cloneRange()

  // 创建新段落
  const newParagraph = createParagraphElement()

  // 在当前段落前插入新段落
  currentParagraph.parentNode?.insertBefore(newParagraph, currentParagraph)

  // 更新新段落中的所有span的ID
  updateAllSpanIds(newParagraph)

  // 处理新段落的空段落标记
  handleEmptyState(newParagraph)

  // 使用ID管理器处理段落插入后的重排序
  paragraphIdManager.onParagraphInserted(editor, newParagraph)

  // 立即恢复光标位置到原段落开头，不使用setTimeout
  // 这样可以避免与其他异步光标操作冲突
  try {
    selection.removeAllRanges()
    selection.addRange(savedRange)
  }
  catch (e) {
    // 如果恢复失败，手动设置到段落开头
    placeCursorInParagraph(currentParagraph, false)
  }
}

/**
 * 插入新段落
 * @param editor 编辑器元素
 * @returns 返回光标最终所在的段落元素
 */
export function insertNewParagraph(editor: HTMLElement): HTMLElement | undefined {
  // 阻止默认行为
  const selection = window.getSelection()
  if (!selection) return

  // 获取选中的范围
  const range = selection.getRangeAt(0)

  // 找到当前段落
  const currentParagraph = findClosestParagraph(range.startContainer)

  if (!currentParagraph) {
    // 如果没有找到段落，创建一个新段落
    const newParagraph = createParagraphElement()
    editor.appendChild(newParagraph)
    // 将光标放在新段落中
    placeCursorInParagraph(newParagraph)

    // 使用ID管理器处理段落插入
    paragraphIdManager.onParagraphInserted(editor, newParagraph)
    return newParagraph
  }

  // 检查光标是否在段落开头
  if (isCursorAtParagraphStart(currentParagraph, range)) {
    // 光标在段落开头，在当前段落前面创建新段落，但光标保持在原段落开头
    // console.log('光标在段落开头，在当前段落前面创建新段落，光标保持在原段落开头');
    insertNewParagraphBefore(editor, currentParagraph)
    return currentParagraph
  }

  // 光标不在段落开头，按原有逻辑在段落后面创建新段落
  // console.log('光标不在段落开头，在段落后面创建新段落');

  // 创建新段落
  const newParagraph = createParagraphElement()

  // 处理内容分割
  splitContentAtCursor(currentParagraph, newParagraph, range)

  // 在当前段落后插入新段落
  if (currentParagraph.nextSibling) {
    // console.log('在当前段落后插入新段落', currentParagraph.nextSibling);
    currentParagraph.parentNode?.insertBefore(newParagraph, currentParagraph.nextSibling)
  }
  else {
    // console.log('在当前段落后面插入新段落');
    currentParagraph.parentNode?.appendChild(newParagraph)
  }

  // 更新新段落中的所有span的ID
  updateAllSpanIds(newParagraph)

  // 将光标放在新段落中
  placeCursorInParagraph(newParagraph)

  // 处理空段落标记
  handleEmptyState(currentParagraph)
  // 处理新段落的空段落标记
  handleEmptyState(newParagraph)

  // 使用ID管理器处理段落插入后的重排序
  paragraphIdManager.onParagraphInserted(editor, newParagraph)

  return newParagraph
}

/**
 * 在光标位置分割内容
 * @param currentParagraph 当前段落元素
 * @param newParagraph 新段落元素
 * @param range 当前选区范围
 */
function splitContentAtCursor(currentParagraph: HTMLElement, newParagraph: HTMLElement, range: Range): void {
  // 如果当前段落没有子节点，则不进行分割
  if (!currentParagraph.hasChildNodes()) return

  // 创建从光标到段落末尾的Range
  const endRange = new Range()
  endRange.setStart(range.startContainer, range.startOffset)
  endRange.setEndAfter(currentParagraph.lastChild!)

  // 提取内容并插入新段落
  const fragment = endRange.extractContents()
  newParagraph.appendChild(fragment)
}
