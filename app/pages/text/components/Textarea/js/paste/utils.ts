/*
 * 粘贴处理工具
 */

import {
  createParagraphElement,
  createDefaultParagraph,
  findClosestParagraph,
  handleEmptyState,
  updateAllSpanIds,
  isParagraphEmpty,
  placeCursorInParagraph,
  fixParagraphStructureAfterDeletion,
  insertTextAtCursor
} from '../dom-utils'
import { normalizeLineBreaks, splitParagraphToSentences } from '../sentence-splitter'
import { paragraphIdManager } from '../paragraph-id-manager'

// -------------------- Selection helpers --------------------

/**
 * 判断当前 selection 是否跨越多个段落。
 */
export function isMultiParagraphSelection(range: Range): boolean {
  const startParagraph = findClosestParagraph(range.startContainer)
  const endParagraph = findClosestParagraph(range.endContainer)

  return startParagraph !== endParagraph && startParagraph !== null && endParagraph !== null
}

/**
 * 获取 selection 覆盖到的段落元素列表。
 * 仅在内部使用，因此不导出。
 */
function getParagraphsInRange(range: Range): HTMLElement[] {
  const paragraphs: HTMLElement[] = []
  const startParagraph = findClosestParagraph(range.startContainer)
  const endParagraph = findClosestParagraph(range.endContainer)

  if (!startParagraph || !endParagraph) {
    return paragraphs
  }

  // 同一段落
  if (startParagraph === endParagraph) {
    return [startParagraph]
  }

  // 从起始段落开始向后遍历直到结束段落
  let currentParagraph: HTMLElement | null = startParagraph
  paragraphs.push(currentParagraph)

  while (currentParagraph && currentParagraph !== endParagraph) {
    let nextSibling: Element | null | undefined = currentParagraph.nextElementSibling
    // 跳过非段落元素
    while (nextSibling && !nextSibling.getAttribute('data-paragraph-id')) {
      nextSibling = nextSibling.nextElementSibling
    }

    if (nextSibling && nextSibling.getAttribute('data-paragraph-id')) {
      currentParagraph = nextSibling as HTMLElement
      paragraphs.push(currentParagraph)
    }
    else {
      break
    }
  }

  return paragraphs
}

// -------------------- Deletion helpers --------------------

/**
 * 当用户选中了多个段落内容并进行粘贴时，需要先删除所选内容。
 * 该函数负责删除逻辑并返回用于后续插入的目标段落与光标位置。
 */
export function handleMultiParagraphDeletion(editor: HTMLElement, range: Range): { targetParagraph: HTMLElement, cursorPosition: 'start' | 'end' } {
  const paragraphsInRange = getParagraphsInRange(range)

  if (paragraphsInRange.length === 0) {
    // 未找到段落，创建默认段落
    const newParagraph = createDefaultParagraph()
    editor.appendChild(newParagraph)
    return { targetParagraph: newParagraph, cursorPosition: 'start' }
  }

  if (paragraphsInRange.length === 1) {
    // 单段落，直接修复结构
    const paragraph = paragraphsInRange[0]
    fixParagraphStructureAfterDeletion(paragraph)
    return { targetParagraph: paragraph, cursorPosition: 'start' }
  }

  // 多段落选择
  const firstParagraph = paragraphsInRange[0]
  const lastParagraph = paragraphsInRange[paragraphsInRange.length - 1]

  // 选区起始前的内容
  const firstParagraphRange = document.createRange()
  firstParagraphRange.selectNodeContents(firstParagraph)
  firstParagraphRange.setEnd(range.startContainer, range.startOffset)
  const beforeContent = firstParagraphRange.toString()

  // 选区结束后的内容
  const lastParagraphRange = document.createRange()
  lastParagraphRange.setStart(range.endContainer, range.endOffset)
  lastParagraphRange.selectNodeContents(lastParagraph)
  const afterContent = lastParagraphRange.toString()

  // 移除中间段落（不含头尾）
  for (let i = 1; i < paragraphsInRange.length; i++) {
    const paragraph = paragraphsInRange[i]
    paragraph.parentNode?.removeChild(paragraph)
  }

  // 合并内容到第一个段落
  const mergedContent = beforeContent + afterContent
  firstParagraph.innerHTML = ''
  if (mergedContent.trim()) {
    const sentenceSpans = splitParagraphToSentences(mergedContent)
    firstParagraph.appendChild(sentenceSpans)
  }
  else {
    handleEmptyState(firstParagraph)
  }

  updateAllSpanIds(firstParagraph)

  return { targetParagraph: firstParagraph, cursorPosition: 'start' }
}

// -------------------- Fragment builders --------------------

/**
 * 根据粘贴的纯文本内容构建段落 DOM 片段。
 * 每一行文本会拆分成独立段落；空行会插入默认空段落。
 */
export function buildParagraphFragments(content: string): DocumentFragment {
  const fragment = document.createDocumentFragment()
  const paragraphs = normalizeLineBreaks(content) // 已去除首尾空行

  // 粘贴内容为空 -> 创建空段落
  if (paragraphs.length === 0) {
    const paragraph = createDefaultParagraph()
    fragment.appendChild(paragraph)
    return fragment
  }

  paragraphs.forEach((text) => {
    if (!text) {
      // 空行
      fragment.appendChild(createDefaultParagraph())
      return
    }

    const paragraph = createParagraphElement()
    const sentenceSpans = splitParagraphToSentences(text)
    paragraph.appendChild(sentenceSpans)
    updateAllSpanIds(paragraph)

    fragment.appendChild(paragraph)
  })

  return fragment
}

// -------------------- Paste helpers --------------------

/**
 * 在跨段落或空段落情况下，将粘贴内容按段落插入到编辑器。
 */
export function insertParagraphsFromPaste(editor: HTMLElement, content: string): void {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return

  const range = selection.getRangeAt(0)
  const currentParagraph = findClosestParagraph(range.startContainer)

  // 先根据文本内容构建 DOM 片段
  const fragments = buildParagraphFragments(content)
  const fragmentParagraphs = fragments.querySelectorAll('div[data-paragraph-id]')
  const lastFragment = fragmentParagraphs.length > 0 ? (fragmentParagraphs[fragmentParagraphs.length - 1] as HTMLElement) : null

  if (!currentParagraph) {
    // 编辑器为空，直接插入
    editor.appendChild(fragments)
  }
  else {
    const parent = currentParagraph.parentNode
    if (!parent) return

    if (isParagraphEmpty(currentParagraph)) {
      // 当前段落为空，直接替换
      parent.replaceChild(fragments, currentParagraph)
    }
    else {
      // 使用注释节点保存光标位置
      const cursorMarker = document.createComment('cursor-marker')
      range.insertNode(cursorMarker)

      // 在当前段落之后插入
      if (currentParagraph.nextSibling) {
        parent.insertBefore(fragments, currentParagraph.nextSibling)
      }
      else {
        parent.appendChild(fragments)
      }

      // 恢复光标到注释处
      const restoreRange = document.createRange()
      restoreRange.selectNodeContents(cursorMarker)
      restoreRange.collapse(true)
      selection.removeAllRanges()
      selection.addRange(restoreRange)
      cursorMarker.remove()
    }
  }

  // 仅处理新插入的段落
  fragmentParagraphs.forEach((p) => {
    handleEmptyState(p as HTMLElement)
    updateAllSpanIds(p as HTMLElement)
  })

  // 重新索引段落 ID
  paragraphIdManager.markDirty(editor, 50)

  // 触发 input 事件
  editor.dispatchEvent(new InputEvent('input', { bubbles: true }))

  // 下一帧将光标放到最后一个新段落末尾
  requestAnimationFrame(() => {
    const targetParagraph = lastFragment || (Array.from(editor.querySelectorAll('div[data-paragraph-id]')).pop() as HTMLElement)

    if (targetParagraph) {
      placeCursorInParagraph(targetParagraph, true)
    }
    else {
      // 回退：清除选择
      selection.removeAllRanges()
    }
  })
}

/**
 * 在单个段落内粘贴多行文本时，将段落拆分并保持原有结构。
 */
export function handleMultiLinePasteWithinParagraph(editor: HTMLElement, targetParagraph: HTMLElement, insertRange: Range, normalizedLines: string[]): void {
  // 1. 提取插入点之后的内容
  const afterRange = insertRange.cloneRange()
  afterRange.setEndAfter(targetParagraph.lastChild as Node)
  const afterFragment = afterRange.extractContents()

  // 2. 插入首行文本
  const firstLine = normalizedLines[0] ?? ''
  if (firstLine) {
    insertTextAtCursor(firstLine, insertRange)
  }

  // 3. 修复首段结构
  fixParagraphStructureAfterDeletion(targetParagraph)

  // 4. 创建新段落承载光标后内容
  const afterParagraph = createParagraphElement()
  if (afterFragment.childNodes.length) {
    afterParagraph.appendChild(afterFragment)
  }
  handleEmptyState(afterParagraph)
  updateAllSpanIds(afterParagraph)

  const parent = targetParagraph.parentNode as Node
  parent.insertBefore(afterParagraph, targetParagraph.nextSibling)

  // 5. 处理剩余行（除首行外）
  if (normalizedLines.length > 1) {
    const fragments = document.createDocumentFragment()

    normalizedLines.slice(1).forEach((line) => {
      let paragraph: HTMLElement

      if (line) {
        paragraph = createParagraphElement()
        const sentenceSpans = splitParagraphToSentences(line)
        paragraph.appendChild(sentenceSpans)
      }
      else {
        paragraph = createDefaultParagraph()
      }

      handleEmptyState(paragraph)
      updateAllSpanIds(paragraph)

      fragments.appendChild(paragraph)
    })

    parent.insertBefore(fragments, afterParagraph)
  }

  // 6. 重新索引段落 ID
  paragraphIdManager.markDirty(editor, 50)

  // 7. 将光标放置在首段末尾
  placeCursorInParagraph(targetParagraph, true)

  // 8. 触发 input 事件
  editor.dispatchEvent(new InputEvent('input', { bubbles: true }))
}
