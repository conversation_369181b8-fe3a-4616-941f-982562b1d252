/**
 * 粘贴处理工具
 */
import { findClosestParagraph, isParagraphEmpty } from '../dom-utils'

import { isMultiParagraphSelection, handleMultiParagraphDeletion, insertParagraphsFromPaste, handleMultiLinePasteWithinParagraph } from './utils'

import { normalizeLineBreaks } from '../sentence-splitter'

/**
 * TODO 处理粘贴事件-Textarea
 * @param editor 编辑器元素
 * @param e 粘贴事件
 */
export function handlePaste(editor: HTMLElement, e: ClipboardEvent): void {
  console.log('🔄 开始处理粘贴事件')
  e.preventDefault()

  let content = ''

  // 尝试获取纯文本
  if (e.clipboardData) {
    content = e.clipboardData.getData('text/plain')
  }

  console.log('📋 粘贴内容:', JSON.stringify(content))
  if (!content) {
    console.log('❌ 粘贴内容为空，退出处理')
    return
  }

  // 获取当前选择
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) {
    console.log('❌ 没有选择范围，退出处理')
    return
  }

  const range = selection.getRangeAt(0)
  console.log('📍 当前选择范围:', {
    startContainer: range.startContainer,
    startOffset: range.startOffset,
    endContainer: range.endContainer,
    endOffset: range.endOffset,
    collapsed: range.collapsed
  })

  // 检查是否为跨段落选择
  const isMultiParagraph = isMultiParagraphSelection(range)
  console.log('🔍 是否跨段落选择:', isMultiParagraph)

  let targetParagraph: HTMLElement
  let cursorPosition: 'start' | 'end' = 'start'
  let insertRange: Range

  /**
   * 处理跨段落选择
   */
  if (isMultiParagraph) {
    console.log('🔄 处理跨段落选择')
    // 处理跨段落选择
    const result = handleMultiParagraphDeletion(editor, range)
    targetParagraph = result.targetParagraph
    cursorPosition = result.cursorPosition
    console.log('✅ 跨段落删除完成，目标段落:', targetParagraph, '光标位置:', cursorPosition)

    // 创建新的选择范围
    const newRange = document.createRange()
    if (cursorPosition === 'start') {
      newRange.setStart(targetParagraph, 0)
    }
    else {
      newRange.setStart(targetParagraph, targetParagraph.childNodes.length)
    }
    newRange.collapse(true)

    selection.removeAllRanges()
    selection.addRange(newRange)
    insertRange = newRange
    console.log('📍 创建新的插入范围')
  }
  else {
    /**
     * 处理单段落选择
     */
    console.log('🔄 处理单段落选择')
    // 删除当前选择内容
    range.deleteContents()
    console.log('🗑️ 删除选择内容')

    // 查找当前段落
    const currentParagraph = findClosestParagraph(range.startContainer)
    console.log('🔍 查找到的当前段落:', currentParagraph.outerHTML)

    if (!currentParagraph) {
      console.log('❌ 没有找到当前段落，使用段落插入逻辑')
      // 如果没有找到当前段落，按原逻辑处理
      insertParagraphsFromPaste(editor, content)
      return
    }

    targetParagraph = currentParagraph

    // 在结构修复前保存光标位置信息
    const cursorMarker = document.createComment('cursor-insertion-point')
    range.insertNode(cursorMarker)
    console.log('📌 插入光标标记')

    // 在结构修复后恢复插入范围
    if (cursorMarker.parentNode) {
      const newRange = document.createRange()
      newRange.setStartBefore(cursorMarker)
      newRange.collapse(true)
      insertRange = newRange
      cursorMarker.remove()
      console.log('📍 恢复插入范围并移除标记')
    }
    else {
      console.log('⚠️ 光标标记丢失，创建新的插入范围')
      // 如果标记丢失，在段落的适当位置创建新range
      const newRange = document.createRange()
      const firstSpan = targetParagraph.querySelector('span')
      if (firstSpan && firstSpan.firstChild) {
        newRange.setStart(firstSpan.firstChild, 0)
      }
      else {
        newRange.setStart(targetParagraph, 0)
      }
      newRange.collapse(true)
      insertRange = newRange
    }
  }

  /**
   * 粘贴问题处理：
   * 原句子："Node.js runs the V8 JavaScript engine..."
    选择："JavaScript"
    粘贴："你好\n\n\n"
    应该能正确处理为：
    第一段："Node.js runs the V8 你好"
    第二段：空行
    第三段：空行
    第四段：" engine, the core of Google Chrome, outside of the browser. This allows Node.js to be very performant."
   */

  // 标准化换行后，基于实际行数判断是否为单行内容
  const normalizedLines = normalizeLineBreaks(content)

  // 多行内容处理
  if (!isMultiParagraph && targetParagraph && !isParagraphEmpty(targetParagraph)) {
    console.log('🛠️ 在单个段落中粘贴多行内容，执行段落拆分逻辑')
    handleMultiLinePasteWithinParagraph(editor, targetParagraph, insertRange, normalizedLines)
  }
  else {
    console.log('📄 使用段落插入逻辑（跨段落或空段落情况）')
    insertParagraphsFromPaste(editor, content)
  }
  console.log('✅ 粘贴处理完成')
}
