/**
 * DOM渲染工具
 */
import { ensureEditorHasContent, findClosestSpan } from './dom-utils'
import { handlePaste } from './paste/index'
import { UndoManager } from './undo-manager'
import { paragraphIdManager } from './paragraph-id-manager'
import { setupInputHandlers } from './input_handlers'

// 创建全局撤销管理器实例
export const undoManager = new UndoManager()

/**
 * 初始化富文本编辑器
 */
export function initializeEditor(editorElement: HTMLElement): void {
  // 设置必要的属性
  setupEditorAttributes(editorElement)

  // 确保编辑器至少有一个段落
  ensureEditorHasContent(editorElement)

  // 强制执行一次完整的段落ID重排序
  paragraphIdManager.forceReorder(editorElement)

  // 添加输入事件监听
  setupInputHandlers(editorElement)

  // 添加粘贴事件处理
  setupPasteHandler(editorElement)

  // 初始状态记录
  setTimeout(() => {
    // undoManager.captureState(editorElement, false);
  }, 0)
}

/**
 * 设置编辑器属性
 */
function setupEditorAttributes(editor: HTMLElement): void {
  editor.setAttribute('data-ztsl-input', 'true')
  editor.setAttribute('role', 'textarea')
}

/**
 * 设置粘贴事件处理程序
 */
function setupPasteHandler(editor: HTMLElement): void {
  editor.addEventListener('paste', (e: ClipboardEvent) => {
    // 记录操作前状态
    // undoManager.captureState(editor, false);

    handlePaste(editor, e)

    // 记录操作后状态
    requestAnimationFrame(() => {
      // !获取当前光标所在的span并滚动到视图中
      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        // 获取当前光标所在的span, 滚动到视图中
        const currentSpan = findClosestSpan(range.startContainer)
        if (currentSpan) {
          // 将光标所在的span滚动到视图中，避免超长文本时光标消失
          currentSpan.scrollIntoView({ block: 'nearest' })
        }
      }

      // undoManager.captureState(editor, false);
    })
  })
}

/**
 * 清理编辑器资源
 * 用于组件销毁时清理定时器和监听器
 */
export function cleanupEditor(): void {
  // 清理段落ID管理器的定时器
  paragraphIdManager.cleanup()

  // 清理撤销管理器的定时器和状态
  undoManager.cleanup()
}
