/**
 * 富文本编辑器撤销功能管理
 */

import { paragraphIdManager } from './paragraph-id-manager'

/**
 * 撤销栈项目接口
 */
interface UndoItem {
  timestamp: number
  selection: {
    startPath: number[]
    startOffset: number
    endPath: number[]
    endOffset: number
    isCollapsed: boolean
  }
  content: DocumentFragment
}

/**
 * 撤销管理器类
 */
export class UndoManager {
  private stack: UndoItem[] = []
  private pointer: number = -1
  private readonly MAX_STACK_SIZE = 50
  private isCapturing: boolean = false
  private captureTimer: any = null

  /**
   * 是否可以撤销
   */
  public get canUndo(): boolean {
    return this.pointer > 0
  }

  /**
   * 是否可以重做
   */
  public get canRedo(): boolean {
    return this.pointer < this.stack.length - 1
  }

  /**
   * 捕获编辑器状态
   * @param editor 编辑器元素
   * @param throttle 是否使用节流（默认为true）
   */
  public captureState(editor: HTMLElement, throttle: boolean = true): void {
    // console.log('捕获状态被调用', {
    //   isCapturing: this.isCapturing,
    //   throttle: throttle,
    //   stackSize: this.stack.length,
    //   pointer: this.pointer
    // });

    if (this.isCapturing) {
      //   console.log('已在捕获中，忽略此次调用');
      return
    }

    // 使用节流控制记录频率
    if (throttle) {
      clearTimeout(this.captureTimer)
      this.captureTimer = setTimeout(() => {
        // console.log('执行延迟捕获');
        this.doCapture(editor)
      }, 300)
    }
    else {
      //   console.log('立即执行捕获');
      this.doCapture(editor)
    }
  }

  /**
   * 实际执行状态捕获
   * @param editor 编辑器元素
   */
  private doCapture(editor: HTMLElement): void {
    // console.log('开始执行捕获');
    this.isCapturing = true

    try {
      // 克隆当前编辑器内容
      const fragment = document.createDocumentFragment()
      Array.from(editor.childNodes).forEach((node) => {
        fragment.appendChild(node.cloneNode(true))
      })

      // 保存选区状态
      const selection = this.saveSelectionPosition(editor)

      // 如果已经有状态，且内容未实质变化，则不记录
      if (this.stack.length > 0 && this.pointer >= 0) {
        const lastState = this.stack[this.pointer]
        if (!this.hasMeaningfulChange(lastState.content, fragment)) {
          //   console.log('内容无实质变化，忽略此次捕获');
          this.isCapturing = false
          return
        }
      }

      //   console.log('检测到内容有变化，记录新状态');

      // 删除当前状态之后的所有状态
      if (this.pointer !== -1 && this.pointer < this.stack.length - 1) {
        // console.log(`删除从 ${this.pointer + 1} 到 ${this.stack.length - 1} 的状态`)
        this.stack.splice(this.pointer + 1)
      }

      // 添加新状态
      this.stack.push({
        timestamp: Date.now(),
        selection: selection,
        content: fragment
      })

      // 如果栈超过最大尺寸，移除最早的状态
      if (this.stack.length > this.MAX_STACK_SIZE) {
        this.stack.shift()
        // console.log('栈超过最大尺寸，移除最早的状态');
      }

      // 更新指针
      this.pointer = this.stack.length - 1
      //   console.log('状态已记录，新指针位置:', this.pointer, '栈大小:', this.stack.length);
    }
    finally {
      this.isCapturing = false
    }
  }

  /**
   * 撤销操作
   * @param editor 编辑器元素
   * @returns 是否执行了撤销
   */
  public undo(editor: HTMLElement): boolean {
    if (!this.canUndo) return false

    // 指针前移
    this.pointer--
    this.restoreState(editor, this.pointer, 'historyUndo')

    // 撤销后强制重排序ID，确保ID顺序正确
    paragraphIdManager.forceReorder(editor)

    return true
  }

  /**
   * 重做操作
   * @param editor 编辑器元素
   * @returns 是否执行了重做
   */
  public redo(editor: HTMLElement): boolean {
    // console.log('redo被调用', {
    //   canRedo: this.canRedo,
    //   pointer: this.pointer,
    //   stackLength: this.stack.length,
    //   condition: `${this.pointer} < ${this.stack.length - 1}`
    // });

    if (this.pointer >= this.stack.length - 1) {
      //   console.log('无法执行重做：已经到达最新状态');
      return false
    }

    // 指针后移
    this.pointer++
    // console.log('重做操作后指针位置:', this.pointer);
    this.restoreState(editor, this.pointer, 'historyRedo')

    // 重做后强制重排序ID，确保ID顺序正确
    paragraphIdManager.forceReorder(editor)

    return true
  }

  /**
   * 恢复编辑器状态
   * @param editor 编辑器元素
   * @param index 状态索引
   * @param eventType 触发的事件类型
   */
  private restoreState(editor: HTMLElement, index: number, eventType: string = 'historyUndo'): void {
    if (index < 0 || index >= this.stack.length) return

    this.isCapturing = true
    try {
      const state = this.stack[index]

      // 清空编辑器内容
      while (editor.firstChild) {
        editor.removeChild(editor.firstChild)
      }

      // 恢复内容
      Array.from(state.content.childNodes).forEach((node) => {
        editor.appendChild(node.cloneNode(true))
      })

      // 恢复选区 - 使用路径而不是直接的范围
      this.restoreSelectionPosition(editor, state.selection)

      // 触发 input 事件，确保 placeholder 和其他依赖 input 事件的逻辑正常工作
      setTimeout(() => {
        const inputEvent = new InputEvent('input', {
          bubbles: true,
          cancelable: true,
          inputType: eventType, // 标识这是撤销/重做操作触发的事件
          data: null
        })
        editor.dispatchEvent(inputEvent)
      }, 0)
    }
    finally {
      this.isCapturing = false
    }
  }

  /**
   * 保存选区位置 - 使用DOM路径
   * @param editor 编辑器元素
   * @returns 选区路径信息
   */
  private saveSelectionPosition(editor: HTMLElement): {
    startPath: number[]
    startOffset: number
    endPath: number[]
    endOffset: number
    isCollapsed: boolean
  } {
    const sel = window.getSelection()

    // 默认值 - 选中编辑器开头
    const defaultPosition = {
      startPath: [0, 0],
      startOffset: 0,
      endPath: [0, 0],
      endOffset: 0,
      isCollapsed: true
    }

    if (!sel || sel.rangeCount === 0) return defaultPosition

    const range = sel.getRangeAt(0)

    try {
      return {
        startPath: this.getNodePath(editor, range.startContainer),
        startOffset: range.startOffset,
        endPath: this.getNodePath(editor, range.endContainer),
        endOffset: range.endOffset,
        isCollapsed: range.collapsed
      }
    }
    catch (e) {
      // console.error('保存选区位置失败:', e);
      return defaultPosition
    }
  }

  /**
   * 恢复选区位置 - 使用DOM路径
   * @param editor 编辑器元素
   * @param position 选区路径信息
   */
  private restoreSelectionPosition(
    editor: HTMLElement,
    position: {
      startPath: number[]
      startOffset: number
      endPath: number[]
      endOffset: number
      isCollapsed: boolean
    }
  ): void {
    try {
      const startNode = this.getNodeByPath(editor, position.startPath)
      const endNode = this.getNodeByPath(editor, position.endPath)

      if (!startNode || !endNode) {
        // 降级：选择编辑器中的第一个文本节点
        this.selectFirstTextPosition(editor)
        return
      }

      const range = document.createRange()
      range.setStart(startNode, Math.min(position.startOffset, this.getNodeLength(startNode)))

      if (position.isCollapsed) {
        range.collapse(true)
      }
      else {
        range.setEnd(endNode, Math.min(position.endOffset, this.getNodeLength(endNode)))
      }

      const sel = window.getSelection()
      if (sel) {
        sel.removeAllRanges()
        sel.addRange(range)
      }
    }
    catch (e) {
      // console.error('恢复选区位置失败:', e);
      // 降级：选择编辑器中的第一个文本节点
      this.selectFirstTextPosition(editor)
    }
  }

  /**
   * 获取节点在DOM树中的路径
   * @param root 根节点
   * @param node 目标节点
   * @returns 路径数组
   */
  private getNodePath(root: Node, node: Node): number[] {
    const path: number[] = []
    let current: Node | null = node

    while (current && current !== root) {
      let index = 0
      let sibling = current.previousSibling

      while (sibling) {
        index++
        sibling = sibling.previousSibling
      }

      path.unshift(index)
      current = current.parentNode
    }

    if (current !== root) {
      // 节点不在根节点内
      return [0]
    }

    return path
  }

  /**
   * 根据路径获取节点
   * @param root 根节点
   * @param path 路径数组
   * @returns 目标节点
   */
  private getNodeByPath(root: Node, path: number[]): Node | null {
    let current: Node = root

    for (const index of path) {
      if (!current.childNodes || index >= current.childNodes.length) {
        return null
      }
      current = current.childNodes[index]
    }

    return current
  }

  /**
   * 获取节点的长度
   * @param node 节点
   * @returns 长度
   */
  private getNodeLength(node: Node): number {
    if (node.nodeType === Node.TEXT_NODE) {
      return (node as Text).length
    }
    return node.childNodes.length
  }

  /**
   * 选择编辑器中的第一个可能的文本位置
   * @param editor 编辑器元素
   */
  private selectFirstTextPosition(editor: HTMLElement): void {
    // 找到第一个文本节点
    const walker = document.createTreeWalker(editor, NodeFilter.SHOW_TEXT, null)

    const firstTextNode = walker.nextNode()
    if (firstTextNode) {
      const range = document.createRange()
      range.setStart(firstTextNode, 0)
      range.collapse(true)

      const sel = window.getSelection()
      if (sel) {
        sel.removeAllRanges()
        sel.addRange(range)
      }
    }
    else {
      // 没有文本节点，选择编辑器本身
      const range = document.createRange()
      range.setStart(editor, 0)
      range.collapse(true)

      const sel = window.getSelection()
      if (sel) {
        sel.removeAllRanges()
        sel.addRange(range)
      }
    }
  }

  /**
   * 检查两个状态之间是否有实质性变化
   * @param oldContent 旧内容
   * @param newContent 新内容
   * @returns 是否有实质性变化
   */
  private hasMeaningfulChange(oldContent: DocumentFragment, newContent: DocumentFragment): boolean {
    // 比较文本内容
    const oldText = oldContent.textContent || ''
    const newText = newContent.textContent || ''

    // 如果文本内容不同，肯定有变化
    if (oldText !== newText) {
      //   console.log('文本内容不同，有实质变化');
      return true
    }

    // 即使文本相同，也要检查DOM结构
    // 比较子节点数量
    const oldChildCount = this.countNodes(oldContent)
    const newChildCount = this.countNodes(newContent)

    if (oldChildCount !== newChildCount) {
      //   console.log('节点数量不同，有实质变化');
      return true
    }

    // 比较第一级子节点类型和属性
    const oldChildren = Array.from(oldContent.childNodes)
    const newChildren = Array.from(newContent.childNodes)

    for (let i = 0; i < oldChildren.length; i++) {
      const oldChild = oldChildren[i]
      const newChild = newChildren[i]

      // 比较节点类型
      if (oldChild.nodeType !== newChild.nodeType) {
        //   console.log('节点类型不同，有实质变化');
        return true
      }

      // 比较元素节点的标签名
      if (oldChild.nodeType === Node.ELEMENT_NODE && (oldChild as Element).tagName !== (newChild as Element).tagName) {
        //   console.log('元素标签名不同，有实质变化');
        return true
      }
    }

    // 如果都一样，说明没有实质变化
    return false
  }

  /**
   * 计算节点及其子节点的总数
   * @param node 节点
   * @returns 节点总数
   */
  private countNodes(node: Node): number {
    let count = 1
    if (node.childNodes) {
      for (let i = 0; i < node.childNodes.length; i++) {
        count += this.countNodes(node.childNodes[i])
      }
    }
    return count
  }

  /**
   * 导出历史记录（用于持久化）
   * @returns 序列化后的历史记录
   */
  public exportHistory(): string {
    // 简单实现：只保存当前状态的文本内容
    if (this.pointer >= 0 && this.stack.length > 0) {
      const currentState = this.stack[this.pointer]
      return currentState.content.textContent || ''
    }
    return ''
  }

  /**
   * 从字符串导入历史记录
   * @param history 历史记录
   * @param editor 编辑器元素
   */
  public importHistory(history: string, editor: HTMLElement): void {
    if (!history) return

    // 简单实现：只恢复文本内容
    editor.textContent = history
    this.captureState(editor, false)
  }

  /**
   * 清理撤销管理器资源
   * 用于组件销毁时清理定时器
   */
  public cleanup(): void {
    if (this.captureTimer) {
      clearTimeout(this.captureTimer)
      this.captureTimer = null
    }

    // 重置捕获状态
    this.isCapturing = false
  }

  /**
   * 重置撤销栈
   * 清空所有历史记录
   */
  public reset(): void {
    this.cleanup()
    this.stack = []
    this.pointer = -1
  }
}
