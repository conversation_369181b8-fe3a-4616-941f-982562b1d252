<template>
  <!-- 不要使用 h-full 否则无法将底部的翻译按钮撑到底部 -->
  <div class="flex h-auto flex-col rounded-lg rounded-t-none px-3 pt-3 pb-2">
    <!-- 输入框顶部操作区 -->
    <div class="mb-2 flex h-6 flex-shrink-0 items-center justify-between">
      <!-- 输入框左侧操作区域 -->
      <Operate :is-empty="isEmpty" @restore="restoreOriginalContent" @clear="clearInputContent" />
    </div>

    <!-- 输入框 -->
    <div class="relative flex h-full flex-col">
      <div
        ref="textareaEditable"
        contenteditable="true"
        :class="{ 'has-content': !isEmpty }"
        class="editable-textarea delete-scrollbar max-h-[50vh] min-h-[12.5rem] px-2 pt-1 pb-2 font-sans leading-7 max-sm:max-h-[14.5rem] dark:text-neutral-400"
        @input="handleImmediateInput"
        @compositionend="handleCompositionEnd"
        @compositionstart="handleCompositionStart"
      />

      <!-- 占位符 -->
      <!-- 请至少选择一个翻译模型 / 请输入要翻译的文本 -->
      <div
        v-show="isEmpty"
        :data-text="getCurrentTranslateModel.length === 0 ? t('selected_trans.textarea.select_at_least_one_model') : t('selected_trans.textarea.text_to_be_translated')"
        class="placeholder absolute top-1 left-2"
      />
    </div>
    <!-- 字数统计显示 -->
    <div
      v-if="!isSingleSentenceAiSelect"
      class="char-count pointer-events-none absolute bottom-2 left-4 text-xs text-gray-400 select-none"
      :class="{ 'text-red-500': currentCharCount > getCurrentMaxTextLength() }"
    >
      {{ currentCharCount }} / {{ getCurrentMaxTextLength() }}
    </div>
    <!-- 翻译按钮 -->
    <div v-if="getCurrentTranslateModel.length > 0" class="mt-2 flex flex-shrink-0 justify-end">
      <UButton
        square
        :label="isShowTranslateBtn"
        size="xl"
        variant="solid"
        :class="['px-3', 'rounded-[7px]', translateButtonClasses]"
        :disabled="currentTranslateMode !== 'traditional' && (isTranslateStatus || isAiSelectLoading)"
        @click="handleClickTranslate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import Operate from '@/pages/text/components/Textarea/components/Operate.vue'
import { initializeEditor } from './js/render-dom'
import { storeToRefs } from 'pinia'
import { isEmptyContent } from '@/utils/utils'
import { restoreEditorState, storeEditorState } from '@/pages/text/components/Textarea/js/dom-serializer'
import { detectLanguage } from '@/pages/text/js/utils/detectLanguage'
import { TranslationStatus } from '@/types/translateOptions'
import { contenteditableInsertText } from '@/pages/text/js/utils/editableInsertText'

// 清除输入框内容
import { clearEditorContent } from '@/pages/text/components/Textarea/js/dom-utils'

// 存储
import { useInputTranslate } from '@/store/inputTranslate'

// 事件监听
import { eventListening } from '@/pages/text/translate/eventListening'

/** 使用共享数据 */
import { sharedState } from '@/pages/text/useData/sharedState'

// 翻译状态管理
import { useTranslate } from '@/pages/text/translate/translate.ts'

// 处理dom的工具函数
import { useDomControl } from '@/pages/text/translate/domControl'

// 导入翻译状态管理
import { translationState } from '@/pages/text/useData/useTranslationState'

const route = useRoute()

// 定义组件可触发的事件
const emit = defineEmits(['content-cleared'])
const { handleSentenceClickHighlight, cleanup } = eventListening()

const useInputTransStore = useInputTranslate()

const { enabledEnginesList, traditionalEngines, isSingleSentenceAiSelect, originalLanguage, targetLanguage, currentTranslateMode, originalLanguageList, isAutoTranslate }
  = storeToRefs(useInputTransStore)
const { sourceDOMStructure, isTranslateStatus, reTrans, reSelect, messageEvent, originalTextList, isAiSelectLoading } = sharedState
const { buildOriginalTextListSendRequest } = useTranslate

const { resetAllTranslateData } = useDomControl
const { updateTranslationEngineStatus } = translationState

// 国际化
const { t, locale } = useI18n()

// 定义最大文本长度常量
const MAX_TEXT_LENGTH = 3000

// 定义择优翻译最大长度
const MAX_SELECT_TEXT_LENGTH = 2000

// 判断是否为全文翻译
const isSingleSentence = computed(() => {
  return currentTranslateMode.value === 'aiSelect' && isSingleSentenceAiSelect.value
})

/**
 * 存储输入框内容
 */
const inputContent = ref('')

/**
 * 判断是否显示立即翻译按钮
 */
const isShowTranslateBtn = computed(() => {
  // 如果处于重新翻译状态
  if (reTrans.value && originalTextList.value.isTranslated) {
    const operationKeys = {
      aiSelect: 'selected_trans.operation.re_scoring', // 重新择优
      compare: 'selected_trans.operation.re_compare', // 重新对比
      classic: 'selected_trans.operation.re_translate' // 重新翻译
    }
    return t(operationKeys[currentTranslateMode.value] || operationKeys.classic)
  }

  // 正常翻译状态
  const translationKeys = {
    aiSelect: 'selected_trans.operation.ai_selected_translation_btn', // 择优翻译
    compare: 'selected_trans.operation.compare_translation_btn2', // 对比翻译
    classic: 'selected_trans.operation.traditional_ranslation_btn2' // 传统翻译
  }
  return t(translationKeys[currentTranslateMode.value] || translationKeys.classic)
})

/**
 * 根据当前选择的模式选择模型
 */
const getCurrentTranslateModel = computed(() => {
  return currentTranslateMode.value === 'aiSelect' ? enabledEnginesList.value : [traditionalEngines.value.value]
})

// 是否为空- 用于判断是否显示占位符
const isEmpty = ref(true)

// 输入框的dom元素
const textareaEditable = ref(null)

// 原文节点
const originalSplitNode = ref([])

// 使用 Nuxt UI 的通知系统
const toast = useToast()

// 添加一个变量记录是否处于输入法编辑状态
const isComposing = ref(false)

/**
 * 计算当前用户的使用状态
 * 判断用户是否开启了自动翻译， 并且当前不是逐句择优翻译模式
 */
const isAutoTranslateAndTraditional = computed(() => {
  return isAutoTranslate.value && currentTranslateMode.value === 'traditional'
})

/**
 * 获取当前适用的最大文本长度
 * @returns {number} - 当前应用的最大文本长度限制
 */
const getCurrentMaxTextLength = () => {
  // 逐句择优翻译模式不限制字数
  if (isSingleSentence.value) {
    return Number.MAX_SAFE_INTEGER // 使用一个非常大的数值表示不限制
  }
  return currentTranslateMode.value === 'aiSelect' ? MAX_SELECT_TEXT_LENGTH : MAX_TEXT_LENGTH
}

/**
 * 显示文本长度超限提示
 */
const showTextLengthExceededToast = () => {
  const maxLength = getCurrentMaxTextLength()
  toast.add({
    title: t('selected_trans.error.text_length_exceeded'),
    description: t('selected_trans.error.text_length_exceeded_desc', { max: maxLength }),
    color: 'error',
    icon: 'i-mdi-alert-circle',
    duration: 1500
  })
}

/**
 * 处理输入框的输入事件
 * @param {*} event
 */
const handleImmediateInput = async (event) => {
  const target = event.target
  // 获取输入框的文本内容
  const content = target.innerHTML || ''

  /**
   * 创建一个div用于处理内容，先检查innerHTML是否只包含段落标签和换行符
   */
  const div = document.createElement('div')
  div.innerHTML = content

  // 获取纯文本内容
  const textContent = div.textContent
  currentCharCount.value = textContent.length

  // 防止用户输入完文本之后点击清除还是会进入翻译
  inputContent.value = textContent

  // 关闭占位符 - 使用isEmptyContent函数检测是否为空
  isEmpty.value = isEmptyContent(textContent)

  // 如果内容为空的处理
  if (isEmptyContent(textContent)) {
    // 如果内容为空，则重置翻译状态
    updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
    isTranslateStatus.value = false
    sourceDOMStructure.value = ''
    // 重置所有翻译数据
    resetAllTranslateData()

    // 触发内容清除事件，通知父组件清除URL参数
    emit('content-cleared')
    return
  }

  // 检查文本长度是否超出限制，则不进行处理
  if (currentCharCount.value > getCurrentMaxTextLength()) {
    showTextLengthExceededToast()
    return
  }

  // 如果不是从历史记录恢复，才执行翻译相关操作
  if (window && !(window as any).isRestoringFromHistory) {
    /**
     * 检测原文语言
     * 检测到语言之后如果原文跟目标语言相同则切换目标语言
     */
    sendDetectLanguage(content)

    // 增加一个条件判断：如果当前正在翻译中，先取消之前的翻译
    if (isTranslateStatus.value) {
      resetAllTranslateData()
    }

    // 如果是中文输入法正在输入中，不进行句子拆分和翻译
    if (event.inputType === 'insertCompositionText' || isComposing.value) {
      return
    }

    // 仅在不是中文输入法输入过程中才进行句子拆分
    // 立即设置所有启用的翻译引擎为"翻译中"状态
    if (isAutoTranslateAndTraditional.value) {
      // 设置翻译状态为"翻译中"
      if (getCurrentTranslateModel.value.length > 0) {
        updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.Translating)
      }

      // 如果不在输入法输入中，则进行句子拆分和翻译
      await handleInputTextSplitShow(content)
    }
  }
}

// 监听输入法完成事件的处理函数
const handleCompositionEnd = async (event) => {
  // 设置一个短暂的延迟再将isComposing设为false
  // 这有助于防止输入法完成和回车键事件紧密相连时的问题
  setTimeout(() => {
    isComposing.value = false
  }, 20)

  const target = event.target
  const content = target.innerHTML || ''

  // 创建div获取纯文本内容
  const div = document.createElement('div')
  div.innerHTML = content
  const textContent = div.textContent || ''

  currentCharCount.value = textContent.length

  // 输入法输入完成后再进行句子拆分和翻译
  if (isAutoTranslateAndTraditional.value) {
    // 只有当内容不为空且有启用的翻译引擎时才设置翻译状态
    if (getCurrentTranslateModel.value.length > 0 && !isEmptyContent(textContent)) {
      // 使用防抖处理拆分和翻译
      await handleInputTextSplitShow(content)
    }
    else {
      // 如果内容为空，则重置翻译状态
      updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
      isTranslateStatus.value = false
    }
  }
}

/**
 * 监听输入法开始输入事件
 * 用于跟踪输入法状态
 */
const handleCompositionStart = (e) => {
  isComposing.value = true

  // 设置翻译状态为"翻译中"
  if (getCurrentTranslateModel.value.length > 0 && isAutoTranslateAndTraditional.value) {
    updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.Translating)
  }
}

/**
 * 这里使用防抖
 * 因为检测语言的频率很高，所以使用防抖
 */
const handleInputTextSplitShow = useDebounceFn(async (content) => {
  if (!content) return

  /**
   * 一定要在防抖函数里面添加清除翻译数据，如果不添加，在输入框输入内容的时候，会重复添加翻译数据
   * 重置所有翻译数据- 重新翻译让数据初始化
   */
  resetAllTranslateData()

  // 创建临时div获取纯文本
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = content
  const plainText = tempDiv.textContent || ''

  // 检查内容是否全为空白字符（空格、制表符等）
  // 注意：这里只是检查，不会影响原始文本中的空格
  const hasNonWhitespace = plainText.trim().length > 0

  if (!hasNonWhitespace) {
    // 如果内容为空，则重置翻译状态
    updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
    isTranslateStatus.value = false
    return
  }

  try {
    // 在防抖处理完成后，如果有原始节点且需要自动翻译，则发送翻译请求
    // 将发送请求的逻辑移到这里，确保只在输入完成且处理完文本后才发送一次请求
    if (isAutoTranslate.value && currentTranslateMode.value === 'traditional') {
      // 检查原文语言与目标语言是否相同 (额外检查)
      if (originalLanguage.value === targetLanguage.value) {
        showSameLanguageToast()
        updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
        isTranslateStatus.value = false
      }
      else {
        // console.log('inputContent', inputContent.value);

        // 防止用户输入完文本之后点击清除还是会进入翻译
        if (inputContent.value.trim()) {
          // console.log('执行翻译前的准备工作- 发送请求翻译');
          // 执行翻译前的准备工作- 发送请求翻译
          buildOriginalTextListSendRequest()
        }
      }
    }
  }
  catch (error) {
    // 错误处理：重置翻译状态
    updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
    isTranslateStatus.value = false
  }
}, 1000)

/**
 * 清空输入框内容
 * 并且清空虽有翻译数据
 */
const clearInputContent = () => {
  // 重置所有翻译数据- 重新翻译让数据初始化
  resetAllTranslateData()

  // 清除输入框内容
  clearEditorContent()

  // 重置翻译状态
  updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
  isTranslateStatus.value = false

  // 清理翻译事件监听器
  window.removeEventListener('message', messageEvent.value.translateMessage)
  window.removeEventListener('message', messageEvent.value.aiSelectTranslateMessage)

  // 保存原始文本内容
  sourceDOMStructure.value = ''
  currentCharCount.value = 0
  isEmpty.value = true // 更新状态
  originalSplitNode.value = []

  // 触发内容清除事件，通知父组件清除URL参数
  emit('content-cleared')

  // 手动触发输入事件，确保内容变化被检测到
  if (textareaEditable.value) {
    // 创建并分发自定义input事件
    const inputEvent = new Event('input', { bubbles: true })
    textareaEditable.value.dispatchEvent(inputEvent)
  }
}

/**
 * 立即翻译
 */
const handleClickTranslate = () => {
  // 检查原文语言与目标语言是否相同
  if (originalLanguage.value === targetLanguage.value) {
    const { hasContent } = getTextareaContent()
    if (hasContent) {
      showSameLanguageToast()
      updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
      isTranslateStatus.value = false
      return
    }
  }

  if (currentCharCount.value > getCurrentMaxTextLength()) {
    showTextLengthExceededToast()
    return
  }

  const content = textareaEditable.value.innerHTML

  // 过滤掉换行符如果还是没有文本就直接返回
  if (isEmptyContent(textareaEditable.value.textContent)) return

  // 创建一个临时div来获取纯文本内容
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = content

  // 立即设置所有启用的翻译引擎为"翻译中"状态
  if (getCurrentTranslateModel.value.length > 0 && isEmptyContent(tempDiv.textContent)) {
    updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.Translating)
  }

  // 重置所有翻译数据- 重新翻译让数据初始化
  resetAllTranslateData()

  // 执行翻译前的准备工作- 发送请求翻译
  buildOriginalTextListSendRequest()

  // 重置重新翻译状态
  reTrans.value = false
  reSelect.value = false
}

/**
 * 还原输入框内容到原始状态
 */
const restoreOriginalContent = () => {
  if (sourceDOMStructure.value) {
    // 添加临时标志，标识这是从记录恢复的内容
    (window as any).isRestoringFromHistory = true

    // 重置所有翻译数据
    resetAllTranslateData()

    // 清除输入框内容
    textareaEditable.value.innerHTML = ''

    // 还原输入框内容
    restoreEditorState(textareaEditable.value, sourceDOMStructure.value)

    // 更新空状态
    isEmpty.value = isEmptyContent(sourceDOMStructure.value);

    // 移除临时标志
    (window as any).isRestoringFromHistory = false
  }
}

/**
 * 当前输入框的字数统计，统计纯文本长度
 * @returns {number} 当前输入框的字符数
 */
const currentCharCount = ref(0)

let observer = null

onMounted(() => {
  // 初始化输入框
  initializeEditor(textareaEditable.value)

  handleUrlParams()

  /**
   * 添加事件监听
   * 当输入框的 dom 发生变化时，重新绑定点击事件防止在择优翻译逐句的时候点击原文句子无法高亮句子
   */

  observer = new MutationObserver(() => {
    const hasSentenceHighlightClass = textareaEditable.value && textareaEditable.value.querySelector('.sentence-selected-highlight')
    if (currentTranslateMode.value === 'aiSelect' && isSingleSentenceAiSelect.value && hasSentenceHighlightClass) {
      handleSentenceClickHighlight()
    }
    else {
      cleanup()
    }
  })

  if (textareaEditable.value) {
    observer.observe(textareaEditable.value, {
      attributes: true,
      attributeFilter: ['class'],
      subtree: true // 只监听 class 变化
    })
  }
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  // 移除翻译事件监听器, 防止内存泄漏
  window.removeEventListener('message', messageEvent.value.translateMessage)
  window.removeEventListener('message', messageEvent.value.aiSelectTranslateMessage)

  if (textareaEditable.value) {
    textareaEditable.value.removeEventListener('compositionstart', () => {
      isComposing.value = true
    })

    textareaEditable.value.removeEventListener('compositionend', handleCompositionEnd)

    observer.disconnect()
  }

  // 组件卸载时重置所有翻译数据
  resetAllTranslateData()
})

/**
 * 显示原文语言与目标语言相同的提示
 * @param {string} sourceCode - 检测到的源语言代码
 * @param {string} targetCode - 当前目标语言代码
 */
const showSameLanguageToast = () => {
  toast.add({
    title: t('selected_trans.error.same_language'),
    description: t('selected_trans.error.same_language_desc'),
    color: 'warning',
    icon: 'i-mdi-alert-circle',
    duration: 1500
  })
}

/**
 * 这里使用防抖
 * 因为检测语言的频率很高，所以使用防抖
 */
const sendDetectLanguage = useDebounceFn((content) => {
  // 检测原文语言
  detectLanguage(content, t('selected_trans.auto_detect'), locale.value, originalLanguageList.value)
}, 200)

// 添加一个计算属性来根据是否为重新翻译返回 Tailwind 类
const translateButtonClasses = computed(() => {
  // 如果是重新翻译模式（重新择优或重新对比）
  if (reTrans.value) {
    return 'bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white'
  }
  else {
    // 否则，对于普通翻译模式（择优、对比、传统）
    return 'bg-primary-500 hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 text-white'
  }
})

// 添加监听器以确保 reTrans 的变化会强制更新计算属性
watch(
  reTrans,
  (newValue) => {
    // 这里不需要额外操作，只需确保 Vue 监听到变化即可
  },
  { immediate: true }
)

// 监听当前用户切换 tab 栏是否需要自动翻译
watch(currentTranslateMode, (newValue, oldValue) => {
  if (newValue === 'traditional' && isAutoTranslate.value) {
    handleClickTranslate()
  }
})

// 监听原文与译文语言是否相同
watch([originalLanguage, targetLanguage], (newValue, oldValue) => {
  // 只在传统翻译模式且开启自动翻译时处理
  if (currentTranslateMode.value === 'traditional' && isAutoTranslate.value) {
    const { hasContent } = getTextareaContent()

    // 如果没有内容，不进行任何处理
    if (!hasContent) {
      return
    }

    if (newValue[0] === newValue[1]) {
      // 语言相同时的处理
      showSameLanguageToast()
      updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
      isTranslateStatus.value = false
      // 重置所有翻译数据
      resetAllTranslateData()
      // 清除历史记录相关数据
      sourceDOMStructure.value = ''
      // 触发内容清除事件，通知父组件清除URL参数
      emit('content-cleared')
    }
    else {
      // 语言不同时，触发翻译
      isTranslateStatus.value = true
      updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.Translating)
      handleClickTranslate()
    }
  }
})

// 监听自动翻译是否开启，如果开启，则立即翻译
watch(isAutoTranslate, (newVal, oldVal) => {
  if (
    newVal // 自动翻译刚刚被打开
    && currentTranslateMode.value === 'traditional'
    && inputContent.value.trim() // 输入框有内容
  ) {
    // 触发自动翻译
    handleClickTranslate()
  }
})

/**
 * 获取输入框内容并处理相关状态
 * @returns {Object} 包含 hasContent 属性，表示是否有有效内容
 */
const getTextareaContent = () => {
  const target = textareaEditable.value
  // 获取输入框的文本内容
  const content = target.innerHTML || ''
  /**
   * 创建一个div用于处理内容，先检查innerHTML是否只包含段落标签和换行符
   */
  const div = document.createElement('div')
  div.innerHTML = content

  // 获取纯文本内容
  const textContent = div.textContent
  currentCharCount.value = textContent.length

  // 防止用户输入完文本之后点击清除还是会进入翻译
  inputContent.value = textContent

  // 关闭占位符 - 使用isEmptyContent函数检测是否为空
  isEmpty.value = isEmptyContent(textContent)

  // 如果内容为空或自动翻译关闭，返回false
  if (isEmptyContent(textContent)) {
    // 如果内容为空，则重置翻译状态
    updateTranslationEngineStatus(getCurrentTranslateModel.value, TranslationStatus.NotTranslated)
    isTranslateStatus.value = false
    sourceDOMStructure.value = ''
    // 重置所有翻译数据
    resetAllTranslateData()

    // 触发内容清除事件，通知父组件清除URL参数
    emit('content-cleared')

    return { hasContent: false }
  }
  return { hasContent: true }
}

/**
 * 处理URL参数，填充内容到输入框
 */
const handleUrlParams = async () => {
  // 检查URL是否包含content参数
  if (route.query.content) {
    // 延迟执行，确保组件完全渲染
    await nextTick()

    // 添加额外的延迟确保DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 100))

    // 使用组件的 ref 而不是 document.querySelector
    const textarea = textareaEditable.value

    if (textarea) {
      const content = decodeURIComponent(route.query.content as string)

      // 清空输入框内容
      textarea.innerHTML = ''

      // 这里一定要添加上这个 等待 nextTick否则，内容无法写入输入框
      await nextTick()

      // 使用 contenteditableInsertText 方法插入文本
      contenteditableInsertText(textarea, content)

      // 如果有mode参数且为aiSelect，可以考虑在这里触发翻译
      if (route.query.mode === 'aiSelect') {
        // 进行翻译...
      }
    }
    else {
      console.error('找不到文本输入区域组件引用')
    }
  }

  // 如果有指定mode参数，设置翻译模式
  if (route.query.mode) {
    const mode = route.query.mode as string
    if (['aiSelect', 'compare', 'classic'].includes(mode)) {
      useInputTransStore.setCurrentTranslateMode(mode as any)
    }
  }

  // 如果有指定targetLang参数，设置目标语言
  if (route.query.targetLang) {
    try {
      const targetLang = decodeURIComponent(route.query.targetLang as string)
      useInputTransStore.setTargetLanguage(targetLang)
      console.log('目标语言已设置:', targetLang)
    }
    catch (error) {
      console.error('设置目标语言失败:', error)
    }
  }
}

// 暴露方法给父组件
defineExpose({
  // 暴露方法给父组件
  clearInputContent,
  restoreOriginalContent
})
</script>

<style scoped>
.editable-textarea {
  /* padding: 0.625rem 0.6875rem 1rem 0.6875rem; */
  white-space: pre-wrap;
  outline: none;
  /* 移除默认的焦点轮廓 */
  word-break: break-all;
  /* 原有样式保持不变 */
  transition: all 0.3s ease;
  /* 添加平滑滚动 */
  scroll-behavior: smooth;
  /* 优化光标样式 */
  caret-color: #1890ff;
  /* 添加垂直滚动但隐藏滚动条 */
  overflow-y: auto;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* 请不要在输入框内添加-webkit-user-modify: read-write-plaintext-only;这个属性会让浏览器倾向于将内容作为纯文本处理，可能会移除HTML结构。 */
}

/* 添加Chrome/Safari滚动条隐藏 */
.editable-textarea::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}

/* 确保光标在所有情况下可见 */
.editable-textarea:focus {
  caret-color: #1890ff;
  outline: none;
}

/* 防止滚动时光标丢失 */
.editable-textarea::selection {
  background-color: rgba(24, 144, 255, 0.2);
}

.placeholder {
  color: #999999;
  pointer-events: none;
  /* 阻止扩展的 hover 事件 */
  border: none;
  transition: opacity 0.2s ease;
  opacity: 0.6;
  z-index: 1;
}

div:empty:not(:focus):before {
  content: attr(data-text);
}

/* 字数统计 */
.char-count {
  z-index: 2;
  user-select: none;
}
</style>
