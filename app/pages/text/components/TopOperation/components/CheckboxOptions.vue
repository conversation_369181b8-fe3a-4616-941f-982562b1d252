<template>
  <!-- 自动翻译 -->
  <div v-if="currentTranslateMode === 'traditional'" class="checkbox-group" @click="handleSettingChange('isAutoTranslate')">
    <UCheckbox v-model="isAutoTranslate" @update:model-value="handleSettingChange('isAutoTranslate')" />
    <span class="text-sm whitespace-nowrap">{{ t('selected_trans.settings.auto_translate') }}</span>
  </div>

  <template v-if="currentTranslateMode === 'aiSelect'">
    <div class="checkbox-group" @click="handleSettingIsSingleSentenceAiSelectChange('isSingleSentenceAiSelect')">
      <UCheckbox v-model="isSingleSentenceAiSelect" @update:model-value="handleSettingIsSingleSentenceAiSelectChange('isSingleSentenceAiSelect')" />
      <!-- 逐句择优 -->
      <span class="text-sm whitespace-nowrap">{{ t('selected_trans.settings.selected_mode') }}</span>
      <UPopover
        :mode="isMobileDevice() ? 'click' : 'hover'"
        arrow
        :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
      >
        <!-- 这里添加 @click.stop 是为了防止点击后触发 handleSettingIsAiSelectAnalysisChange 方法 -->
        <UIcon name="heroicons:information-circle" class="size-4 shrink-0 text-neutral-400" @click.stop />
        <template #content>
          <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
            <!-- 启用：逐句择优，关闭：全文择优 -->
            {{ t('selected_trans.settings.selected_mode_desc') }}
          </div>
        </template>
      </UPopover>
    </div>

    <div class="checkbox-group" @click="handleSettingIsAiSelectAnalysisChange('isAiSelectAnalysis')">
      <UCheckbox v-model="isAiSelectAnalysis" @update:model-value="handleSettingIsAiSelectAnalysisChange('isAiSelectAnalysis')" />
      <!-- 择优分析 -->
      <span class="text-sm whitespace-nowrap">{{ t('selected_trans.settings.selected_rationale') }}</span>
      <UPopover
        :mode="isMobileDevice() ? 'click' : 'hover'"
        arrow
        :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
      >
        <!-- 这里添加 @click.stop 是为了防止点击后触发 handleSettingIsAiSelectAnalysisChange 方法 -->
        <UIcon name="heroicons:information-circle" class="size-4 shrink-0 text-neutral-400" @click.stop />
        <template #content>
          <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
            <!-- 显示择优评分的说明（会增加翻译额度使用量） -->
            {{ t('selected_trans.settings.selected_rationale_desc') }}
          </div>
        </template>
      </UPopover>
    </div>
  </template>
</template>

<script setup lang="ts">
import { useInputTranslate } from '@/store/inputTranslate'
import { sharedState } from '@/pages/text/useData/sharedState'
import { useTranslate } from '@/pages/text/translate/translate'

const { reSelect, originalTextList, reTrans, currentTranslateStatus } = sharedState

const { buildOriginalTextListSendRequest } = useTranslate

const useInputTransStore = useInputTranslate()
const { isSingleSentenceAiSelect, isAiSelectAnalysis, currentTranslateMode, isAutoTranslate, aiSelectModel } = storeToRefs(useInputTransStore)

// 国际化
const { t } = useI18n()

/**
 * 判断本次翻译的逐句翻译模式是否跟当前设置的逐句翻译模式一致
 * 如果一直就不需要重新择优翻译
 * ! isSingleSentence是本次翻译之后存储的一个翻译模式响应式数据
 * ! isSingleSentenceAiSelect是当前存储到store的翻译模式
 */
const isSingleSentenceAiSelectModeChanged = computed(() => {
  return currentTranslateStatus.value.isSingleSentence == isSingleSentenceAiSelect.value
})
/**
 * 判断本次翻译的分析翻译模式是否跟当前设置的分析翻译模式一致
 */
const isAiSelectAnalysisChanged = computed(() => {
  return currentTranslateStatus.value.isAiSelectAnalysis == isAiSelectAnalysis.value
})

/**
 * 判断当前翻译选中的择优翻译模型是否跟当前存储的择优翻译模型一致
 */
const isSelectedAiSelectModelChanged = computed(() => {
  return currentTranslateStatus.value.currentSelectedAiSelectModel === aiSelectModel.value.value
})

/**
 * 逐句翻译设置变更
 * @param settingKey 设置项的键名
 */
const handleSettingIsSingleSentenceAiSelectChange = (settingKey) => {
  // 更新存储
  handleSettingChange(settingKey)

  /** 判断是否有原文并且 是不是择优翻译的 tab */
  if (currentTranslateMode.value !== 'aiSelect') return

  // 如果两个翻译模式(1.isSingleSentence是本次翻译之后存储的一个翻译模式,2.isSingleSentenceAiSelect是当前存储到store的翻译模式)不一致就显示重新择优
  if (originalTextList.value.isTranslated && !isSingleSentenceAiSelectModeChanged.value) {
    reTrans.value = true
    // 将重新择优翻译关闭
    reSelect.value = false
  }
  else {
    reTrans.value = false
  }

  /**
   * 1. 判断当前是否开启了翻译（originalTextList.value 在开启翻译的时候会将原文赋值给到它）
   * 2. 判断当前选中的择优翻译模型是否跟当前存储的择优翻译模型不一致
   * 3. 判断本次翻译模式是否跟当前设置的翻译模式一致
   * 条件通过之后才会执行重新择优翻译按钮
   */
  if (originalTextList.value.isTranslated && !isSelectedAiSelectModelChanged.value && isSingleSentenceAiSelectModeChanged.value) {
    reSelect.value = true
    reTrans.value = false
  }
}

/**
 * 择优分析设置变更
 * @param settingKey 设置项的键名
 */
const handleSettingIsAiSelectAnalysisChange = (settingKey) => {
  // 更新存储
  handleSettingChange(settingKey)

  /** 判断是否有原文并且 是不是择优翻译的 tab */
  if (originalTextList.value.isTranslated && currentTranslateMode.value === 'aiSelect') {
    // 判断用户是否开启/关闭择优分析
    const isAiSelectAnalysisDisabled = isAiSelectAnalysis.value && settingKey === 'isAiSelectAnalysis'

    // 1. 判断用户是否开启了择优分析, 2. 判断本次翻译模式是否跟当前设置的翻译模式一致
    if (isAiSelectAnalysisDisabled && isSingleSentenceAiSelectModeChanged.value) {
      reSelect.value = true
    }
    else {
      reSelect.value = false
    }

    /**
     * 1. 判断当前是否开启了翻译（originalTextList.value 在开启翻译的时候会将原文赋值给到它）
     * 2. 判断当前选中的择优翻译模型是否跟当前存储的择优翻译模型不一致
     * 3. 判断本次翻译模式是否跟当前设置的翻译模式一致
     * 条件通过之后才会执行重新择优翻译按钮
     */
    if (originalTextList.value.isTranslated && isSelectedAiSelectModelChanged.value && isAiSelectAnalysisChanged.value) {
      reSelect.value = false
      reTrans.value = false
    }
  }
}

/**
 * 择优翻译设置变更
 * @param settingKey 设置项的键名
 */
const handleSettingChange = (settingKey) => {
  // 所有设置项都是直接从UI组件同步到store，可简化为通用逻辑
  if (settingKey in useInputTransStore) {
    // 切换当前设置项的值（取反）并同步到store
    useInputTransStore[settingKey] = !useInputTransStore[settingKey]
  }

  // 如果用户开启了自动翻译
  if (settingKey === 'isAutoTranslate' && originalTextList.value.isTranslated && isAutoTranslate.value && currentTranslateMode.value === 'traditional') {
    buildOriginalTextListSendRequest()
  }
}
</script>

<style scoped>
.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.3125rem;
  cursor: pointer;
  padding: 0.375rem;
  border-radius: 0.5rem;
  background-color: #fff;
  transition: background-color 0.3s ease;
}
.dark .checkbox-group {
  background-color: var(--ui-color-neutral-800);
}
.checkbox-group:hover {
  background-color: #f0f0f0;
}
.dark .checkbox-group:hover {
  background-color: #333;
}
</style>
