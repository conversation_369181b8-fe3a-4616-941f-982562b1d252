<!-- 精挑翻译模型选择设置 -->
<template>
  <div class="flex items-center gap-1 max-sm:w-full max-sm:flex-col max-sm:items-stretch">
    <!-- 择优模型 -->
    <div class="relative flex flex-col max-sm:w-full">
      <!-- 用于择优评分和分析的 AI 模型 -->
      <div class="mr-2 flex items-center gap-1 max-sm:mr-0 max-sm:w-full">
        <!-- 使用UPopover组件实现下拉菜单 -->
        <UPopover>
          <!-- 触发按钮 -->
          <UButton
            variant="ghost"
            color="neutral"
            type="button"
            class="flex h-10 w-auto items-center gap-2 rounded-md border border-gray-200 bg-white px-2 text-base focus:outline-none max-sm:w-full xl:h-10 dark:border-gray-700 dark:bg-gray-800"
          >
            <div class="flex items-center gap-2.5 truncate md:gap-2 lg:gap-2 xl:gap-2">
              <!-- 标签集成到选择器内部 -->
              <div class="flex items-center gap-1">
                <span class="text-sm text-neutral-600 md:text-sm lg:text-sm xl:text-sm dark:text-neutral-400">{{ t('selected_trans.settings.selected_model') }}:</span>
                <!-- 模型图标 -->
                <img :src="translateModeIcon[aiSelectTranslateEngine.value]" class="size-6 rounded bg-white p-0.5 sm:size-6 md:size-6 lg:size-6 xl:size-6" />
                <!-- <span v-if="aiSelectTranslateEngine.label" class="text-sm truncate max-w-20">{{ aiSelectTranslateEngine.label }}</span> -->
                <!-- 这个一般是在插件中某个模型被设置了不可用状态才会显示的 -->
                <!-- <span v-else class="text-base">{{ aiSelectTranslateEngine.value }}</span> -->
              </div>

              <!-- 模型不可用提示 -->
              <UTooltip v-if="aiSelectTranslateEngine.isAvailable === false" placement="top" :text="t('common.model_unavailable')">
                <UIcon name="i-material-symbols-error-outline" class="size-4 text-red-500" />
              </UTooltip>
            </div>
            <Icon name="heroicons:chevron-down" class="ml-auto h-4 w-4 text-gray-500" />
          </UButton>

          <!-- 下拉内容 -->
          <template #content>
            <div
              class="custom-scrollbar max-h-[min(50vh,400px)] max-w-[40rem] overflow-y-auto rounded-md border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-800"
              :style="{
                width: 'min(calc(100vw - 2rem), 40rem)'
              }"
            >
              <!-- 网格布局选项 - 响应式列数 -->
              <div class="grid auto-rows-max grid-cols-1 gap-2 p-3 sm:grid-cols-2 sm:p-4 md:p-5">
                <div
                  v-for="item in aiSelectModelList"
                  :key="item.value"
                  class="flex cursor-pointer items-start gap-2 rounded-md bg-gray-50 px-2 py-2 transition-colors duration-200 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700"
                  :class="{ 'dark:bg-gray-750 bg-gray-100': aiSelectTranslateEngine.value === item.value }"
                  @click="changeAISelectTranslateEngine(item)"
                >
                  <!-- 引擎图标 -->
                  <div class="mt-0.5 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-xs sm:h-8 sm:w-8 dark:bg-gray-100">
                    <img :src="translateModeIcon[item.value]" :alt="item.label" class="h-6 w-6 rounded bg-white object-contain p-0.5 sm:h-7 sm:w-7" />
                  </div>

                  <div class="flex min-w-0 flex-1 flex-col">
                    <!-- 模型名称 -->
                    <span class="truncate text-sm font-medium text-neutral-600 sm:text-sm dark:text-neutral-300" :title="item.label">{{ item.label }}</span>

                    <!-- 模型版本和服务模式信息 -->
                    <div class="flex flex-wrap items-center gap-1 text-xs">
                      <!-- 模型版本信息 -->
                      <span v-if="item.modelName" class="truncate text-neutral-500 dark:text-neutral-400">({{ item.modelName }})</span>

                      <!-- 服务模式标签 -->
                      <span
                        class="flex-shrink-0 whitespace-nowrap"
                        :class="[
                          item.serviceMode === 'plus'
                            ? 'text-[#f29a02] dark:text-yellow-400'
                            : item.serviceMode === 'free'
                              ? 'text-[#04ae82] dark:text-green-400'
                              : 'text-neutral-500 dark:text-neutral-400',
                          { 'text-neutral-500 dark:text-neutral-400': item.serviceMode === 'custom' }
                        ]"
                      >
                        {{ item.serviceModeName }}
                      </span>
                    </div>
                  </div>

                  <div class="flex h-full flex-shrink-0 items-center">
                    <!-- 不可用提示（AI模型特有） -->
                    <div v-if="item.isAvailable === false" class="flex items-center justify-center">
                      <UTooltip :text="t('common.model_unavailable')" :popper="{ placement: 'top' }">
                        <UIcon name="i-material-symbols-error-outline" class="h-4 w-4 text-red-500 sm:h-5 sm:w-5" />
                      </UTooltip>
                    </div>
                    <!-- 选中时显示对勾 -->
                    <Icon
                      name="i-heroicons-check"
                      class="text-primary ml-1 h-5 w-6 transition-opacity duration-200 sm:h-6 sm:w-6"
                      :style="{ opacity: aiSelectTranslateEngine.value === item.value ? 1 : 0 }"
                    />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </UPopover>

        <!-- 用于择优评分和分析的 AI 模型帮助信息 -->
        <UPopover
          :mode="isMobileDevice() ? 'click' : 'hover'"
          arrow
          :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
        >
          <UIcon name="heroicons:information-circle" class="size-4 shrink-0 text-neutral-400" />
          <template #content>
            <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
              {{ t('selected_trans.settings.selected_model_desc') }}
            </div>
          </template>
        </UPopover>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInputTranslate } from '@/store/inputTranslate'
import { useUpdatePluginData } from '@/pages/text/js/sendExtension'
import { sharedState } from '@/pages/text/useData/sharedState'
import { translateModeIcon } from '@/utils/translateModeIcon'

// 存储
import { storeToRefs } from 'pinia'

const { reSelect, originalTextList, reTrans, currentTranslateStatus } = sharedState // 翻译引擎的图标icon

// 通知插件更新精挑翻译模型
const { aiSelectTranslateModel } = useUpdatePluginData
const useInputTransStore = useInputTranslate()
const { aiSelectModelList, aiSelectModel, isSingleSentenceAiSelect } = storeToRefs(useInputTransStore)

const { t } = useI18n()

// 精挑翻译模型- 默认选择openai
const aiSelectTranslateEngine = ref(aiSelectModel.value)

/**
 * 判断本次翻译的逐句翻译是否跟当前设置的逐句翻译一致
 * 如果一致就不需要重新择优翻译
 * ! isSingleSentence是本次翻译之后存储的一个翻译模式响应式数据
 * ! isSingleSentenceAiSelect是当前存储到store的翻译模式
 */
const isAiSelectAnalysisModeChanged = computed(() => {
  return currentTranslateStatus.value.isSingleSentence == isSingleSentenceAiSelect.value
})
/**
 * 判断当前翻译选中的择优翻译模型是否跟当前存储的择优翻译模型一致
 */
const isSelectedAiSelectModelChanged = computed(() => {
  return currentTranslateStatus.value.currentSelectedAiSelectModel === aiSelectModel.value.value
})

/**
 * 处理选择精挑翻译模型
 * @param data
 */
const changeAISelectTranslateEngine = (data) => {
  if (!data) return

  try {
    // 向扩展发送更新精挑模型的消息
    aiSelectTranslateModel(data)

    // 更新本地存储
    aiSelectModel.value = data

    /**
     * 判断是否需要重新择优翻译
     * 1. 如果原文有内容
     * 2. 如果已经显示了重新翻译就关闭 reSelect，不要显示
     * 3. 如果本次翻译的逐句翻译跟存储的逐句翻译一致
     * 4. 如果当前翻译模型跟存储的翻译模型不一致
     */
    if (originalTextList.value.isTranslated && !reTrans.value && isAiSelectAnalysisModeChanged.value && !isSelectedAiSelectModelChanged.value) {
      reSelect.value = true
    }
    else if (originalTextList.value.isTranslated && isSelectedAiSelectModelChanged.value) {
      // 如果当前翻译模型跟存储的翻译模型一致，则不重新择优翻译
      reSelect.value = false
    }
  }
  catch (error) {
    console.error('Failed to change AI select model:', error)
  }
}

// 监听模型变化
watch(
  () => aiSelectModel.value,
  (newValue) => {
    if (newValue && newValue !== aiSelectTranslateEngine.value) {
      aiSelectTranslateEngine.value = newValue
    }
  }
)
</script>

<style scoped></style>
