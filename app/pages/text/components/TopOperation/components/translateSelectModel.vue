<!-- 翻译模型选择 -->
<template>
  <div class="relative flex flex-col">
    <!-- 使用UPopover组件实现下拉菜单 -->
    <UPopover>
      <!-- 触发按钮 -->
      <UButton
        variant="ghost"
        color="neutral"
        type="button"
        class="flex h-10 w-auto items-center gap-2 rounded-md border border-gray-200 bg-white px-2 text-base focus:outline-none max-sm:w-full xl:h-10 dark:border-gray-700 dark:bg-gray-800"
        :style="{ width: currentTranslateMode === 'aiSelect' ? 'calc(100% - 20px)' : 'auto' }"
      >
        <div class="flex items-center gap-2.5 truncate md:gap-2 lg:gap-2 xl:gap-2">
          <!-- 标签集成到选择器内部 -->
          <span class="text-sm text-neutral-600 md:text-sm lg:text-sm xl:text-sm dark:text-neutral-400">{{ t('selected_trans.settings.translation_models') }}:</span>

          <!-- 传统模式显示选中的引擎名称 -->
          <template v-if="currentTranslateMode === 'traditional'">
            <div class="flex items-center gap-1">
              <img :src="translateModeIcon[selectedEngines.value]" class="size-6 rounded border border-gray-100 bg-white object-contain p-0.5 sm:size-6 md:size-6 lg:size-6 xl:size-6" />
              <span class="max-w-20 truncate text-sm">{{ selectedEngines.label }}</span>
            </div>
          </template>
          <!-- 择优模式显示选中的引擎数量 -->
          <template v-else>
            <div class="flex items-center">
              <div v-for="(engine, index) in selectedEngines.slice(0, 3)" :key="index" class="-mr-1">
                <img :src="translateModeIcon[engine]" class="size-6 rounded border border-gray-100 bg-white object-contain p-0.5 sm:size-6 md:size-6 lg:size-6 xl:size-6" :alt="engine" />
              </div>
              <div v-if="selectedEngines.length > 3" class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs dark:bg-gray-700">
                +{{ selectedEngines.length - 3 }}
              </div>
            </div>
          </template>
        </div>
        <Icon name="heroicons:chevron-down" class="ml-auto h-4 w-4 text-gray-500" />
      </UButton>

      <!-- 下拉内容 -->
      <template #content>
        <div
          class="custom-scrollbar max-h-[min(50vh,400px)] max-w-[40rem] overflow-y-auto rounded-md border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-800"
          :style="{
            width: 'min(calc(100vw - 2rem), 40rem)'
          }"
        >
          <!-- 网格布局选项 - 响应式列数 -->
          <div class="grid auto-rows-max grid-cols-1 gap-2 p-2 sm:grid-cols-2 sm:p-4 md:p-5">
            <div
              v-for="item in originalTranslationEngineList"
              :key="item.value"
              class="flex cursor-pointer items-start gap-2 rounded-md bg-gray-50 px-2 py-2 transition-colors duration-200 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700"
              :class="{ 'dark:bg-gray-750 bg-gray-100': isItemSelected(item.value) }"
              @click="selectModel(item)"
            >
              <!-- 引擎图标 -->
              <div class="mt-0.5 flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-xs sm:h-8 sm:w-8 dark:bg-gray-100">
                <img :src="translateModeIcon[item.value]" :alt="item.label" class="h-6 w-6 rounded bg-white object-contain p-0.5 sm:h-7 sm:w-7" />
              </div>

              <div class="flex min-w-0 flex-1 flex-col">
                <!-- 模型名称 -->
                <span class="truncate text-sm font-medium text-neutral-600 sm:text-sm dark:text-neutral-300" :title="item.label">{{ item.label }}</span>

                <!-- 模型版本和服务模式信息 -->
                <div class="flex flex-wrap items-center gap-1 text-xs">
                  <!-- 模型版本信息 -->
                  <span v-if="item.modelName" class="max-w-32 truncate text-neutral-500 dark:text-neutral-400">({{ item.modelName }})</span>

                  <!-- 服务模式标签 -->
                  <span
                    class="flex-shrink-0 whitespace-nowrap"
                    :class="[
                      item.serviceMode === 'plus'
                        ? 'text-[#f29a02] dark:text-yellow-400'
                        : item.serviceMode === 'free'
                          ? 'text-[#04ae82] dark:text-green-400'
                          : 'text-neutral-500 dark:text-neutral-400'
                    ]"
                  >
                    {{ item.serviceModeName }}
                  </span>
                </div>
              </div>

              <div class="flex h-full flex-shrink-0 items-center">
                <!-- 不支持当前目标语言 -->
                <div v-if="!modelSupportState.isEngineSupported(item.value)" class="flex items-center justify-center">
                  <UTooltip :text="t('selected_trans.settings.trans_models.target_language_is_not_supported')" :popper="{ placement: 'top' }">
                    <Icon name="material-symbols:error" class="h-4 w-4 text-red-500 sm:h-5 sm:w-5" />
                  </UTooltip>
                </div>
                <!-- 选中时显示 check 图标 -->
                <Icon name="i-heroicons-check" class="text-primary ml-1 h-5 w-6 transition-opacity duration-200 sm:h-6 sm:w-6" :style="{ opacity: isItemSelected(item.value) ? 1 : 0 }" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </UPopover>
  </div>
</template>

<script setup lang="ts">
import { translationState } from '@/pages/text/useData/useTranslationState'
import { translateModeIcon } from '@/utils/translateModeIcon'
import { TranslationStatus } from '@/types/translateOptions'

import { useInputTranslate } from '@/store/inputTranslate'
import { storeToRefs } from 'pinia'

import { modelSupportState } from '@/pages/text/useData/useModelSupport'

import { sharedState } from '@/pages/text/useData/sharedState'

// 执行翻译的方法
import { useTranslate } from '@/pages/text/translate/translate'
import { isMobileDevice } from '@/utils/utils'

const { translationEngineOptions, originalTranslationEngineList, updateTranslationEngineStatus } = translationState
const useInputTransStore = useInputTranslate()
const { enabledEnginesList, targetLanguage, currentTranslateMode, isAutoTranslate } = storeToRefs(useInputTransStore)

const { originalTextList, reTrans, modelCollapsed, currentTranslateStatus } = sharedState

const { buildOriginalTextListSendRequest } = useTranslate

const { t, locale } = useI18n()

const isMobile = ref(false)

// 是否自动翻译且不是择优翻译
const isAutoTranslateAndNotAiSelect = computed(() => {
  return isAutoTranslate.value && currentTranslateMode.value === 'traditional' && originalTextList.value.isTranslated
})

/**
 * 选择的引擎列表
 */
const selectedEngines: any = computed(() => {
  if (currentTranslateMode.value === 'traditional') {
    return useInputTransStore.traditionalEngines
  }
  else {
    return enabledEnginesList.value
  }
})

/**
 * 判断当前引擎是否被选中
 */
const isItemSelected = (engineId: string) => {
  if (currentTranslateMode.value === 'traditional') {
    return useInputTransStore.traditionalEngines?.value === engineId
  }
  else {
    return enabledEnginesList.value?.includes(engineId)
  }
}

/**
 * 选择模型
 */
const selectModel = (item) => {
  const addEngines = []

  // 传统模式单选，择优模式多选
  if (currentTranslateMode.value === 'traditional') {
    handleEngineSelection(item.value)
  }
  else {
    // 多模型翻译

    // 获取当前选中的引擎
    const currentSelected = [...enabledEnginesList.value]
    const index = currentSelected.indexOf(item.value)

    // 检查是否是添加还是移除引擎
    const isAdding = index === -1
    if (isAdding) {
      currentSelected.push(item.value)
    }
    else if (currentSelected.length > 1) {
      currentSelected.splice(index, 1)
    }

    // 更新存储
    handleEngineSelection(currentSelected)

    // 只有在添加引擎时才触发翻译
    if (isAutoTranslateAndNotAiSelect.value && isAdding && originalTextList.value.isTranslated) {
      updateTranslationEngineStatus(item.value, TranslationStatus.Translating)
      // 立即触发翻译
      addEngines.push(item.value)
      buildOriginalTextListSendRequest(addEngines)
    }
    else if (!isAutoTranslateAndNotAiSelect.value && originalTextList.value.isTranslated) {
      /**
       * 比较当前翻译模型列表和选中的引擎列表是否一致
       */
      const currentModelList = currentTranslateStatus.value.currentModelList || []
      const selectedEngineList = [...currentSelected]

      // 检查两个数组是否包含相同的元素（顺序无关）
      const arraysHaveSameElements = (arr1, arr2) => {
        if (arr1.length !== arr2.length) return false
        return arr1.every(item => arr2.includes(item))
      }

      // 如果两个数组内容不一致，开启重新翻译；一致则关闭重新翻译
      reTrans.value = !arraysHaveSameElements(currentModelList, selectedEngineList)
    }
  }

  // 更新折叠状态
  modelCollapsed.value[item.value] = false
}

/**
 * 处理引擎选择变更
 */
const handleEngineSelection = (engines) => {
  // 确保引擎不为空
  if ((Array.isArray(engines) && engines.length === 0) || !engines) return

  if (currentTranslateMode.value === 'traditional') {
    // 传统模式下设置单个引擎
    const selectedEngine = translationEngineOptions.value.find(engine => engine.value === engines)
    if (selectedEngine) {
      useInputTransStore.setTraditionalEngines({
        label: selectedEngine.label,
        value: selectedEngine.value
      })
      if (isAutoTranslateAndNotAiSelect.value && originalTextList.value.isTranslated) {
        buildOriginalTextListSendRequest()
      }
      else if (!isAutoTranslateAndNotAiSelect.value && originalTextList.value.isTranslated) {
        // 获取当前正在使用的翻译模型列表
        const currentModelList = currentTranslateStatus.value.currentModelList || []

        // 检查选择的引擎是否已在当前使用的模型列表中
        // 在传统模式下，如果当前使用的模型列表只包含这一个引擎，说明不需要重新翻译
        const modelAlreadyInUse = currentModelList.length === 1 && currentModelList[0] === selectedEngine.value

        // 如果模型不一致，则开启重新翻译
        reTrans.value = !modelAlreadyInUse
      }
    }
  }
  else {
    // 择优模式下可以选择多个引擎

    // 仅在不支持时显示提示
    engines.forEach((engine) => {
      const isSupported = modelSupportState.isEngineSupported(engine)
      if (!isSupported && !enabledEnginesList.value.includes(engine)) {
        modelSupportState.showUnsupportedMessage(engine)
      }
    })
    if (engines.length > 0) {
      useInputTransStore.enabledEnginesList = engines
    }
  }
}

/**
 * 检查引擎语言支持
 */
const checkEngineLanguageSupport = () => {
  try {
    window.postMessage(
      {
        type: 'getAllEnginesLanguageSupport',
        interfaceLanguage: locale.value
      },
      window.location.origin
    )
  }
  catch (error) {
    console.error('Error checking language support:', error)
  }
}

/**
 * 清理引擎缓存
 */
const cleanupEngineCache = () => {
  const currentEngineIds = new Set(translationEngineOptions.value.map(e => e.value))

  // 清理模型支持状态缓存
  modelSupportState.cleanupEngineCache(currentEngineIds)

  // 清理存储中的无效引擎
  const validEngineList = enabledEnginesList.value.filter(id => currentEngineIds.has(id))

  if (validEngineList.length !== enabledEnginesList.value.length && validEngineList.length > 0) {
    useInputTransStore.enabledEnginesList = validEngineList
    handleEngineSelection(validEngineList)
  }
}

/**
 * 初始化引擎选择
 */
const initializeEngineSelection = () => {
  // 只在传统模式下需要确保有选中的引擎
  if (currentTranslateMode.value !== 'traditional' || useInputTransStore.traditionalEngines.value || translationEngineOptions.value.length === 0) {
    return
  }

  // 优先使用已启用的引擎
  const engineToSelect = enabledEnginesList.value.length > 0 ? translationEngineOptions.value.find(e => e.value === enabledEnginesList.value[0]) : translationEngineOptions.value[0]

  if (engineToSelect) {
    useInputTransStore.setTraditionalEngines({
      label: engineToSelect.label,
      value: engineToSelect.value
    })

    if (enabledEnginesList.value.length === 0) {
      useInputTransStore.enabledEnginesList = [engineToSelect.value]
    }
  }
}

/**
 * 处理扩展消息
 */
const handleExtensionMessage = (event) => {
  if (event.data?.type === 'ALL_ENGINES_LANGUAGE_SUPPORT') {
    // 更新引擎支持状态
    modelSupportState.setEngineSupportCache(event.data.engineSupports, targetLanguage.value)
  }
}

// 监听器
watch(targetLanguage, () => {
  modelSupportState.updateEngineSupport(enabledEnginesList.value, targetLanguage.value)
  checkEngineLanguageSupport()
})

watch(enabledEnginesList, (newEngines) => {
  const needsUpdate = newEngines.some(engine => !modelSupportState.engineSupportCache.value.has(engine))

  if (needsUpdate) {
    checkEngineLanguageSupport()
  }
  else {
    modelSupportState.updateEngineSupport(newEngines, targetLanguage.value)
  }
})

watch(
  translationEngineOptions,
  (newOptions) => {
    if (newOptions.length > 0) {
      cleanupEngineCache()
    }
  },
  { immediate: true }
)

// 监听翻译模式变化
watch(
  currentTranslateMode,
  () => {
    initializeEngineSelection()
  },
  { immediate: true }
)

// 生命周期钩子
onMounted(() => {
  checkEngineLanguageSupport()
  window.addEventListener('message', handleExtensionMessage)
  isMobile.value = isMobileDevice()
})

onUnmounted(() => {
  window.removeEventListener('message', handleExtensionMessage)
})
</script>

<style scoped>
/* 添加过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
