<!-- 顶部操作 -->
<template>
  <div class="flex w-full flex-wrap items-center justify-between gap-2 rounded-tr-lg border border-neutral-200 px-5 py-4 max-md:rounded-tr-none dark:border-neutral-700">
    <!-- 左侧 -->
    <div class="title flex flex-wrap items-center gap-2 max-sm:gap-1" :class="{ 'justify-between': currentTranslateMode === 'traditional' && isMobile }">
      <!-- 传统翻译跟择优翻译的多选模型组件 -->
      <div class="flex w-auto items-center max-sm:grid max-sm:w-full max-sm:grid-cols-1" :class="{ 'gap-3': isMobile }">
        <div class="flex-shrink-0">
          <TranslateSelectModel />
        </div>
        <!-- AI 择优设置 -->
        <div v-if="currentTranslateMode === 'aiSelect'" class="flex-shrink-0">
          <AiSelectedTranslateModel />
        </div>
      </div>
      <!-- 精挑翻译设置 -->
      <CheckboxOptions />
      <History v-if="isMobile" />
    </div>
    <!-- 历史记录 -->
    <History v-if="!isMobile" />
  </div>
</template>

<script setup lang="ts">
import TranslateSelectModel from '@/pages/text/components/TopOperation/components/translateSelectModel.vue'
import AiSelectedTranslateModel from '@/pages/text/components/TopOperation/components/AiSelectedTranslateModel.vue'
import CheckboxOptions from '@/pages/text/components/TopOperation/components/CheckboxOptions.vue'
import History from '@/pages/text/components/History/index.vue'
import { storeToRefs } from 'pinia'
import { useInputTranslate } from '@/store/inputTranslate'
import { isMobileDevice } from '@/utils/utils'

const inputTranslateStore = useInputTranslate()
const { currentTranslateMode } = storeToRefs(inputTranslateStore)

const isMobile = ref(false)

onMounted(() => {
  isMobile.value = isMobileDevice()
})
</script>

<style scoped>
@media (max-width: 768px) {
  .title {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    width: 100%;
  }
}
</style>
