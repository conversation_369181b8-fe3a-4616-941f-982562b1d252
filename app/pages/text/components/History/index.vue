<!-- 历史记录 -->
<template>
  <UPopover v-model:open="isPopoverOpen">
    <!-- 历史记录 -->
    <UButton
      color="neutral"
      variant="link"
      icon="i-mdi-history"
      :label="t('selected_trans.settings.history')"
      size="md"
      class="cursor-pointer px-1 font-normal text-neutral-500 hover:text-neutral-400"
    />
    <template #content>
      <!-- 历史记录容器 -->
      <div
        class="h-full w-[calc(100vw-1rem)] p-1 sm:w-[calc(100vw-2rem)] sm:p-2 md:w-[30rem] md:max-w-lg md:min-w-[20rem] lg:w-[28rem] lg:max-w-xl lg:min-w-[22rem] xl:w-[32rem] xl:max-w-2xl xl:min-w-[24rem]"
      >
        <!-- 别在这处理拖拽定位 -->
        <div class="w-full p-2 sm:p-3 md:p-5">
          <div v-if="isLoading" class="history-loading flex items-center justify-center space-x-2">
            <UIcon name="i-heroicons-arrow-path" class="animate-spin text-gray-400 dark:text-gray-500" />
            <!-- 加载中... -->
            <span>{{ $t('common.loading') }}</span>
          </div>
          <div v-else-if="historyList.length === 0" class="history-empty flex flex-col items-center justify-center p-4">
            <UIcon name="i-heroicons-document-text" class="mb-2 text-4xl text-gray-400 dark:text-gray-500" />
            <!-- 暂无历史记录 -->
            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('selected_trans.history.no_history') }}</span>
          </div>
          <div v-else>
            <!-- 历史记录头部操作栏 -->
            <div class="sticky top-0 z-10 mb-0 flex flex-col bg-white px-1 pt-1 pb-2.5 backdrop-blur-sm sm:px-2 dark:bg-gray-900">
              <!-- 搜索框和清空历史按钮 -->
              <div class="flex items-center justify-between gap-2">
                <!-- 左侧占位，与右侧按钮宽度相同 -->
                <div class="w-16 flex-shrink-0 sm:w-24" />

                <!-- 搜索框居中 -->
                <UInput
                  ref="searchInputRef"
                  v-model="searchQuery"
                  :placeholder="$t('selected_trans.history.search_history')"
                  size="lg"
                  class="max-w-xs flex-1 sm:max-w-sm"
                  @update:model-value="searchHistory"
                >
                  <template #trailing>
                    <UButton
                      v-if="searchQuery"
                      color="neutral"
                      active-color="neutral"
                      active-variant="ghost"
                      variant="link"
                      icon="i-solar-close-circle-linear"
                      size="md"
                      class="rounded-full text-neutral-400"
                      @click="clearSearch"
                    />
                  </template>
                </UInput>

                <!-- 清空历史按钮在右侧 -->
                <div class="flex w-16 flex-shrink-0 justify-end sm:w-24">
                  <UButton
                    size="xs"
                    color="neutral"
                    variant="ghost"
                    icon="i-heroicons-trash"
                    class="text-xs font-normal text-neutral-400"
                    @click="confirmClearAllHistory"
                  >
                    <span>{{ $t('selected_trans.history.clear_history') }}</span>
                  </UButton>
                </div>
              </div>
            </div>

            <div
              ref="historyContainerRef"
              class="custom-scrollbar max-h-[calc(60vh-4rem)] space-y-2 overflow-y-auto sm:max-h-[calc(55vh-3rem)] md:max-h-[calc(50vh-2rem)] lg:max-h-[calc(50vh-2rem)] xl:max-h-[calc(50vh-4rem)]"
            >
              <!-- 按日期分组显示历史记录 -->
              <div v-for="(group, date) in groupedHistoryList" :key="date">
                <!-- 日期头部 -->
                <div
                  v-if="group.some((item) => item.multiModelTranslationOptions && item.multiModelTranslationOptions.length > 0)"
                  class="date-header sticky top-0 z-10 mb-0.5 bg-white px-1 text-xs font-medium text-gray-500 sm:px-2 dark:bg-gray-900 dark:text-gray-400"
                >
                  {{ formatDateHeader(date) }}
                </div>

                <div v-if="group.length > 0" class="history-items space-y-1.5 sm:space-y-2">
                  <div v-for="item in group" :key="item.key" @click="handleHistoryItemClick(item)">
                    <div
                      v-if="item.multiModelTranslationOptions && item.multiModelTranslationOptions.length > 0 && item.sourceText.trim() && item.translateType"
                      class="history-item group relative flex cursor-pointer items-start justify-between rounded-md border border-gray-200 p-2 transition-colors duration-200 hover:bg-gray-50 sm:p-3 dark:border-gray-700 dark:hover:bg-gray-800"
                    >
                      <div class="flex-1 overflow-hidden pr-1 sm:pr-2">
                        <!-- 历史记录列表文本原文显示 -->
                        <div v-if="item.sourceText && item.sourceText.trim()" class="mb-1 text-xs sm:text-sm">
                          <!-- 历史记录列表文本原文显示 -->
                          <UTooltip :popper="{ placement: 'top' }" :ui="{ content: 'h-auto' }">
                            <p class="line-clamp-2" v-html="searchQuery ? highlightSearchQuery(item.sourceText) : item.sourceText" />
                            <template #content>
                              <!-- 原文 -->
                              <div class="max-w-[250px] whitespace-normal sm:max-w-[280px] md:max-w-[350px]">
                                {{ item.sourceText }}
                              </div>
                            </template>
                          </UTooltip>
                        </div>
                        <div
                          v-if="item.multiModelTranslationOptions && item.multiModelTranslationOptions.length > 0"
                          class="mt-1.5 flex flex-col gap-1.5 sm:mt-2 sm:flex-row sm:items-center sm:justify-between sm:gap-2"
                        >
                          <div class="flex flex-wrap items-center gap-1 sm:gap-2">
                            <!-- 翻译模式显示 -->
                            <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                              <span class="rounded-full bg-gray-100 px-1.5 py-0.5 text-xs sm:px-2 dark:bg-gray-700">
                                <!-- 择优翻译 -->
                                <span v-if="item.translateType === 'aiSelect'">
                                  <!-- 逐句择优 -->
                                  <span v-if="item.aiSelectSetting && item.aiSelectSetting.isSingleSentenceAiSelect">{{ $t('selected_trans.history.by_sentence') }}</span>
                                  <!-- 全文择优 -->
                                  <span v-else>{{ $t('selected_trans.history.by_fulltext') }}</span>
                                </span>
                                <!-- 对比翻译 -->
                                <span v-else-if="item.translateType === 'compare'">{{ $t('selected_trans.operation.compare_translation_btn') }}</span>
                                <!-- 传统翻译 -->
                                <span v-else>{{ $t('selected_trans.operation.traditional_ranslation_btn') }}</span>
                              </span>
                            </div>

                            <!-- 翻译模型图标 -->
                            <div class="flex -space-x-0.5 sm:-space-x-1">
                              <div v-for="(model, index) in item.multiModelTranslationOptions.slice(0, 5)" :key="index">
                                <UTooltip :text="t('selected_trans.settings.translation_models')" :popper="{ placement: 'top' }">
                                  <img :src="translateModeIcon[model.value]" class="h-4 w-4 rounded bg-white object-contain p-0.5 sm:h-5 sm:w-5" :alt="model.label" />
                                  <template #content>
                                    <div class="text-xs">
                                      {{ item.multiModelTranslationOptions.map((m) => translationEngineOptions.find((option) => option.value === m.value)?.label).join(', ') }}
                                    </div>
                                  </template>
                                </UTooltip>
                              </div>
                              <div v-if="item.multiModelTranslationOptions.length > 5" class="flex h-4 w-4 items-center justify-center rounded-full bg-gray-200 text-xs sm:h-5 sm:w-5 dark:bg-gray-700">
                                <!-- 翻译模型数量显示 -->
                                +{{ item.multiModelTranslationOptions.length - 5 }}
                              </div>
                            </div>

                            <!-- 择优模型显示 -->
                            <UTooltip :text="t('selected_trans.settings.selected_model')" :popper="{ placement: 'top' }">
                              <div
                                v-if="item.translateType === 'aiSelect' && item.aiSelectSetting && item.aiSelectSetting.aiSelectModel"
                                class="flex items-center rounded-full bg-gray-100 px-1.5 py-0.5 text-xs text-gray-500 sm:px-2 dark:bg-gray-700 dark:text-gray-400"
                              >
                                <!-- 择优翻译模型图标 -->
                                <img :src="translateModeIcon[item.aiSelectSetting.aiSelectModel.value]" class="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                                <span class="max-w-16 truncate sm:max-w-20">{{ item.aiSelectSetting.aiSelectModel.label }}</span>
                              </div>
                            </UTooltip>
                          </div>

                          <!-- 时间 -->
                          <div class="self-start text-xs text-gray-500 sm:self-auto dark:text-gray-400">
                            {{ formatTime(item.timestamp) }}
                          </div>
                        </div>
                      </div>
                      <!-- 删除按钮 -->
                      <UButton
                        v-if="item.sourceText"
                        size="sm"
                        color="neutral"
                        variant="ghost"
                        icon="i-heroicons-trash"
                        class="absolute top-1 right-1 flex-shrink-0 opacity-0 transition-opacity group-hover:opacity-100 sm:top-2 sm:right-2"
                        @click.stop="deleteHistoryItem(item)"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 加载更多指示器 -->
              <div v-if="isLoadingMore" class="flex items-center justify-center py-2">
                <UIcon name="i-heroicons-arrow-path" class="mr-1 animate-spin text-gray-400 dark:text-gray-500" />
                <!-- 加载中... -->
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ $t('common.loading') }}</span>
              </div>
            </div>
          </div>

          <!-- 清空所有历史确认弹窗 -->
          <UModal v-model:open="isClearAllModalOpen">
            <template #content>
              <UCard>
                <template #header>
                  <!-- 清空历史记录 -->
                  <div class="font-medium">
                    {{ $t('selected_trans.history.clear_history') }}
                  </div>
                </template>
                <!-- 确认要清空所有历史记录？ -->
                <p>{{ $t('selected_trans.history.clear_history_confirm') }}</p>
                <template #footer>
                  <div class="flex justify-end gap-2">
                    <!-- 取消 -->
                    <UButton
                      color="neutral"
                      variant="outline"
                      class="rounded-sm"
                      @click="isClearAllModalOpen = false"
                    >
                      {{ $t('common.cancel') }}
                    </UButton>
                    <!-- 确认 -->
                    <UButton
                      color="primary"
                      variant="solid"
                      class="rounded-sm"
                      @click="clearAllHistory"
                    >
                      {{ $t('common.confirm') }}
                    </UButton>
                  </div>
                </template>
              </UCard>
            </template>
          </UModal>
        </div>
      </div>
    </template>
  </UPopover>
</template>

<script setup lang="ts">
import { useTranslationHistoryState } from '@/pages/text/useData/useHistoryState.ts' // 历史记录状态
import { useInputTranslate } from '@/store/inputTranslate' // 输入框的翻译引擎配置
import { storeToRefs } from 'pinia' // 状态管理
import { sharedState } from '@/pages/text/useData/sharedState' // 共享状
import { useUpdatePluginData } from '@/pages/text/js/sendExtension' // 通知插件更新精挑翻译模型
import { translateModeIcon } from '@/utils/translateModeIcon' // 翻译引擎的图标icon
// import { useDrag } from '@/utils/dragWin'; // 引入拖拽函数
import type { TranslationHistory } from '@/types/history'
import { useDomControl } from '@/pages/text/translate/domControl'
import { restoreEditorState } from '@/pages/text/components/Textarea/js/dom-serializer'
import { translationState } from '@/pages/text/useData/useTranslationState'

// 搜索历史记录
import { useTranslationHistory } from '@/store/indexedDB/selectTransHistory'

const { sourceDOMStructure, aiSelectFinallyResultText, highlightId, historyModelOptions, currentTranslateStatus, historyKey, customResult, originalTextList } = sharedState
const { handleHighlightFirstParagraph, removeAllHighlightedSentences } = useDomControl
const { translationEngineOptions } = translationState
const { searchHistoryByQueryAndType } = useTranslationHistory()

const inputTransStore = useInputTranslate()
const { isAiSelectAnalysis, isSingleSentenceAiSelect, aiSelectModel, aiSelectModelList, currentTranslateMode, traditionalEngines } = storeToRefs(inputTransStore)

const { aiSelectTranslateModel } = useUpdatePluginData

/**
 * 翻译历史记录组件
 * 按照日期分组展示用户的翻译历史记录
 */
const { historyList, loadHistoryByTypeAndPage, removeHistory, clearHistory } = useTranslationHistoryState()
const { t } = useI18n()
// Popover 开关状态
const isPopoverOpen = ref(false)

const isLoading = ref(false)
const isClearAllModalOpen = ref(false)
const searchQuery = ref('')
const filteredHistoryList = ref([])

// 分页加载相关变量
const pageSize = ref(15) // 每页加载数量
const currentPage = ref(1) // 当前页码
const hasMoreData = ref(true) // 是否还有更多数据
const isLoadingMore = ref(false) // 是否正在加载更多
const totalRecords = ref(0) // 总记录数
const isDataLoaded = ref(false) // 是否已加载数据
const historyContainerRef = ref(null) // 历史记录容器引用

const searchInputRef = ref(null)

/**
 * 加载第一页历史记录
 */
const loadFirstPage = async () => {
  isLoading.value = true
  currentPage.value = 1

  // 使用当前翻译模式来加载历史记录
  const { hasMore, total, data } = await loadHistoryByTypeAndPage(1, pageSize.value, currentTranslateMode.value)
  // console.log('加载历史记录', { hasMore, total, data });

  // 如果没有历史记录，立即关闭加载状态
  if (total === 0 || !data || data.length === 0) {
    isLoading.value = false
  }

  hasMoreData.value = hasMore
  totalRecords.value = total
  isDataLoaded.value = true
  isLoading.value = false

  // 加载完毕后设置滚动监听
  nextTick(() => {
    setupScrollListener()
  })
}

/**
 * 加载更多历史记录
 */
const loadMoreHistory = async () => {
  if (!hasMoreData.value || isLoadingMore.value) return

  isLoadingMore.value = true
  currentPage.value++

  try {
    // 使用当前翻译模式加载更多历史记录
    const { hasMore } = await loadHistoryByTypeAndPage(currentPage.value, pageSize.value, currentTranslateMode.value)
    hasMoreData.value = hasMore
  }
  catch (error) {
    console.error('加载更多历史记录失败:', error)
  }
  finally {
    isLoadingMore.value = false
  }
}

/**
 * 设置滚动监听
 */
const setupScrollListener = () => {
  const container = historyContainerRef.value
  if (container) {
    container.addEventListener('scroll', handleScroll)
  }
}

/**
 * 处理滚动事件，到底部时自动加载更多
 * @param e 滚动事件
 */
const handleScroll = (e) => {
  if (!hasMoreData.value || isLoadingMore.value || searchQuery.value) return

  const { scrollTop, scrollHeight, clientHeight } = e.target
  // 当剩余滚动距离小于50px时加载更多
  if (scrollHeight - scrollTop - clientHeight < 50) {
    if (searchQuery.value) {
      loadMoreSearchResults()
    }
    else {
      loadMoreHistory()
    }
  }
}

/**
 * 按日期对历史记录进行分组
 */
const groupedHistoryList = computed<Record<string, TranslationHistory[]>>(() => {
  const groups = {}
  // 如果搜索框有值，则使用过滤后的历史记录，否则使用当前列表
  const listToGroup = searchQuery.value ? filteredHistoryList.value : historyList.value

  if (!listToGroup || listToGroup.length === 0) return groups

  listToGroup.forEach((item) => {
    const date = new Date(item.timestamp)
    // 修改日期键格式，确保正确排序
    // 月份需要+1，且使用固定长度格式YYYY-MM-DD
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const dateKey = `${date.getFullYear()}-${month}-${day}`

    if (!groups[dateKey]) {
      groups[dateKey] = []
    }

    groups[dateKey].push(item)
  })

  // 对每个日期组内的历史记录按时间倒序排序
  Object.keys(groups).forEach((key) => {
    groups[key].sort((a, b) => b.timestamp - a.timestamp)
  })

  // 对日期键进行排序，最近的日期在前
  return Object.fromEntries(
    Object.entries(groups).sort(([keyA], [keyB]) => {
      return keyB.localeCompare(keyA)
    })
  )
})

/**
 * 格式化日期头部显示
 * @param dateKey 日期键
 * @returns 格式化后的日期字符串
 */
const formatDateHeader = (dateKey) => {
  const [year, month, day] = dateKey.split('-').map(Number)
  // 月份参数需要减1，因为Date构造函数中月份是从0开始的
  const date = new Date(year, month - 1, day)

  // 判断是否是今天、昨天
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() && date.getDate() === today.getDate()) {
    return t('selected_trans.history.today') // 今天
  }
  else if (date.getFullYear() === yesterday.getFullYear() && date.getMonth() === yesterday.getMonth() && date.getDate() === yesterday.getDate()) {
    return t('selected_trans.history.yesterday') // 昨天
  }
  else {
    // 返回完整日期
    return date.toLocaleDateString()
  }
}

/**
 * 格式化时间显示
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })
}

/**
 * 处理历史记录点击事件
 * !恢复翻译状态
 * @param item 历史记录
 */
const handleHistoryItemClick = (item) => {
  // console.log('恢复历史数据', item);

  /**
   * 现重置一下高亮 id 因为会出现以下开启，如果用户点击恢复的历史记录记录是逐句翻译
   * 1. 如果用户点击恢复的历史记录记录是逐句翻译
   * 2. 然后再切换到全文翻译这个 highlightId 保留了上一次的 id 就会导致 翻译结果没有出现 ，是因为 id 对应不上
   * 3. 所以需要重置一下高亮 id
   */
  highlightId.value = ''

  // 恢复择优设置
  if (item.aiSelectSetting) {
    isSingleSentenceAiSelect.value = item.aiSelectSetting.isSingleSentenceAiSelect // 是否开启单句精挑翻译
    isAiSelectAnalysis.value = item.aiSelectSetting.isAiSelectAnalysis // 是否开启精挑分析
  }

  if (item.translateType) {
    currentTranslateMode.value = item.translateType // 翻译引擎

    // 传统翻译只有一个模型
    if (item.translateType === 'traditional') {
      // 恢复传统翻译模型
      traditionalEngines.value.value = item.multiModelTranslationOptions[0].value
      traditionalEngines.value.label = item.multiModelTranslationOptions[0].label
    }
  }

  // 恢复精挑翻译模型
  if (item.aiSelectSetting && item.aiSelectSetting.aiSelectModel && item.aiSelectSetting.aiSelectModel.value) {
    const res = aiSelectModelList.value.find(model => model.value === item.aiSelectSetting.aiSelectModel.value)
    if (res) {
      // 更新精挑翻译模型本地状态
      aiSelectModel.value = res

      // 通知插件更新精挑翻译模型
      aiSelectTranslateModel({
        value: res.value
      })
    }
  }

  // 恢复原始文本
  if (item.sourceDOMStructure) {
    sourceDOMStructure.value = item.sourceDOMStructure

    // 标记已经存在翻译结果，便于后续切换模型时触发自动翻译逻辑
    originalTextList.value.isTranslated = true
  }

  // 清空精挑翻译结果
  aiSelectFinallyResultText.value = []

  // 清空用户自定义翻译结果
  customResult.value = []

  /**
   * 恢复显示模型列表
   */
  currentTranslateStatus.value.currentModelList = item.showModelList || []

  /**
   * 重新赋值给当前的翻译状态的逐句翻译模式
   */
  if (item.translateType === 'aiSelect' && item.aiSelectSetting) {
    currentTranslateStatus.value.isSingleSentence = item.aiSelectSetting.isSingleSentenceAiSelect
  }

  // 恢复精挑翻译结果
  if (item.aiSelectTranslationResult && item.aiSelectTranslationResult.length > 0) {
    restoreAiSelectFinallyResultText(item, 'aiSelectTranslationResult')
  }

  // 恢复用户自定义翻译结果
  if (item.userCustomTranslationResult && item.userCustomTranslationResult.length > 0) {
    restoreAiSelectFinallyResultText(item, 'userCustomTranslationResult')
  }

  // 恢复翻译结果
  if (item.multiModelTranslationOptions) {
    restoreTranslationResult(item)
  }

  // 恢复输入框内的 dom 结构
  nextTick(() => {
    restoreInputDom(item)
  })

  historyKey.value = item.key

  // 如果当前是移动端，则关闭历史记录弹窗
  if (isMobileDevice()) {
    isPopoverOpen.value = false
  }
}

/**
 * 恢复翻译结果
 * 1. 翻译结果
 * 2. 排序索引
 * 3. 评分
 * 4. 择优分析
 * @param item 历史记录
 */
const restoreTranslationResult = (item) => {
  // 获取翻译结果
  const modelOptions = item.multiModelTranslationOptions

  // 清空历史记录模型配置项，防止重复添加
  historyModelOptions.value = []

  /**
   * 恢复翻译结果
   * 这个只能给用户点击恢复历史记录的时候让用户可以看到所有模型的历史记录
   * 不能在翻译的时候使用， 因为翻译的时候使用的是 translationEngineOptions 数据
   */
  for (let index = 0; index < modelOptions.length; index++) {
    const element = modelOptions[index]

    try {
      // 构建翻译结果数据
      const translationsResult = element.translationsResult.map((result) => {
        return {
          ...result,
          text: result.text
        }
      })

      // 更新翻译引擎配置
      const updateHistoryModelOptions = {
        ...element,
        img: translateModeIcon[element.value] || '',
        translationsResult: translationsResult
      }

      historyModelOptions.value.push(updateHistoryModelOptions)
    }
    catch (error) {
      console.error('恢复翻译结果错误:', error)
    }
  }
}

/**
 * 恢复输入框内的 DOM 结构
 * 将历史记录中保存的 DOM 结构恢复到输入框中
 * @param item - 历史记录项
 */
const restoreInputDom = (item) => {
  // 获取输入框 DOM 元素
  const inputDom = document.querySelector('.editable-textarea')

  if (inputDom) {
    // 添加临时标志，标识这是从历史记录恢复的内容
    (window as any).isRestoringFromHistory = true

    // 恢复编辑器DOM结构
    restoreEditorState(inputDom as HTMLElement, item.sourceDOMStructure)

    // 查找高亮句子并恢复 highlightId
    setTimeout(() => {
      // 恢复完成后移除标志
      (window as any).isRestoringFromHistory = false

      // 恢复完成后移除所有高亮
      removeAllHighlightedSentences()

      // 如果用户开启了单句翻译，则不恢复高亮
      if (item.translateType === 'aiSelect' && item.aiSelectSetting.isSingleSentenceAiSelect) {
        // console.log('恢复高亮第一个段落');
        // 恢复高亮第一个段落
        handleHighlightFirstParagraph()
      }
      else {
        // 恢复高亮
        highlightId.value = 'combined'
      }
    }, 0)
  }
}

// 恢复精挑翻译结果
const restoreAiSelectFinallyResultText = (item, setDataType: 'aiSelectTranslationResult' | 'userCustomTranslationResult') => {
  for (const element of item[setDataType] || []) {
    try {
      if (setDataType === 'aiSelectTranslationResult') {
        aiSelectFinallyResultText.value.push({
          statusNodeGlobalId: element.statusNodeGlobalId || '',
          text: element.text || '',
          engine: element.engine || '',
          paragraphId: element.paragraphId || ''
        })
      }
      else {
        customResult.value.push({
          statusNodeGlobalId: element.statusNodeGlobalId || '',
          text: element.text || '',
          engine: element.engine || '',
          paragraphId: element.paragraphId || ''
        })
      }
    }
    catch (error) {
      console.error('恢复精挑翻译结果错误:', error)
      continue
    }
  }
}

/**
 * 删除历史记录
 */
const deleteHistoryItem = async (item) => {
  if (!item) return

  try {
    await removeHistory(item.key)

    // 如果搜索结果中有该条记录，也需要从搜索结果中移除
    if (searchQuery.value) {
      filteredHistoryList.value = filteredHistoryList.value.filter(history => history.key !== item.key)
    }

    // 更新总记录数
    totalRecords.value--
  }
  catch (error) {
    console.error('delete history error:', error) // 删除历史记录失败
  }
}

/**
 * 确认清空所有历史记录
 */
const confirmClearAllHistory = () => {
  isClearAllModalOpen.value = true
}

/**
 * 清空所有历史记录
 */
const clearAllHistory = async () => {
  try {
    await clearHistory()
    isDataLoaded.value = false
    hasMoreData.value = false
    totalRecords.value = 0
    isClearAllModalOpen.value = false
  }
  catch (error) {
    console.error('clear all history error:', error) // 清空历史记录失败
  }
}

/**
 * 高亮搜索关键字
 * @param text 需要高亮的文本
 * @returns 高亮后的HTML文本
 */
const highlightSearchQuery = (text) => {
  const regex = new RegExp(`(${escapeRegExp(searchQuery.value)})`, 'gi')
  return text.replace(regex, '<span class="search-highlight">$1</span>')
}

/**
 * 转义正则表达式特殊字符
 * @param string 需要转义的字符串
 * @returns 转义后的字符串
 */
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 搜索历史记录
 */
const searchHistory = async () => {
  if (!searchQuery.value.trim()) {
    filteredHistoryList.value = []
    return
  }

  debouncedSearch()
}

// 创建防抖版本的搜索函数
const debouncedSearch = useDebounceFn(async () => {
  try {
    if (!searchQuery.value.trim()) {
      filteredHistoryList.value = []
      return
    }
    isLoading.value = true

    // 调用数据库搜索
    const { data, total } = await searchHistoryByQueryAndType(searchQuery.value, currentTranslateMode.value, 1, pageSize.value)
    filteredHistoryList.value = data
    totalRecords.value = total
    searchPage.value = 1 // 重置搜索页码
  }
  catch (error) {
    console.error('搜索历史记录失败:', error)
  }
  finally {
    isLoading.value = false

    // 搜索完成后聚焦到输入框
    nextTick(() => {
      if (searchInputRef.value?.$el) {
        const inputElement = searchInputRef.value.$el.querySelector('input')
        if (inputElement) {
          inputElement.focus()
        }
      }
    })
  }
}, 300) // 300毫秒的延迟

// 当前搜索页码
const searchPage = ref(1)

// 加载更多搜索结果
const loadMoreSearchResults = async () => {
  if (!searchQuery.value.trim() || isLoadingMore.value) {
    return
  }

  isLoadingMore.value = true
  searchPage.value++

  try {
    const { data } = await searchHistoryByQueryAndType(searchQuery.value, currentTranslateMode.value, searchPage.value, pageSize.value)

    if (data.length > 0) {
      filteredHistoryList.value = [...filteredHistoryList.value, ...data]
    }
    else {
      hasMoreData.value = false
    }
  }
  catch (error) {
    console.error('加载更多搜索结果失败:', error)
  }
  finally {
    isLoadingMore.value = false
  }
}

/**
 * 清除搜索
 */
const clearSearch = () => {
  searchQuery.value = ''
  filteredHistoryList.value = []
}

/**
 * 清理资源，移除事件监听器
 */
const cleanupResources = () => {
  const container = historyContainerRef.value
  if (container) {
    container.removeEventListener('scroll', handleScroll)
  }
}

/**
 * 监听当前翻译模式变化，重新加载对应模式的历史记录
 */
watch(
  () => currentTranslateMode.value,
  (newMode) => {
    if (isDataLoaded.value && newMode && isPopoverOpen.value) {
      currentPage.value = 1
      loadHistoryByTypeAndPage(1, pageSize.value, newMode)
    }
  }
)

/**
 * 监听Popover开关状态，在打开时加载历史记录
 */
watch(
  () => isPopoverOpen.value,
  (isOpen) => {
    // console.log('监听Popover开关状态', { isOpen });
    if (isOpen) {
      loadFirstPage()
    }
  }
)

// 组件卸载时清理资源
onBeforeUnmount(() => {
  cleanupResources()
})
</script>

<style scoped>
.history-loading {
  min-height: 6.25rem;
}

.history-empty {
  min-height: 12.5rem;
}

/* 文本省略与悬停效果 */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-word;
}

/* 搜索高亮样式 */
:deep(.search-highlight) {
  font-weight: 700;
  background-color: rgba(var(--ui-primary-rgb), 0.15);
  border-radius: 2px;
}

/* 暗黑模式下的高亮样式 */
:root.dark :deep(.search-highlight) {
  background-color: rgba(var(--ui-primary-rgb), 0.25);
}
</style>
