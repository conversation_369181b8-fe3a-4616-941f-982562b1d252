<template>
  <div class="relative flex w-full flex-col rounded-br-lg px-3 pb-2">
    <!-- 顶部loading 线 -->
    <!-- <div class="ai-select-loading-line absolute top-0 left-0 w-full"></div> -->
    <!-- 头部 - 使用固定高度 -->
    <div
      :class="{ 'ai-select-loading-line': isAiSelectLoading && currentTranslateMode === 'aiSelect' }"
      class="mb-2 flex flex-shrink-0 items-center justify-between pt-2 text-sm text-neutral-600 dark:text-neutral-300"
    >
      <div class="flex items-center gap-2">
        <!-- 翻译结果 -->
        <span class="pl-3 text-sm font-medium text-neutral-600 md:text-sm lg:text-sm xl:text-sm dark:text-neutral-400">{{ $t('common.translation_results') }}:</span>
        <!-- 左侧：翻译模型选择 -->
        <USelectMenu
          v-model="selectedResultModel"
          :items="selectModel"
          :search-input="false"
          :ui="{ content: 'w-full' }"
          class="w-auto cursor-pointer"
          variant="ghost"
          @update:model-value="handleSelectModel"
        >
          <template #item="{ item }">
            <div class="flex items-center gap-2">
              <!-- 模型图标 -->
              <img :src="translateModeIcon[item.value]" :alt="item.label" class="size-6 rounded-xs bg-white p-0.5" />
              <!-- 模型名称 -->
              <span class="text-sm">{{ item.label }}</span>
            </div>
          </template>
          <template #default>
            <div class="flex items-center gap-2">
              <!-- 模型图标 -->
              <img :src="translateModeIcon[selectedResultModel.value]" :alt="selectedResultModel.label" class="size-5 rounded-xs bg-white p-0.5" />
              <!-- 模型名称 -->
              <span class="text-sm">{{ selectedResultModel.label }}</span>
            </div>
          </template>
        </USelectMenu>
        <div v-if="isAiSelectLoading && currentTranslateMode === 'aiSelect'" class="flex items-center gap-2">
          <div class="loading" />
          <!-- 评分中 -->
          <span v-if="!isAiSelectAnalysis" class="loading-text">{{ t('selected_trans.main.scoring') }}</span>
          <!-- 评分分析中 -->
          <span v-else-if="isAiSelectAnalysis" class="loading-text">{{ t('selected_trans.main.scoring_and_analyzing') }}</span>
        </div>
      </div>

      <!-- 右侧：复制按钮  -->
      <div v-if="translationTextToDisplay" class="flex items-center gap-2">
        <CopyButton :content="translationTextToDisplay" />
      </div>
    </div>

    <div class="relative flex h-full flex-col overflow-hidden">
      <!-- 择优翻译结果区域 -->
      <div class="select-results-container relative max-h-[50vh] min-h-[12.5rem] flex-1 overflow-y-auto px-3 whitespace-pre-wrap max-sm:max-h-[14.5rem]">
        <!-- 根据选择的模型显示不同的结果 -->
        <div v-if="!isAiSelectTranslateError" class="h-full">
          <!-- ! AI择优结果 / 用户自定义结果 -->
          <!-- 这里不要使用 if 判断是否显示择优翻译， 添加了切换模型就无法高亮了 -->
          <div :current-model="selectedResultModel.value">
            <template v-for="group in groupedResultText" :key="group.paragraphId">
              <div :data-paragraph-id="group.paragraphId" selected-translate-paragraph="y" :selected-translate-id="group.paragraphId">
                <template v-for="item in group.items" :key="item.statusNodeGlobalId">
                  <span
                    :class="[{ 'highlight-on-hover': isSingleSentenceAiSelectModeChanged }, 'trigger-element-' + item.statusNodeGlobalId]"
                    class="select-result-item inline rounded py-0.5 text-base leading-7 dark:text-neutral-400 dark:hover:bg-gray-800"
                    @click="handleClickSelectResults(item.statusNodeGlobalId, item)"
                  >
                    <span v-if="item.text === '<n0></n0>'" selected-result-status-br="true" :selected-result-status-br-id="item.statusNodeGlobalId">
                      <br />
                    </span>
                    <span
                      v-if="item.text !== '<n0></n0>'"
                      :class="{ 'sentence-selected-highlight': highlightId === item.statusNodeGlobalId && highlightId !== 'combined' }"
                      :selected-translate-result-id="item.statusNodeGlobalId"
                      :selected-translate-result-engine="item.engine"
                    >
                      {{ handleAddSpace(item.text) }}
                    </span>
                  </span>
                </template>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- 择优翻译底部操作区 -->
      <div class="mt-auto flex flex-shrink-0 items-center justify-end gap-2 pt-2">
        <!-- 重新择优按钮 -->
        <UButton
          v-if="reSelect"
          square
          class="rounded-[7px] px-3"
          size="xl"
          variant="solid"
          color="primary"
          :disabled="isTranslateStatus || isAiSelectLoading"
          @click="handleReSelect"
        >
          {{ t('selected_trans.operation.re_scoring') }}
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CopyButton from '@/components/CopyButton/index.vue'
import { useInputTranslate } from '@/store/inputTranslate'
import { translateModeIcon } from '@/utils/translateModeIcon'

/** 获取共享数据 */
import { translationState } from '@/pages/text/useData/useTranslationState'
import { sharedState } from '@/pages/text/useData/sharedState'
import { useAiSelectTranslate } from '@/pages/text/translate/selectTranlstae'
import { useTranslationHistoryState } from '@/pages/text/useData/useHistoryState.ts'
// 翻译引擎的图标icon
const useInputTransStore = useInputTranslate()
const { enabledEnginesList, isSingleSentenceAiSelect, currentTranslateMode, isAiSelectAnalysis } = storeToRefs(useInputTransStore)
const { originalTranslationEngineList, translationEngineOptions } = translationState
const {
  highlightId,
  aiSelectFinallyResultText,
  customResult,
  historyModelOptions,
  reSelect,
  selectedResultModel,
  isAiSelectLoading,
  isTranslateStatus,
  editSelectedTransResult,
  historyKey,
  currentTranslateStatus
} = sharedState
const { aiSelectTranslateReq } = useAiSelectTranslate
const { updateUserCustomTranslationResult } = useTranslationHistoryState()

// 国际化
const { t } = useI18n()

const { isAiSelectTranslateError } = defineProps({
  // 是否开启精挑翻译失败
  isAiSelectTranslateError: {
    type: Boolean,
    default: false
  }
})

/**
 * 判断本次翻译的逐句翻译模式是否跟当前设置的逐句翻译模式一致
 * 如果一直就不需要重新择优翻译
 * ! isSingleSentence是本次翻译之后存储的一个翻译模式响应式数据
 * ! isSingleSentenceAiSelect是当前存储到store的翻译模式
 */
const isSingleSentenceAiSelectModeChanged = computed(() => {
  return currentTranslateStatus.value.isSingleSentence == isSingleSentenceAiSelect.value
})

// 判断显示历史记录还是翻译引擎选项
const currentDisplayResults = computed(() => (historyModelOptions.value.length > 0 ? historyModelOptions.value : translationEngineOptions.value))

// 构建选择模型列表
const selectModel = computed(() => {
  // AI择优选项
  const aiSelectModel = {
    // AI择优/翻译结果
    label: `${t('common.ai_selected')}`,
    value: 'AISelectResults'
  }

  // 用户自定义选项
  const customModel = {
    // 自定义/翻译结果
    label: `${t('selected_trans.settings.custom_results')}`,
    value: 'CustomResults'
  }

  // 根据是否有历史记录构建选项列表
  const modelList = historyModelOptions.value.length > 0 ? historyModelOptions.value : originalTranslationEngineList.value.filter(item => enabledEnginesList.value.includes(item.value))

  // 映射选项并添加AI择优选项和用户自定义选项（当存在编辑时）
  return [
    ...(customResult.value.length > 0 ? [customModel] : []), // 如果存在自定义结果，则添加自定义选项
    aiSelectModel, // AI择优选项
    ...modelList.map(item => ({
      // 翻译引擎/翻译结果
      label: `${item.label}`,
      value: item.value
    }))
  ]
})

/**
 * 仅当文本包含英文字母时添加空格
 * 这样可以避免在中文句子后面错误地添加空格
 */

const handleAddSpace = (text) => {
  // 检查sentencesNode是否为有效对象
  if (!text || typeof text !== 'string') {
    return ''
  }

  if (text && /[a-zA-Z]/.test(text) && !/[\u4e00-\u9fa5]/.test(text)) {
    if (!text.endsWith(' ')) {
      text = text + ' '
    }
  }
  return text
}

// 添加计算属性来缓存当前翻译文本
const translationTextToDisplay = ref('')

// 处理模型选择
const handleSelectModel = async (model) => {
  selectedResultModel.value = model

  // 等待DOM更新后获取文本
  await nextTick()
  updateCopyText()
}

/**
 * 更新要复制的文本
 */
const updateCopyText = () => {
  // 获取当前选择模型的DOM元素
  // 检查 selectedResultModel.value.value 是否存在，避免无效选择器
  if (!selectedResultModel.value?.value) {
    translationTextToDisplay.value = ''
    return
  }

  const selector = `div[current-model="${selectedResultModel.value.value}"]`

  const translateType = document.querySelector(selector) as HTMLElement

  if (translateType) {
    // 复制 selected-model、current-model 中的所有文本 一定要使用 innerText 不要使用 textContent， 否则复制不了换行符
    translationTextToDisplay.value = translateType.innerText
  }
  else {
    translationTextToDisplay.value = ''
  }
}

// 这里由于返回的数据不是有序的返回, 所以需要根据全局id 的来排序
const sortedResultText = computed(() => {
  // 接收需要排序的数据（aiSelectFinallyResultText.value 或者 customResult.value）
  let data = []
  if (['CustomResults', 'AISelectResults'].includes(selectedResultModel.value.value)) {
    // 获取用户自定义或者AI择优的翻译结果
    data = selectedResultModel.value.value === 'CustomResults' ? JSON.parse(JSON.stringify(customResult.value)) : JSON.parse(JSON.stringify(aiSelectFinallyResultText.value))
  }
  else {
    // 获取当前选择模型的翻译结果
    const currentModelData = currentDisplayResults.value.find(item => item.value === selectedResultModel.value.value)
    data = currentModelData?.translationsResult ? JSON.parse(JSON.stringify(currentModelData.translationsResult)) : []
  }

  const result = data.sort((a, b) => {
    // 将 "1-1" 格式的字符串分割成数组
    const [aMajor, aMinor] = a.statusNodeGlobalId.split('-').map(Number)
    const [bMajor, bMinor] = b.statusNodeGlobalId.split('-').map(Number)

    // 先比较主要数字
    if (aMajor !== bMajor) {
      return aMajor - bMajor
    }
    // 如果主要数字相同，则比较次要数字
    return aMinor - bMinor
  })

  return result
})

// 添加一个新的计算属性来按paragraphId分组翻译结果
const groupedResultText = computed(() => {
  const data = sortedResultText.value
  if (!data || !data.length) return []

  // 按paragraphId分组
  const groupedByParagraph = {}
  data.forEach((item) => {
    if (!groupedByParagraph[item.paragraphId]) {
      groupedByParagraph[item.paragraphId] = []
    }
    groupedByParagraph[item.paragraphId].push(item)
  })

  return Object.entries(groupedByParagraph).map(([paragraphId, items]) => ({
    paragraphId,
    items
  }))
})

/**
 * 处理输入框点击择优翻译结果高亮
 * @param {string} statusNodeGlobalId - 句子的全局唯一标识符
 */
const handleClickSelectResults = (statusNodeGlobalId, item) => {
  // 如果单句精挑翻译，则需要添加选中效果
  if (currentTranslateMode.value === 'aiSelect' && isSingleSentenceAiSelect.value) {
    // 如果是换行符就不允许选中
    if (item.text === '<n0></n0>') return

    // 添加选中效果
    const textareaDom = document.querySelector('.editable-textarea')

    // 更新高亮 id， 这里更新之后 在MultiModelTransResult.vue 组件会监听到该 id 的变化，然后对模型进行高亮（selected-translate-result-id）

    highlightId.value = statusNodeGlobalId
    const paragraphs = textareaDom.querySelectorAll(`span.container-source`)

    // 移除所有现有高亮
    const allHighlightedNodes = textareaDom.querySelectorAll('.sentence-selected-highlight')
    for (const highlightedNode of allHighlightedNodes) {
      highlightedNode.classList.remove('sentence-selected-highlight')
    }

    // 添加原文句子选中效果
    for (const item of paragraphs) {
      if (item.getAttribute('selected-translate-id') === statusNodeGlobalId) {
        item.classList.add('sentence-selected-highlight')
      }
    }
  }
}

// 组件挂载时添加事件监听
onMounted(() => {
  // 初始化选择模型为第一个选项
  selectedResultModel.value = selectModel.value[0]

  // 初始化复制文本
  nextTick().then(() => {
    updateCopyText()
  })
})

/**
 * 监听编辑状态
 * 1. 如果已经被用户编辑过了就根据 id 来修改数据
 * 2. 没有被修改过的， 新增数据
 * 3. 已经存在customResult数据，用户切换模型翻译结果
 */
watch(
  () => editSelectedTransResult.value,
  async (newValue) => {
    const { engine, text, statusNodeGlobalId, paragraphId } = newValue

    if (!statusNodeGlobalId || !text || !engine) return

    // 使用深拷贝创建独立副本，避免修改原始数据
    const sortedResultTextData = JSON.parse(JSON.stringify(sortedResultText.value))

    // 检查是否存在匹配的项
    let foundMatch = false
    for (const item of sortedResultTextData) {
      if (item.statusNodeGlobalId === statusNodeGlobalId) {
        item.text = text
        item.engine = engine
        item.paragraphId = paragraphId
        foundMatch = true
      }
    }

    // 如果没找到匹配项，添加新条目
    if (!foundMatch) {
      sortedResultTextData.push({
        statusNodeGlobalId,
        text,
        engine,
        paragraphId
      })
    }

    // 更新自定义结果
    customResult.value = sortedResultTextData

    // 切换到用户自定义选项
    const customResultsOption = selectModel.value.find(option => option.value === 'CustomResults')
    if (customResultsOption) {
      selectedResultModel.value = customResultsOption
    }

    // 更新历史记录
    await updateUserCustomTranslationResult(historyKey.value, customResult.value)
  }
)

/**
 * 监听翻译结果变化，更新复制文本
 */
watch(
  () => [aiSelectFinallyResultText.value, customResult.value],
  () => {
    nextTick().then(() => {
      updateCopyText()
    })
  },
  { deep: true }
)

// 监听customResult数据
watch(customResult, (newValue) => {
  // console.log('customResult', newValue);

  // 如果customResult数据为空，则切换到AI择优选项
  if (customResult.value.length === 0) {
    const aiSelectModel = selectModel.value.find(option => option.value === 'AISelectResults')
    if (aiSelectModel) {
      selectedResultModel.value = aiSelectModel
    }
  }
  else if (customResult.value.length > 0) {
    const customResultsOption = selectModel.value.find(option => option.value === 'CustomResults')
    if (customResultsOption) {
      selectedResultModel.value = customResultsOption
    }
  }
})

/**
 * 重新择优
 */
const handleReSelect = () => {
  // 清空AI择优结果，避免数据混淆
  aiSelectFinallyResultText.value = []

  // 调用择优翻译请求
  aiSelectTranslateReq()
  reSelect.value = false
}
</script>

<style scoped>
/* 添加提示信息样式 */
.insufficient-engines-message {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 12.5rem;
  padding: 1.25rem;
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  max-width: 25rem;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: rgba(24, 144, 255, 0.1);
}

.dark .message-content {
  background-color: rgba(17, 24, 39, 0.8);
}

.message-text {
  flex: 1;
}

.message-text .title {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: var(--primary-color, #1890ff);
}

.dark .message-text .title {
  color: var(--ui-primary-400, #60a5fa);
}

.message-text .description {
  color: #666;
  font-size: 0.9em;
  line-height: 1.5;
}

.dark .message-text .description {
  color: #9ca3af;
}

/* 隐藏滚动条但保持内容可见 */
.select-results-container {
  -ms-overflow-style: none;
  /* IE 和 Edge */
  scrollbar-width: none;
  /* Firefox */
}

.select-results-container::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari 和 Opera */
}
</style>
