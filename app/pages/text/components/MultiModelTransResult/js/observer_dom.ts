/**
 * DOM变化观察器，监听DOM结构变化
 */
let observer: MutationObserver | null = null

/**
 * 初始化DOM观察器
 */
const initDomObserver = (selectedModels, highlightId, editSelectedTransResult) => {
  // 如果已经存在观察器，先断开连接
  if (observer) {
    observer.disconnect()
    observer = null
  }

  // 创建一个新的变异观察器
  observer = new MutationObserver((mutations) => {
    // 处理每个变化
    for (const mutation of mutations) {
      // 只检查节点添加
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // 处理被移除的节点
        for (const node of mutation.removedNodes) {
          if (node.nodeType !== Node.ELEMENT_NODE) continue
          const element = node as HTMLElement
          // 如果移除的是含有select-result-id属性的节点或其子元素
          const finallyNode = element.querySelector
            ? element.querySelector(`[selected-translate-result-id]`)
            : element.hasAttribute && element.hasAttribute('selected-translate-result-id')
              ? element
              : null

          if (finallyNode) {
            // 节点被移除但保留其选中状态在内存中
            // 不执行任何清除操作，让selectedModels中的状态保持不变
            // 获取节点的ID
            const nodeId = finallyNode.getAttribute('selected-translate-result-id')
            // 如果ID存在，从选中状态中删除
            if (nodeId && selectedModels[nodeId]) {
              delete selectedModels[nodeId]
            }
          }
        }

        // 处理新增的节点
        for (const node of mutation.addedNodes) {
          if (node.nodeType !== Node.ELEMENT_NODE) continue
          const element = node as HTMLElement

          // 在这里处理新增节点的逻辑
          const hasChildWithResultId = element.querySelector(`[selected-translate-result-id]`) ? element : null
          if (hasChildWithResultId) {
            const hasResultIdAttr = hasChildWithResultId.querySelector('span[selected-translate-result-id]') || null
            const hasEngineAttr = hasChildWithResultId.querySelector('span[selected-translate-result-engine]') || null
            // 获取节点的ID和引擎
            const nodeId = hasResultIdAttr ? hasResultIdAttr.getAttribute('selected-translate-result-id') : null
            const engine = hasEngineAttr ? hasEngineAttr.getAttribute('selected-translate-result-engine') : null

            // 如果ID和引擎都存在，更新选中状态
            if (nodeId && engine) {
              selectedModels[nodeId] = engine
            }
            else if (nodeId && !engine) {
              // 如果有ID但没有引擎，清除选中状态
              delete selectedModels[nodeId]
            }
          }
          else {
            // 检查是否需要清除所有选中状态
            const resultContainer = element.closest('.select-results-container')
            if (resultContainer && Object.keys(selectedModels).length > 0) {
              // 检查所有已选中的ID是否仍然存在于DOM中
              for (const id in selectedModels) {
                const idElement = resultContainer.querySelector(`[selected-translate-result-id="${id}"]`)
                const engineElement = resultContainer.querySelector(`[selected-translate-result-engine]`)
                if (!idElement || !engineElement) {
                  delete selectedModels[id]
                }
              }
            }
          }
        }
      }
    }
  })

  // 只观察带有current-model属性的节点及其子树
  const currentModelNodes = document.querySelector('.select-results-container')

  if (currentModelNodes) {
    observer.observe(currentModelNodes, {
      childList: true, // 只观察子节点变化
      subtree: true // 观察所有后代节点
    })
  }
}

const disconnectObserver = () => {
  if (observer) {
    observer.disconnect()
    observer = null
  }
}

export { disconnectObserver, initDomObserver }
