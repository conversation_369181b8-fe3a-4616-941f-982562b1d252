<!-- 显示模型翻译结果 -->
<template>
  <div class="flex h-full w-full flex-col">
    <transition-group name="list" tag="div" class="flex flex-col">
      <!-- !这个 v-for 的 div 不要添加 class 样式， 会将所有模型的容器显示出来，如果需要在每个模型的容器上添加样式请在：trans-target_card -->
      <div
        v-for="(author, index) in sortedTranslationEngines"
        :key="index"
        @mouseenter="hoveredAuthor = author.value"
        @mouseleave="hoveredAuthor = null"
      >
        <!--
            判断逻辑说明：
            1. 传统翻译模式(traditional)下，只显示traditionalEngines对象中包含的模型
            2. 非传统模式下，显示 enabledEnginesList(当前选中的模型) 中包含的模型或 historyModelOptions(历史记录) 中包含的模型
          -->
        <div
          v-if="
            currentTranslateMode === 'traditional'
              ? traditionalEngines.value === author.value
              : enabledEnginesList.includes(author.value) || historyModelOptions.some((item) => item.value === author.value)
          "
          class="trans-target_card relative mb-3 rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-neutral-800"
          :class="{
            'transition-bg-color selected-bg-color rounded-lg':
              getDisplayTranslations(author).some((item) => selectedModelsStatus[item.statusNodeGlobalId] === author.value) && currentTranslateMode === 'aiSelect'
          }"
        >
          <div class="flex justify-between">
            <div class="inline-flex items-center gap-1.5">
              <!-- 1. 翻译模型图标 -->
              <span class="flex items-center">
                <UTooltip :text="author.label" :popper="{ placement: 'top', strategy: 'fixed' }">
                  <span class="flex h-6 w-6 rounded-xs bg-white p-0.5 dark:bg-gray-100">
                    <img :src="translateModeIcon[author.value]" />
                  </span>
                </UTooltip>
              </span>

              <!-- 语言不支持错误提示 -->
              <UTooltip
                v-if="!modelSupportState.isEngineSupported(author.value) && originalTextList.isTranslated"
                :text="$t('selected_trans.settings.trans_models.target_language_is_not_supported')"
                :popper="{ placement: 'top', strategy: 'fixed' }"
              >
                <UIcon name="i-heroicons-exclamation-triangle" class="ml-2 h-4 w-4 text-red-500 dark:text-red-400" />
              </UTooltip>

              <!-- 3. 错误提示图标 - 翻译异常 -->
              <UTooltip v-if="author.error && author.error.length > 0" :text="$t('selected_trans.main.translation_exception')" :popper="{ placement: 'top', strategy: 'fixed' }">
                <div class="ml-2">
                  <div v-if="isError(author.error)">
                    <!-- 根据句子高亮显示来显示错误信息， 如果没有高亮显示就显示默认的错误信息 -->
                    <UIcon name="i-heroicons-exclamation-triangle" class="h-4 w-4 text-red-500 dark:text-red-400" />
                  </div>
                </div>
              </UTooltip>

              <!-- 2. 择优评分 -->
              <div v-if="author.score && author.score.length > 0 && currentTranslateMode === 'aiSelect'">
                <UTooltip :text="$t('selected_trans.main.selected_score')" :popper="{ placement: 'top', strategy: 'fixed' }">
                  <div class="score-container flex text-sm text-green-500">
                    <span v-html="aiSelectShowContent(author.score)" />
                  </div>
                </UTooltip>
              </div>

              <!-- loading -->
              <!-- 翻译状态显示 - 基础翻译状态或择优状态 -->
              <div v-if="modelSupportState.isEngineSupported(author.value) && !isError(author.error)" class="flex items-center gap-2">
                <!-- 基础翻译状态 - 翻译中或等待择优 -->
                <template v-if="[2, 7].includes(author.status)" class="loading-layout ml-1">
                  <div class="loading" />
                  <span class="loading-text">
                    <!-- 翻译中 -->
                    <template v-if="author.status === 2">{{ $t('selected_trans.main.translating') }}</template>
                    <!-- 等待择优 -->
                    <template v-else-if="author.status === 7 && currentTranslateMode === 'aiSelect'">{{ $t('selected_trans.main.waiting_for_optimization') }}</template>
                  </span>
                </template>

                <!-- 择优翻译状态 - 评分中或分析中 -->
                <template v-else-if="currentTranslateMode === 'aiSelect' && highlightId && 'selectedTransStatus' in author && author.selectedTransStatus && author.selectedTransStatus.length > 0">
                  <template v-for="item in author.selectedTransStatus" :key="item.statusNodeGlobalId">
                    <template v-if="highlightId === item.statusNodeGlobalId && item.status === 5" class="loading-layout ml-1">
                      <div class="loading" />
                      <!-- 评分中 -->
                      <span v-if="!isAiSelectAnalysis" class="loading-text">{{ $t('selected_trans.main.scoring') }}</span>
                      <!-- 评分分析中 -->
                      <span v-else class="loading-text">{{ $t('selected_trans.main.scoring_and_analyzing') }}</span>
                    </template>
                  </template>
                </template>
              </div>

              <!-- 择优结果 -->
              <div
                v-if="aiSelectFinallyResultText.length > 0 && aiSelectFinallyResultText.some((item) => item.engine === author.value && item.statusNodeGlobalId === highlightId)"
                class="flex items-center"
              >
                <span class="flex-shrink-0 rounded-md bg-orange-100 p-0.5 px-2 text-[.75rem] dark:bg-orange-900/30">{{ $t('selected_trans.main.ai_selected_result') }}</span>
              </div>

              <!-- 3. 选中状态标签 -->
              <div v-if="getDisplayTranslations(author).length > 0 && currentTranslateMode === 'aiSelect'" class="flex items-center">
                <!-- 选择此结果 -->
                <span
                  v-if="
                    hoveredAuthor === author.value
                      && Object.keys(selectedModelsStatus).length > 0
                      && !getDisplayTranslations(author).some((item) => selectedModelsStatus[item.statusNodeGlobalId] === author.value)
                  "
                  :class="[{ 'transition-bg-color cursor-pointer': hoveredAuthor === author.value }]"
                  class="select-button z-10 flex flex-shrink-0 items-center gap-0.5 rounded-md p-0.5 text-neutral-400"
                  @click="handleContainerClick(author)"
                >
                  <UIcon name="i-heroicons-check-circle-16-solid" class="size-4 flex-shrink-0" />
                  <!-- 选择此结果 -->
                  <span class="text-[.8rem]">{{ $t('selected_trans.main.select_this_result') }}</span>
                </span>
              </div>
            </div>

            <!-- 右上角区域 - 从右到左: 1.关闭图标, 2.复制图标, 3.展开/收缩按钮, 4.重新翻译图标 -->
            <div class="z-10 flex items-center gap-1">
              <!-- 重新翻译图标 - 仅在有错误时显示 -->
              <UTooltip v-if="isError(author.error) && currentTranslateMode !== 'aiSelect'" :text="$t('selected_trans.operation.re_translate')" :popper="{ placement: 'top', strategy: 'fixed' }">
                <UButton
                  active
                  color="error"
                  variant="ghost"
                  active-color="error"
                  active-variant="ghost"
                  size="sm"
                  :ui="{
                    leadingIcon: 'size-5'
                  }"
                  icon="i-heroicons-arrow-path"
                  class="rounded-full text-red-500 hover:bg-red-50"
                  @click="handleRetranslate(author)"
                />
              </UTooltip>

              <!-- 4. 展开/收缩按钮 -->
              <UTooltip
                v-if="getDisplayTranslations(author).length > 0"
                :text="modelCollapsed[author.value] ? $t('selected_trans.operation.expand') : $t('selected_trans.operation.collapse')"
                :popper="{ placement: 'top', strategy: 'fixed' }"
              >
                <UButton
                  active
                  color="neutral"
                  variant="ghost"
                  active-color="neutral"
                  active-variant="ghost"
                  size="sm"
                  :ui="{
                    leadingIcon: 'size-5'
                  }"
                  :icon="modelCollapsed[author.value] ? 'i-solar-alt-arrow-right-linear' : 'i-solar-alt-arrow-down-linear'"
                  class="rounded-full text-neutral-500"
                  @click="toggleCollapse(author.value)"
                />
              </UTooltip>

              <!-- 2. 复制译文 -->
              <CopyButton
                v-if="getDisplayTranslations(author).length > 0"
                :content="
                  getDisplayTranslations(author)
                    .map((item) => item.text)
                    .join('')
                "
              />

              <!-- 1. 关闭翻译模型按钮 -->
              <UTooltip :text="$t('selected_trans.settings.trans_models.close')" :popper="{ placement: 'right', strategy: 'fixed' }">
                <UButton
                  active
                  color="neutral"
                  variant="ghost"
                  active-color="neutral"
                  active-variant="ghost"
                  size="sm"
                  :ui="{
                    leadingIcon: 'size-5'
                  }"
                  icon="i-solar-close-circle-linear"
                  class="rounded-full text-neutral-500"
                  @click="handleCloseChannel(author)"
                />
              </UTooltip>
            </div>
          </div>

          <!-- 语言不支持错误信息显示 -->
          <div v-if="!modelSupportState.isEngineSupported(author.value) && originalTextList.isTranslated" class="mt-1 text-sm text-red-600">
            {{ $t('selected_trans.settings.trans_models.target_language_is_not_supported') }}
          </div>

          <!-- 翻译结果/ 错误信息 -->
          <div
            v-if="
              !modelCollapsed[author.value]
                && author.translationsResult.length > 0
                && modelSupportState.isEngineSupported(author.value)
                && author.translationsResult.some((item) => item.text.trim() !== '')
            "
            class="flex flex-col gap-1"
          >
            <div
              :key="textContainerKey"
              class="text-main-container delete-scrollbar mt-2 mb-1 max-h-[80vh] overflow-y-auto text-base leading-7 whitespace-pre-wrap text-black max-sm:max-h-[14.5rem] dark:text-neutral-400"
            >
              <!-- 当没有highlightId或当前highlightId没有错误时显示翻译结果 -->
              <div v-if="!isError(author.error)">
                <!-- 按段落分组显示翻译结果 -->
                <div v-for="(group, groupIndex) in getGroupedTranslations(author)" :key="groupIndex" :data-paragraph-id="group.paragraphId">
                  <span
                    v-for="(item, index) in group.items"
                    :key="index"
                    selected-translate-result-node="y"
                    :selected-translate-result-id="item.statusNodeGlobalId"
                    :selected-translate-result-engine="item.engine"
                    class="container-target"
                  >
                    {{ item.text }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 逐句翻译的错误信息 -->
          <span v-if="isError(author.error)" class="mt-8 space-y-1 text-sm leading-5 text-red-600 dark:text-red-400">
            <span v-for="(item, index) in getDisplayError(author)" :key="index">
              {{ item.message }}
            </span>
          </span>

          <!-- 择优分析（分析结果） -->
          <div v-if="!modelCollapsed[author.value] && author.rationale && author.rationale.length > 0 && isAiSelectAnalysis && currentTranslateMode === 'aiSelect'">
            <transition name="collapse-transition">
              <div class="mt-3 text-[.75rem] leading-5 text-neutral-500">
                <span v-html="aiSelectShowContent(author.rationale)" />
              </div>
            </transition>
          </div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import { useInputTranslate } from '@/store/inputTranslate'
import { translateModeIcon } from '@/utils/translateModeIcon'
import { modelSupportState } from '@/pages/text/useData/useModelSupport'
import { useDomControl } from '@/pages/text/translate/domControl.ts'
import { disconnectObserver, initDomObserver } from './js/observer_dom'
import { storeToRefs } from 'pinia'
import { useTranslate } from '@/pages/text/translate/translate'

// 翻译配置数据管理
import { translationState } from '@/pages/text/useData/useTranslationState'
// 历史记录数据管理
import { useTranslationHistoryState } from '@/pages/text/useData/useHistoryState.ts'
// 共享状态
import { sharedState } from '@/pages/text/useData/sharedState'

const { buildOriginalTextListSendRequest } = useTranslate

const { changeSelectedResult } = useDomControl

const { translationEngineOptions, clearSpecifiedAiSelectTranslateResults } = translationState

// 输入译本地存储
const useInputTransStore = useInputTranslate()
const { enabledEnginesList, isAiSelectAnalysis, currentTranslateMode, traditionalEngines } = storeToRefs(useInputTransStore)

const { clearSingleModelHistoryData } = useTranslationHistoryState()

const { highlightId, originalTextList, historyModelOptions, selectedResultModel, modelCollapsed, editSelectedTransResult, selectedModelsStatus, aiSelectFinallyResultText } = sharedState

// 跟踪当前鼠标悬停的模型
const hoveredAuthor = ref(null)

// 添加文本容器的渲染键，用于在 selectedModelsStatus 更新时重新渲染
const textContainerKey = ref(0)

/**
 * 监听 selectedModelsStatus 和 highlightId 的变化，触发 text-main-container 重新渲染
 * 防止 dom 元素被修改之后无法恢复
 */
watch(
  [() => selectedModelsStatus.value, () => highlightId.value],
  () => {
    textContainerKey.value++
  },
  { deep: true }
)

/**
 * 处理重新翻译请求
 * @param {Object} author - 模型数据对象
 */
const handleRetranslate = (author) => {
  // 发送单个模型的重新翻译， 不会重新择优翻译
  buildOriginalTextListSendRequest([author.value], false)
}

/**
 * 切换指定模型的折叠状态
 * @param {string} modelValue - 模型的唯一标识值
 */
const toggleCollapse = (modelValue) => {
  modelCollapsed.value[modelValue] = !modelCollapsed.value[modelValue]
}

/**
 * 处理整个模型容器的点击事件
 * @param {Object} author - 模型数据对象
 */
const handleContainerClick = (author) => {
  // 获取当前高亮的句子ID，或者使用该模型的第一个翻译结果作为默认值
  const statusNodeId = highlightId.value || (author.translationsResult.length > 0 ? author.translationsResult[0].statusNodeGlobalId : null)

  // 如果有有效的句子ID，则调用翻译点击处理函数
  if (statusNodeId) {
    handleModelTranslateClick(statusNodeId, author)
  }
}

/**
 * 点击翻译结果更新择优翻译结果
 */
const handleModelTranslateClick = (statusNodeGlobalId, author) => {
  if (currentTranslateMode.value === 'aiSelect') {
    const groupedTranslations = getGroupedTranslations.value(author)

    // 获取更新的文本, 不管是什么翻译模式（整句翻译还是单句翻译），都使用第一个翻译结果的文本
    const updateText = groupedTranslations[0]?.items?.find(item => item.statusNodeGlobalId === statusNodeGlobalId)?.text

    // 更新择优翻译结果
    changeSelectedResult(statusNodeGlobalId, author.value, selectedResultModel.value, updateText)
  }
}

// 对单个翻译引擎进行关闭
const handleCloseChannel = (data) => {
  const { value, translationsResult } = data

  // 确保至少保留一个引擎
  if (enabledEnginesList.value.length > 1) {
    // 更新存储
    useInputTransStore.enabledEnginesList = enabledEnginesList.value.filter(item => item !== value)

    // 删除该模型对应的折叠状态
    delete modelCollapsed.value[value]

    // 如果翻译结果为空，则不进行关闭
    if (translationsResult.length === 0) {
      return
    }

    const { messageEvent } = sharedState

    // 先清除一次择优翻译的监听事件- 防止重复接收择优翻译结果
    window.removeEventListener('message', messageEvent.value.aiSelectTranslateMessage)
    messageEvent.value.aiSelectTranslateMessage = null

    // 清除指定模型的择优翻译结果
    clearSpecifiedAiSelectTranslateResults(value)

    // 清除历史数据，防止混合展示
    clearSingleModelHistoryData(value)
  }
}

/**
 * 清除历史数据
 * 当开始新的翻译时，需要清除历史记录数据，防止数据混合显示
 */
const clearHistoryTransData = () => {
  if (historyModelOptions.value.length > 0) {
    historyModelOptions.value = []
  }
}

// 监听新的翻译请求，清除历史数据
watch(
  () => originalTextList.value,
  (newVal) => {
    if (newVal && newVal.paragraphs.length > 0) {
      clearHistoryTransData()
    }
  }
)

/**
 * 计算择优评分跟择优分析的显示
 */
const aiSelectShowContent = computed(() => {
  return (content) => {
    // 如果内容为空，直接返回
    if (!content || !content.length) return null

    // 处理数组情况
    if (Array.isArray(content)) {
      // 如果有高亮ID，只显示匹配的内容
      if (highlightId.value) {
        const matchedItem = content.find(item => item.statusNodeGlobalId === highlightId.value)
        if (matchedItem) {
          return typeof matchedItem.score === 'number' ? `${matchedItem.score}` : matchedItem.rationale
        }
        return null
      }

      // 如果没有高亮ID，显示第一个内容
      const firstItem = content[0]
      return typeof firstItem.score === 'number' ? `${firstItem.score}` : firstItem.rationale
    }

    // 处理单个值的情况
    return typeof content === 'number' ? `${content}` : content
  }
})

// 修改计算属性来过滤和排序显示的翻译结果
const getDisplayTranslations = computed(() => {
  return (author) => {
    // 如果没有翻译结果，返回空数组
    if (!author.translationsResult || author.translationsResult.length === 0) {
      return []
    }

    // 如果启用单句择优并且有高亮ID，只显示高亮ID对应的翻译结果
    if (currentTranslateMode.value === 'aiSelect' && highlightId.value) {
      // 检查当前高亮ID是否有错误
      const hasError = author.error && author.error.some(err => err.statusNodeGlobalId === highlightId.value)

      // 如果有错误，返回空数组，避免同时显示错误和翻译
      if (hasError) {
        return []
      }

      // 返回高亮ID对应的翻译结果
      return author.translationsResult.filter(item => item.statusNodeGlobalId === highlightId.value)
    }

    // 否则返回按paragraphId分组的翻译结果
    return author.translationsResult
  }
})

// 添加一个新的计算属性来按paragraphId分组翻译结果
const getGroupedTranslations = computed(() => {
  return (author) => {
    const translations = getDisplayTranslations.value(author)
    if (!translations.length) return []

    // 按paragraphId分组
    const groupedByParagraph: Record<string, any[]> = {}
    translations.forEach((item) => {
      if (!groupedByParagraph[item.paragraphId]) {
        groupedByParagraph[item.paragraphId] = []
      }
      groupedByParagraph[item.paragraphId].push(item)
    })

    return Object.entries(groupedByParagraph).map(([paragraphId, items]) => ({
      paragraphId,
      items
    }))
  }
})

/**
 * 对翻译引擎进行排序的计算属性
 * 处理以下逻辑:
 * 1. 判断是否使用历史记录数据作为数据源
 * 2. 在传统翻译模式下只显示传统翻译模型
 * 3. 在非择优翻译模式下直接返回原始数据
 * 4. 在择优翻译模式下:
 *    - 单句择优时使用当前高亮句子ID作为排序依据
 *    - 整句择优时使用第一个句子ID作为排序依据
 * 5. 根据句子ID对翻译引擎进行排序
 */
const sortedTranslationEngines = computed(() => {
  // 判断是否使用历史记录数据
  const isUsingHistoryData = historyModelOptions.value.length > 0
  // 数据源选择
  let dataSource = isUsingHistoryData ? historyModelOptions.value : translationEngineOptions.value

  // 如果是传统翻译模式，只显示传统翻译模型
  if (currentTranslateMode.value === 'traditional') {
    dataSource = dataSource.filter(engine => traditionalEngines.value.value === engine.value)
  }

  // 修改排序逻辑，根据当前高亮ID获取对应的sortIndex
  // console.log('多模型的排序结果', sortedDataSource);

  const sortedDataSource = dataSource.slice().sort((a, b) => {
    // 如果有高亮ID，则根据高亮ID对应的sortIndex排序
    if (highlightId.value && a.sortIndexArray && b.sortIndexArray) {
      // 查找对应高亮ID的sortIndex
      const aItem = a.sortIndexArray.find(item => item.statusNodeGlobalId === highlightId.value)
      const bItem = b.sortIndexArray.find(item => item.statusNodeGlobalId === highlightId.value)

      // 如果找到了对应的sortIndex，则使用它；否则使用第一个或默认值0
      const aSort = aItem ? aItem.sortIndex : a.sortIndexArray.length > 0 ? a.sortIndexArray[0].sortIndex : 0
      const bSort = bItem ? bItem.sortIndex : b.sortIndexArray.length > 0 ? b.sortIndexArray[0].sortIndex : 0

      // 如果sortIndex为0，则排在最后
      const finalASort = aSort === 0 ? Number.MAX_SAFE_INTEGER : aSort
      const finalBSort = bSort === 0 ? Number.MAX_SAFE_INTEGER : bSort

      return finalASort - finalBSort
    }
    else {
      // 没有高亮ID时，使用第一个sortIndex
      const aSort = a.sortIndexArray && a.sortIndexArray.length > 0 ? a.sortIndexArray[0].sortIndex : 0
      const bSort = b.sortIndexArray && b.sortIndexArray.length > 0 ? b.sortIndexArray[0].sortIndex : 0

      // 如果sortIndex为0，则排在最后
      const finalASort = aSort === 0 ? Number.MAX_SAFE_INTEGER : aSort
      const finalBSort = bSort === 0 ? Number.MAX_SAFE_INTEGER : bSort

      return finalASort - finalBSort
    }
  })

  // console.log('排序后的数据', sortedDataSource);

  return sortedDataSource
})

/**
 * 计算当前的错误信息
 */
const getDisplayError = computed(() => {
  return (author) => {
    // 如果没有错误信息，返回空数组
    if (!author.error || author.error.length === 0) {
      return []
    }

    // 如果启用单句择优并且有高亮ID，只显示高亮ID对应的翻译结果
    if (currentTranslateMode.value === 'aiSelect' && highlightId.value) {
      // 检查当前高亮ID是否有翻译结果
      const hasError = author.error && author.translationsResult.some(err => err.statusNodeGlobalId === highlightId.value)

      // 如果有翻译结果，返回空数组，避免同时显示错误和翻译
      if (hasError) {
        // console.log('有翻译结果，返回空数组', author.error);
        return []
      }
      // console.log('没有翻译结果，返回错误信息', author.error);

      // 返回高亮ID对应的翻译结果
      return author.error.filter(item => item.statusNodeGlobalId === highlightId.value)
    }

    // 否则显示所有翻译结果
    return author.error
  }
})

/**
 * 判断当前 id 的翻译结果是否存在错误
 */
const isError = computed(() => {
  return (errorArray) => {
    if (!errorArray || !errorArray.length) return false

    // 如果有高亮ID，检查对应ID是否有错误
    if (highlightId.value) {
      const hasError = errorArray.some(err => err.statusNodeGlobalId === highlightId.value)
      // console.log('hasError', hasError);
      return hasError
    }

    // 如果没有高亮ID，只要有错误就返回true
    return errorArray.length > 0
  }
})

/**
 * 监听用户切换择优翻译区域的模型
 * 1. 切换模型更新选中状态
 */
watch([() => selectedResultModel.value, () => highlightId.value], async ([newModel, newHighlightId]) => {
  if (!newModel || currentTranslateMode.value !== 'aiSelect' || !newHighlightId) return
  await nextTick() // 等待 DOM 更新
  const translateType = document.querySelector(`div[current-model="${newModel.value}"]`)
  if (translateType) {
    const selectedNode = translateType.querySelector(`span[selected-translate-result-id="${newHighlightId}"]`)
    if (selectedNode) {
      selectedModelsStatus.value[newHighlightId] = selectedNode.getAttribute('selected-translate-result-engine')
    }
  }
})

/**
 * 监听 highlightId 变化，自动滚动到对应的翻译卡片
 */
watch(() => highlightId.value, (newHighlightId) => {
  if (newHighlightId && currentTranslateMode.value === 'aiSelect') {
    // 延迟执行，确保 DOM 已更新
    nextTick(() => {
      // 添加额外延迟，确保DOM完全更新
      setTimeout(() => {
        scrollToHighlightedTranslationCard(newHighlightId)
      }, 100) // 100ms 延迟
    })
  }
})

/**
 * 滚动到高亮的翻译卡片
 * @param {string} highlightId - 高亮的句子ID
 */
const scrollToHighlightedTranslationCard = (highlightId) => {
  // 查找对应的翻译结果元素
  const targetElement = document.querySelector(`span[selected-translate-result-id="${highlightId}"]`)
  
  if (targetElement) {
    // 查找包含该元素的翻译卡片容器
    let translationCard = targetElement.closest('.trans-target_card')
    
    // 如果没找到，尝试查找父级容器
    if (!translationCard) {
      // 查找所有翻译卡片
      const allCards = document.querySelectorAll('.trans-target_card')
      
      // 查找包含该引擎的翻译卡片
      const engine = targetElement.getAttribute('selected-translate-result-engine')
      if (engine) {
        for (const card of allCards) {
          // 检查卡片内是否有该引擎的翻译结果
          const hasEngineResult = card.querySelector(`[selected-translate-result-engine="${engine}"]`)
          if (hasEngineResult) {
            translationCard = card
            break
          }
        }
      }
    }
    
    if (translationCard) {
      // 查找 SidebarResizer 的滚动容器
      let scrollContainer = null
      
      // 首先尝试查找 SidebarResizer 内部的滚动容器
      const sidebarResizer = document.querySelector('.custom-scrollbar')
      if (sidebarResizer) {
        scrollContainer = sidebarResizer.querySelector('.overflow-y-auto')
      }
      
      // 如果没找到，尝试查找父级的滚动容器
      if (!scrollContainer) {
        scrollContainer = translationCard.closest('.overflow-y-auto')
      }
      
      // 如果还没找到，尝试查找有滚动功能的容器
      if (!scrollContainer) {
        let parent = translationCard.parentElement
        while (parent && parent !== document.body) {
          if (parent.scrollHeight > parent.clientHeight) {
            scrollContainer = parent
            break
          }
          parent = parent.parentElement
        }
      }
      
    if (scrollContainer) {
      // 确保容器有滚动功能
      if (scrollContainer.scrollHeight <= scrollContainer.clientHeight) {
        return
      }
      
      // 计算翻译卡片相对于滚动容器的位置
      const containerRect = scrollContainer.getBoundingClientRect()
      const cardRect = translationCard.getBoundingClientRect()
      
      // 计算需要滚动的距离
      const scrollTop = scrollContainer.scrollTop
      const cardTop = cardRect.top - containerRect.top
      const containerHeight = containerRect.height
      const cardHeight = cardRect.height
      
        // 如果卡片不在可视区域内，滚动到合适位置
        if (cardTop < 0 || cardTop + cardHeight > containerHeight) {
          // 计算目标滚动位置，让卡片显示在容器顶部附近
          const targetScrollTop = scrollTop + cardTop - 10 // 留出10px的边距
          
          // 确保滚动位置在有效范围内
          const maxScrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight
          const finalScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop))
          
          scrollContainer.scrollTo({
            top: finalScrollTop,
            behavior: 'smooth'
          })
        }
      } else {
        // 如果没有找到滚动容器，使用默认的 scrollIntoView
        translationCard.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'nearest'
        })
      }
    }
  }
}

onMounted(() => {
  // 初始化DOM观察器
  initDomObserver(selectedModelsStatus.value, highlightId.value, editSelectedTransResult.value)
})

onUnmounted(() => {
  // 停止DOM观察器
  disconnectObserver()
})
</script>

<style scoped>
.text-main-container {
  /* 注意要使用  word-wrap: break-word + white-space: pre-wrap 否则翻译结果跟原文的格式不对称 */
  word-wrap: break-word; /* 自动换行 */
}

.trans-target_card {
  padding: 0.9375rem 1.125rem 0.9375rem;
  will-change: transform;
  transition: transform 0.2s ease;
  min-height: 2.875rem;
  overflow: visible;
  z-index: 1;
}

/* 选中状态 */
.transition-bg-color {
  transition: background-color 0.2s ease;
}

/* 选中状态 */
.selected-bg-color {
  background-color: #e1f0f5;
}

.dark .selected-bg-color {
  background-color: #10507a;
}

/* 列表动画- vue官方过渡动画- 没有高亮的 不要删除 */
.list-move,
.list-enter-active,
.list-leave-active {
  transition:
    transform 0.1s ease,
    opacity 0.1s ease;
  will-change: transform, opacity;
}

/* 列表动画- vue官方过渡动画- 没有高亮的 不要删除 */
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(0.625rem);
}

/* 列表动画- vue官方过渡动画- 没有高亮的 不要删除 */
.list-leave-active {
  position: absolute;
}

/* 折叠展开过渡效果 */
.collapse-transition-enter-active,
.collapse-transition-leave-active {
  transition: max-height 0.3s ease;
  max-height: 25rem;
  overflow: hidden;
}

.collapse-transition-enter-from,
.collapse-transition-leave-to {
  max-height: 0;
}
</style>
