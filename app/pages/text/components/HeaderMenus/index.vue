<!-- 导航 -->
<template>
  <TranslateTab v-model="activeTab" :tabs="tabs" @change="handleTabChange" />
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useInputTranslate } from '@/store/inputTranslate'
import TranslateTab from '@/pages/components/translateTab.vue'
import { useDomControl } from '@/pages/text/translate/domControl'

import { sharedState } from '@/pages/text/useData/sharedState'

const { resetAllTranslateData } = useDomControl
const { historyKey, currentTranslateStatus } = sharedState

const inputTranslateStore = useInputTranslate()
const { currentTranslateMode } = storeToRefs(inputTranslateStore)

const activeTab = computed({
  get: () => currentTranslateMode.value,
  set: value => inputTranslateStore.setCurrentTranslateMode(value)
})

const { t } = useI18n()

/**
 * 定义标签页（一定要使用computed计算属性，否则切换语言的时候不生效）
 */
const tabs = computed(() => [
  {
    // 传统翻译
    label: t('selected_trans.operation.traditional_ranslation_btn'),
    value: 'traditional'
  },
  {
    // 对比翻译
    label: t('selected_trans.operation.compare_translation_btn'),
    value: 'compare'
  },
  {
    // AI 择优翻译
    label: t('selected_trans.settings.ai_selected_translation'),
    value: 'aiSelect',
    // 基于对比翻译，AI 自动评分多个翻译结果，智能排序并选出最优译文。
    tooltip: t('selected_trans.settings.ai_selected_translation_desc')
  }
])

// 处理标签变化
const handleTabChange = (value) => {
  activeTab.value = value
  inputTranslateStore.setCurrentTranslateMode(value)
  // 防止切换tab 的时候 历史记录不丢失， 如果不清除的话会根据这个 历史记录id 进行修改这个 id 的历史记录
  historyKey.value = ''

  // 移除所有翻译数据- 重置所有的数据会初始化只保留输入框的内容
  resetAllTranslateData()
}
</script>
