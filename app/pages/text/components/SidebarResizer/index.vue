<template>
  <div class="relative flex h-full" :style="sidebarStyles">
    <!-- 拖拽条 -->
    <!-- <div class="absolute top-0 left-0 bottom-0 w-3 cursor-col-resize" @mousedown="handleMouseDown" @mouseenter="isHover = true" @mouseleave="isHover = false"></div> -->
    <div class="min-h-0 min-w-0 flex-1 overflow-y-auto pr-3 pl-0 max-sm:pr-0 md:pl-4">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useInputTranslate } from '@/store/inputTranslate'
import { useSidebarSize } from '@/store/inputTranslate/sidebarSize'

const useInputTransStore = useInputTranslate()
const { currentTranslateMode } = storeToRefs(useInputTransStore)

const sidebarSizeStore = useSidebarSize()
const { aiSelectModeWidth, normalModeWidth, aiSelectModeMaxWidth, normalModeMaxWidth } = storeToRefs(sidebarSizeStore)

const MIN_WIDTH = 200 // 设置最小宽度为200px
const isDragging = ref(false)

// 悬停在拖拽条上高亮
const isHover = ref(false)

// 检测是否为小屏幕
const isMobile = ref(false)

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth < 768 // md断点为768px
}

// 获取当前选中模式下的宽度
const getCurrentModeWidth = computed(() => {
  return currentTranslateMode.value === 'aiSelect' ? aiSelectModeWidth.value : normalModeWidth.value
})

// 获取当前选中模式下的最大宽度
const getCurrentModeMaxWidth = computed(() => {
  return currentTranslateMode.value === 'aiSelect' ? aiSelectModeMaxWidth.value : normalModeMaxWidth.value
})

const currentWidth = ref(Math.max(getCurrentModeWidth.value, MIN_WIDTH))

// 计算侧边栏样式
const sidebarStyles = computed(() => {
  // 小屏幕使用全宽，大屏幕使用动态宽度
  if (isMobile.value) {
    return {
      width: '100%',
      maxWidth: 'none',
      minWidth: 'auto',
      transition: 'none'
    }
  }

  return {
    width: `${currentWidth.value}px`,
    maxWidth: `${getCurrentModeMaxWidth.value}px`,
    minWidth: `${MIN_WIDTH}px`,
    transition: isDragging.value ? 'none' : 'width 0.2s ease'
  }
})

// 计算并更新最大可用宽度的函数
const updateAvailableWidth = () => {
  sidebarSizeStore.setDefaultWidth()
  sidebarSizeStore.setCurrentModeMaxWidth()
  // 更新当前宽度，并确保不小于最小宽度
  currentWidth.value = Math.max(getCurrentModeWidth.value, MIN_WIDTH)
}

// 处理大小改变的回调函数
const handleSizeChange = (newSize: number) => {
  // 确保不小于最小宽度
  const finalSize = Math.max(newSize, MIN_WIDTH)

  // 更新当前显示宽度
  currentWidth.value = finalSize

  // 根据当前模式更新对应的宽度
  if (currentTranslateMode.value === 'aiSelect') {
    sidebarSizeStore.aiSelectModeWidth = finalSize
  }
  else {
    sidebarSizeStore.normalModeWidth = finalSize
  }
}

// 手动处理拖拽
const handleMouseDown = (event: MouseEvent) => {
  // 小屏幕禁用拖拽
  if (isMobile.value) {
    return
  }

  // 防止默认行为和冒泡
  event.preventDefault()
  event.stopPropagation()

  isDragging.value = true

  // 获取初始位置
  const startX = event.clientX
  const startWidth = currentWidth.value

  // 使用requestAnimationFrame优化性能
  let animationFrameId: number | null = null
  let lastClientX = startX

  // 添加鼠标移动监听
  const handleMouseMove = (e: MouseEvent) => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }

    lastClientX = e.clientX

    animationFrameId = requestAnimationFrame(() => {
      // 计算宽度，注意方向和位置的计算
      const diff = startX - lastClientX
      const newWidth = startWidth + diff

      // 限制宽度在允许的范围内（最小宽度到最大宽度）
      const maxWidth = getCurrentModeMaxWidth.value
      currentWidth.value = Math.min(Math.max(newWidth, MIN_WIDTH), maxWidth)

      animationFrameId = null
    })
  }

  // 鼠标释放时的处理
  const handleMouseUp = () => {
    isDragging.value = false

    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }

    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''

    // 保存最终宽度
    handleSizeChange(currentWidth.value)
  }

  // 添加事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp, { once: true })
  document.body.style.cursor = 'ew-resize'
}

// 监听切换 tab 模式，更新最大可用宽度
watch(
  currentTranslateMode,
  () => {
    nextTick(() => {
      if (currentTranslateMode.value === 'aiSelect') {
        currentWidth.value = Math.max(aiSelectModeWidth.value, MIN_WIDTH)
      }
      else {
        currentWidth.value = Math.max(normalModeWidth.value, MIN_WIDTH)
      }
    })
  },
  { immediate: true }
)

onMounted(() => {
  // 初始化屏幕尺寸检测
  checkScreenSize()

  // 创建一个包含屏幕尺寸检测的resize处理函数
  const handleResize = useThrottleFn(() => {
    checkScreenSize()
    updateAvailableWidth()
  }, 16)

  window.addEventListener('resize', handleResize)

  // 设置默认宽度
  sidebarSizeStore.setDefaultWidth()
  // 设置最大宽度
  sidebarSizeStore.setCurrentModeMaxWidth()
})

// 暴露方法给父组件使用
defineExpose({
  handleMouseDown
})
</script>
