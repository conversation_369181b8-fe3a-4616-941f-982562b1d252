<template>
  <div class="mx-auto mt-4 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mt-6">
      <UCard>
        <template #header>
          <!-- 反馈列表 -->
          <div class="flex flex-row">
            <div class="w-full">
              <h3 class="text-left text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
                {{ t('feedback.list.title') }}
              </h3>
            </div>
            <div class="flex flex-auto justify-end" />
            <!-- 创建反馈 -->
            <div>
              <UButton
                icon="i-heroicons-plus-16-solid"
                color="primary"
                size="md"
                class="rounded-md whitespace-nowrap"
                @click="goToFeedback"
              >
                {{ t('feedback.list.actions.create_feedback') }}
              </UButton>
            </div>
          </div>
        </template>
        <!-- 你还没有反馈 -->
        <UTable
          :data="feedbackDataList"
          :columns="columns"
          node-key="id"
          :empty-state="{
            icon: 'i-heroicons-circle-stack-20-solid',
            label: t('feedback.list.empty')
          }"
          :ui="tableUi as any"
          :loading="loading"
        >
          <!-- 反馈内容列的自定义渲染 -->
          <template #feedback_content-cell="{ row }">
            <UPopover :ui="{ width: 'max-w-lg' }" mode="hover">
              <div class="max-w-[400px] cursor-pointer truncate">
                {{ row.original.feedback_content }}
              </div>
              <template #content>
                <div class="max-w-lg p-3 text-sm whitespace-normal text-neutral-900 dark:text-white">
                  {{ row.original.feedback_content_full }}
                </div>
              </template>
            </UPopover>
          </template>

          <template #action-cell="{ row }">
            <div class="flex flex-wrap items-center gap-2">
              <!-- 查看回复 -->
              <UButton
                size="xs"
                color="primary"
                variant="subtle"
                class="rounded-sm"
                :label="t('feedback.list.actions.view_reply')"
                :trailing="false"
                @click="goToReplyList(row)"
              />
              <!-- 图片缩略图 -->
              <UButton
                v-if="row.original && row.original.all_images && row.original.all_images.length > 0"
                size="xs"
                color="teal"
                variant="subtle"
                icon="i-heroicons-photo"
                class="rounded-sm text-sm"
                @click="openImageViewer(row, 0)"
              >
                ({{ row.original.all_images.length }}{{ t('feedback.list.actions.images') }})
              </UButton>
            </div>
          </template>
        </UTable>

        <!-- 修改分页控件 -->
        <div class="flex justify-end border-t border-gray-200 px-3 py-3.5 dark:border-gray-700">
          <UPagination
            v-model:page="currentPage"
            :items-per-page="pageCount"
            :total="totalCount"
            show-edges
            @update:page="changePage"
          />
        </div>
      </UCard>
    </div>

    <!-- 图片查看器 -->
    <UModal v-model:open="isImageViewerOpen" fullscreen>
      <template #content>
        <div class="relative flex h-full flex-col items-center justify-center p-4">
          <!-- 关闭按钮 -->
          <UButton
            icon="i-heroicons-x-mark"
            size="4xl"
            color="neutral"
            variant="ghost"
            class="absolute top-10 right-10 z-10 text-5xl"
            aria-label="Close"
            @click="isImageViewerOpen = false"
          />

          <div class="image-viewer flex h-full w-full items-center justify-center">
            <UColorModeImage :light="currentImageUrl" :dark="currentImageUrl" class="max-h-full max-w-full object-contain" />
          </div>
          <div class="mt-4 flex justify-center gap-2">
            <UButton
              icon="i-heroicons-arrow-left"
              size="sm"
              :disabled="currentImageIndex === 0"
              @click="changeImage(-1)"
            />
            <span class="text-sm">{{ currentImageIndex + 1 }} / {{ viewerImages.length }}</span>
            <UButton
              icon="i-heroicons-arrow-right"
              size="sm"
              :disabled="currentImageIndex >= viewerImages.length - 1"
              @click="changeImage(1)"
            />
          </div>
        </div>
      </template>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// 这里不需要引入 ref, reactive, computed, onMounted, h, resolveComponent 因为已经自动引入

import { getFeedbackListApi, getFeedbackImages } from '@/api/feedback'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import type { HeadersTokenType } from '@/api/login/types'
import { useRouter } from 'vue-router'
// 添加导入 MemberInfoCard 组件
import MemberInfoCard from './components/MemberInfoCard.vue'
// 添加导入获取产品信息的 API
import { getProductByPositionNoApi } from '@/api/product'
import type { GetProductType } from '@/api/types'

// definePageMeta 定义页面的布局和权限路由守卫
definePageMeta({
  layout: 'member',
  middleware: 'auth' // 使用 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
})

const { t, locale } = useI18n()

// 页面标题及描述，这个会员内部页面：不需要做SEO优化，所以不设置描述
useSeoMeta({
  titleTemplate: '',
  title: t('feedback.list.title') + ' - ' + t('common.site_name') // 我的问题反馈
})

defineOgImageComponent('Saas')

const router = useRouter()
const toast = useToast()
const UPopover = resolveComponent('UPopover')

// 添加产品信息对象
const product = ref({})

// 添加文本截断辅助函数
const truncateText = (text: string, maxLength: number): string => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const currentPage = ref<number>(1) // 当前页面
const pageCount = ref<number>(10) // 每页显示的条数
const totalCount = ref<number>(0) // 总记录数
const paramData = reactive<any>({})
const loading = ref(false)
const changePage = (page: number) => {
  currentPage.value = page
  handleQuery()
}

const statusMap = {
  0: t('feedback.list.status.pending'), // 待处理
  1: t('feedback.list.status.processing'), // 处理中
  2: t('feedback.list.status.completed') // 处理完成
}

const typeMap = {
  0: t('feedback.list.type.suggestion'), // 功能建议
  1: t('feedback.list.type.feedback') // 问题反馈
}

// 确保URL包含https://前缀
const ensureHttps = (url) => {
  if (!url) return ''
  if (url.startsWith('blob:') || url.startsWith('data:') || url.startsWith('https://') || url.startsWith('http://')) {
    return url
  }
  return `https://${url}`
}

// 图片查看器相关
const isImageViewerOpen = ref(false)
const viewerImages = ref<any[]>([])
const currentImageIndex = ref(0)
const currentImageUrl = computed(() => {
  if (viewerImages.value.length === 0) return ''
  return ensureHttps(viewerImages.value[currentImageIndex.value]?.image_url || '')
})

const openImageViewer = (row: any, index: number) => {
  viewerImages.value = row.original.all_images
  console.log('openImageViewer', viewerImages.value)
  currentImageIndex.value = index
  isImageViewerOpen.value = true
}

const changeImage = (direction: number) => {
  const newIndex = currentImageIndex.value + direction
  if (newIndex >= 0 && newIndex < viewerImages.value.length) {
    currentImageIndex.value = newIndex
  }
}

// 添加到现有变量区域
const loadingMore = ref(false)

const handleQuery = async (isLoadMore = false) => {
  loading.value = true

  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }

  paramData.page = currentPage.value
  paramData.limit = pageCount.value
  paramData.platform = 'ztsl'

  try {
    // 获取反馈列表
    const res = await getFeedbackListApi(headers, paramData)
    if (res.code === 200 && res.data) {
      // 处理反馈状态和反馈类型字段
      const processedData = res.data.map((item: any) => ({
        ...item,
        feedback_status: statusMap[item.feedback_status],
        feedback_type: typeMap[item.feedback_type],
        feedback_content_full: item.feedback_content, // 保存完整内容
        feedback_content: truncateText(item.feedback_content, 20), // 直接截断显示内容
        all_images: [] // 初始化图片数组
      }))

      // 获取所有问题ID
      const feedbackIds = processedData.map(item => item.id)

      if (feedbackIds.length > 0) {
        // 获取这些问题相关的图片
        const imagesRes = await getFeedbackImages(headers, {
          page: 1,
          limit: 100 // 获取足够多的图片以覆盖所有问题
        })

        if (imagesRes.code === 200 && imagesRes.data) {
          // 创建图片映射
          const imageMap = {}
          imagesRes.data.forEach((image) => {
            if (image.feedback_id) {
              if (!imageMap[image.feedback_id]) {
                imageMap[image.feedback_id] = []
              }
              imageMap[image.feedback_id].push(image)
            }
          })

          // 合并数据
          processedData.forEach((item) => {
            item.all_images = imageMap[item.id] || []
          })
        }
      }

      // 根据是否为加载更多模式来决定如何设置数据
      if (isLoadMore) {
        // 加载更多模式：合并数据
        feedbackDataList.value = [...feedbackDataList.value, ...processedData]
      }
      else {
        // 常规查询模式：替换数据
        feedbackDataList.value = processedData
      }

      totalCount.value = res.count || 0
    }
  }
  catch (error) {
    console.error('获取数据失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 跳转到反馈页面
const goToFeedback = () => {
  router.push({ path: `/${locale.value}/feedback` })
}

// 添加统一的获取行 ID 的辅助函数
const getRowId = (row: any): string | number | null => {
  if (!row) return null

  // 行对象可能是TanStack Table的行封装
  if (row.original && row.original.id !== undefined) {
    return row.original.id
  }

  // 也可能是直接的数据对象
  if (row.id !== undefined) {
    return row.id
  }

  console.warn(t('feedback.list.errors.no_id'))
  return null
}

// 修改后的跳转到回复列表函数
const goToReplyList = (row: any) => {
  const id = getRowId(row)
  if (!id) {
    console.warn(t('feedback.list.errors.no_id')) // 无法跳转，未找到有效的 ID
    return
  }

  // 获取 feedback_no，考虑行对象的嵌套结构
  let feedbackNo = null

  // 如果 row 是 TanStack Table 行对象
  if (row.original && row.original.feedback_no) {
    feedbackNo = row.original.feedback_no
  }
  // 如果 row 是直接的数据对象
  else if (row.feedback_no) {
    feedbackNo = row.feedback_no
  }

  if (!feedbackNo) {
    console.error('跳转失败: feedback_no 不存在', row)
    toast.add({
      title: t('feedback.list.errors.navigation_failed'), // 跳转失败
      description: t('feedback.list.errors.missing_feedback_no'), // 缺少必要的反馈编号信息
      color: 'red'
    })
    return
  }

  console.log('跳转时的 feedback_no:', feedbackNo)
  console.log('跳转时的 feedback_id:', id)

  router.push({
    path: `/${locale.value}/reply_list`,
    query: {
      feedback_no: feedbackNo,
      feedback_id: id.toString()
    }
  })
}

onMounted(async () => {
  loading.value = true

  // 获取产品信息，与 account.vue 和 billing.vue 保持一致
  const paramData: GetProductType = {
    platform: 'ztsl',
    position_no: 'ztsl-plus'
  }
  product.value = (await getProductByPositionNoApi(paramData)).data

  // 现有的查询逻辑
  handleQuery().then(() => {
    // 检查数据结构
    if (feedbackDataList.value.length > 0) {
      console.log('数据示例:', feedbackDataList.value[0])
      if (!feedbackDataList.value[0].feedback_no) {
        console.warn('警告: 数据缺少 feedback_no 字段')
      }
    }
  })
})

// 反馈列表
const feedbackDataList = ref<any[]>([])
const columns = ref([
  {
    accessorKey: 'feedback_no',
    header: t('feedback.list.columns.no') // 编号
  },
  {
    accessorKey: 'feedback_type',
    header: t('feedback.list.columns.type') // 类型
  },
  {
    accessorKey: 'create_datetime',
    header: t('feedback.list.columns.time') // 时间
  },
  {
    accessorKey: 'feedback_content',
    header: t('feedback.list.columns.content') // 内容
  },
  {
    accessorKey: 'feedback_status',
    header: t('feedback.list.columns.status') // 状态
  },
  {
    accessorKey: 'action',
    header: t('feedback.list.columns.action') // 操作
  }
])

const tableUi = {
  wrapper: 'relative overflow-x-auto',
  base: 'min-w-max lg:min-w-full table-fixed',
  divide: 'divide-y divide-gray-300 dark:divide-gray-700',
  thead: 'relative',
  tbody: 'divide-y divide-gray-200 dark:divide-gray-800',
  caption: 'sr-only',
  tr: {
    base: '',
    selected: 'bg-gray-50 dark:bg-(--ui-bg-inverted)',
    expanded: 'bg-gray-50 dark:bg-(--ui-bg-inverted)',
    active: 'hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer'
  },
  th: {
    base: 'text-left rtl:text-right',
    padding: 'px-4 py-3.5',
    color: 'text-neutral-900 dark:text-white',
    font: 'font-semibold',
    size: 'text-sm'
  },
  td: {
    base: 'whitespace-nowrap',
    padding: 'px-4 py-4',
    color: 'text-neutral-500 dark:text-neutral-400',
    font: '',
    size: 'text-sm'
  },
  checkbox: {
    padding: 'ps-4'
  },
  loadingState: {
    wrapper: 'flex flex-col items-center justify-center flex-1 px-6 py-14 sm:px-14',
    label: 'text-sm text-center text-neutral-900 dark:text-white',
    icon: 'w-6 h-6 mx-auto text-neutral-400 dark:text-neutral-500 mb-4 animate-spin'
  },
  emptyState: {
    wrapper: 'flex flex-col items-center justify-center flex-1 px-6 py-14 sm:px-14',
    label: 'text-sm text-center text-neutral-900 dark:text-white',
    icon: 'w-6 h-6 mx-auto text-neutral-400 dark:text-neutral-500 mb-4'
  },
  expand: {
    icon: 'transform transition-transform duration-200'
  },
  progress: {
    wrapper: 'absolute inset-x-0 -bottom-[0.5px] p-0'
  },
  default: {
    sortAscIcon: 'i-heroicons-bars-arrow-up-20-solid',
    sortDescIcon: 'i-heroicons-bars-arrow-down-20-solid',
    sortButton: {
      icon: 'i-heroicons-arrows-up-down-20-solid',
      trailing: true,
      square: true,
      color: 'gray',
      variant: 'ghost',
      class: '-m-1.5'
    },
    expandButton: {
      icon: 'i-heroicons-chevron-down',
      color: 'gray',
      variant: 'ghost',
      size: 'xs',
      class: '-my-1.5 align-middle'
    },
    checkbox: {
      color: 'primary'
    },
    progress: {
      color: 'primary',
      animation: 'carousel'
    },
    loadingState: {
      icon: 'i-heroicons-arrow-path-20-solid',
      label: 'Loading...'
    },
    emptyState: {
      icon: 'i-heroicons-circle-stack-20-solid',
      label: 'No items.'
    }
  }
}
</script>

<style scoped>
.image-viewer img {
  object-fit: contain;
}

/* 添加截断文本的CSS样式 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 添加缩略图样式 */
.thumbnail-container {
  transition: all 0.2s ease-in-out;
}

.thumbnail-container:hover {
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
</style>
