<template>
  <!--  <div
    id="content-text"
    ref="markdownContainer"
    :data-zing-translate-walked="id"
  /> -->
  <!-- 这个data-translation="true" 是给翻译插件用的，表示这个元素需要被翻译 -->
  <div
    :id="id"
    ref="markdownContainer"
    class="markdown_container dark:text-black max-w-[800px]"
    style="font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif"
    data-translation="true"
    v-html="htmlMM"
  />
</template>

<script setup>
const emit = defineEmits(['valueUpdated'])
const markdownContainer = ref(null)
markdownContainer.value = undefined
const props = defineProps({
  markdown: {
    type: String,
    required: true
  },
  id: {
    type: String,
    required: true
  }
})
const htmlMM = ref('')
// MMD渲染器
onMounted(() => {
  // 动态创建 script 标签加载 mathpix-markdown-it
  const script = document.createElement('script')
  script.src = 'https://cdn.jsdelivr.net/npm/mathpix-markdown-it@2.0.22/es5/bundle.js'
  document.head.appendChild(script)

  script.onload = async function () {
    // 确保 MathJax 已经加载
    const isLoaded = await window.loadMathJax()
    if (isLoaded) {
      console.log('Styles loaded!')
    }

    // 获取容器元素
    const el = markdownContainer.value
    if (el) {
      // 配置选项
      const options = {
        htmlTags: true
      }
      // console.log('-------------------', props.markdown)

      // 渲染 Markdown 内容
      const html = window.render(props.markdown, options)
      htmlMM.value = html

      el.innerHTML = html // 使用 innerHTML 而不是 outerHTML
      emit('valueUpdated', htmlMM.value)
    }
  }
})
</script>

<style scoped>
.markdown_container :deep(h1),
.markdown_container :deep(h2),
.markdown_container :deep(h3),
.markdown_container :deep(h4),
.markdown_container :deep(h5),
.markdown_container :deep(h6) {
  font-size: inherit;
  margin: 1em 0;
}

:deep(#setText > *) {
  overflow: auto;
}
</style>
