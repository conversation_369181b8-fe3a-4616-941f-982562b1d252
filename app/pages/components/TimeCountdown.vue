<template>
  <font>{{ hours }}:{{ minutes }}:{{ seconds }}</font>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// Define the props that the component expects
const props = defineProps({
  endTime: {
    type: String,
    required: true
  }
})

// <!-- 显示剩余的时间 -->

// Vue 组件在挂载时开始倒计时，并在卸载前清除定时器。
// onMounted 和 onUnmounted 钩子函数分别在组件挂载和卸载时运行。
// setInterval 函数每秒更新剩余的时间，并将结果保存在组件的响应式引用中。

// 请注意，你需要将 '2022-12-31T00:00:00' 替换为你的实际截止时间，
// 以及将 new Date() 替换为你的实际当前时间。

const currentTime = new Date() // 你的当前时间 new Date()
const endTime = new Date(props.endTime) // 你的截止时间
const hours = ref(0)
const minutes = ref(0)
const seconds = ref(0)
let intervalId = null

onMounted(() => {
  intervalId = setInterval(() => {
    const distance = new Date(endTime) - currentTime

    if (distance < 0) {
      clearInterval(intervalId)
      return
    }

    hours.value = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    minutes.value = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    seconds.value = Math.floor((distance % (1000 * 60)) / 1000)

    // Format the numbers to have at least two digits
    hours.value = hours.value < 10 ? '0' + hours.value : hours.value
    minutes.value = minutes.value < 10 ? '0' + minutes.value : minutes.value
    seconds.value = seconds.value < 10 ? '0' + seconds.value : seconds.value
  }, 1000)
})

onUnmounted(() => {
  clearInterval(intervalId)
})
</script>
