<template>
  <UModal v-model:open="isOpenDialogCard">
    <template #content>
      <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-base leading-6 font-semibold text-neutral-900 dark:text-white">
              <!-- 传入的弹窗标题 -->
              {{ props.title }}
            </h3>
            <UButton
              color="neutral"
              variant="subtle"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="closeDialogCard"
            />
          </div>
        </template>
        <!-- 传入的弹窗内容 -->
        <div v-if="props.message">
          <p class="mb-8 text-center font-semibold text-neutral-700">
            {{ props.message }}
          </p>
        </div>
        <div v-if="props.button" class="mt-5 flex justify-center">
          <!-- 传入的要跳转的URL -->
          <UButton
            color="neutral"
            variant="outline"
            size="lg"
            class="rounded-md font-bold"
            @click="closeDialogCard"
          >
            <!-- 取消 -->
            {{ t('common.cancel') }}
          </UButton>
          <UButton
            icon="i-heroicons-arrow-right-end-on-rectangle-20-solid"
            variant="solid"
            size="lg"
            :color="props.button.color"
            class="ml-4 rounded-md font-bold"
            @click="openUriAndCloseDialogCard"
          >
            <!-- 传入的按钮文字 -->
            {{ props.button.label }}
          </UButton>
        </div>
      </UCard>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import avatar from '@/assets/images/avatar.svg'
import { useAuthStoreWithOut } from '@/store/modules/auth'

const authStore = useAuthStoreWithOut()
const member = computed(() => authStore.getMember)

const { t, locale } = useI18n()

const router = useRouter()

/**
 *  父组件通过 props 向子组件传递数据
 */
const props = defineProps({
  openDialogCard: Boolean,
  title: String,
  message: String,
  button: Object
})

/**
 * 子组件通过 $emit 向父组件发送了一个事件并传递数据
 */
const emit = defineEmits(['close-dialog-page-event'])

/**
 * 是否打开对话框页面(接受父组件传递过来的数据进行判断)
 */
const isOpenDialogCard = ref(props.openDialogCard)

/**
 * 关闭购买产品页面, 并发送消息给父组件
 */
function closeDialogCard() {
  isOpenDialogCard.value = false
  // 向父组件发送消息，告诉父组件关闭购买产品页面
  emit('close-dialog-page-event', false)
}

/**
 * 跳转uri,并关闭对话框页面, 并发送消息给父组件
 */
function openUriAndCloseDialogCard() {
  // 跳转到父组件传入的URL
  router.push({ path: `${props.button?.toUri}`, query: { skipToUri: `${props.button?.skipToUri}` } })
  isOpenDialogCard.value = false
  // 向父组件发送消息，告诉父组件关闭购买产品页面
  emit('close-dialog-page-event', false)
}
</script>
