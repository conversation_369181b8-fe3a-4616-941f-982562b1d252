<template>
  <UModal v-model:open="isOpenFreeTrialCard" prevent-close>
    <template #content>
      <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="font-semibold text-neutral-900 dark:text-white">
              <!-- 免费试用 Plus 会员 -->
              {{ t('account.free_trial_plus') }}
            </h3>
            <UButton
              color="neutral"
              variant="subtle"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="closeFreeTrialCard"
            />
          </div>
        </template>
        <p class="mb-4 text-center font-semibold">
          <!--  您可免费申请试用 Plus 会员【 X 】天！ -->
          {{ t('account.apply_for_free_trial_plus_tip_1') }} 3 {{ t('account.apply_for_free_trial_plus_tip_2') }}
        </p>
        <p class="mb-8 text-center">
          <!-- 确认要申请吗？ -->
          {{ t('account.apply_for_free_trial_plus_tip_3') }}
        </p>
        <div class="mt-5 flex justify-center">
          <UButton
            color="neutral"
            variant="outline"
            size="md"
            class="rounded-md"
            @click="closeFreeTrialCard"
          >
            <!-- 取消 -->
            {{ t('common.cancel') }}
          </UButton>
          <UButton
            color="primary"
            variant="solid"
            size="md"
            class="ml-4 rounded-md"
            @click="handFreeTrial"
          >
            <!-- 确认 -->
            {{ t('common.confirm') }}
          </UButton>
        </div>
      </UCard>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { useAuthStoreWithOut } from '~/store/modules/auth'
import type { FreeTrialType } from '~/api/types'
import { freeTrial } from '~/api/product'

const { t, locale } = useI18n()

/**
 *  父组件通过 props 向子组件传递数据
 */
const props = defineProps({
  openFreeTrialCard: Boolean
})

/**
 * 子组件通过 $emit 向父组件发送了一个事件并传递数据
 */
const emit = defineEmits(['close-free-trial-page-event', 'refresh-page'])

/**
 * 是否打开购买产品页面(接受父组件传递过来的数据进行判断)
 */
const isOpenFreeTrialCard = ref(props.openFreeTrialCard)

/**
 * 关闭免费试用页面, 并发送消息给父组件
 */
function closeFreeTrialCard() {
  isOpenFreeTrialCard.value = false
  // 向父组件发送消息，告诉父组件关闭免费试用页面
  emit('close-free-trial-page-event', false)
}

async function handFreeTrial() {
  const authStore = useAuthStoreWithOut()
  const fromData: FreeTrialType = {
    platform: 'ztsl',
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }

  const result = await freeTrial(fromData)
  if (result.code === 200) {
    closeFreeTrialCard()
    emit('refresh-page')
  }
}
</script>
