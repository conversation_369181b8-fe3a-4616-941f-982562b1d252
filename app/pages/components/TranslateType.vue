<template>
  <div class="relative">
    <!-- 展开/收缩 -->
    <button v-if="props.type === 'text'" class="hover:text-primary-500 absolute right-17 -bottom-8 z-10 hidden items-center text-sm text-gray-500 md:flex" @click="toggleTabBar">
      <span class="text-sm">{{ isTabBarExpanded ? t('selected_trans.operation.collapse') : t('selected_trans.operation.expand') }}</span>
      <UIcon :name="isTabBarExpanded ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'" class="ml-1" />
    </button>
    <!-- 应用场景 :title="t('home.scenarios.title')" -->
    <!-- NuxtUI3 版本迁移 不要使用（PageSection）， 请使用 -->
    <!-- :class="'py-4 sm:py-12 md:py-8 relative'" :ui="{ container: 'mx-auto max-w-screen-2xl gap-4 sm:gap-y-8 flex flex-col' }" -->
    <UPageSection v-show="isTabBarExpanded || props.type !== 'text'" :ui="{ container: 'mx-auto max-w-7xl gap-4 flex flex-col py-0 sm:py-0 md:py-0 lg:py-0 xl:py-0' }">
      <UPageGrid class="relative grid grid-cols-4 gap-2 pb-6 sm:grid-cols-3 sm:gap-2 md:gap-4 lg:grid-cols-4 lg:gap-8 xl:gap-8">
        <UPageCard
          v-for="(card, index) in cards"
          :key="index"
          v-bind="card"
          variant="outline"
          orientation="horizontal"
          :highlight="props.type === card.code"
          highlight-color="primary"
          reverse
          spotlight
          spotlight-color="primary"
          class="[--spotlight-size:400px]"
          :ui="{
            container: 'flex gap-4 lg:grid-cols-1 p-3 sm:p-3 md:p-3 lg:p-4 xl:p-4',
            wrapper: 'flex-col items-center justify-center gap-0 md:flex-row md:items-start md:justify-start md:gap-1 lg:gap-4 xl:gap-4',
            body: '',
            leading: 'mb-0', // 这里是图标的样式
            description: 'mt-0 text-xs sm:text-xs md:text-sm lg:text-sm xl:text-sm' // 这里是描述的样式
          }"
        >
          <template #leading>
            <!-- 择优翻译 -->
            <span v-if="card.code === 'text'" class="p-1 size-6 rounded-lg dark:bg-gray-100 sm:size-6 md:flex md:size-12 lg:flex lg:size-12 xl:flex xl:size-12">
              <img src="../../assets/images/ztsl-ai-selected.svg" class="size-4 sm:size-4 md:flex md:size-10 lg:flex lg:size-10 xl:flex xl:size-10" />
            </span>
            <!-- 文档翻译 -->
            <span v-if="card.code === 'document'" class="p-0.5 size-6 rounded-lg dark:bg-gray-100 sm:size-6 md:flex md:size-12 lg:flex lg:size-12 xl:flex xl:size-12">
              <img src="../../assets/images/hugeicons--files-01.svg" class="size-5 sm:size-5 md:flex md:size-11 lg:flex lg:size-11 xl:flex xl:size-11" />
            </span>
            <!-- MinerU 文档翻译 -->
            <span v-if="card.code === 'miner_u'" class="size-6 rounded-lg dark:bg-gray-100 sm:size-6 md:flex md:size-12 lg:flex lg:size-12 xl:flex xl:size-12">
              <img src="../../assets/images/miner-u.svg" class="size-6 sm:size-6 md:flex md:size-12 lg:flex lg:size-12 xl:flex xl:size-12" />
            </span>
            <!-- PDF Plus -->
            <span v-if="card.code === 'pdf_plus'" class="p-1 size-6 rounded-lg dark:bg-gray-100 sm:size-6 md:flex md:size-12 lg:flex lg:size-12 xl:flex xl:size-12">
              <img src="../../assets/images/catppuccin--pdf.svg" class="size-4 sm:size-4 md:flex md:size-10 lg:flex lg:size-10 xl:flex xl:size-10" />
            </span>
          </template>
          <template #title>
            <span
              class="flex min-h-[40px] items-center justify-center text-center text-xs sm:text-sm md:flex md:min-h-0 md:items-start md:justify-start md:text-left md:text-base lg:text-base xl:text-base"
            >
              {{ card.title }}
            </span>
          </template>
          <template #description>
            <!-- 隐藏 小于 768px 的屏幕 -->
            <p class="hidden md:flex lg:flex xl:flex">
              {{ card.description }}
            </p>
          </template>
        </UPageCard>
      </UPageGrid>
    </UPageSection>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useTranslateTabShow } from '@/store/translateTabShow'

const { t, locale } = useI18n()
const useTranslateTabShowStore = useTranslateTabShow()
const { isTabBarExpanded } = storeToRefs(useTranslateTabShowStore)

const props = defineProps({
  type: String // 选中类型
})

// 控制整个tab栏的展开/收缩
const toggleTabBar = () => {
  useTranslateTabShowStore.toggleTabBar()
}

const cards = ref([
  {
    code: 'text',
    title: t('translate_type.text.title'), // 择优翻译
    description: t('translate_type.text.description'), // 传统翻译、对比翻译、AI 择优翻译
    to: '/' + locale.value + '/text'
  },
  {
    code: 'document',
    title: t('translate_type.file.title'), // 免费 PDF 翻译
    description: t('translate_type.file.description'), // 满足大部分 PDF 翻译阅读需求
    to: '/' + locale.value + '/file'
  },
  {
    code: 'miner_u',
    title: t('translate_type.miner_u.title'), // MinerU
    description: t('translate_type.miner_u.description'), // 支持.pdf、.doc、.docx、.ppt、.pptxW多种格式
    to: '/' + locale.value + '/file/miner-u'
  },
  {
    code: 'pdf_plus',
    title: t('translate_type.pdf_plus.title'), // PDF Plus
    description: t('translate_type.pdf_plus.description'), // 专业文档翻译，公式、图表高保真还原
    to: '/' + locale.value + '/pdf-plus'
  }
])
</script>
