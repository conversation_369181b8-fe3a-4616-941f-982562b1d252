/** * 翻译进度组件 * <AUTHOR> * @date 2025-05-22 */
<template>
  <div v-if="translationProgress && (translationProgress.overallProgress > 0 || translationProgress.hasError)">
    <UPopover
      :mode="isMobile ? 'click' : 'hover'"
      class="cursor-pointer align-bottom"
      arrow
      :ui="{
        content: 'p-4 bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700',
        arrow: 'fill-white dark:fill-neutral-800'
      }"
    >
      <div class="flex items-center">
        <!-- 翻译进度图标，根据状态显示不同颜色 -->
        <Icon v-if="translationProgress.hasError" name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-600 dark:text-red-400" />
        <!-- <Icon v-else-if="translationProgress.overallProgress >= 100" name="heroicons:check-circle" class="h-5 w-5 text-green-600 dark:text-green-400" /> -->
        <div v-else class="relative h-5 w-16">
          <UProgress
            v-model="translationProgress.overallProgress"
            class="h-5 w-full"
            :ui="{ base: 'h-5 rounded-xs', indicator: 'rounded-none' }"
            :color="translationProgress.overallProgress >= 100 ? 'success' : 'primary'"
          />
          <div class="absolute inset-0 flex items-center justify-center">
            <span class="text-xs font-medium text-white">{{ translationProgress.completedPages }}/{{ translationProgress.totalPages }}</span>
          </div>
        </div>
      </div>
      <!-- <div -->
      <!-- class="flex h-full cursor-pointer items-center rounded-md bg-gray-100 px-3 dark:bg-gray-800" -->
      <!-- :class="translationProgress.overallProgress < 100 ? 'hover:bg-blue-50 dark:hover:bg-blue-900/30' : 'hover:bg-green-50 dark:hover:bg-green-900/30'" -->
      <!-- > -->
      <!-- <span :class="translationProgress.overallProgress < 100 ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400'" class="px-1 text-sm font-medium"> -->
      <!-- 翻译进度 -->
      <!-- {{ $t('doc_trans_progress.trans_progress') }} {{ Math.round(translationProgress.overallProgress) }}% -->
      <!-- </span> -->
      <!-- </div> -->

      <template #content>
        <div class="w-64 py-1">
          <!-- 文档翻译进度 -->
          <h3 class="mb-2 text-sm font-medium text-gray-900 dark:text-white">
            {{ $t('doc_trans_progress.doc_trans_progress') }}
          </h3>
          <div
            :class="[
              'text-gray-800 dark:text-white',
              translationProgress.totalPages > 5
                ? 'max-h-48 overflow-y-auto pr-1 [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 [&::-webkit-scrollbar-track]:bg-gray-100 dark:[&::-webkit-scrollbar-track]:bg-neutral-700'
                : ''
            ]"
          >
            <div v-for="page in translationProgress.pagesStatus" :key="page.pageNumber" class="flex justify-between border-b border-gray-200 py-1 text-xs last:border-0 dark:border-neutral-700">
              <!-- 第 X 页 -->
              <span>{{ $t('doc_trans_progress.the') }} {{ page.pageNumber }} {{ $t('doc_trans_progress.page') }}</span>
              <div class="flex items-center">
                <!-- 已翻译 -->
                <div v-if="page.status === 'completed'" class="flex items-center text-green-600 dark:text-green-400">
                  <Icon name="heroicons:check-circle" class="mr-1 h-3 w-3" />
                  <span>{{ $t('doc_trans_progress.translated') }} ({{ page.progress }}%)</span>
                </div>
                <!-- 翻译中 -->
                <div v-else-if="page.status === 'in-progress'" class="flex items-center text-blue-600 dark:text-blue-400">
                  <Icon name="heroicons:arrow-path" class="mr-1 h-3 w-3 animate-spin" />
                  <span>{{ $t('doc_trans_progress.translation_in_progress') }} ({{ page.progress }}%)</span>
                </div>
                <!-- 无需翻译 -->
                <div v-else-if="page.status === 'no-translation-needed'" class="flex items-center text-gray-600 dark:text-gray-400">
                  <Icon name="heroicons:minus-circle" class="mr-1 h-3 w-3" />
                  <span>{{ $t('doc_trans_progress.no_translation_needed') }}</span>
                </div>
                <!-- 等待中 -->
                <div v-else class="flex items-center text-orange-600 dark:text-orange-400">
                  <Icon name="heroicons:clock" class="mr-1 h-3 w-3" />
                  <span>{{ $t('doc_trans_progress.waiting') }} ({{ page.progress }}%)</span>
                </div>
                <!-- X 处失败 -->
                <div v-if="page.failedNodes > 0" class="ml-1 flex items-center text-red-600 dark:text-red-400">
                  <Icon name="heroicons:x-circle" class="mr-1 h-3 w-3" />
                  <span>({{ page.failedNodes }}{{ $t('doc_trans_progress.failed_nodes') }} )</span>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-2 flex justify-between border-t border-gray-200 pt-1 text-xs text-gray-600 dark:border-neutral-700 dark:text-neutral-300">
            <!-- 已翻译 -->
            <span>{{ $t('doc_trans_progress.translated') }}: {{ Math.round(translationProgress.overallProgress) }}%</span>
          </div>
        </div>
      </template>
    </UPopover>
  </div>
  <div v-else-if="nodeTranslationProgress && nodeTranslationProgress.overallProgress > 0">
    <!-- 节点翻译进度 -->
    <div class="relative h-5 w-16">
      <UProgress
        v-model="nodeTranslationProgress.overallProgress"
        class="h-5 w-full"
        :ui="{ base: 'h-5 rounded-xs', indicator: 'rounded-none' }"
        :color="nodeTranslationProgress.overallProgress >= 100 ? 'success' : 'primary'"
      />
      <div class="absolute inset-0 flex items-center justify-center">
        <span class="text-xs font-medium text-white">{{ nodeTranslationProgress.overallProgress }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDocTranslationProgressStore } from '@/store/documentTranslate/translationProgress'

const { translationProgress, nodeTranslationProgress } = storeToRefs(useDocTranslationProgressStore())
const isMobile = isMobileDevice()
</script>

<style scoped>
/* 翻译状态样式 */
.translation-status {
  font-size: 0.875rem;
  padding: 0.25rem 0.625rem;
  border-radius: 0.25rem;
  background-color: rgba(240, 248, 255, 0.7);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.dark .translation-status {
  background-color: rgba(30, 58, 138, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}
</style>
