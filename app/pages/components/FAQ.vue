<template>
  <!-- FAQ 常见问题 -->
  <UPageSection :description="t('faq_common.description')">
    <template #title>
      <span class="inline-flex">
        <span class="text-sky-500"><UIcon name="i-wpf-faq" /></span>
        <span class="ml-4">{{ t('faq_common.title') }}</span>
      </span>
    </template>
    <UPageAccordion
      :items="fqa_items"
      multiple
      :ui="{
        wrapper: 'divide-y divide-gray-200 dark:divide-gray-800 -mt-6',
        item: {
          size: 'text-base text-neutral-600 dark:text-neutral-400',
          padding: 'py-6'
        },
        button: {
          base: 'text-left text-lg py-3',
          label: 'text-neutral-900 dark:text-white',
          trailingIcon: {
            name: 'i-heroicons-chevron-down-20-solid',
            base: 'w-5 h-5 ms-auto transform transition-transform duration-200 flex-shrink-0 mr-1.5',
            active: '',
            inactive: '-rotate-90'
          }
        }
      }"
    >
      <template #body="{ item }">
        <!-- 给内容显示标签加上 class="custom_faq_content"， 在用js 给 其它的 a 标签加上 target="_blank" 属性， 以此实现点击跳转新页面 -->
        <MDC
          v-if="item.content"
          :value="item.content"
          unwrap="p"
          class="custom_faq_content"
        />
      </template>
    </UPageAccordion>
  </UPageSection>
</template>

<script setup lang="ts">
const { t, locale } = useI18n()

/** FAQ 常见问题 */
// 注意不要使用静态数组， 使用 computed 动态获取
const fqa_items = computed(() => [
  {
    label: t('faq_common.items.item_10.label'),
    content: t('faq_common.items.item_10.content'),
    defaultOpen: 1
  },
  {
    label: t('faq_common.items.item_20.label'),
    content: t('faq_common.items.item_20.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_30.label'),
    content: t('faq_common.items.item_30.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_35.label'),
    content: t('faq_common.items.item_35.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_40.label'),
    content: t('faq_common.items.item_40.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_45.label'),
    content: t('faq_common.items.item_45.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_50.label'),
    content: t('faq_common.items.item_50.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_60.label'),
    content: t('faq_common.items.item_60.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_70.label'),
    content: t('faq_common.items.item_70.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_75.label'),
    content: t('faq_common.items.item_75.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_80.label'),
    content: t('faq_common.items.item_80.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_90.label'),
    content: t('faq_common.items.item_90.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_100.label'),
    content: t('faq_common.items.item_100.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_110.label'),
    content: t('faq_common.items.item_110.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_120.label'),
    content: t('faq_common.items.item_120.content'),
    defaultOpen: 0
  }
])

onMounted(async () => {
  // 获取所有的a标签，设置target为_blank，解决Markdown中链接在新窗口打开的问题
  const faq_content_divs = document.querySelectorAll('[class="custom_faq_content"]')
  faq_content_divs.forEach(function (div) {
    div.querySelectorAll('a').forEach(function (link) {
      link.setAttribute('target', '_blank')
      link.setAttribute('rel', 'noopener noreferrer')
    })
  })

  // 信用卡组织图片样式
  const pay_card_images = document.querySelectorAll('[alt="pay_card"]')
  pay_card_images.forEach(function (pay_card_img) {
    pay_card_img.style.display = 'inline-block'
    pay_card_img.style.height = '24px'
  })
})
</script>
