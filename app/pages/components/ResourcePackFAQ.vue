<template>
  <!-- 资源包购买页面：FAQ 常见问题 -->
  <UContainer>
    <!-- 常见问题 -->
    <h1 class="mt-16 mb-2 text-3xl font-bold">
      {{ t('faq_common.title') }}
    </h1>
    <UPageAccordion
      :items="fqa_items"
      multiple
      :ui="{
        wrapper: 'divide-y divide-gray-200 dark:divide-gray-800 -mt-6',
        item: {
          size: 'text-base text-neutral-600 dark:text-neutral-400',
          padding: 'py-6'
        },
        button: {
          base: 'text-left text-lg py-3',
          label: 'text-neutral-900 dark:text-white',
          trailingIcon: {
            name: 'i-heroicons-chevron-down-20-solid',
            base: 'w-5 h-5 ms-auto transform transition-transform duration-200 flex-shrink-0 mr-1.5',
            active: '',
            inactive: '-rotate-90'
          }
        }
      }"
    >
      <template #body="{ item }">
        <!-- 给内容显示标签加上 class="custom_faq_content"， 在用js 给 其它的 a 标签加上 target="_blank" 属性， 以此实现点击跳转新页面 -->
        <MDC
          v-if="item.content"
          :value="item.content"
          unwrap="p"
          class="custom_faq_content"
        />
      </template>
    </UPageAccordion>
  </UContainer>
</template>

<script setup lang="ts">
const { t, locale } = useI18n()

/** 资源包购买：FQA 常见问题 */
const fqa_items = [
  {
    label: t('faq_common.items.item_45.label'), // 翻译额度（300万 TQ）约等于模型的多少 Token ？
    content: t('faq_common.items.item_45.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_common.items.item_50.label'), // 翻译额度（TQ）与各模型翻译使用的 Token 换算规则？
    content: t('faq_common.items.item_50.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_resource_pack.items.item_210.label'),
    content: t('faq_resource_pack.items.item_210.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_resource_pack.items.item_220.label'),
    content: t('faq_resource_pack.items.item_220.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_resource_pack.items.item_230.label'),
    content: t('faq_resource_pack.items.item_230.content'),
    defaultOpen: 0
  },
  {
    label: t('faq_resource_pack.items.item_240.label'),
    content: t('faq_resource_pack.items.item_240.content'),
    defaultOpen: 0
  }
]

onMounted(async () => {
  // 获取所有的a标签，设置target为_blank，解决Markdown中链接在新窗口打开的问题
  const faq_content_divs = document.querySelectorAll('[class="custom_faq_content"]')
  faq_content_divs.forEach(function (div) {
    div.querySelectorAll('a').forEach(function (link) {
      link.setAttribute('target', '_blank')
      link.setAttribute('rel', 'noopener noreferrer')
    })
  })

  // 信用卡组织图片样式
  const pay_card_images = document.querySelectorAll('[alt="pay_card"]')
  pay_card_images.forEach(function (pay_card_img) {
    pay_card_img.style.display = 'inline-block'
    pay_card_img.style.height = '24px'
  })
})
</script>
