<!-- 产品价格套餐 -->
<template>
  <div>
    <!-- title: 精挑翻译 Plus 会员 -->
    <!-- description: 升级为 Plus 会员，智享 OpenAI、Gemini、DeepSeek、智谱GLM、通义千问等多种高级 AI 智能翻译服务 -->
    <UPageHero
      v-if="props.product"
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-3xl sm:text-3xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #title>
        <!-- <PlusActiveIcon class="inline-flex mr-1 size-8 sm:size-8 md:size-9 lg:size-10 xl:size-14 align-text-top" /> -->
        <span class="">
          <!-- 套餐 -->
          {{ t('pricing.hero.title') }}
        </span>
      </template>
      <template #description>
        <!-- <span class="font-bold tracking-normal leading-6 lg:leading-8 text-neutral-600 dark:text-neutral-200">{{ t('pricing.hero.description1') }}</span> -->
        <br />
        <div class="align-item mt-5 flex flex-wrap justify-center gap-2 text-base leading-6 font-bold tracking-tight text-neutral-600 lg:leading-8 dark:text-neutral-300">
          <span class="leading-6 font-bold tracking-tight lg:leading-8 dark:text-neutral-300">{{ t('pricing.hero.description2') }}</span>
          <span class="inline-flex">
            <span class="flex size-6 items-center justify-center rounded-xs leading-6 lg:my-0 lg:size-8 lg:leading-8 dark:bg-gray-100">
              <img src="../../assets/icon/transModel/deepseek.png" class="size-5 flex-shrink-0 leading-5 lg:my-0 lg:size-7 lg:leading-7" />
            </span>
            <span class="ml-1 leading-6 font-semibold lg:leading-8">{{ t('common.engins.deepseek') }}</span>
          </span>
          <span class="inline-flex">
            <span class="flex size-6 items-center justify-center rounded-xs leading-6 lg:my-0 lg:size-8 lg:leading-8 dark:bg-gray-100">
              <img src="../../assets/icon/transModel/openai.svg" class="size-5 flex-shrink-0 leading-5 lg:my-0 lg:size-7 lg:leading-7" />
            </span>
            <span class="ml-1 leading-6 font-semibold lg:leading-8">{{ t('common.engins.openai') }}</span>
          </span>
          <span class="inline-flex">
            <span class="flex size-6 items-center justify-center rounded-xs leading-6 lg:my-0 lg:size-8 lg:leading-8 dark:bg-gray-100">
              <img src="../../assets/icon/transModel/gemini.svg" class="size-5 flex-shrink-0 leading-5 lg:my-0 lg:size-7 lg:leading-7" />
            </span>
            <span class="ml-1 leading-6 font-semibold lg:leading-8">{{ t('common.engins.gemini') }}</span>
          </span>
          <span class="inline-flex">
            <span class="flex size-6 items-center justify-center rounded-xs leading-6 lg:my-0 lg:size-8 lg:leading-8 dark:bg-gray-100">
              <img src="../../assets/icon/transModel/doubao.svg" class="size-5 flex-shrink-0 leading-5 lg:my-0 lg:size-7 lg:leading-7" />
            </span>
            <span class="ml-1 leading-6 font-semibold lg:leading-8">{{ t('common.engins.doubao') }}</span>
          </span>
          <span class="inline-flex">
            <span class="flex size-6 items-center justify-center rounded-xs leading-6 lg:my-0 lg:size-8 lg:leading-8 dark:bg-gray-100">
              <img src="../../assets/icon/transModel/zhipu.png" class="h-5 w-5 flex-shrink-0 leading-5 lg:my-0 lg:h-7 lg:w-7 lg:leading-7" />
            </span>
            <span class="ml-1 leading-6 font-semibold lg:leading-8">{{ t('common.engins.zhipu') }}</span>
          </span>
          <span class="leading-6 font-bold tracking-tight lg:leading-8 dark:text-neutral-300">{{ t('pricing.hero.description3') }}</span>
        </div>
      </template>
    </UPageHero>

    <UPricingPlans scale :class="pricing_grid_class">
      <!-- 版本迁移请使用（PricingPlan） https://ui.nuxt.com/components/pricing-plan -->
      <div
        v-for="item in product_list"
        :key="item.code"
        v-bind="item"
        :class="{ 'card-highlight': highlightCode === item.code }"
        class="pricing-card-item shrink-0 basis-1/3"
        @click="selectSpecification(item.code)"
      >
        <!-- 注意不要在PricingPlan 里面使用 click 点击事件因为不会生效 -->
        <UPricingPlan
          :description="item.description"
          orientation="vertical"
          class="h-full"
          :features="item.features"
          :highlight="highlightCode === item.code"
        >
          <template #title>
            <span class="font-boldtext-xl truncate text-xl font-semibold text-neutral-900 sm:text-xl md:text-2xl lg:text-2xl dark:text-white">
              <span v-if="item.code !== 'free'" class="plus_text text-2xl sm:text-2xl md:text-3xl lg:text-3xl">{{ item.product_name }}</span>
              <span v-else>{{ item.product_name }}</span>
              <span class="ml-1">{{ item.cycle_name }}</span>
            </span>
          </template>
          <template #price>
            <span class="text-2xl font-semibold text-neutral-900 sm:text-4xl dark:text-white">{{ item.price }}</span>
            <span class="truncate text-base/6 font-medium text-neutral-500 dark:text-neutral-400">{{ item.cycle_unit }}</span>
          </template>
          <template #features>
            <div>
              <div class="mb-4 flex w-full py-4">
                <!--
                  * member_product_status
                  * 会员产品 Plus会员 状态(API返回的业务状态，非数据库字段状态)：
                  * 0：未试用（免费版），
                  * 1：Plus会员试用中（试用版），
                  * 2：Plus会员试用已过期（免费版），
                  * 3：Plus会员套餐有效期内，
                  * 4：Plus会员套餐已过期（免费版）
                  * --------------------------------
                  * subscription_status
                  * 订阅状态(1:未订阅、2:订阅中、3:到期取消)
                  * --------------------------------
                  * subscription_buy_mode
                  * 订阅购买模式（weekly:按周,monthly:按月,quarterly:按季,yearly:按年）
                -->
                <div v-if="item.code === 'free'" class="w-full">
                  <!-- 免费套餐，且用户未登录：显示：登录 / 注册 -->
                  <UButton
                    v-if="item.code === 'free' && !memberHasLogin"
                    :label="t('pricing.plans.free.button.login_or_signup')"
                    size="xl"
                    color="primary"
                    variant="outline"
                    class="w-full justify-center rounded-md"
                    @click="skipToLogin"
                  />
                  <!-- 免费套餐，且用户已登录：显示：当前套餐 -->
                  <UButton
                    v-else-if="item.code === 'free' && memberHasLogin && memberProduct.member_product_status == 0"
                    :label="t('pricing.plans.current_plan')"
                    size="xl"
                    color="neutral"
                    variant="outline"
                    class="w-full justify-center rounded-md text-neutral-600"
                  />
                </div>
                <div v-if="item.code === 'weekly'" class="w-full">
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 非订阅中，说明是购买模式有效期内，显示：续费 / 订阅 -->
                  <UButton
                    v-if="memberHasLogin && memberProduct.member_product_status == 3 && memberProduct.subscription_status != 2"
                    :label="t('pricing.plans.plus.button.renewal') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="subtle"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 按周订阅，说明是按周订阅有效期内，显示：当前套餐 -->
                  <UButton
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode === 'weekly'
                    "
                    :label="t('pricing.plans.current_plan')"
                    size="xl"
                    color="neutral"
                    variant="outline"
                    class="w-full justify-center rounded-md text-neutral-600"
                    @click="router.push({ path: '/' + locale + '/account' })"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 但不是按周订阅：不显示按钮 -->
                  <div
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode !== 'weekly'
                    "
                  />
                  <!-- 其它情况，显示：购买 / 订阅 -->
                  <UButton
                    v-else
                    :label="t('pricing.plans.plus.button.purchase') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="subtle"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                </div>
                <div v-if="item.code === 'monthly'" class="w-full">
                  <!-- 用户未登录或（用户已登录、还没有试用（member_product_status == 0））：显示：免费试用3天 -->
                  <UButton
                    v-if="!memberHasLogin || (memberHasLogin && memberProduct.member_product_status == 0)"
                    :label="t('pricing.plans.plus.button.free_trial')"
                    size="xl"
                    color="primary"
                    variant="solid"
                    class="w-full justify-center rounded-md"
                    @click="freeTrialButton"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 非订阅中，说明是购买模式有效期内，显示：续费 / 订阅 -->
                  <UButton
                    v-else-if="memberHasLogin && memberProduct.member_product_status == 3 && memberProduct.subscription_status != 2"
                    :label="t('pricing.plans.plus.button.renewal') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="solid"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 按月订阅，说明是按月订阅有效期内，显示：当前套餐 -->
                  <UButton
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode === 'monthly'
                    "
                    :label="t('pricing.plans.current_plan')"
                    size="xl"
                    color="neutral"
                    variant="outline"
                    class="w-full justify-center rounded-md text-neutral-600"
                    @click="router.push({ path: '/' + locale + '/account' })"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 但不是按月订阅：不显示按钮 -->
                  <div
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode !== 'monthly'
                    "
                  />
                  <!-- 其它情况，显示：购买 / 订阅 -->
                  <UButton
                    v-else
                    :label="t('pricing.plans.plus.button.purchase') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="solid"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                </div>
                <div v-if="item.code === 'quarterly'" class="w-full">
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 非订阅中，说明是购买模式有效期内，显示：续费 / 订阅 -->
                  <UButton
                    v-if="memberHasLogin && memberProduct.member_product_status == 3 && memberProduct.subscription_status != 2"
                    :label="t('pricing.plans.plus.button.renewal') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="subtle"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 按季度订阅，说明是按季度订阅有效期内，显示：当前套餐 -->
                  <UButton
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode === 'quarterly'
                    "
                    :label="t('pricing.plans.current_plan')"
                    size="xl"
                    color="neutral"
                    variant="outline"
                    class="w-full justify-center rounded-md text-neutral-600"
                    @click="router.push({ path: '/' + locale + '/account' })"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 但不是按季度订阅：不显示按钮 -->
                  <div
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode !== 'quarterly'
                    "
                  />
                  <!-- 其它情况，显示：购买 / 订阅 -->
                  <UButton
                    v-else
                    :label="t('pricing.plans.plus.button.purchase') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="subtle"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                </div>
                <div v-if="item.code === 'yearly'" class="w-full">
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 非订阅中，说明是购买模式有效期内，显示：续费 / 订阅 -->
                  <UButton
                    v-if="memberHasLogin && memberProduct.member_product_status == 3 && memberProduct.subscription_status != 2"
                    :label="t('pricing.plans.plus.button.renewal') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="subtle"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 按年订阅，说明是按年订阅有效期内，显示：当前套餐 -->
                  <UButton
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode === 'yearly'
                    "
                    :label="t('pricing.plans.current_plan')"
                    size="xl"
                    color="neutral"
                    variant="outline"
                    class="w-full justify-center rounded-md text-neutral-600"
                    @click="router.push({ path: '/' + locale + '/account' })"
                  />
                  <!-- 会员已登录 + Plus会员套餐有效期内 + 订阅中 + 但不是按年度订阅：不显示按钮 -->
                  <div
                    v-else-if="
                      memberHasLogin
                        && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)
                        && memberProduct.subscription_status == 2
                        && memberProduct.subscription_buy_mode !== 'yearly'
                    "
                  />
                  <!-- 其它情况，显示：购买 / 订阅 -->
                  <UButton
                    v-else
                    :label="t('pricing.plans.plus.button.purchase') + ' / ' + t('pricing.plans.plus.button.subscribe')"
                    size="xl"
                    color="primary"
                    variant="subtle"
                    class="w-full justify-center rounded-md"
                    @click="buyPlusButton(item.code)"
                  />
                </div>
              </div>
              <ul class="space-y-3">
                <!-- 免费版功能 / Plus 尊享顶级 AI 翻译模型 -->
                <USeparator class="py-1" />
                <li v-if="item.options.models_title" class="flex min-w-0 items-center gap-2 py-1">
                  <span class="text-base truncate font-semibold text-neutral-700 dark:text-neutral-200">{{ item.options.models_title }}</span>
                  <UPopover
                    :mode="isMobileDevice() ? 'click' : 'hover'"
                    arrow
                    :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
                  >
                    <UIcon v-if="item.options.models_description" name="heroicons:information-circle" class="size-5 shrink-0 text-neutral-400" />
                    <template #content>
                      <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                        {{ item.options.models_description }}
                      </div>
                    </template>
                  </UPopover>
                </li>
                <li v-for="(model, index) in item.options.models" :key="index" class="flex min-w-0 items-center gap-2 py-1">
                  <!-- <UIcon name="heroicons:check-16-solid" class="size-5 shrink-0 text-(--ui-primary)" /> -->
                  <!-- <UAvatar :src="model.icon_path" size="sm" class="p-1" /> -->
                  <img
                    :src="translateModeIcon[model.code]"
                    width="28"
                    height="28"
                    class="p-1 rounded-full bg-gray-100 dark:bg-gray-50"
                  />
                  <!-- 判断是否要做链接跳转 -->
                  <span v-if="model.uri" class="truncate text-sm text-neutral-700 underline underline-offset-3 dark:text-neutral-300">
                    <a :href="model.uri" target="_blank">{{ model.title }}</a>
                  </span>
                  <span v-else class="truncate text-sm text-neutral-700 dark:text-neutral-300">{{ model.title }}</span>
                  <span v-if="model.sub_title" class="truncate text-xs text-gray-400 dark:text-gray-400">/&nbsp;{{ model.sub_title }}</span>
                </li>
                <!-- 套餐功能 -->
                <!-- 是否包含免费版功能 -->
                <USeparator v-if="item.options.title" class="py-1" />
                <li v-if="item.options.title" class="flex min-w-0 items-center gap-2 py-1">
                  <UIcon name="i-bi:check-circle" class="size-5 shrink-0 text-(--ui-primary)" />
                  <span class="text-base truncate text-neutral-700 dark:text-neutral-200">{{ item.options.title }}</span>
                </li>
                <USeparator v-if="item.options.features_title" class="py-1" />
                <li v-if="item.options.features_title" class="flex min-w-0 items-center gap-2 py-1">
                  <span class="text-base truncate font-semibold text-neutral-700 dark:text-neutral-200">{{ item.options.features_title }}</span>
                </li>
                <li v-for="(feature, index) in item.options.features" :key="index" class="flex min-w-0 items-center gap-2 py-1">
                  <UIcon name="heroicons:check-16-solid" class="size-5 shrink-0 text-(--ui-primary)" />
                  <!-- 判断是否要做链接跳转 -->
                  <span v-if="feature.uri" class="truncate text-sm text-neutral-700 underline underline-offset-3 dark:text-neutral-300">
                    <a :href="feature.uri" target="_blank">{{ feature.title }}</a>
                  </span>
                  <span v-else class="truncate text-sm text-neutral-700 dark:text-neutral-300">{{ feature.title }}</span>
                  <span v-if="feature.sub_title" class="truncate text-xs text-gray-400 dark:text-gray-400">/&nbsp;{{ feature.sub_title }}</span>
                  <UPopover
                    :mode="isMobileDevice() ? 'click' : 'hover'"
                    arrow
                    :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
                  >
                    <UIcon v-if="feature.tips" name="heroicons:information-circle" class="size-5 shrink-0 text-neutral-400" />
                    <template #content>
                      <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                        {{ feature.tips }}
                      </div>
                    </template>
                  </UPopover>
                </li>
              </ul>
            </div>
          </template>
          <template #button>
            <!-- 不用默认的 Button 显示，放到 features 前自定义显示 -->
          </template>
        </UPricingPlan>
      </div>
    </UPricingPlans>

    <!-- 未登录弹窗提示 -->
    <!-- <div v-if="isOpenDialogPage">
      <DialogCard
        :title="t('pricing.dialog.title')"
        :message="t('pricing.dialog.message')"
        :button="{ label: t('pricing.dialog.button'), color: 'primary',
        toUri: '/' + locale + '/login',
        skipToUri: skipToUri }"
        :open-dialog-card="openDialogCard"
        @close-dialog-page-event="closeDialogPage"
      />
    </div> -->

    <div v-if="isOpenFreeTrialPage">
      <!-- 免费试用弹窗页面 -->
      <FreeTrialCard :open-free-trial-card="openFreeTrialCard" @close-free-trial-page-event="closeFreeTrialPage" @refresh-page="handleRefreshPage" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
// import PlusActiveIcon from '~/components/icons/PlusActiveIcon.vue';
// import DialogCard from './DialogCard.vue';
import { useAuthStoreWithOut } from '@/store/modules/auth'
import FreeTrialCard from '~/pages/components/FreeTrialCard.vue'
import { translateModeIcon } from '@/utils/translateModeIcon' // 翻译引擎的图标icon

const authStore = useAuthStoreWithOut()
const { member, memberProduct } = storeToRefs(authStore)

const { t, locale } = useI18n()
const router = useRouter()

/**
 *  父组件通过 props 向子组件传递数据
 */
const props = defineProps({
  skipToUri: String,
  product: Object // 平台在售产品信息
})

/**
 * 是否登录
 */
const memberHasLogin = ref(false)

// /**
//  * 是否打开未登录提示对话框页面
//  */
// const isOpenDialogPage = ref(false);

// /**
//  * 父组件传递给子组件的消息
//  */
// const openDialogCard = ref(isOpenDialogPage);

// /**
//  * 接收子组件传递的事件，关闭对话框页面
//  * @param data
//  */
// const closeDialogPage = (data: boolean) => {
//   isOpenDialogPage.value = data;
// };

/**
 * 是否打开申请试用免费产品确认页面
 */
const isOpenFreeTrialPage = ref(false)

/**
 * 父组件传递给子组件的消息
 */
const openFreeTrialCard = ref(isOpenFreeTrialPage)

/**
 * 接收子组件传递的事件，关闭免费试用页面
 * @param data
 */
const closeFreeTrialPage = (data: boolean) => {
  isOpenFreeTrialPage.value = data
}

/**
 * 免费试用 Plus 会员 按钮
 */
function freeTrialButton() {
  // 判断会员是否已登录
  if (memberHasLogin.value && isOpenFreeTrialPage.value === false) {
    isOpenFreeTrialPage.value = true
  }
  else {
    // // 打开未登录提示框
    // isOpenDialogPage.value = true;
    skipToLogin() // 跳转到登录页面
  }
}

function handleRefreshPage() {
  // 刷新页面的逻辑
  window.location.reload()
}

const plus_description = t('pricing.plans.plus.description') // 智享 AI 翻译服务新体验

// 免费版权益
const free_options = {
  title: '', // 空
  description: '', // 空
  models_title: t('pricing.plans.free.options.models_title'), // 免费版的功能
  models: [
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/microsoft.svg',
      code: 'microsoft', // 微软翻译
      title: t('pricing.plans.free.options.models.model0.title'), // 微软翻译
      sub_title: t('pricing.plans.free.options.models.model0.sub_title'),
      tips: t('pricing.plans.free.options.models.model0.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/google.png',
      code: 'google', // 谷歌翻译
      title: t('pricing.plans.free.options.models.model1.title'), // 谷歌翻译
      sub_title: t('pricing.plans.free.options.models.model1.sub_title'),
      tips: t('pricing.plans.free.options.models.model1.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/zhipu.png',
      code: 'zhipu', // 智谱 GLM 翻译
      title: t('pricing.plans.free.options.models.model2.title'),
      sub_title: t('pricing.plans.free.options.models.model2.sub_title'),
      tips: t('pricing.plans.free.options.models.model2.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/transmart.png',
      code: 'transmart', // 腾讯翻译
      title: t('pricing.plans.free.options.models.model3.title'),
      sub_title: t('pricing.plans.free.options.models.model3.sub_title'),
      tips: t('pricing.plans.free.options.models.model3.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/yandex.png',
      code: 'yandex', // Yandex 翻译
      title: t('pricing.plans.free.options.models.model4.title'),
      sub_title: t('pricing.plans.free.options.models.model4.sub_title'),
      tips: t('pricing.plans.free.options.models.model4.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/other-models.svg',
      code: 'other_models', // 其他翻译模型
      title: t('pricing.plans.free.options.models.model5.title'),
      sub_title: t('pricing.plans.free.options.models.model5.sub_title'),
      tips: t('pricing.plans.free.options.models.model5.tips')
    }
  ],
  features_title: t('pricing.plans.free.options.features_title'), // 免费版的功能
  features: [
    {
      title: t('pricing.plans.free.options.features.feature0.title'), // 网页翻译
      sub_title: t('pricing.plans.free.options.features.feature0.sub_title'), // 无限制
      tips: t('pricing.plans.free.options.features.feature0.tips')
    },
    {
      title: t('pricing.plans.free.options.features.feature1.title'), // AI 择优翻译
      sub_title: t('pricing.plans.free.options.features.feature1.sub_title'), // 无限制
      tips: t('pricing.plans.free.options.features.feature1.tips'),
      uri: '/text' // URI
    },
    {
      title: t('pricing.plans.free.options.features.feature2.title'), // 输入翻译
      sub_title: t('pricing.plans.free.options.features.feature2.sub_title'), // 无限制
      tips: t('pricing.plans.free.options.features.feature2.tips')
    },
    {
      title: t('pricing.plans.free.options.features.feature3.title'), // 划词翻译
      sub_title: t('pricing.plans.free.options.features.feature3.sub_title'), // 无限制
      tips: t('pricing.plans.free.options.features.feature3.tips')
    },
    {
      title: t('pricing.plans.free.options.features.feature4.title'), // PDF 翻译
      sub_title: t('pricing.plans.free.options.features.feature4.sub_title'), // 无限制
      tips: t('pricing.plans.free.options.features.feature4.tips'),
      uri: '/file'
    },
    {
      title: t('pricing.plans.free.options.features.feature5.title'), // ePub 电子书翻译
      sub_title: t('pricing.plans.free.options.features.feature5.sub_title'),
      tips: t('pricing.plans.free.options.features.feature5.tips'),
      uri: '/file'
    },
    {
      title: t('pricing.plans.free.options.features.feature6.title'), // MinerU 文档翻译
      sub_title: t('pricing.plans.free.options.features.feature6.sub_title'),
      tips: t('pricing.plans.free.options.features.feature6.tips'),
      uri: '/file'
    }
  ]
}

// Plus 版权益
const plus_options = {
  title: t('pricing.plans.plus.options.title'), // 包含免费版所有功能
  description: t('pricing.plans.plus.options.description'),
  models_title: t('pricing.plans.plus.options.models_title'), // 高级模型
  models_description: t('pricing.plans.plus.options.models_description'),
  models: [
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/deepseek.png',
      code: 'deepseek', // DeepSeek 翻译
      title: t('pricing.plans.plus.options.models.model0.title'), // DeepSeek 翻译
      sub_title: t('pricing.plans.plus.options.models.model0.sub_title'),
      tips: t('pricing.plans.plus.options.models.model0.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/openai.svg',
      code: 'openai', // OpenAI - ChatGPT 翻译
      title: t('pricing.plans.plus.options.models.model1.title'), // OpenAI - ChatGPT 翻译
      sub_title: t('pricing.plans.plus.options.models.model1.sub_title'),
      tips: t('pricing.plans.plus.options.models.model1.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/gemini.svg',
      code: 'gemini', // Gemini 翻译
      title: t('pricing.plans.plus.options.models.model2.title'), // Gemini 翻译
      sub_title: t('pricing.plans.plus.options.models.model2.sub_title'),
      tips: t('pricing.plans.plus.options.models.model2.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/doubao.svg',
      code: 'doubao', // 豆包 翻译
      title: t('pricing.plans.plus.options.models.model3.title'), // 豆包 翻译
      sub_title: t('pricing.plans.plus.options.models.model3.sub_title'),
      tips: t('pricing.plans.plus.options.models.model3.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/zhipu.png',
      code: 'zhipu', // 智谱GLM 翻译
      title: t('pricing.plans.plus.options.models.model4.title'), // 智谱GLM 翻译
      sub_title: t('pricing.plans.plus.options.models.model4.sub_title'),
      tips: t('pricing.plans.plus.options.models.model4.tips')
    },
    {
      // icon_path: 'https://assets.selecttranslate.com/web/translate_model_icon/tongyi.svg',
      code: 'qwen', // 通义千问 翻译
      title: t('pricing.plans.plus.options.models.model5.title'), // 通义千问 翻译
      sub_title: t('pricing.plans.plus.options.models.model5.sub_title'),
      tips: t('pricing.plans.plus.options.models.model5.tips')
    }
  ],
  features_title: t('pricing.plans.plus.options.features_title'), // 高级功能
  features: [
    {
      title: t('pricing.plans.plus.options.features.feature0.title'), // 择优翻译（高级版）
      sub_title: t('pricing.plans.plus.options.features.feature0.sub_title'),
      tips: t('pricing.plans.plus.options.features.feature0.tips'),
      uri: '/text'
    },
    {
      title: t('pricing.plans.plus.options.features.feature1.title'), // 输入翻译（高级版）
      sub_title: t('pricing.plans.plus.options.features.feature1.sub_title'),
      tips: t('pricing.plans.plus.options.features.feature1.tips')
    },
    // {
    //   title: t('pricing.plans.plus.options.features.feature2.title'), // MinerU 文档翻译（高级版）
    //   sub_title: t('pricing.plans.plus.options.features.feature2.sub_title'),
    //   tips: t('pricing.plans.plus.options.features.feature2.tips'),
    //   uri: '/pdf-plus'
    // },
    {
      title: t('pricing.plans.plus.options.features.feature3.title'), // PDF Plus
      sub_title: t('pricing.plans.plus.options.features.feature3.sub_title'),
      tips: t('pricing.plans.plus.options.features.feature3.tips'),
      uri: '/file'
    },
    {
      title: t('pricing.plans.plus.options.features.feature4.title'), // 优先的售后服务支持
      sub_title: t('pricing.plans.plus.options.features.feature4.sub_title'),
      tips: t('pricing.plans.plus.options.features.feature4.tips') + ' <EMAIL>' // 服务支持邮箱：<EMAIL>
    }
  ]
}

const product_list = computed(() => {
  if (!props.product) {
    return []
  }
  const product = props.product

  const result = []
  if (product.try_enable === 'on') {
    result.push({
      product_name: t('pricing.plans.free.title'), // 免费版
      cycle_name: '',
      code: 'free',
      price: formatCurrency(0),
      cycle_unit: '', // 空字，不显示周期
      description: t('pricing.plans.free.description'), // 无需付费
      features: [], // 为了自定义 features 例表而又避免原生组件报错，这里设置空数组，通过自定义的 options 传递
      options: free_options
    })
  }
  if (product.weekly_enable === 'on') {
    result.push({
      product_name: product.name, // Plus
      cycle_name: t('pricing.cycle_name.weekly'), // 周会员
      code: 'weekly',
      price: formatCurrency(product.weekly_price),
      cycle_unit: t('pricing.cycle_unit.weekly'), // 每周
      description: plus_description,
      features: [], // 为了自定义 features 例表而又避免原生组件报错，这里设置空数组，通过自定义的 options 传递
      options: plus_options
    })
  }
  if (product.monthly_enable === 'on') {
    result.push({
      product_name: product.name,
      cycle_name: t('pricing.cycle_name.monthly'), // 月度会员
      code: 'monthly',
      price: formatCurrency(product.monthly_price),
      cycle_unit: t('pricing.cycle_unit.monthly'), // 每月
      description: plus_description,
      scale: true, // 放大显示
      features: [], // 为了自定义 features 例表而又避免原生组件报错，这里设置空数组，通过自定义的 options 传递
      options: plus_options
    })
  }
  if (product.quarterly_enable === 'on') {
    result.push({
      product_name: product.name,
      cycle_name: t('pricing.cycle_name.quarterly'), // 季度会员
      code: 'quarterly',
      price: formatCurrency(product.quarterly_price),
      cycle_unit: t('pricing.cycle_unit.quarterly'), // 每季
      description: plus_description,
      features: [], // 为了自定义 features 例表而又避免原生组件报错，这里设置空数组，通过自定义的 options 传递
      options: plus_options
    })
  }
  if (product.yearly_enable === 'on') {
    result.push({
      product_name: product.name,
      cycle_name: t('pricing.cycle_name.yearly'), // 年度会员
      code: 'yearly',
      price: formatCurrency(product.yearly_price),
      cycle_unit: t('pricing.cycle_unit.yearly'), // 每年
      description: plus_description,
      features: [], // 为了自定义 features 例表而又避免原生组件报错，这里设置空数组，通过自定义的 options 传递
      options: plus_options
    })
  }

  return result
})

/**
 * 定义价格网格的样式(根据产品数量适配)
 */
const pricing_grid_class = computed(() => {
  let custom_class = 'gap-6 lg:gap-8' // 增大卡片间距
  // console.log('product_list.value', product_list.value);
  if (product_list.value.length === 1) {
    custom_class = 'gap-6 lg:gap-8 lg:grid-cols-1'
  }
  else if (product_list.value.length === 2) {
    custom_class = 'gap-6 lg:gap-8 lg:grid-cols-2'
  }
  else {
    custom_class = 'gap-6 lg:gap-8 lg:grid-cols-3'
  }
  return custom_class
})

const highlightCode = ref('monthly')

watch(product_list, (newValue) => {
  // 判断是否存在高亮的产品
  let match = false
  newValue.forEach((item) => {
    if (item.code === highlightCode.value) {
      match = true
    }
  })

  if (!match) {
    const filterResult = newValue.filter(item => item.code !== 'free')
    if (filterResult && filterResult.length > 0) {
      highlightCode.value = filterResult[0].code
    }
  }
})

/**
 * 是否按年购买
 */
const isYearly = ref(false)

/**
 * 跳转登录页面（带上当前页面的 uri ，登录成功后跳回来）
 */
function skipToLogin() {
  // 如果用户未登录，则跳转到登录页面，登录成功后跳回到当前页面
  // 获取路径部分（不含域名、参数和锚点）
  const pathOnly = window.location.pathname // 如：/zhHans/pricing
  // console.log('==>pathOnly:', pathOnly);
  router.push({ path: '/' + locale.value + '/login', query: { skipToUri: pathOnly } })
}

/**
 * Plus会员套餐的 购买 / 订阅
 */
const buyPlusButton = (buy_mode: any) => {
  // 判断会员是否已登录
  const authStore = useAuthStoreWithOut()
  if (authStore.getToken && authStore.getToken !== '') {
    router.push({
      path: '/' + locale.value + '/buy', // 跳到购买支付页面
      query: {
        platform: props.product.platform,
        position_no: props.product.position_no,
        buy_mode: buy_mode
      }
    })
  }
  else {
    // // 打开未登录提示框
    // isOpenDialogPage.value = true;

    skipToLogin() // 跳转到登录页面
  }
}

const selectSpecification = (code: any) => {
  highlightCode.value = code
}

onMounted(async () => {
  // console.log('props.product', props.product.yearly_enable);
  if (props.product && props.product?.yearly_enable === 'on') {
    isYearly.value = true
  }
  else {
    isYearly.value = false
  }

  // 判断用户是否登录
  const authStore = useAuthStoreWithOut()
  if (authStore.getToken && authStore.getToken !== '') {
    // 已登录
    memberHasLogin.value = true
    authStore.setMemberProductInfo()
  }
  else {
    // 未登录
    memberHasLogin.value = false
  }
})

/** 格式化货币 */
function formatCurrency(val: string | number | bigint) {
  if (typeof val !== 'number') {
    return val
  }
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 1,
    maximumFractionDigits: 2
  }).format(val)
}
</script>

<style scoped>
@media (min-width: 1280px) {
  .pricing-card-item > :deep(.xl\:p-10) {
    padding: 2.5rem;
  }
}

.color_text {
  background: -webkit-linear-gradient(120deg, #bd34fe 30%, #00a3d7);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.plus_text {
  background: -webkit-linear-gradient(265deg, #ff861b 30%, #f32424);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pricing-card-item {
  transition: transform 0.2s;
  position: relative;
  cursor: pointer;
  z-index: 0;
}
.card-highlight {
  transform: scale(1.02) translateY(-2%);
  z-index: 2;
  position: relative;
  transition: transform 0.2s;
}

.pricing-card-item :deep(.space-y-3) > li > span {
  white-space: normal;
}
@media (max-width: 1023px) {
  .card-highlight {
    transform: none;
    box-shadow: none;
    margin-left: 0;
    margin-right: 0;
  }
}
</style>
