<!-- 安装扩展模块 -->
<template>
  <div class="flex flex-wrap justify-center gap-x-6 gap-y-3">
    <template v-for="(browser, index) in getBrowsersList()" :key="index">
      <UButton
        v-if="browser.type === 'icon'"
        :to="browser.url"
        :label="t(browser.label)"
        :icon="browser.icon"
        target="_blank"
        :color="currentBrowser === browser.id ? 'secondary' : 'neutral'"
        variant="subtle"
        size="xl"
        class="rounded-lg px-6 py-4"
      />
      <UButton
        v-else
        :to="browser.url"
        target="_blank"
        :color="(browser.id === 'zip' && showZipHighlight) || (browser.id !== 'zip' && currentBrowser === browser.id) ? 'primary' : 'neutral'"
        :variant="(browser.id === 'zip' && showZipHighlight) || (browser.id !== 'zip' && currentBrowser === browser.id) ? 'solid' : 'subtle'"
        size="xl"
        class="rounded-lg px-6 py-4"
      >
        <img :src="browser.imgSrc" class="iconify h-6 w-6 flex-shrink-0" />
        {{ t(browser.label) }}
      </UButton>
    </template>
    <!-- <UButton :to=safari_extensions_url :label="t('home.hero.extension_pc_safari_label')" icon="i-logos-safari" target="_blank" color="neutral" variant="subtle" size="xl" class="rounded-md px-6 py-4" />
    <UButton :to=firefox_extensions_url :label="t('home.hero.extension_pc_firefox_label')" icon="i-logos-firefox" target="_blank" color="neutral" variant="subtle" size="xl" class="rounded-md px-6 py-4" /> -->
  </div>
</template>

<script setup>
import { getSystemInfoFromSession } from '@/utils/utils'
import { getBrowsersList } from '@/utils/browsersList'

const { t } = useI18n()

const systemInfo = ref(null)
const currentBrowser = ref('')

// 是否显示zip高亮（Mac OS用户特殊处理）
const showZipHighlight = computed(() => {
  return systemInfo.value && systemInfo.value.os_name === 'Mac OS' && !['Chrome', 'Edge', '360'].includes(currentBrowser.value)
})

onBeforeMount(async () => {
  systemInfo.value = getSystemInfoFromSession()

  // 根据浏览器名称设置当前浏览器
  if (systemInfo.value) {
    const browserName = systemInfo.value.browser_name
    if (browserName === 'Chrome') {
      currentBrowser.value = 'Chrome'
    }
    else if (browserName === 'Microsoft Edge' || browserName === 'Edge') {
      currentBrowser.value = 'Edge'
    }
    else if (browserName === '360' || browserName === '360Browser' || browserName === '360SE') {
      currentBrowser.value = '360Browser'
    }
    else if (browserName === 'Safari') {
      currentBrowser.value = 'Safari'
    }
    else if (browserName === 'Firefox') {
      currentBrowser.value = 'Firefox'
    }
    else {
      // 对于其他浏览器，推荐使用crx方式安装
      currentBrowser.value = 'crx'
    }
  }
})
</script>
