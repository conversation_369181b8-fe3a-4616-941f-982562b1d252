<!-- 翻译专用的 tab 栏 -->
<template>
  <div class="relative -mb-px">
    <div class="relative z-10 flex gap-0.5 rounded-t-lg border border-b-0 border-neutral-200 bg-neutral-100 md:inline-flex dark:border-neutral-700 dark:bg-neutral-900">
      <template v-for="(item, index) in tabs" :key="item.value">
        <!-- tooltip 的标签 -->
        <UPopover
          v-if="item.tooltip && !isMobile"
          mode="hover"
          :distance="8"
          :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
        >
          <button
            :class="['tab-btn', modelValue === item.value ? 'active' : '', index === 0 ? 'first-tab' : '', index === tabs.length - 1 ? 'last-tab' : '']"
            class="max-md:w-full"
            @click="handleTabChange(item.value)"
          >
            <span class="mted:xt-sm lg:text-sm xl:text-sm">{{ item.label }}</span>
          </button>

          <template #content>
            <div class="max-w-60 px-3 py-2 text-sm text-neutral-100 dark:text-neutral-200">
              {{ item.tooltip }}
            </div>
          </template>
        </UPopover>

        <!-- 非 tooltip 的标签 -->
        <button
          v-else
          :class="['tab-btn', modelValue === item.value ? 'active' : '', index === 0 ? 'first-tab' : '', index === tabs.length - 1 ? 'last-tab' : '']"
          class="max-md:w-full"
          @click="handleTabChange(item.value)"
        >
          <span class="md:text-sm lg:text-sm xl:text-sm">{{ item.label }}</span>
        </button>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { isMobileDevice } from '@/utils/utils'

const isMobile = ref(isMobileDevice())

interface Tab {
  label: string
  value: string
  tooltip?: string // 可选的提示信息
}

defineProps<{
  modelValue: string
  tabs: Tab[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()

// 处理标签变化
const handleTabChange = (value: string) => {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>

<style scoped>
:root.dark .tab-wrapper {
  box-shadow:
    -2px -1px 2px 0 rgba(0, 0, 0, 0.1),
    2px -1px 2px 0 rgba(0, 0, 0, 0.1);
}

.tab-btn {
  position: relative;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ui-color-neutral-600);
  background-color: var(--ui-color-neutral-100);
  border: none;
  border-radius: 0.5rem 0.5rem 0 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

@media (max-width: 768px) {
  .tab-btn {
    padding: 0.625rem 0.5rem;
  }
}

:root.dark .tab-btn {
  color: var(--ui-color-neutral-400);
  background-color: var(--ui-color-neutral-900);
}

.tab-btn:hover {
  background-color: var(--ui-color-neutral-200);
}

:root.dark .tab-btn:hover {
  background-color: var(--ui-color-neutral-800);
}

/* 标签激活样式 */
.tab-btn.active {
  color: var(--ui-color-primary-500);
  background-color: #ffffff;
  z-index: 2;
}

:root.dark .tab-btn.active {
  color: var(--ui-color-primary-500);
  background-color: var(--ui-color-neutral-800);
}

/* 添加一个底部遮罩层，确保底部过渡时不会出现变色 */
.tab-btn.active::after,
.tab-btn.active::before {
  z-index: 1;
}

/* 为所有标签添加伪元素，但默认隐藏 */
.tab-btn::before,
.tab-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  width: 10px; /* 增加宽度以确保完全覆盖 */
  height: 10px; /* 增加高度以确保完全覆盖 */
  background-color: transparent;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.tab-btn::before {
  left: -10px; /* 调整位置以匹配新的宽度 */
  border-bottom-right-radius: 0.5rem;
}

.tab-btn::after {
  right: -10px; /* 调整位置以匹配新的宽度 */
  border-bottom-left-radius: 0.5rem;
}

/* 调整激活状态下的边角遮罩 */
.tab-btn.active::before {
  box-shadow: 0.25rem 0.25rem 0 0.25rem #ffffff; /* 增加尺寸确保覆盖 */
}

/* 激活标签时显示圆角效果 */
.tab-btn.active::before,
.tab-btn.active::after {
  opacity: 1;
}

.tab-btn.active::after {
  box-shadow: -0.25rem 0.25rem 0 0.25rem #ffffff; /* 增加尺寸确保覆盖 */
}

:root.dark .tab-btn.active::before {
  box-shadow: 0.25rem 0.25rem 0 0.25rem var(--ui-color-neutral-800);
}

:root.dark .tab-btn.active::after {
  box-shadow: -0.25rem 0.25rem 0 0.25rem var(--ui-color-neutral-800);
}

/* 处理第一个和最后一个标签的特殊效果 */
.tab-btn.first-tab.active::before,
.tab-btn.last-tab.active::after {
  opacity: 0;
}
</style>
