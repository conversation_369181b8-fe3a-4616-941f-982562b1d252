<!-- 择优翻译的产品介绍 -->
<template>
  <!-- 在择优翻译页面，如果没有安装或启用插件的情况下，显示择优翻译的产品介绍相关信息，并提示用户安装插件 -->
  <UPageSection
    :ui="{
      container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20 gap-6 sm:gap-6',
      title: 'text-2xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight text-neutral-900 dark:text-white',
      description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
    }"
  >
    <template #title>
      <!-- AI 择优翻译 -->
      <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{{ $t('home.ai_selected_translation.title') }}</span>
    </template>
    <template #description>
      <!-- 使用多个翻译模型对同一原文进行翻译会得到多个不同的结果，通过智能评分从中择优获取最佳译文，能大幅提升翻译结果的准确度和可靠性。 -->
      <span class="text-gray-600 dark:text-gray-300" v-html="$t('home.ai_selected_translation.description')" />
      <br />
      <!-- 安装并启用精挑翻译插件后即可马上体验！ -->
      <span class="text-gray-600 dark:text-gray-300 font-semibold">{{ $t('check_installed.extension_not_installed.tip_3') }}</span>
    </template>
    <!-- HTML5 Video标签的属性、方法和事件汇总 https://blog.csdn.net/2301_78542842/article/details/142456899 -->
    <!-- src: 定义视频的URL -->
    <!-- poster: 设置视频尚未加载或播放时显示的图像URL -->
    <!-- preload: 控制视频数据的加载策略，可以是 "auto"（默认，根据浏览器决定）、"metadata"（只加载元数据，如长度）或 "none"（不预先加载） -->
    <!-- autoplay: 规定视频是否应该在就绪后立即自动播放 -->
    <!-- controls: 是否显示浏览器自带的播放控制（如播放/暂停按钮、进度条等） -->
    <!-- loop: 规定视频是否应在结束时重新开始播放 -->
    <!-- muted: 规定视频是否静音播放 -->
    <!-- playsinline: 规定视频是否应在元素的播放区域内播放，而不是全屏播放 -->
    <!-- width: 规定视频的宽度 -->
    <!-- height: 规定视频的高度 -->
    <video
      ref="mainVideo"
      controls
      playsinline
      preload="none"
      poster="https://assets.selecttranslate.com/web/videos/ztsl-ai-selected-translation-face.jpg"
      class="w-full rounded-lg cursor-pointer video_container"
    >
      <!-- 由于不同浏览器对视频格式的支持不同，通常需要提供多种格式的视频文件，以确保兼容性。可以使用多个 <source> 标签来定义不同格式的视频文件 -->
      <source src="https://assets.selecttranslate.com/web/videos/ztsl-ai-selected-translation.mp4" type="video/mp4" />
    </video>
  </UPageSection>
</template>
