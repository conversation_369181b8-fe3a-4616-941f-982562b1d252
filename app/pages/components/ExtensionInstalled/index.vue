<!-- 交互式浏览器安装引导按钮组 -->
<template>
  <div
    v-if="getIsExtensionInstalledDefault && getIsExtensionChecking"
    class="h-full w-full flex items-center justify-center"
  >
    <!-- 按钮 loading -->
    <UButton
      v-if="loadingType === 'button' "
      :loading="getIsExtensionChecking"
      size="xl"
      class="rounded-lg px-10 sm:px-8 md:px-12 py-2 whitespace-nowrap text-sm sm:text-base md:text-xl"
    />
    <!-- loading -->
    <div v-else-if="getIsExtensionChecking" class="fixed top-1/6 flex items-center justify-center z-50">
      <UseLoading
        size="large"
        :z-index="50"
      />
    </div>
  </div>
  <div v-else-if="!getIsExtensionInstalled || getExtensionVersionStatus === 'blocked'" class="space-y-3 flex flex-col justify-center items-center" :class="{ 'mt-10': loadingType === 'icon' }">
    <div class="font-bold" :class="{ 'text-2xl': size === 'large', 'text-lg': size === 'default' }">
      <span v-if="!getIsExtensionInstalled">
        <!--  您的浏览器未安装/未启用 精挑翻译插件 , 请安装/启用插件后重试 -->
        {{ t('check_installed.extension_not_installed.tip_1') }}
        <span class="text-(--ui-primary)">{{ t('check_installed.extension_name') }}</span>
        <span>，</span>
        <!--  请安装/启用插件后重试 -->
        {{ t('check_installed.extension_not_installed.tip_2') }}
      </span>
      <span v-if="getExtensionVersionStatus === 'blocked'">
        <!-- 检测到您的精挑翻译插件版本过低 -->
        {{ t('check_installed.extension_version_too_low.tip_1') }}
        <span class="text-(--ui-primary)">{{ t('check_installed.extension_name') }}</span>
        {{ t('check_installed.extension_version_too_low.tip_2') }}
        <span>，</span>
        <!-- 请升级插件至最新版本后重试 -->
        <span>{{ t('check_installed.extension_version_too_low.tip_3') }}</span>
      </span>
    </div>
    <div class="flex items-center justify-center gap-3 sm:gap-4 md:gap-5 lg:gap-6 xl:gap-7">
      <UButton color="secondary" class="rounded-md p-3" @click="handleMainButtonClick">
        <!-- 当前浏览器图标 -->
        <UIcon
          v-if="currentBrowserInfo?.icon"
          :name="currentBrowserInfo.icon"
          class="w-5 h-5 flex-shrink-0"
        />
        <img
          v-else-if="currentBrowserInfo?.imgSrc"
          :src="currentBrowserInfo.imgSrc"
          class="w-5 h-5 flex-shrink-0"
        />
        <UIcon
          v-else
          name="i-lucide-download"
          class="w-5 h-5 flex-shrink-0"
        />

        <!-- 按钮文本 -->
        <span :class="isMobile ? 'text-sm' : 'whitespace-nowrap'">
          <template v-if="isMobile">
            <!-- 添加到 XXX -->
            {{ t('check_installed.add_browser_1') }}{{ currentBrowser }}
          </template>
          <template v-else>
            <!-- 免费添加到 XXX -->
            {{ t('check_installed.add_browser_2') }}
            {{ currentBrowser }}
          </template>
        </span>
      </UButton>

      <!-- 展开指示器 -->
      <UPopover :mode="isMobile ? 'click' : 'hover'" :ui="{ content: 'w-64 p-2' }">
        <!-- 其他浏览器 -->
        <UButton
          color="secondary"
          variant="soft"
          :class="isMobile ? 'rounded-md px-4 py-3 text-sm' : 'rounded-md p-3'"
          :label="t('check_installed.other_browsers')"
          trailing-icon="i-lucide-chevron-down"
        />
        <template #content>
          <!-- 浏览器选项列表 -->
          <div
            v-for="browser in browsersList"
            :key="browser.id"
            class="flex items-center gap-3 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-100 cursor-pointer group rounded-lg"
            @click="handleBrowserClick(browser)"
          >
            <!-- 浏览器图标 -->
            <UIcon
              v-if="browser.icon"
              :name="browser.icon"
              class="w-6 h-6 flex-shrink-0"
            />
            <img
              v-else-if="browser.imgSrc"
              :src="browser.imgSrc"
              :alt="browser.label"
              class="w-6 h-6 flex-shrink-0 object-contain"
            />

            <!-- 浏览器信息 -->
            <div class="flex-1 min-w-0">
              <div class="font-medium text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-100">
                {{ browser.label }}
              </div>
            </div>
          </div>
        </template>
      </UPopover>
    </div>
  </div>
  <div v-if="!getIsEnvMatching" class="flex flex-col justify-center items-center" :class="{ 'mt-10': loadingType === 'icon' }">
    <!-- 当前环境与插件环境不匹配 -->
    <span class="text-neutral-800 dark:text-neutral-100">检测到当前环境与插件环境不一致</span>
    <br />
    <!-- 当前环境: {{ currentEnv }}，插件环境: {{ extensionEnv }} -->
    <span class="text-base text-neutral-500">
      当前环境：
      <span class="text-(--ui-primary)">{{ envMap[currentEnv] }}</span>
      <br />
      插件环境：
      <span class="text-(--ui-primary)">{{ envMap[getExtensionEnv] }}</span>
      <br />
      请确保两者一致！
    </span>
  </div>

  <!-- 已安装插件 -->
  <slot v-if="getIsExtensionInstalledDefault && !getIsExtensionChecking && getIsEnvMatching" />

  <!-- 产品介绍相关信息 -->
  <div v-if="isShowProduct && currentProductComponent && !getIsExtensionChecking && !getIsExtensionInstalledDefault">
    <component :is="currentProductComponent" />
  </div>
</template>

<script setup lang="ts">
import { getBrowsersList } from '@/utils/browsersList.ts'
import { getSystemInfoFromSession } from '@/utils/utils.ts'
import { useMobileDetection } from '@/composables/useMobileDetection'
import { useExtensionStore } from '@/store/modules/extension'
import SelectTranslate from '@/pages/components/ExtensionInstalled/components/product/SelectTranslate.vue'

// 国际化
const { t } = useI18n()

const extensionStore = useExtensionStore()
const { getIsExtensionInstalled, getIsExtensionInstalledDefault, getIsExtensionChecking, getExtensionEnv, getIsEnvMatching, getExtensionVersionStatus } = storeToRefs(extensionStore)

// 移动端检测
const { isMobile } = useMobileDetection()

// 响应式数据
const systemInfo = ref(null)
const currentBrowser = ref('')
const browsersList = ref([])

// loading 类型
type LoadingType = 'button' | 'icon'

type Size = 'default' | 'large'

const props = defineProps({
  translateType: {
    type: String,
    default: ''
  },
  // 是否开启产品介绍
  isShowProduct: {
    type: Boolean,
    default: false
  },
  loadingType: {
    type: String as PropType<LoadingType>,
    default: 'button'
  },
  size: {
    type: String as PropType<Size>,
    default: 'default'
  }
})

// 动态组件映射
const productComponents = {
  text: SelectTranslate
  // 后面可以添加其他文件类型
}

// 计算当前应该显示的产品组件
const currentProductComponent = computed(() => {
  return productComponents[props.translateType] || null
})

// 计算属性
const currentBrowserInfo = computed(() => {
  return browsersList.value.find(browser => browser.id === currentBrowser.value)
})

// 获取当前发布环境变量
const currentEnv = useRuntimeConfig().public.env

// 映射环境变量
const envMap = {
  dev: '开发环境',
  pro: '生产环境',
  test: '测试环境'
}

// 方法
const handleMainButtonClick = () => {
  if (currentBrowserInfo.value) {
    handleBrowserClick(currentBrowserInfo.value)
  }
}

const handleBrowserClick = (browser) => {
  // 跳转到对应 url
  window.open(browser.url, '_blank')
}

// 生命周期
onBeforeMount(async () => {
  // 获取系统信息
  systemInfo.value = getSystemInfoFromSession()

  // 获取浏览器列表
  browsersList.value = getBrowsersList()

  // 根据浏览器名称设置当前浏览器
  if (systemInfo.value) {
    const browserName = systemInfo.value.browser_name
    if (browserName === 'Chrome') {
      currentBrowser.value = 'Chrome'
    }
    else if (browserName === 'Microsoft Edge' || browserName === 'Edge') {
      currentBrowser.value = 'Edge'
    }
    else if (browserName === '360' || browserName === '360Browser' || browserName === '360SE') {
      currentBrowser.value = '360'
    }
    else if (browserName === 'Safari') {
      currentBrowser.value = 'Safari'
    }
    else if (browserName === 'Firefox') {
      currentBrowser.value = 'Firefox'
    }
    else {
      // 对于其他浏览器，默认显示第一个选项
      currentBrowser.value = browsersList.value[0]?.id || 'Chrome'
    }
  }
  else {
    // 如果无法获取系统信息，默认显示Chrome
    currentBrowser.value = 'Chrome'
  }
})
</script>
