<template>
  <div>
    <UCard class="mt-6">
      <template #header>
        <h3 class="text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
          <!-- 账户 -->
          {{ t('account.account') }}
        </h3>
      </template>

      <!-- 用户信息 -->
      <div class="mb-5">
        <div v-if="member.signup_mode === 'email' || member.signup_mode === 'add'">
          <UAlert color="neutral" variant="outline" :avatar="{ src: member.avatar ? member.avatar : avatar_svg }">
            <template #title>
              <div class="flex flex-wrap items-center">
                <!-- 会员ID -->
                <span class="text-base">{{ `ID: ${member.member_id}` }}</span>
                <CopyButton :content="member.member_id" :size="'xs'" class="ml-2" />
              </div>
            </template>
            <template #description>
              <!-- 邮箱地址 -->
              <span class="text-sm font-normal break-all">{{ t('account.email_address') + `: ${member.email}` }}</span>
            </template>
          </UAlert>
        </div>
        <div v-else-if="member.signup_mode === 'wechat'">
          <!-- 微信昵称 -->
          <UAlert
            color="neutral"
            variant="outline"
            :avatar="{ src: member.avatar ? member.avatar : avatar_svg }"
            :title="`ID: ${member.member_id}`"
            :description="t('account.wechat_nickname') + `: ${member.nickname}`"
          />
        </div>
        <div v-else-if="member.signup_mode === 'google'">
          <!-- Google 昵称 -->
          <UAlert
            color="neutral"
            variant="outline"
            :avatar="{ src: avatar_svg }"
            :title="`ID: ${member.member_id}`"
            :description="t('account.google_nickname') + `: ${member.nickname}`"
          />
        </div>
        <div v-else>
          <UAlert
            color="neutral"
            variant="outline"
            :avatar="{ src: avatar_svg }"
            :title="`ID: ${member.member_id ? member.member_id : ''}`"
            :description="' '"
          />
        </div>
      </div>

      <!-- 套餐信息 -->
      <div class="mb-5">
        <!-- 套餐信息加载中 -->
        <div v-if="isLoading">
          <UCard color="neutral" variant="outline">
            <div class="flex items-center justify-center py-12">
              <div class="text-center">
                <UIcon name="i-heroicons-arrow-path" class="text-primary-500 mx-auto h-8 w-8 animate-spin" />
                <p class="mt-3 text-sm font-medium text-gray-600 dark:text-gray-300">
                  Loading...
                </p>
              </div>
            </div>
          </UCard>
        </div>

        <!-- 实际套餐信息内容 -->
        <div v-else>
          <!-- 会员产品Plus会员状态：0:未试用（免费版），1：Plus会员试用中（Plus 会员试用版），2：Plus会员试用已过期（免费版），3：Plus会员套餐有效期内（Plus 会员），4：Plus会员套餐已过期（免费版） -->
          <UCard color="neutral" variant="outline" :actions="[{ class: 'bg-white' }]">
            <div>
              <!-- 兼容移动端：纵向排列并允许换行 -->
              <div class="flex flex-wrap leading-6 sm:flex-nowrap">
                <!-- 当前套餐 -->
                <span class="inline-flex text-lg leading-6 font-medium">{{ t('account.current_plan') }}：</span>
                <!-- 会员产品套餐状态 -->
                <div v-if="memberProduct.member_product_status == 0" class="member-product-text inline-flex">
                  <!-- 0：免费版（新用户） -->
                  <!-- <PlusInActiveIcon /> -->
                  <!-- 免费版 -->
                  <span class="text-emerald-500">{{ t('account.free_version') }}</span>
                </div>
                <div v-else-if="memberProduct.member_product_status == 1" class="member-product-text inline-flex">
                  <!-- 1：Plus（试用版） -->
                  <PlusActiveIcon />
                  <span class="text-amber-500">{{ memberProduct.product_name }}</span>
                  <!-- 试用版 -->
                  <span class="text-emerald-500">（{{ t('account.trial_version') }}）</span>
                </div>
                <div v-else-if="memberProduct.member_product_status == 2" class="member-product-text inline-flex">
                  <!-- 2：免费版（Plus会员试用结束） -->
                  <PlusInActiveIcon />
                  <!-- 免费版 -->
                  <span class="text-emerald-500">{{ t('account.free_version') }}</span>
                </div>
                <div v-else-if="memberProduct.member_product_status == 3" class="member-product-text inline-flex">
                  <!-- 3：Plus会员 -->
                  <PlusActiveIcon />
                  <span class="font-semibold text-amber-500">{{ memberProduct.product_name }}</span>
                  <!-- 订阅中且是按月订阅，显示：月度订阅会员 -->
                  <span v-if="memberProduct.subscription_status == 2 && memberProduct.subscription_buy_mode === 'monthly'" class="ml-1 text-sm text-neutral-500">
                    （{{ t('account.monthly_subscription') }}）
                  </span>
                  <!-- 订阅中且是按年订阅，显示：年度订阅会员 -->
                  <span v-if="memberProduct.subscription_status == 2 && memberProduct.subscription_buy_mode === 'yearly'" class="ml-1 text-sm text-neutral-500">
                    （{{ t('account.yearly_subscription') }}）
                  </span>
                </div>
                <div v-else-if="memberProduct.member_product_status == 4" class="member-product-text inline-flex">
                  <!-- 4：免费版（Plus会员已过期） -->
                  <PlusInActiveIcon />
                  <!-- 免费版 -->
                  <span class="text-emerald-500">{{ t('account.free_version') }}</span>
                </div>
                <div v-else class="inline-flex">
                  <!-- （其它情况） -->
                </div>
              </div>
            </div>
            <!-- 套餐信息 -->
            <div>
              <div class="flex max-sm:flex-col">
                <div class="flex-1">
                  <div v-if="memberProduct.member_product_status == 0" class="mt-2 flex gap-3 max-sm:ml-0 max-sm:flex-col max-sm:gap-2 md:ml-20 md:text-base">
                    <UButton
                      color="success"
                      variant="subtle"
                      size="sm"
                      class="rounded-sm"
                      @click="freeTrialButton"
                    >
                      <!-- 免费试用 Plus 会员 -->
                      {{ t('account.free_trial_plus') }}
                    </UButton>
                    <!-- &nbsp; -->
                    <!-- 0：免费版（新用户） -->
                    <UButton
                      v-if="product.platform && product.position_no"
                      color="primary"
                      variant="subtle"
                      size="sm"
                      class="rounded-sm"
                      @click="buyPlusButton"
                    >
                      <!-- 升级为 Plus 会员 -->
                      {{ t('account.upgrade_to_plus') }}
                    </UButton>
                  </div>
                  <div v-else-if="memberProduct.member_product_status == 1" class="mt-1 text-sm leading-4 max-sm:flex max-sm:flex-col md:text-base">
                    <!-- 1：试用中，但没订阅，显示：Plus（试用版）套餐服务将于 YYYY-MM-DD 到期 -->
                    <span v-if="memberProduct.subscription_status != 2" class="text-neutral-800 dark:text-neutral-300">
                      {{ memberProduct.product_name }}（{{ t('account.trial_version') }}）{{ t('account.plan_will_expire_on_1') }}：{{
                        dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm')
                      }}
                      {{ t('account.plan_will_expire_on_2') }}
                    </span>
                    <!-- 2. 试用中，并且已订阅中，但试用期未结束还没正式扣费，显示：您订阅的 Plus 会员将在试用期结束后于 YYYY-MM-DD HH:mm 自动扣费生效。  -->
                    <span v-if="memberProduct.subscription_status == 2" class="text-neutral-800 dark:text-neutral-300">
                      {{ t('account.subscription_billing_time_tip1') }}
                      <span class="px-1">{{ dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm') }}</span>
                      {{ t('account.subscription_billing_time_tip2') }}
                    </span>
                    &nbsp;&nbsp;
                    <UButton
                      v-if="memberProduct.subscription_status == 2"
                      color="warning"
                      variant="subtle"
                      size="sm"
                      class="mt-2 rounded-sm"
                      @click="subscriptionCancelConfirmOpen"
                    >
                      <!-- 试用中，已订阅： 取消订阅 -->
                      {{ t('account.unsubscribe') }}
                    </UButton>
                    <UButton
                      v-else-if="product.platform && product.position_no"
                      color="primary"
                      variant="subtle"
                      size="sm"
                      class="mt-2 rounded-sm"
                      @click="buyPlusButton"
                    >
                      <!-- 升级为正式版 Plus 会员 -->
                      {{ t('account.upgrade_to_official_version_plus') }}
                    </UButton>
                  </div>
                  <div v-else-if="memberProduct.member_product_status == 2" class="mt-1 text-sm leading-4 max-sm:flex max-sm:flex-col md:text-base">
                    <!-- 2：免费版（Plus 会员试用结束） -->
                    <!-- Plus（试用版）套餐服务已于 YYYY-MM-DD 到期 -->
                    <span class="text-neutral-800 dark:text-neutral-300">
                      {{ memberProduct.product_name }}（{{ t('account.trial_version') }}）{{ t('account.plan_expired_on_1') }}：{{
                        dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm')
                      }}
                      {{ t('account.plan_expired_on_2') }}
                    </span>
                    &nbsp;&nbsp;
                    <UButton
                      v-if="product.platform && product.position_no"
                      color="primary"
                      variant="subtle"
                      size="sm"
                      class="mt-2 rounded-sm"
                      @click="buyPlusButton"
                    >
                      <!-- 升级为 Plus 会员 -->
                      {{ t('account.upgrade_to_plus') }}
                    </UButton>
                  </div>
                  <div v-else-if="memberProduct.member_product_status == 3" class="mt-1 text-sm leading-4 max-sm:flex max-sm:flex-col md:text-base">
                    <!-- 3：Plus会员（服务有效期内） -->
                    <!-- 非订阅中状态，显示：Plus 套餐服务将于 YYYY-MM-DD HH:mm 到期 -->
                    <span v-if="memberProduct.subscription_status != 2" class="text-neutral-800 dark:text-neutral-300">
                      {{ memberProduct.product_name }}&nbsp;{{ t('account.plan_will_expire_on_1') }}：{{ dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm') }}
                      {{ t('account.plan_will_expire_on_2') }}
                    </span>
                    <!-- 订阅中状态，显示：下次续费时间 YYYY-MM-DD HH:mm -->
                    <span v-if="memberProduct.subscription_status == 2" class="text-neutral-800 dark:text-neutral-300">
                      {{ t('account.next_renewal_time') }}：{{ dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm') }}
                    </span>
                    &nbsp;&nbsp;
                    <UButton
                      v-if="memberProduct.subscription_status == 2"
                      color="warning"
                      variant="subtle"
                      size="sm"
                      class="rounded-sm max-sm:mt-2"
                      @click="subscriptionCancelConfirmOpen"
                    >
                      <!-- 取消订阅 -->
                      {{ t('account.unsubscribe') }}
                    </UButton>
                    <UButton
                      v-else-if="product.platform && product.position_no"
                      color="primary"
                      variant="subtle"
                      size="sm"
                      class="rounded-sm max-sm:mt-2"
                      @click="buyPlusButton"
                    >
                      <!-- 续费 / 订阅 -->
                      {{ t('account.renewal_or_subscribe') }}
                    </UButton>
                  </div>
                  <div v-else-if="memberProduct.member_product_status == 4" class="mt-1 text-sm leading-4 max-sm:flex max-sm:flex-col md:text-base">
                    <!-- 4：免费版（Plus会员已过期） -->
                    <!-- Plus 套餐服务已于 YYYY-MM-DD 到期 -->
                    <span class="text-neutral-800 dark:text-neutral-300">
                      {{ memberProduct.product_name }}&nbsp;{{ t('account.plan_expired_on_1') }}：{{ dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm') }}
                      {{ t('account.plan_expired_on_2') }}
                    </span>
                    &nbsp;&nbsp;
                    <UButton
                      v-if="product.platform && product.position_no"
                      color="primary"
                      variant="subtle"
                      size="sm"
                      class="mt-2 rounded-sm"
                      @click="buyPlusButton"
                    >
                      <!-- 续费 / 订阅 -->
                      {{ t('account.renewal_or_subscribe') }}
                    </UButton>
                    <!-- 订阅续费扣费失败提示 -->
                    <div v-if="memberProduct.subscription_renewal_status == 2" class="mt-4 mb-4 rounded-lg border border-red-200 bg-red-50 p-3">
                      <div class="flex items-start gap-3">
                        <UIcon name="i-heroicons-exclamation-circle" class="mt-1 text-red-500" />
                        <div class="flex-1">
                          <!-- 订阅续订扣费失败 -->
                          <h4 class="mb-2 text-xs font-medium text-red-700">
                            {{ t('account.subscription_renewal_failed') }}
                          </h4>
                          <p class="mb-2 flex items-center justify-between text-xs text-red-600">
                            <span v-if="memberProduct.error_code != '' && memberProduct.error_code == 'insufficient_funds'">
                              <!-- 您的Plus 会员续费未成功，因您的支付卡余额不足。请在充值后重新尝试订阅，如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。 -->
                              {{ t('account.subscription_renewal_failed_tip_1', { email: '<EMAIL>' }) }}
                            </span>
                            <span v-else-if="memberProduct.error_code != '' && memberProduct.error_code == 'expired_card'">
                              <!-- 您的Plus 会员续费未成功，因您的支付卡已过期。请在更换支付卡后重新尝试订阅，如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。 -->
                              {{ t('account.subscription_renewal_failed_tip_2', { email: '<EMAIL>' }) }}
                            </span>
                            <span v-else>
                              <!-- 您的 Plus 会员订阅续费扣款失败，原因可能是银行拦截，请在更换支付卡后重新尝试订阅。如有疑问，请通过电子邮件 <EMAIL> 联系我们获取支持。 -->
                              {{ t('account.subscription_renewal_failed_tip_3', { email: '<EMAIL>' }) }}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="mt-1 text-sm leading-4 md:text-base">
                    <!-- （其它情况） -->
                  </div>
                </div>
                <div class="flex max-sm:justify-end">
                  <!-- 使用兑换码 -->
                  <div class="mt-1 cursor-pointer text-sm leading-10 text-sky-500" @click="openCouponCodeRedeem">
                    {{ t('coupon.code.use_title') }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Plus 套餐额度使用情况（要判断会员状态） -->
            <div v-if="memberProduct.member_product_status == 1 || (memberProduct.member_product_status == 3 && showContent)">
              <div class="credit-limit mt-4">
                <div v-if="memberProduct.member_product_status == 1" class="flex text-base font-semibold">
                  <!-- 试用套餐额度 -->
                  {{ t('account.usage_of_trial_plan_quota') }}
                </div>
                <div v-else class="flex text-base font-semibold">
                  <!-- 本月套餐额度使用情况 -->
                  {{ t('account.usage_of_this_month_plan_quota') }}
                </div>

                <div class="mt-2 pt-2 text-sm leading-4 text-neutral-800 dark:text-neutral-300">
                  <!-- 翻译额度剩余 -->
                  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="flex flex-wrap items-center">
                      <span class="mr-2">{{ t('account.remaining_translation_quota') }}:</span>
                      <span class="font-medium">{{ memberProductTokenUsage.tokens_remaining_num }} / {{ memberProductTokenUsage.tokens_total_num }}</span>
                      <span class="ml-1">TQ</span>
                    </div>
                    <div class="mt-1 flex justify-end text-xs text-neutral-500 sm:mt-0 sm:text-sm">
                      {{ Math.round((memberProductTokenUsage.tokens_remaining_num / memberProductTokenUsage.tokens_total_num) * 100) || 0 }}%
                    </div>
                  </div>
                  <!-- 进度条 -->
                  <div class="mt-2 flex items-center">
                    <div class="flex-1">
                      <UProgress
                        v-model="memberProductTokenUsage.tokens_remaining_num"
                        :max="memberProductTokenUsage.tokens_total_num"
                        size="md"
                        color="primary"
                        indicator
                      />
                    </div>
                  </div>
                </div>
                <div class="mt-4 pt-2 text-sm leading-4 text-neutral-800 dark:text-neutral-300">
                  <!-- PDF Plus 页数剩余 -->
                  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="flex flex-wrap items-center">
                      <span class="mr-2">{{ t('account.pdf_plus_pages_remaining') }}:</span>
                      <span class="font-medium">{{ memberProductTokenUsage.pdf_plus_remaining_page_num }} / {{ memberProductTokenUsage.pdf_plus_total_page_num }}</span>
                      <span class="ml-1">{{ t('account.pages') }}</span>
                    </div>
                    <div class="mt-1 flex justify-end text-xs text-neutral-500 sm:mt-0 sm:text-sm">
                      {{ Math.round((memberProductTokenUsage.pdf_plus_remaining_page_num / memberProductTokenUsage.pdf_plus_total_page_num) * 100) || 0 }}%
                    </div>
                  </div>
                  <!-- 进度条 -->
                  <div class="mt-2 flex items-center">
                    <div class="flex-1">
                      <UProgress
                        v-model="memberProductTokenUsage.pdf_plus_remaining_page_num"
                        :max="memberProductTokenUsage.pdf_plus_total_page_num"
                        size="md"
                        color="primary"
                        indicator
                      />
                    </div>
                  </div>
                  <!-- 下次额度重置时间 -->
                  <div class="mt-4 rounded-lg bg-neutral-50 p-3 dark:bg-neutral-800">
                    <p class="text-center text-xs text-neutral-500 sm:text-left sm:text-sm">
                      {{ t('account.quota_reset_time') }}: {{ dayjs(memberProductTokenUsage.cycle_end_time).format('YYYY-MM-DD HH:mm') }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </UCard>
        </div>
      </div>

      <!--  服务资源加量包（要判断会员状态） -->
      <div v-if="memberProduct.member_product_status != 0">
        <!-- 加量包信息加载中 -->
        <div v-if="isLoading">
          <UCard color="neutral" variant="outline">
            <div class="flex items-center justify-center py-8">
              <div class="text-center">
                <UIcon name="i-heroicons-arrow-path" class="text-primary-500 mx-auto h-6 w-6 animate-spin" />
                <p class="mt-2 text-sm font-medium text-gray-600 dark:text-gray-300">
                  Loading...
                </p>
              </div>
            </div>
          </UCard>
        </div>

        <!-- 实际加量包信息内容 -->
        <div v-else>
          <UCard color="neutral" variant="outline" :actions="[{ class: 'bg-white' }]">
            <!-- 加量包额度 使用情况 -->
            <div class="inline-flex text-base leading-6 font-medium">
              {{ t('account.usage_of_add_on_quota') }}
            </div>
            <!-- 翻译额度 -->
            <div class="mt-2 pt-2 text-sm leading-4 text-neutral-800 dark:text-neutral-300">
              <div class="flex items-center">
                <span class="flex">
                  <!-- 翻译额度剩余 -->
                  {{ t('account.remaining_translation_quota') }}&nbsp;:&nbsp;{{ memberProductTokenUsage.pack_tokens_remaining_num }} /
                  {{ memberProductTokenUsage.pack_tokens_current_total_num }}&nbsp;TQ
                </span>
                <!-- 购买翻译额度加量包 -->
                <span
                  v-if="memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3"
                  class="ml-4 hidden cursor-pointer text-sm text-sky-500 md:flex"
                  @click="goToRecharge('general_token')"
                >
                  {{ t('account.purchase_translation_quota_add_on_package') }}
                </span>
                <UPopover
                  class="hidden md:inline"
                  :mode="isMobileDevice() ? 'click' : 'hover'"
                  arrow
                  :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
                >
                  <UIcon name="heroicons:information-circle" class="ml-1 size-4 shrink-0 text-neutral-400" />
                  <template #content>
                    <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                      <!-- 加量包额度仅限 Plus 会员有效期内购买和使用 -->
                      {{ t('account.purchase_add_on_package_tips') }}
                    </div>
                  </template>
                </UPopover>
              </div>
            </div>
            <!-- 进度条 -->
            <div class="flex justify-end text-sm text-neutral-500">
              {{
                memberProductTokenUsage.pack_tokens_current_total_num
                  ? Math.round((memberProductTokenUsage.pack_tokens_remaining_num / memberProductTokenUsage.pack_tokens_current_total_num) * 100)
                  : 0
              }}%
            </div>
            <div class="flex items-center">
              <div class="flex-1">
                <UProgress
                  v-model="memberProductTokenUsage.pack_tokens_remaining_num"
                  :max="memberProductTokenUsage.pack_tokens_current_total_num"
                  size="md"
                  color="primary"
                  indicator
                />
              </div>
            </div>

            <!-- 移动端：购买翻译额度加量包链接位于进度条右下角 -->
            <div v-if="isMobileDevice() && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)" class="mt-1 flex items-center justify-end md:hidden">
              <span class="cursor-pointer text-sm text-sky-500" @click="goToRecharge('general_token')">
                {{ t('account.purchase_translation_quota_add_on_package') }}
              </span>
            </div>

            <!-- PDF Plus 页数剩余 -->
            <div class="mt-2 pt-2 text-sm leading-4 text-neutral-800 dark:text-neutral-300">
              <div class="flex items-center">
                <span class="flex">
                  <!-- PDF Plus 页数剩余 -->
                  {{ t('account.pdf_plus_pages_remaining') }}&nbsp;:&nbsp;{{ memberProductTokenUsage.pack_pdf_plus_remaining_num }} /
                  {{ memberProductTokenUsage.pack_pdf_plus_current_total_num }}&nbsp;{{ t('account.pages') }}
                </span>
                <!-- 购买 PDF Plus 加量包 -->
                <span
                  v-if="memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3"
                  class="ml-4 hidden cursor-pointer text-sm text-sky-500 md:flex"
                  @click="goToRecharge('pdf_plus')"
                >
                  {{ t('account.purchase_pdf_plus_add_on_package') }}
                </span>
                <UPopover
                  class="hidden md:inline"
                  :mode="isMobileDevice() ? 'click' : 'hover'"
                  arrow
                  :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
                >
                  <UIcon name="heroicons:information-circle" class="ml-1 size-4 shrink-0 text-neutral-400" />
                  <template #content>
                    <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                      <!-- 加量包额度仅限 Plus 会员有效期内购买和使用 -->
                      {{ t('account.purchase_add_on_package_tips') }}
                    </div>
                  </template>
                </UPopover>
              </div>
            </div>
            <!-- 进度条 -->
            <div class="flex justify-end text-sm text-neutral-500">
              {{
                memberProductTokenUsage.pack_pdf_plus_current_total_num
                  ? Math.round((memberProductTokenUsage.pack_pdf_plus_remaining_num / memberProductTokenUsage.pack_pdf_plus_current_total_num) * 100)
                  : 0
              }}%
            </div>
            <div class="flex items-center">
              <div class="flex-1">
                <UProgress
                  v-model="memberProductTokenUsage.pack_pdf_plus_remaining_num"
                  :max="memberProductTokenUsage.pack_pdf_plus_current_total_num"
                  size="md"
                  color="primary"
                  indicator
                />
              </div>
            </div>

            <!-- 移动端：购买 PDF Plus 加量包链接位于进度条右下角 -->
            <div v-if="isMobileDevice() && (memberProduct.member_product_status == 1 || memberProduct.member_product_status == 3)" class="mt-1 flex items-center justify-end md:hidden">
              <span class="cursor-pointer text-sm text-sky-500" @click="goToRecharge('pdf_plus')">
                {{ t('account.purchase_pdf_plus_add_on_package') }}
              </span>
            </div>
          </UCard>
        </div>
      </div>
    </UCard>

    <!-- 对话框和弹窗保持不变 -->
    <div v-if="isOpenDialogPage">
      <!-- 未登录提示窗 -->
      <!-- not_logged_in:未登录, not_logged_in_tip:请先登录, login:登录 -->
      <DialogCard
        :title="t('account.not_logged_in')"
        :message="t('account.not_logged_in_tip')"
        :button="{ label: t('account.login'), color: 'primary', toUri: '/' + locale + '/login', skipToUri: skipToUri }"
        :open-dialog-card="openDialogCard"
        @close-dialog-page-event="closeDialogPage"
      />
    </div>
    <div v-if="isOpenFreeTrialPage">
      <!-- 免费试用弹窗页面 -->
      <FreeTrialCard :open-free-trial-card="openFreeTrialCard" @close-free-trial-page-event="closeFreeTrialPage" @refresh-page="handleRefreshPage" />
    </div>

    <!-- 取消您的订阅 -->
    <UModal
      :open="subscriptionCancelConfirm"
      :title="t('account.unsubscribe_dialog_title')"
      :close="{ onClick: () => subscriptionCancelConfirmClose() }"
      :ui="{ footer: 'justify-end' }"
    >
      <template #body>
        <p class="text-md text-neutral-600">
          <!-- 试用中，并且已订阅中，但试用期未结束还没正式扣费，显示：您订阅的 Plus 会员将在试用期结束后于 YYYY-MM-DD HH:mm 自动扣费生效。  -->
          <span v-if="memberProduct.member_product_status == 1 && memberProduct.subscription_status == 2" class="text-neutral-800 dark:text-neutral-300">
            {{ t('account.subscription_billing_time_tip1') }}
            <span class="px-1">{{ dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm') }}</span>
            {{ t('account.subscription_billing_time_tip2') }}
          </span>
          <!-- 您当前订阅的套餐服务将于 XXX 到期，取消订阅后您仍可在服务期结束前继续使用。 -->
          <span v-else class="text-neutral-800 dark:text-neutral-300">
            {{ t('account.unsubscribe_dialog_content_1') }} {{ dayjs(memberProduct.service_expire_time).format('YYYY-MM-DD HH:mm') }} {{ t('account.unsubscribe_dialog_content_2') }}
          </span>
          <br />
          <br />
          <!-- 如果您确认要取消订阅，请点击【取消订阅】按钮。 -->
          {{ t('account.unsubscribe_dialog_content_3') }}
          <br />
          <br />
          <!-- 如果您想继续保留当前订阅，请点击【返回】按钮。 -->
          {{ t('account.unsubscribe_dialog_content_4') }}
        </p>
      </template>
      <template #footer>
        <!-- 取消订阅 -->
        <UButton color="info" class="rounded-sm" @click="subscriptionCancel">
          {{ t('account.unsubscribe') }}
        </UButton>
        <!-- 返回 -->
        <UButton
          color="neutral"
          variant="outline"
          class="ml-2 rounded-sm"
          @click="subscriptionCancelConfirmClose"
        >
          {{ t('account.unsubscribe_dialog_btn_back') }}
        </UButton>
      </template>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import DialogCard from './DialogCard.vue'
import PlusActiveIcon from '~/components/icons/PlusActiveIcon.vue'
import PlusInActiveIcon from '~/components/icons/PlusInActiveIcon.vue'
import CopyButton from '~/components/CopyButton/index.vue'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import type { HeadersTokenType, MemberProductType } from '@/api/login/types'
import { memberProductSubscriptionCancel } from '@/api/login'
import avatar from '@/assets/images/avatar.svg'
import avatar_white from '@/assets/images/avatar_white.svg'
import FreeTrialCard from '~/pages/components/FreeTrialCard.vue'
import { storeToRefs } from 'pinia'
import { getProductByPositionNoApi } from '@/api/product'

const authStore = useAuthStoreWithOut()
const { member, memberProduct, memberProductTokenUsage } = storeToRefs(authStore)

const toast = useToast()

const { t, locale } = useI18n()

const colorMode = useColorMode() // dark or white

/** 按颜色模式显示不同的头像图片 */
const avatar_svg = computed(() => {
  return colorMode.value === 'dark' ? avatar_white : avatar
})

const showContent = ref(true) // 是否显示套餐额度使用情况（默认开启）
const isLoading = ref(true) // 添加加载状态

/**
 *  父组件通过 props 向子组件传递数据
 */
const props = defineProps({
  skipToUri: String
})

const product = ref({
  platform: '',
  position_no: ''
})

/// /弹出购买页专用部分/////////////////////////////////////////////////
/**
 * 是否打开对话框页面
 */
const isOpenDialogPage = ref(false)

/**
 * 父组件传递给子组件的消息
 */
const openDialogCard = ref(isOpenDialogPage)

/**
 * 接收子组件传递的事件，22
 * @param data
 */
const closeDialogPage = (data: boolean) => {
  isOpenDialogPage.value = data
}

/**
 * 是否按年购买
 */
const isYearly = ref(false)

// 跳转到充值资源包页面
function goToRecharge(type: string) {
  // 翻译文件
  navigateTo({ path: '/' + locale.value + '/' + 'resource', query: { type: type } })
}

/**
 * Plus会员套餐的购买/续费按钮
 */
function buyPlusButton() {
  // 判断会员是否已登录
  const authStore = useAuthStoreWithOut()

  if (authStore.getToken && authStore.getToken !== '') {
    const router = useRouter()
    router.push({
      path: '/' + locale.value + '/buy',
      query: {
        platform: product.value.platform,
        position_no: product.value.position_no,
        buy_mode: isYearly.value ? 'yearly' : 'monthly'
      }
    })
  }
  else {
    // 打开未登录提示框
    isOpenDialogPage.value = true
  }
}

/**
 * 订阅取消
 */
const subscriptionCancelConfirm = ref(false)
function subscriptionCancelConfirmOpen() {
  subscriptionCancelConfirm.value = true
}
function subscriptionCancelConfirmClose() {
  subscriptionCancelConfirm.value = false
}
function subscriptionCancel() {
  // 关闭弹框
  subscriptionCancelConfirmClose()
  // 判断会员是否已登录
  const authStore = useAuthStoreWithOut()
  if (authStore.getToken && authStore.getToken !== '') {
    // 处理取消逻辑
    const headers: HeadersTokenType = {
      token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
    }
    const paramData: MemberProductType = {
      platform: 'ztsl'
    }
    memberProductSubscriptionCancel(headers, paramData.platform)
      .then((res) => {
        if (res.code === 200) {
          toast.add({
            title: t('account.unsubscribe_success_info'), // 您已成功取消订阅
            duration: 3000,
            color: 'primary'
          })

          setTimeout(() => {
            // memberProductInfo();
            authStore.setMemberProductInfo()
          }, 3000)
        }
        else {
          toast.add({
            title: res.message,
            color: 'red'
          })
        }
      })
      .catch(() => {
        toast.add({
          title: t('account.unsubscribe_failed_info'), // 取消订阅失败，请稍后再试
          color: 'red'
        })
      })
  }
  else {
    // 打开未登录提示框
    isOpenDialogPage.value = true
  }
}

/**
 * 是否打开申请试用免费产品确认页面
 */
const isOpenFreeTrialPage = ref(false)

/**
 * 父组件传递给子组件的消息
 */
const openFreeTrialCard = ref(isOpenFreeTrialPage)

/**
 * 接收子组件传递的事件，关闭免费试用页面
 * @param data
 */
const closeFreeTrialPage = (data: boolean) => {
  isOpenFreeTrialPage.value = data
}

/**
 * 免费试用 Plus 会员 按钮
 */
function freeTrialButton() {
  // 判断会员是否已登录
  const authStore = useAuthStoreWithOut()
  if (authStore.getToken && authStore.getToken !== '' && isOpenFreeTrialPage.value === false) {
    isOpenFreeTrialPage.value = true
  }
  else {
    // 打开未登录提示框
    isOpenDialogPage.value = true
  }
}

function handleRefreshPage() {
  // 刷新页面的逻辑
  window.location.reload()
}

onBeforeMount(async () => {
  try {
    // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
    const paramData = {
      platform: 'ztsl',
      position_no: 'ztsl-plus'
    }

    product.value = (await getProductByPositionNoApi(paramData)).data

    // 获取会员已购买产品信息
    await authStore.setMemberProductInfo()

    // 并行执行获取会员资源包账户信息
    try {
      await authStore.setMemberResourceAccount()
    }
    catch (error) {
      console.error('获取会员资源信息失败:', error)
    }

    // 处理显示套餐额度使用情况
    showContent.value = [1, 3].includes(memberProduct.value.member_product_status)
  }
  catch (error) {
    console.error('获取会员信息失败:', error)
  }
  finally {
    // 无论成功失败，都关闭加载状态
    isLoading.value = false
  }
})

const router = useRouter()
const openCouponCodeRedeem = () => {
  if (authStore.getToken && authStore.getToken !== '') {
    router.push({
      // path: '/' + locale.value + '/coupon_code_redeem',
      path: '/' + locale.value + '/coupon_code_list'
    })
  }
  else {
    // 打开未登录提示框
    isOpenDialogPage.value = true
  }
}
</script>

<style scoped>
.member-product-text {
  align-items: center;
  font-size: 18px !important;
  margin-left: 2px !important;
  flex-wrap: wrap; /* 移动端换行 */
  svg {
    width: 22px;
    height: 22px;
    margin: 0 4px;
  }
}

.plus_text {
  background: -webkit-linear-gradient(265deg, #ff902d 30%, #f32424);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.credit-limit {
  display: inline-block;
  width: 100%;
}
</style>
