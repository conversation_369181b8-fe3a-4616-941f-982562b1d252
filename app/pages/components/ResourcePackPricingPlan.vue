<template>
  <div class="mt-2 min-h-screen py-8">
    <div v-if="!isDataLoading" class="mx-auto max-w-7xl px-4">
      <!-- 服务资源加量包 -->
      <h1 class="mb-12 text-center text-3xl font-bold">
        {{ t('resource.title') }}
      </h1>
      <div class="mb-10">
        <!-- 资源类型 -->
        <h2 class="mb-2 text-base font-bold">
          {{ t('resource.resource_ype') }}
        </h2>
        <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-2 xl:grid-cols-3">
          <label
            v-for="(item, index) in resource_type_items"
            :key="index"
            class="flex cursor-pointer items-center rounded-lg border-1 border-gray-200 bg-sky-50/40 p-4 transition-all duration-300 dark:border-white"
            :class="{ 'active-info bg-sky-100/60 dark:bg-sky-600': resource_pack_type === item.id }"
            @click="onChangeResourceType(item.id)"
          >
            <input
              v-model="resource_pack_type"
              type="radio"
              :value="item.id"
              class="hidden"
            />
            <div class="flex items-center">
              <UChip
                v-if="item.id === 'pdf_plus'"
                :text="'Plus'"
                color="warning"
                size="sm"
                inset
                :ui="{ base: 'rounded-xs p-[1px] h-[10px]' }"
              >
                <img src="/assets/images/document/bi--file-earmark-pdf.svg" class="size-8 sm:size-8 md:size-8 lg:size-10 xl:size-10" />
              </UChip>
              <UIcon
                v-else
                :name="item.icon"
                class="size-8 flex-shrink-0 sm:size-8 md:size-8 lg:size-10 xl:size-10 dark:bg-gray-100"
                :style="{ color: item.iconColor }"
              />
              <span class="ml-2 text-base">{{ item.label }}</span>
            </div>
          </label>
        </div>
        <!-- <div class="mb-10">
          <p v-for="(item, index) in resource_type_items" :key="index">
            <span v-if="resource_pack_type === item.id" class="text-sm">{{ item.description }}</span>
          </p>
        </div> -->
      </div>

      <div>
        <!-- 资源规格 -->
        <h2 class="mb-2 text-base font-bold">
          {{ t('resource.resource_specifications') }}
        </h2>
        <div class="mb-10 grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-2 xl:grid-cols-3">
          <!-- Loading skeleton for resource specs 骨架 -->
          <template v-if="isDataLoading">
            <div v-for="n in 3" :key="n" class="rounded-lg border-1 border-gray-200 bg-sky-50/40 p-4">
              <div class="space-y-3 sm:space-y-4">
                <div class="flex items-center">
                  <div class="mr-2 size-8 animate-pulse rounded bg-gray-200 sm:size-8 md:size-8 lg:size-10 xl:size-10" />
                  <div class="h-6 flex-1 animate-pulse rounded bg-gray-200" />
                </div>
                <div class="mt-6 mb-3 ml-8 sm:mt-8 sm:mb-4 sm:ml-10">
                  <div class="mb-2 h-8 w-24 animate-pulse rounded bg-gray-200" />
                  <div class="h-4 w-16 animate-pulse rounded bg-gray-200" />
                </div>
                <div class="ml-8 sm:ml-10">
                  <div class="h-4 w-20 animate-pulse rounded bg-gray-200" />
                </div>
              </div>
            </div>
          </template>

          <!-- Actual resource spec cards -->
          <template v-else>
            <!-- 有数据时显示资源规格卡片 -->
            <template v-if="currentPlans.length > 0">
              <UCard
                v-for="(plan, index) in currentPlans"
                :key="index"
                class="border-1 border-gray-200 bg-sky-50/40 transition-shadow duration-300 hover:shadow-md"
                :class="{ 'active-info bg-sky-100/60 dark:bg-sky-600': selectedPlan === plan.id }"
                @click="selectPlan(plan.id)"
              >
                <div class="space-y-4">
                  <div class="flex items-center">
                    <UIcon
                      v-if="resource_pack_type === 'general_token'"
                      name="i-ic-sharp-generating-tokens"
                      style="color: #ff8c00"
                      class="mr-2 size-12 sm:size-12 md:size-12 lg:size-14 xl:size-14 dark:bg-gray-100"
                    />
                    <UChip
                      v-if="resource_pack_type === 'pdf_plus'"
                      :text="'Plus'"
                      color="warning"
                      size="xl"
                      inset
                      :ui="{ base: 'rounded-xs p-[2px] h-[12px]' }"
                    >
                      <img src="/assets/images/document/bi--file-earmark-pdf.svg" class="mr-2 size-12 sm:size-12 md:size-12 lg:size-14 xl:size-14 dark:bg-gray-100" />
                    </UChip>
                    <span class="text-2xl font-medium">{{ plan.quota }}</span>
                    <span class="ml-1 text-xl font-medium">{{ plan.type === 'general_token' ? 'TQ' : t('resource.pages') }}</span>
                    <span class="ml-2 text-sm">{{ t('resource.add_on_package') }}</span>
                  </div>
                  <div class="ml-16 text-2xl font-bold">
                    ¥{{ formatCurrency(plan.price) }}
                  </div>

                  <div v-if="selectedPlan === plan.id" class="flex items-center space-x-3">
                    <span class="ml-16 text-sm">{{ t('resource.quantity_to_purchase') }}</span>
                    <div class="flex items-center">
                      <UButton
                        :disabled="buy_count <= 1"
                        icon="i-heroicons-minus-small-20-solid"
                        class="!rounded-button flex h-4 w-4 items-center justify-center bg-gray-300 text-neutral-500 hover:bg-gray-300 disabled:bg-gray-200"
                        @click.stop="decreaseQuantity(plan.id)"
                      />
                      <UInput
                        v-model="buy_count"
                        variant="none"
                        style="text-align: center"
                        class="w-10 text-center"
                        @click.stop
                      />
                      <UButton
                        :disabled="buy_count >= 10"
                        icon="i-heroicons-plus-small-20-solid"
                        class="!rounded-button flex h-4 w-4 items-center justify-center bg-gray-300 text-neutral-500 hover:bg-gray-300 disabled:bg-gray-200"
                        @click.stop="increaseQuantity(plan.id)"
                      />
                    </div>
                  </div>
                </div>
              </UCard>
            </template>

            <!-- 无数据时占位 -->
            <template v-else>
              <UCard class="border-1 border-gray-200 bg-sky-50/40 transition-shadow duration-300 hover:shadow-md">
                <div class="flex h-32 items-center justify-center">
                  <UIcon name="i-logos-coffeescript" class="mr-2 size-8 sm:size-8 md:size-8 lg:size-10 xl:size-10" />
                  <span class="inline-flex text-lg">{{ t('resource.no_data') }}</span>
                </div>
              </UCard>
            </template>
          </template>
        </div>
      </div>

      <!-- 支付方式 -->
      <div v-if="currentPlans.length > 0">
        <div class="mb-10">
          <!-- 支付方式 -->
          <h2 class="mb-2 text-base font-bold">
            {{ t('resource.payment_methods') }}
          </h2>
          <div class="grid grid-cols-1 gap-3 sm:mb-10 sm:grid-cols-2 md:grid-cols-4 md:gap-6 lg:grid-cols-6 xl:grid-cols-6">
            <label
              v-for="(pay, index) in payWays"
              :key="index"
              class="flex cursor-pointer items-center rounded-lg border-1 border-gray-200 bg-sky-50/40 p-4 transition-all duration-300"
              :class="{ 'active-info bg-sky-100/60 dark:bg-sky-600': payWay === pay.code }"
            >
              <input
                v-model="payWay"
                type="radio"
                :value="pay.code"
                class="hidden"
              />
              <div class="flex items-center">
                <UIcon
                  :name="pay.icon"
                  class="size-8 flex-shrink-0 sm:size-8 md:size-8 lg:size-10 xl:size-10 dark:bg-gray-100"
                  :style="{ color: pay.iconColor }"
                  :class="pay.iconColor"
                />
                <span class="ml-2 text-base">{{ pay.label }}</span>
              </div>
            </label>
          </div>
        </div>
        <!-- 底部按钮 -->
        <div class="mt-2 text-center">
          <!-- 支付金额 -->
          <div class="mb-6 text-xl font-bold">
            {{ t('resource.amount_to_pay') }}：
            <span class="text-2xl text-red-500">¥{{ formatCurrency(totalPrice) }}</span>
          </div>
          <!-- 立即购买 -->
          <UButton
            color="primary"
            variant="solid"
            class="w-50 rounded-md"
            size="xl"
            block
            :loading="loading || isDataLoading"
            :disabled="isDataLoading"
            @click="payCreate()"
          >
            {{ t('resource.buy_now') }}
          </UButton>
          <span />
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <!-- <USeparator class="mt-20" /> -->

    <!-- 资源包常见问题 -->
    <ResourcePackFAQ />

    <div v-if="isOpenDialogPage">
      <!-- 未登录弹窗提示 -->
      <DialogCard
        :title="t('pricing.dialog.title')"
        :message="t('pricing.dialog.message')"
        :button="{ label: t('pricing.dialog.button'), color: 'primary', toUri: '/' + locale + '/login', skipToUri: '/' + locale + '/resource' }"
        :open-dialog-card="openDialogCard"
        @close-dialog-page-event="closeDialogPage"
      />
    </div>

    <ProductPay
      ref="payModel"
      :pay-way="payInfo?.pay_way"
      :pay-way-name="payWayName"
      :pay-amount="payAmount"
      :pay-url="payInfo?.pay_info"
      :order-no="payInfo?.order_no"
      :expire-time="payInfo?.expire_time"
    />
  </div>
</template>

<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import { getResourceByResourceApi } from '@/api/resource_pack'
import { createTradeOrder } from '@/api/product'
import type { CreatePayOrderType, GetResourcePackType } from '@/api/types'
import DialogCard from './DialogCard.vue'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import ProductPay from '@/components/ProductPay.vue'
import ResourcePackFAQ from './ResourcePackFAQ.vue'

const { t, locale } = useI18n()

let stripe_payment_min_amount = 0 // Stripe支付的最小金额(在 nuxt.config.ts 中定义)

// 资源包类型选项
const resource_type_items = [
  {
    id: 'general_token',
    label: t('resource.translation_quota_add_on_package'), // 翻译额度（TQ）加量包
    icon: 'i-ic-sharp-generating-tokens',
    iconColor: '#ff8c00'
  },
  {
    id: 'pdf_plus',
    label: t('resource.pdf_plus_add_on_package'), // PDF Plus 加量包
    icon: 'i-fluent-document-pdf-32-regular',
    iconColor: '#dd2025'
  }
]

// 支付方式
const payWays = computed(() => {
  const payWayList = [
    {
      label: t('buy.pay_way_items.wechat'), // 微信支付
      icon: 'i-ri-wechat-pay-fill',
      code: 'wechat',
      support_subscription: false,
      className: 'wechat-pay-icon',
      iconColor: '#07C160'
    },
    {
      label: t('buy.pay_way_items.alipay'), // 支付宝
      icon: 'i-bi-alipay',
      code: 'alipay',
      support_subscription: false,
      className: 'alipay-pay-icon',
      iconColor: '#1677FF'
    }
  ]
  if (totalPrice.value >= stripe_payment_min_amount) {
    // 只有支付金额大于或等于最小限额时，才显示Stripe支付方式
    payWayList.push({
      label: t('buy.pay_way_items.stripe'), // Stripe
      icon: 'bi:stripe',
      code: 'stripe',
      support_subscription: true,
      className: 'stripe-pay-icon',
      iconColor: '#635BFF'
    })
  }
  return payWayList
})

const payWay = ref('wechat')
const currentPlans = ref([])
// 根据类型拆分的产品列表
const generalTokenPlans = ref([])
const pdfPlusPlans = ref([])
const allPlans = ref([]) // 保存所有产品数据
const selectedPlan = ref('')
const buy_count = ref(1)
const payInfo = ref(null)
const payWayName = ref('')
const payAmount = ref(0)
const payModel = ref()
const totalPrice = ref(0.0)

const route = useRoute()
const loading = ref(false)
const isDataLoading = ref(true)

/**
 * 是否打开未登录提示对话框页面
 */
const isOpenDialogPage = ref(false)

/**
 * 父组件传递给子组件的消息
 */
const openDialogCard = ref(isOpenDialogPage)

/**
 * 接收子组件传递的事件，关闭对话框页面
 * @param data
 */
const closeDialogPage = (data: boolean) => {
  isOpenDialogPage.value = data
}

// 资源包类型(这里不设置默认值，在onMounted中处理)
const resource_pack_type = ref('')
// 金额精度处理，避免小数点问题： 3.1 * 3 = 0.9299999999999999
function calculateTotal(price, quantity) {
  // 转为"分"进行计算，避免小数
  const priceInCents = Math.round(price * 100)
  const totalInCents = priceInCents * quantity
  return totalInCents / 100 // 转回元
}

// 未实现
// 页面初始化时候获取数据
onBeforeMount(async () => {
  isDataLoading.value = true
  // Stripe支付的最小金额(在 nuxt.config.ts 中定义)
  stripe_payment_min_amount = useRuntimeConfig().public.stripePaymentMinAmount
  // console.log('stripe_payment_min_amount:', stripe_payment_min_amount);

  // console.log('resource_pack_type', route.query.type);
  if (route.query.type === 'pdf_plus') {
    resource_pack_type.value = 'pdf_plus'
  }
  else if (route.query.type === 'general_token') {
    resource_pack_type.value = 'general_token'
  }
  else {
    resource_pack_type.value = 'general_token' // 默认值
  }
  const paramData: GetResourcePackType = {
    platform: 'ztsl',
    resource_pack_type: resource_pack_type.value
  }
  const product = (await getResourceByResourceApi(paramData)).data
  updateProductList(product)
})

// 切换资源包类型
async function onChangeResourceType(resource_type: string) {
  isDataLoading.value = true // 开始加载
  resource_pack_type.value = resource_type

  // 如果已经有全部数据，直接从本地筛选，避免重复请求
  if (allPlans.value.length > 0) {
    // 根据选择的类型设置当前显示的计划
    if (resource_type === 'general_token') {
      currentPlans.value = generalTokenPlans.value
    }
    else if (resource_type === 'pdf_plus') {
      currentPlans.value = pdfPlusPlans.value
    }

    // 重置选择状态
    if (currentPlans.value.length > 0) {
      selectedPlan.value = currentPlans.value[0].id
      buy_count.value = 1
      totalPrice.value = currentPlans.value[0].price * buy_count.value
    }

    isDataLoading.value = false
    return
  }

  // 如果没有数据，则从API获取
  const paramData: GetResourcePackType = {
    platform: 'ztsl',
    resource_pack_type: resource_type
  }
  try {
    const response = await getResourceByResourceApi(paramData)
    const product = response.data
    updateProductList(product)
  }
  catch (error) {
    console.error('获取资源包数据时出错:', error)
  }
  finally {
    isDataLoading.value = false // 加载完成
  }
}
/** 切换选择套餐卡 */
const selectPlan = (planId: string) => {
  // console.log('selectPlan', planId);
  if (selectedPlan.value !== planId) {
    buy_count.value = 1 // 如果套餐有变化重置购买数量
  }
  if (buy_count.value < 1) {
    buy_count.value = 1 // 保证购买数量不小于1
  }
  if (buy_count.value > 10) {
    buy_count.value = 10 // 保证购买数量不大于10
  }
  selectedPlan.value = planId // 更新选中套餐
  payWay.value = 'wechat'
  totalPrice.value = calculateTotal(currentPlans.value.find(item => item.id === planId).price, buy_count.value)
}

// 加号
const increaseQuantity = (planId: string) => {
  buy_count.value++
  if (buy_count.value > 10) {
    buy_count.value = 10
  }
  // console.log('buy_count', totalPrice.value);

  totalPrice.value = calculateTotal(currentPlans.value.find(item => item.id === planId).price, buy_count.value)
  // console.log('buy_count', totalPrice.value);
}
// 减号
const decreaseQuantity = (planId: string) => {
  buy_count.value--
  if (buy_count.value < 1) {
    buy_count.value = 1
  }
  totalPrice.value = calculateTotal(currentPlans.value.find(item => item.id === planId).price, buy_count.value)
}
/** 页面初始化或者切换资源包类型时调用查找资源包数据 */
const updateProductList = (product: any) => {
  if (!product) {
    currentPlans.value = []
    generalTokenPlans.value = []
    pdfPlusPlans.value = []
    allPlans.value = []
    return
  }

  // 将产品数据转换为统一格式
  const formattedProducts = Object.entries(product).map(([key, item]) => ({
    code: key, // Ensure each item has a unique code
    id: item.resource_pack_no,
    name: item.name, // 免费版
    description: item.description,
    buy_count: 1,
    type: item.type,
    price: item.price,
    quota: item.quantity // 资源包的个数
  }))

  // 保存所有产品数据
  allPlans.value = formattedProducts

  // 根据类型拆分产品数据
  generalTokenPlans.value = formattedProducts.filter(item => item.type === 'general_token')
  pdfPlusPlans.value = formattedProducts.filter(item => item.type === 'pdf_plus')

  // 根据当前选择的资源包类型设置当前显示的计划
  if (resource_pack_type.value === 'general_token') {
    currentPlans.value = generalTokenPlans.value
  }
  else if (resource_pack_type.value === 'pdf_plus') {
    currentPlans.value = pdfPlusPlans.value
  }
  else {
    currentPlans.value = formattedProducts // 默认显示所有
  }

  // 默认选中第一个套餐
  if (currentPlans.value.length > 0) {
    selectedPlan.value = currentPlans.value[0].id
    // 价格
    totalPrice.value = currentPlans.value[0].price * buy_count.value
  }
  isDataLoading.value = false
  // 调试信息：打印拆分后的数据
  console.log('所有产品数据:', allPlans.value)
  console.log('翻译额度(TQ)加量包:', generalTokenPlans.value)
  console.log('PDF Plus 加量包:', pdfPlusPlans.value)
  console.log('当前显示的计划:', currentPlans.value)
}

// 辅助函数：获取指定类型的产品数据
const getProductsByType = (type: string) => {
  const typeMap = {
    general_token: generalTokenPlans.value,
    pdf_plus: pdfPlusPlans.value,
    all: allPlans.value
  }
  return typeMap[type] || []
}

// 辅助函数：获取产品数据统计信息
const getProductStats = () => {
  return {
    total: allPlans.value.length,
    generalToken: generalTokenPlans.value.length,
    pdfPlus: pdfPlusPlans.value.length,
    breakdown: {
      generalTokenProducts: generalTokenPlans.value.map(p => ({ id: p.id, name: p.name, price: p.price, quota: p.quota })),
      pdfPlusProducts: pdfPlusPlans.value.map(p => ({ id: p.id, name: p.name, price: p.price, quota: p.quota }))
    }
  }
}

/** 创建支付订单 */
const payCreate = async () => {
  if (isDataLoading.value) return // 如果正在加载数据，阻止购买
  // console.log('-->', selectedPlan, selectedPlan.value);
  if (selectedPlan.value !== selectedPlan.value) {
    // 未选择不进行购买
    return
  }
  // 下单购买逻辑
  try {
    loading.value = true
    const authStore = useAuthStoreWithOut()

    if (!authStore.getToken || authStore.getToken === '') {
      // 打开未登录提示框
      isOpenDialogPage.value = true
      return
    }

    const fromData: CreatePayOrderType = {
      platform: 'ztsl',
      platform_endpoint: 'web',
      token: authStore.getToken.slice(7), // 去掉token前缀 "bearer "
      product_no: selectedPlan.value,
      buy_count: buy_count.value,
      pay_mode: 1, // 支付模式(1:直接购买、2:订阅模式)
      buy_mode: 'perpetual', // 购买模式（weekly:按周,monthly:按月,quarterly:按季,yearly:按年, perpetual:永久）
      pay_way: payWay.value,
      pay_type: 'pcweb',
      pay_return_url: window.location.origin + '/' + locale.value + '/account',
      order_type: resource_pack_type.value
      // pay_return_url: 'https://www.baidu.com'
    }

    const res = await createTradeOrder(fromData)
    const payResp = res.data
    payInfo.value = payResp
    payAmount.value = payResp.product_price
    const targetPayWay = payWays.value.filter(item => item.code === payResp.pay_way)
    payWayName.value = targetPayWay.length > 0 ? targetPayWay[0].label : ''
    if (payResp.pay_way === 'stripe') {
      window.location.href = payResp.pay_info
    }
    else {
      payModel.value.handleOpenModel()
    }
  }
  finally {
    loading.value = false
  }
}

/** 格式化货币 */
function formatCurrency(val: string | number | bigint) {
  if (typeof val !== 'number') {
    return val
  }
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 1,
    maximumFractionDigits: 2
  }).format(val)
}
</script>

<style scoped>
@media (min-width: 1280px) {
  .pricing-card-item > :deep(.xl\:p-10) {
    padding: 2.5rem;
  }
}
.wechat-pay-icon {
  color: #00c250;
}

.alipay-pay-icon {
  color: #0f8dff;
}

.stripe-pay-icon {
  color: #675dff;
}
.active-info {
  border: 1px solid #409eff !important;
}

.pricing-card-item {
  transform: scale(0.95);
  cursor: pointer;
}
.pricing-card-item :deep(.space-y-3) > li > span {
  white-space: normal;
}

.card-highlight {
  transform: scale(0.98) translateY(-1.5%);
}
</style>
