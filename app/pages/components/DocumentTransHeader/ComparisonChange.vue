<template>
  <UButtonGroup orientation="horizontal">
    <UPopover mode="hover" arrow :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }">
      <UButton
        color="secondary"
        :variant="openComparison ? 'solid' : 'outline'"
        class="rounded-sm"
        style="padding: 0px 5px"
        @click="handleOpenComparisonChange(true)"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="38"
          height="38"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M4.616 19q-.691 0-1.153-.462T3 17.384V6.616q0-.691.463-1.153T4.615 5h14.77q.69 0 1.152.463T21 6.616v10.769q0 .69-.463 1.153T19.385 19zm7.884-1h6.885q.23 0 .423-.192t.192-.424V6.616q0-.231-.192-.424T19.385 6H12.5zm-6.615-2.5h3.73q.213 0 .357-.144t.144-.357t-.144-.356t-.356-.143H5.885q-.213 0-.357.144t-.143.357t.143.356t.357.143m0-3h3.73q.213 0 .357-.144t.144-.357t-.144-.356t-.356-.143H5.885q-.213 0-.357.144t-.143.357t.143.356t.357.143m0-3h3.73q.213 0 .357-.144t.144-.357t-.144-.356t-.356-.143H5.885q-.213 0-.357.144t-.143.357t.143.356t.357.143m8.5 6h3.73q.213 0 .357-.144t.143-.357t-.143-.356t-.357-.143h-3.73q-.213 0-.357.144t-.143.357t.143.356t.357.143m0-3h3.73q.213 0 .357-.144t.143-.357t-.143-.356t-.357-.143h-3.73q-.213 0-.357.144t-.143.357t.143.356t.357.143m0-3h3.73q.213 0 .357-.144t.143-.357t-.143-.356t-.357-.143h-3.73q-.213 0-.357.144t-.143.357t.143.356t.357.143"
          />
        </svg>
      </UButton>

      <template #content>
        <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
          {{ t('translate_type.miner_u.display_mode.original_text_comparison') }}
        </div>
      </template>
    </UPopover>
    <UPopover mode="hover" arrow :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }">
      <UButton
        color="secondary"
        :variant="!openComparison ? 'solid' : 'outline'"
        class="rounded-sm"
        @click="handleOpenComparisonChange(false)"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="28"
          height="28"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M6.25 2A2.25 2.25 0 0 0 4 4.25v15.5A2.25 2.25 0 0 0 6.25 22h11.5A2.25 2.25 0 0 0 20 19.75V4.25A2.25 2.25 0 0 0 17.75 2zM5.5 4.25a.75.75 0 0 1 .75-.75h11.5a.75.75 0 0 1 .75.75v15.5a.75.75 0 0 1-.75.75H6.25a.75.75 0 0 1-.75-.75zM7.75 6.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5zM7 16.25a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5a.75.75 0 0 1-.75-.75M7.75 11a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5z"
          />
        </svg>
      </UButton>
      <template #content>
        <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
          {{ t('translate_type.miner_u.display_mode.results_focus') }}
        </div>
      </template>
    </UPopover>
  </UButtonGroup>
</template>

<script setup lang="ts">
const { t, locale } = useI18n()

interface Props {
  initialOpenComparison?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialOpenComparison: false
})
// emit
interface EmitType {
  (e: 'openComparison', value: boolean): void
}
const emit = defineEmits<EmitType>()
const openComparison = ref(props.initialOpenComparison)

/**
 * 处理双页对照开关
 */
const handleOpenComparisonChange = (val: boolean) => {
  openComparison.value = val
  emit('openComparison', openComparison.value)
}
</script>
