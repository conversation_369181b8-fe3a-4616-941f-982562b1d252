<template>
  <UHeader :ui="{ root: 'fixed w-full', container: 'max-w-full px-4 sm:px-5 lg:px-5 py-2', left: 'lg:flex-none flex-shrink-0', center: 'flex min-w-0', right: 'flex-shrink-0' }">
    <template #left>
      <!-- <HeaderLogo /> -->
      <div class="cursor-pointer w-15" @click="() => router.push('/' + locale)">
        <div class="flex items-center">
          <img class="h-8 w-auto" src="/assets/images/logo-128.png" />
        </div>
      </div>

      <!-- 当前的文件格式 -->
      <div v-if="documentType" class="flex items-center rounded-md px-3 py-1">
        <span class="text-sm font-medium text-blue-700 dark:text-blue-300">
          <!-- XXX 文件翻译 -->
          {{ getDocumentTypeLabel(documentType) + t('document_translation.translated_text') }}
        </span>
      </div>
    </template>

    <!-- 桌面端布局 -->
    <div v-if="!isMobile" class="ml-5 flex flex-row gap-2 flex-1 min-w-0 max-w-none overflow-hidden">
      <!-- 翻译服务选择 -->
      <div class="flex items-center rounded-md bg-gray-100 p-1 pl-2 xl:pl-4 flex-shrink min-w-0 max-w-[300px] dark:bg-gray-800">
        <!-- 翻译模型 -->
        <span class="flex text-xs xl:text-sm whitespace-nowrap">{{ t('pdf_plus.translation_model') }}:</span>
        <DocumentModelSelect :translate-engine-config="translateEngine" :enabled-engines-list="enabledEnginesList" @update:translate-engine-config="onSelectionChangeTranslateEngine" />
      </div>
      <!-- 目标语言选择 -->
      <div class="flex items-center rounded-md bg-gray-100 p-1 pl-2 xl:pl-4 flex-shrink min-w-[180px] max-w-[300px] dark:bg-gray-800">
        <!-- 目标语言 -->
        <h3 class="select-title text-xs xl:text-sm whitespace-nowrap">
          {{ t('pdf_plus.target_language') }}:
        </h3>
        <div class="min-w-0 flex-1 pl-1 xl:pl-2">
          <USelectMenu
            v-model="targetLanguageZingCode"
            size="lg"
            value-key="value"
            variant="none"
            class="w-full text-sm max-w-[200px]"
            :items="targetLanguageList"
            :ui="{
              content: 'w-full',
              value: 'truncate'
            }"
            @update:model-value="onSelectionChangeTargetLanguage"
          >
            <template #item="{ item }">
              <div class="flex min-w-0 items-center">
                <span class="truncate">{{ item.label }}</span>
              </div>
            </template>
            <template #default>
              <div class="flex min-w-0 items-center">
                <span class="truncate">{{ targetLanguageList.find(item => item.value === targetLanguageZingCode)?.label || targetLanguageZingCode }}</span>
              </div>
            </template>
          </USelectMenu>
        </div>
      </div>

      <!-- 译文显示 -->
      <div v-if="showSwitchBilingual" class="flex min-w-0 max-w-[300px] items-center rounded-md bg-gray-100 p-1 pl-2 xl:pl-4 flex-shrink dark:bg-gray-800">
        <h3 class="select-title flex-shrink-0 text-xs xl:text-sm whitespace-nowrap">
          {{ t('pdf_plus.translation_mode') }}:
        </h3>
        <USelectMenu
          v-model="translationsDisplay"
          size="lg"
          value-key="value"
          variant="none"
          class="flex min-w-0 flex-1 text-xs xl:text-sm max-w-[200px]"
          :items="displayOptions"
          :search-input="false"
          :ui="{
            content: 'w-full',
            value: 'flex-1 min-w-0',
            trailing: 'flex-shrink-0 ml-auto'
          }"
          @update:model-value="onSelectionChangeTranslationsDisplay"
        >
          <template #item="{ item }">
            <div class="flex min-w-0 items-center gap-2">
              <span class="truncate">{{ item.label }}</span>
            </div>
          </template>
          <template #default>
            <div class="flex min-w-0 items-center gap-2">
              <span class="truncate">{{ displayOptions.find((item) => item.value === translationsDisplay)?.label || translationsDisplay }}</span>
            </div>
          </template>
        </USelectMenu>
      </div>

      <!-- 双页对照开关 -->
      <div class="mx-2 flex items-center">
        <ComparisonChange v-if="['miner-u'].includes(documentType)" :initial-open-comparison="initialOpenComparison" @open-comparison="handleOpenComparisonChange" />
      </div>
    </div>

    <!-- 翻译进度显示 -->
    <TranslationProgress class="mx-3 flex items-center" />

    <!-- 桌面端右边按钮区域 -->
    <template #right>
      <div v-if="!isMobile" class="flex items-center gap-3 flex-shrink-0">
        <!-- 保留打开新PDF文件按钮 -->
        <UButton
          v-if="documentType !== 'pdf-plus'"
          :label="t('document_translation.open_new_file')"
          size="xl"
          color="secondary"
          variant="outline"
          class="mr-4 cursor-pointer rounded-md"
          @click="fileOpen"
        />

        <slot name="right" />

        <!-- 返回 -->
        <UButton
          size="xl"
          class="mx-2 rounded-md"
          color="secondary"
          @click="handleTranslationNext"
        >
          {{ t('pdf_plus.back') }}
        </UButton>

        <!-- 界面语言切换 -->
        <LanguageSwitcher />
        <!-- 切换主题颜色 -->
        <UColorModeButton v-if="!isMobile" size="sm" class="ml-3 cursor-pointer" />
      </div>
    </template>

    <!-- 移动端内容区域 -->
    <template #body>
      <div v-if="isMobile" class="space-y-3 p-4">
        <!-- 翻译服务选择 -->
        <div class="flex items-center rounded-md bg-gray-100 p-1 pl-4 dark:bg-gray-800">
          <!-- 翻译模型 -->
          <span class="flex min-w-[60px] flex-shrink-0 text-sm">{{ t('pdf_plus.translation_model') }}:</span>
          <DocumentModelSelect :translate-engine-config="translateEngine" :enabled-engines-list="enabledEnginesList" @update:translate-engine-config="onSelectionChangeTranslateEngine" />
        </div>
        <!-- 目标语言选择 -->
        <div class="flex items-center rounded-md bg-gray-100 p-1 pl-4 dark:bg-gray-800">
          <!-- 目标语言 -->
          <h3 class="select-title flex-shrink-0 text-sm">
            {{ t('pdf_plus.target_language') }}:
          </h3>
          <USelectMenu
            v-model="targetLanguageZingCode"
            size="xl"
            value-key="value"
            variant="none"
            class="w-full truncate text-sm"
            :items="targetLanguageList"
            :ui="{ content: 'w-full' }"
            @update:model-value="onSelectionChangeTargetLanguage"
          />
        </div>

        <!-- 译文显示 -->
        <div v-if="showSwitchBilingual" class="flex min-w-0  pl-4 items-center rounded-md bg-gray-100 p-1 xl:pl-4 flex-shrink dark:bg-gray-800">
          <h3 class="select-title flex-shrink-0 text-sm">
            {{ t('pdf_plus.translation_mode') }}:
          </h3>
          <USelectMenu
            v-model="translationsDisplay"
            size="xl"
            value-key="value"
            variant="none"
            class="flex min-w-0 flex-1 text-sm"
            :items="displayOptions"
            :search-input="false"
            :ui="{
              content: 'w-full',
              value: 'flex-1 min-w-0',
              trailing: 'flex-shrink-0 ml-auto'
            }"
            @update:model-value="onSelectionChangeTranslationsDisplay"
          >
            <template #item="{ item }">
              <div class="flex min-w-0 items-center gap-2">
                <span class="truncate">{{ item.label }}</span>
              </div>
            </template>
            <template #default>
              <div class="flex min-w-0 items-center gap-2">
                <span class="truncate">{{ displayOptions.find((item) => item.value === translationsDisplay)?.label || translationsDisplay }}</span>
              </div>
            </template>
          </USelectMenu>
        </div>

        <div class="h-1" />

        <!-- 操作按钮 - 移动端全宽 -->
        <div class="space-y-3">
          <!-- 如果绑定了 插槽right （<template #right></template>） -->
          <div class="flex flex-col gap-3">
            <!-- 保留打开新PDF文件按钮 -->
            <UButton
              v-if="documentType !== 'pdf-plus'"
              :label="t('free_pdf_translation.open_a_new_pdf_file')"
              size="xl"
              color="secondary"
              variant="outline"
              class="cursor-pointer rounded-md"
              @click="fileOpen"
            />
            <slot name="right" />
            <!-- 返回 -->
            <UButton
              size="xl"
              class="mx-2 rounded-md"
              color="secondary"
              @click="handleTranslationNext"
            >
              {{ t('pdf_plus.back') }}
            </UButton>
          </div>
          <!-- 分割线 -->
          <USeparator class="my-6" />

          <!-- 主题切换 -->
          <ThemeSelector />
        </div>
      </div>
    </template>
  </UHeader>

  <!-- 翻译配置更新 -->
  <UpdateTransConfig ref="updateTransConfigRef" :document-type="documentType" />
</template>

<script setup lang="ts">
import TranslationProgress from '@/pages/components/TranslationProgress/index.vue'
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import UpdateTransConfig from '@/pages/file/components/UpdateTransConfig.vue'
import { useMobileDetection } from '@/composables/useMobileDetection.ts'
import ComparisonChange from './ComparisonChange.vue'

// 移动端响应式检测
const { isMobile } = useMobileDetection()
const router = useRouter()

const { usePluginWebDocumentTranslate } = usePluginTranslate()

const documentTranslateStore = useDocumentTranslateStore()
const { enabledEnginesList, translateEngine, targetLanguageZingCode, translationsDisplay, targetLanguage, targetLanguageList } = storeToRefs(documentTranslateStore)

interface Props {
  showSwitchBilingual?: boolean // 是否开启双语对照切换
  totalPages?: number // 总页数
  documentType?: 'pdf' | 'pdf-plus' | 'docx' | 'epub' | 'html' | 'txt' | 'markdown' | 'subtitle' | 'miner-u'
  initialOpenComparison?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  // 是否显示切换双语/全文按钮
  showSwitchBilingual: false,
  initialOpenComparison: false
})

const updateTransConfigRef = ref(null)
const { t, locale } = useI18n()

/**
 * 根据文档类型使用 显示模式
 * pdf免费、字幕（subtitle），不支持切换 双语对照/全文模式
 * 注意要使用计算属性
 */
const getTranslationsDisplay = computed(() => {
  if (['pdf', 'subtitle', 'docx'].includes(props.documentType)) {
    return 'fulltext'
  }
  return translationsDisplay.value
})

/**
 * 获取文档类型标签
 */
const getDocumentTypeLabel = (type: string) => {
  if (type === 'pdf-plus') return t('document_translation.pdf_plus.label')
  if (type === 'miner-u') return t('document_translation.miner_u.label')
  return t(`document_translation.${type}.label`)
}

// 译文显示选项
const displayOptions = ref([
  // 双语对照（灰色背景）/ 双语对照翻译
  { value: 'bilingual', label: t(`${props.documentType === 'pdf-plus' ? 'pdf_plus.bilingual_translation' : 'document_translation.bilingual_translation'}`) },
  // 全文翻译（仅译文）
  { value: 'fulltext', label: t(`${props.documentType === 'pdf-plus' ? 'pdf_plus.fulltext_translation' : 'document_translation.fulltext_translation'}`) }
])

// emit
interface EmitType {
  (e: 'download' | 'fileOpen'): void
  (e: 'openComparison', value: boolean): void
}

const emit = defineEmits<EmitType>()

const fileOpen = () => {
  /**
   * 重新打开文档需要取消一次翻译，防止出现未翻译的情况
   */
  usePluginWebDocumentTranslate({
    type: 'cancelCurrentPageTranslate',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: getTranslationsDisplay.value,
    documentType: props.documentType
  })

  emit('fileOpen')
}

// 返回
async function handleTranslationNext() {
  // 如果是 MinerU 文档，返回 MinerU 文档列表
  if (props.documentType === 'miner-u') {
    navigateTo({ path: '/' + locale.value + '/file/miner-u' })
    return
  }
  // 如果是 PDF 文档，返回 PDF 文档列表
  else if (props.documentType === 'pdf-plus') {
    navigateTo({ path: '/' + locale.value + '/pdf-plus' })
    return
  }
  // 跳转免费文档入口
  else {
    navigateTo({ path: '/' + locale.value + '/file' })
  }
}

/**
 * 处理双页对照开关
 */
const handleOpenComparisonChange = (val: boolean) => {
  emit('openComparison', val)
}

/**
 * 切换译文显示模式
 */
const onSelectionChangeTranslationsDisplay = () => {
  updateTransConfigRef.value.onSelectionChangeTranslationsDisplay()
}

/**
 * 切换翻译引擎
 */
const onSelectionChangeTranslateEngine = (data) => {
  updateTransConfigRef.value.onSelectionChangeTranslateEngine(data)
}

/**
 * 切换目标语言
 */
const onSelectionChangeTargetLanguage = () => {
  updateTransConfigRef.value.onSelectionChangeTargetLanguage()
}

// 离开组件的时候取消翻译
onBeforeUnmount(() => {
  // 组件卸载之前必须要取消翻译否则会翻译整个网站
  usePluginWebDocumentTranslate({
    type: 'cancelCurrentPageTranslate',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: getTranslationsDisplay.value,
    documentType: props.documentType
  })
})
</script>
