<template>
  <div>
    <HeroBackground
      class="absolute -top-px w-full shrink-0 text-(--ui-color-primary-300) transition-all"
      :class="[isLoading ? 'animate-pulse' : appear ? '' : 'opacity-0', appeared ? 'duration-[400ms]' : 'duration-1000']"
    />
    <!-- home.hero.title: 精挑翻译: AI 择优翻译 开创者 -->
    <!-- home.hero.description: 可以免费使用的浏览器翻译插件，支持 AI 择优翻译、双语对照网页翻译、文本翻译、段落翻译、PDF文档翻译等功能。 -->
    <UPageHero
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-3xl sm:text-3xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #title>
        <span>
          {{ t('common.site_name') }}
        </span>
        <span class="ml-1">: {{ t('home.hero.title') }}</span>
      </template>
      <template #description>
        <span>{{ t('home.hero.description_1') }}</span>
        <br />
        <span>{{ t('home.hero.description_2') }}</span>
      </template>
      <template #links>
        <!-- 安装扩展模块 -->
        <InstallationExpansionModules />
      </template>
      <div class="landing-grid absolute inset-0 z-[-1] [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]" />
    </UPageHero>

    <!-- 应用场景 :title="t('home.scenarios.title')" -->
    <!-- :class="'py-4 sm:py-12 md:py-8 relative'" :ui="{ container: 'mx-auto max-w-screen-2xl gap-4 sm:gap-y-8 flex flex-col' }" -->
    <UPageSection :ui="{ container: 'mx-auto max-w-[1440px] gap-4 sm:gap-y-8 flex flex-col py-8 sm:py-8 md:py-8 lg:py-8 xl:py-8' }">
      <UTabs
        :items="scenarios2"
        :unmount-on-hide="false"
        variant="pill"
        :orientation="isMobile ? 'horizontal' : 'vertical'"
        :ui="{
          root: isMobile ? 'relative flex flex-col gap-2' : 'relative flex gap-4',
          // 注意要添加上 items-stretch 否则tabs 导航栏不对齐
          list: isMobile ? 'my-0 w-full justify-start overflow-x-auto scrollbar-hide flex-nowrap' : 'my-0 h-full w-90 justify-start items-stretch',
          indicator: 'absolute transition-[translate,width] duration-200 bg-(--ui-bg)',
          trigger: [
            'group relative items-center inline-flex shrink-0 min-w-0 data-[state=inactive]:text-(--ui-text-muted) hover:data-[state=inactive]:not-disabled:text-(--ui-text) font-medium rounded-[calc(var(--ui-radius)*1.5)] disabled:cursor-not-allowed disabled:opacity-75 focus:outline-hidden',
            'transition-colors',
            isMobile ? 'min-w-max p-3' : 'gap-3'
          ],
          label: isMobile ? 'text-center text-sm font-medium whitespace-normal' : 'text-left text-base font-medium whitespace-normal',
          content: isMobile ? '' : 'shadow-[0_0_8px_0_#0003] rounded-lg border-gray-400 dark:border-gray-400'
        }"
      >
        <template #leading="{ item, index }">
          <span
            :class="
              isMobile
                ? 'flex size-8 items-center justify-center rounded-sm dark:bg-neutral-50'
                : 'flex size-8 items-center justify-center rounded-sm sm:size-8 md:size-10 lg:size-12 xl:size-14 dark:bg-neutral-50'
            "
          >
            <!-- tab 中的图标 -->
            <!-- 文档翻译 -->
            <img
              v-if="item.slot === 'scenarios_document'"
              src="../assets/images/pdf-2.svg"
              :class="[
                isMobile
                  ? 'size-6 flex-shrink-0 text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
                  : 'size-6 flex-shrink-0 text-neutral-900 sm:size-6 md:size-8 lg:size-10 xl:size-12 dark:bg-gray-100 dark:text-neutral-100',
                'text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
              ]"
            />
            <!-- 资讯获取，X 图标来源： https://uxwing.com/amazon-icon/ -->
            <img
              v-else-if="item.slot === 'scenarios_information'"
              src="../assets/images/logos/twitter-x-icon.svg"
              :class="[
                isMobile
                  ? 'size-6 flex-shrink-0 text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
                  : 'size-6 flex-shrink-0 text-neutral-900 sm:size-6 md:size-8 lg:size-10 xl:size-12 dark:bg-gray-100 dark:text-neutral-100',
                'text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
              ]"
            />
            <!-- 国际贸易，亚马逊 图标来源： https://uxwing.com/amazon-icon/ -->
            <img
              v-else-if="item.slot === 'scenarios_international_trade'"
              src="../assets/images/logos/amazon-icon.svg"
              :class="[
                isMobile
                  ? 'size-6 flex-shrink-0 text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
                  : 'size-6 flex-shrink-0 text-neutral-900 sm:size-6 md:size-8 lg:size-10 xl:size-12 dark:bg-gray-100 dark:text-neutral-100',
                'text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
              ]"
            />
            <UIcon
              v-else
              :name="item.icon"
              :class="[
                isMobile
                  ? 'size-6 flex-shrink-0 text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
                  : 'size-6 flex-shrink-0 text-neutral-900 sm:size-6 md:size-8 lg:size-10 xl:size-12 dark:bg-gray-100 dark:text-neutral-100',
                'text-neutral-900 dark:bg-gray-100 dark:text-neutral-100'
              ]"
            />
          </span>
        </template>
        <template #default="{ item }">
          <!-- tab 中的文本 -->
          <span :class="isMobile ? 'text-center' : 'text-left'">
            <span v-if="!isMobile" class="text-sm font-bold text-neutral-800 sm:text-sm md:text-base lg:text-base xl:text-lg dark:text-neutral-100 text-(--ui-primary)-600 dark:text-(--ui-primary)-400">
              <!-- PC 端，显示图标，移动端不显示 -->
              {{ item.label }}
            </span>
            <br v-if="!isMobile" />
            <span
              v-if="!isMobile"
              class="text-xs font-normal text-neutral-500 sm:text-xs md:text-xs lg:text-xs xl:text-sm dark:text-neutral-200"
              :class="['text-(--ui-primary)-500 dark:text-(--ui-primary)-300']"
            >
              {{ item.content }}
            </span>
          </span>
        </template>

        <!-- 选中 tab 的主区或内容: 资讯浏览 -->
        <template #scenarios_information>
          <div v-if="isMobile" class="mt-2 mb-6 text-center">
            <p class="text-lg font-semibold text-gray-800 dark:text-gray-100">
              {{ scenarios2.find((item) => item.slot === 'scenarios_information')?.label }}
            </p>
            <p class="text-base text-gray-600 dark:text-gray-200">
              {{ scenarios2.find((item) => item.slot === 'scenarios_information')?.content }}
            </p>
          </div>
          <!-- <video
            :src="scenarios_information_video"
            controls
            muted
            autoplay
            loop
            playsinline
            preload="none"
            class="w-full"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"
          ></video> -->
          <img
            :src="scenarios_information_video"
            class="w-full shadow-[0_0_8px_0_#0003]"
            draggable="false"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"
          />
        </template>
        <!-- 选中 tab 的主区或内容: 社交互动 -->
        <template #scenarios_social>
          <div v-if="isMobile" class="mt-2 mb-6 text-center">
            <p class="text-lg font-semibold text-gray-800 dark:text-gray-100">
              {{ scenarios2.find((item) => item.slot === 'scenarios_social')?.label }}
            </p>
            <p class="text-base text-gray-600 dark:text-gray-200">
              {{ scenarios2.find((item) => item.slot === 'scenarios_social')?.content }}
            </p>
          </div>
          <img
            :src="scenarios_social_video"
            class="w-full shadow-[0_0_8px_0_#0003]"
            draggable="false"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"
          />
          <!-- <video :src="scenarios_social_video" controls muted autoplay loop playsinline preload="none" class="w-full" style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"></video> -->
        </template>
        <!-- 选中 tab 的主区或内容: AI 绘画 -->
        <template #scenarios_document>
          <!-- <img :src="scenarios_document_video" class="w-full" draggable="false" style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px;" /> -->
          <div v-if="isMobile" class="mt-2 mb-6 text-center">
            <p class="text-lg font-semibold text-gray-800 dark:text-gray-100">
              {{ scenarios2.find((item) => item.slot === 'scenarios_document')?.label }}
            </p>
            <p class="text-base text-gray-600 dark:text-gray-200">
              {{ scenarios2.find((item) => item.slot === 'scenarios_document')?.content }}
            </p>
          </div>
          <!-- <video
            :src="scenarios_document_video"
            controls
            muted
            autoplay
            loop
            playsinline
            preload="none"
            class="w-full"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"
          ></video> -->
          <img
            :src="scenarios_document_video"
            class="w-full shadow-[0_0_8px_0_#0003]"
            draggable="false"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"
          />
        </template>
        <!-- 选中 tab 的主区或内容: 学习与研究 -->
        <template #scenarios_study>
          <div v-if="isMobile" class="mt-2 mb-6 text-center">
            <p class="text-lg font-semibold text-gray-800 dark:text-gray-100">
              {{ scenarios2.find((item) => item.slot === 'scenarios_study')?.label }}
            </p>
            <p class="text-base text-gray-600 dark:text-gray-200">
              {{ scenarios2.find((item) => item.slot === 'scenarios_study')?.content }}
            </p>
          </div>
          <img
            :src="scenarios_study_video"
            class="w-full shadow-[0_0_8px_0_#0003]"
            draggable="false"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"
          />
        </template>
        <!-- 选中 tab 的主区或内容: 国际贸易 -->
        <template #scenarios_international_trade>
          <div v-if="isMobile" class="mt-2 mb-6 text-center">
            <p class="text-lg font-semibold text-gray-800 dark:text-gray-100">
              {{ scenarios2.find((item) => item.slot === 'scenarios_international_trade')?.label }}
            </p>
            <p class="text-base text-gray-600 dark:text-gray-200">
              {{ scenarios2.find((item) => item.slot === 'scenarios_international_trade')?.content }}
            </p>
          </div>
          <img
            :src="scenarios_international_trade_images"
            class="w-full shadow-[0_0_8px_0_#0003]"
            draggable="false"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 6px"
          />
        </template>
      </UTabs>
    </UPageSection>

    <!-- <UPageSection class="!pt-0"> -->
    <!-- NuxtUI3 版本迁移 不要使用（PageSection）， 请使用 -->
    <!-- HTML5 Video标签的属性、方法和事件汇总 https://blog.csdn.net/2301_78542842/article/details/142456899 -->
    <!-- src: 定义视频的URL -->
    <!-- poster: 设置视频尚未加载或播放时显示的图像URL -->
    <!-- preload: 控制视频数据的加载策略，可以是 "auto"（默认，根据浏览器决定）、"metadata"（只加载元数据，如长度）或 "none"（不预先加载） -->
    <!-- autoplay: 规定视频是否应该在就绪后立即自动播放 -->
    <!-- controls: 是否显示浏览器自带的播放控制（如播放/暂停按钮、进度条等） -->
    <!-- loop: 规定视频是否应在结束时重新开始播放 -->
    <!-- muted: 规定视频是否静音播放 -->
    <!-- playsinline: 规定视频是否应在元素的播放区域内播放，而不是全屏播放 -->
    <!-- width: 规定视频的宽度 -->
    <!-- height: 规定视频的高度 -->
    <!-- <video
        ref="mainVideo"
        controls
        playsinline
        loop
        preload="none"
        poster="https://assets.selecttranslate.com/web/videos/ztsl-vides-face.png"
        class="w-full rounded-xl cursor-pointer video_container"
        @click="controlMainVideo"
      > -->
    <!-- 由于不同浏览器对视频格式的支持不同，通常需要提供多种格式的视频文件，以确保兼容性。可以使用多个 <source> 标签来定义不同格式的视频文件 -->
    <!-- <source src="https://assets.selecttranslate.com/web/videos/ztsl-use-01.mp4" type="video/mp4" />
      </video>
    </UPageSection> -->

    <!-- 翻译引擎列表：汇聚全球顶级 AI 翻译模型 -->
    <UPageSection
      :ui="{
        container: 'py-24 sm:py-24 md:py-24 lg:py-32 xl:py-32',
        title: 'text-2xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #title>
        <span class="">
          {{ t('home.engins.title') }}
        </span>
      </template>
      <UPageMarquee
        pause-on-hover
        :overlay="false"
        :repeat="14"
        :ui="{ root: '[--gap:--spacing(6)] [--duration:30s]', content: 'w-auto py-1' }"
      >
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/deepseek.png" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-lg lg:leading-11">{{ t('common.engins.deepseek') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/openai.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.openai') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/gemini.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.gemini') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/claude.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.claude') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/zhipu.png" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.zhipu') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/tongyi.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.qwen') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/doubao.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.doubao') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/lingyiwanwu.png" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.lingyiwanwu') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/deepl.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.deepl') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/google.png" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.google') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/microsoft.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.microsoft') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/transmart.png" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.transmart') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/yandex.png" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.yandex') }}</span>
        </span>
        <span class="inline-flex w-32 lg:w-40">
          <span class="size-9 flex-shrink-0 rounded-xs bg-white p-0.5 lg:size-11 dark:bg-gray-100">
            <img src="../assets/icon/transModel/xai.svg" class="size-8 lg:size-10" />
          </span>
          <span class="ml-1 text-base leading-9 font-semibold lg:text-xl lg:leading-11">{{ t('common.engins.grok') }}</span>
        </span>
      </UPageMarquee>
    </UPageSection>

    <!-- 使命：推动信息平权，实现母语自由 -->
    <!-- NuxtUI3 版本迁移 不要使用（PageSection）， 请使用 -->
    <UPageSection
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-2xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #title>
        <span class="color_text_title_a">{{ t('home.mission.title') }}</span>
      </template>
      <template #description>
        <span v-html="t('home.mission.description1')" />
        <br />
        <span class="font-semibold" v-html="t('home.mission.description2')" />
      </template>
    </UPageSection>

    <!-- 核心功能展示 -->
    <UPageSection
      v-for="(section, index) in sections"
      :key="index"
      :title="section.title"
      :description="section.description"
      :orientation="section.orientation"
      :reverse="section.reverse"
      :features="section.features"
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-xl sm:text-xl md:text-xl lg:text-2xl xl:text-3xl',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg',
        features: 'text-sm sm:text-sm md:text-base lg:text-base xl:text-base'
      }"
    >
      <template #title>
        <span class="">{{ section.title }}</span>
      </template>
      <template #features />
      <!-- 双语对照网页翻译 -->
      <UCarousel
        v-if="index === 0"
        v-slot="{ item }"
        :items="bilingual_translation_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
      <!-- AI 择优翻译 -->
      <UCarousel
        v-if="index === 1"
        v-slot="{ item }"
        :items="ai_selected_translate_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
      <!-- 输入翻译 -->
      <UCarousel
        v-else-if="index === 2"
        v-slot="{ item }"
        :items="input_translation_videos"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <!-- <video :src="String(item)" controls muted autoplay loop playsinline preload="none" class="w-full" style="border: 1px solid rgb(var(--color-gray-300)); border-radius: 2px"></video> -->
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
      <!-- PDF 翻译 -->
      <UCarousel
        v-else-if="index === 3"
        v-slot="{ item }"
        :items="pdf_free_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
      <!-- PDF Plus -->
      <UCarousel
        v-else-if="index === 4"
        v-slot="{ item }"
        :items="pdf_plus_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
      <!-- 悬停翻译 -->
      <UCarousel
        v-else-if="index === 5"
        v-slot="{ item }"
        :items="hover_translate_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
      <!-- 划词翻译 -->
      <UCarousel
        v-else-if="index === 6"
        v-slot="{ item }"
        :items="highlight_translation_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
    </UPageSection>
  </div>
</template>

<script setup lang="ts">
import InstallationExpansionModules from '@/pages/components/InstallationExpansionModules.vue' // 安装扩展模块
import { isMobileDevice } from '@/utils/utils'

import HeroBackground from '@/components/HeroBackground.vue'

const { t, locale } = useI18n()
const { isLoading } = useLoadingIndicator()

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('common.title'),
  ogTitle: t('common.title'),
  description: t('common.description'),
  ogDescription: t('common.description')
})

const appear = ref(false)
const appeared = ref(false)
const isMobile = ref(false)

onMounted(() => {
  // 初始化移动端检测
  isMobile.value = isMobileDevice()

  // 使用 Nuxt 的节流函数优化 resize 事件处理， 不加这个窗口变化的时候会卡
  const throttledResize = useThrottleFn(() => {
    isMobile.value = isMobileDevice()
  }, 200) // 200ms 的节流时间

  // 监听窗口大小变化
  window.addEventListener('resize', throttledResize)

  setTimeout(() => {
    appear.value = true
    setTimeout(() => {
      appeared.value = true
    }, 1000)
  }, 0)

  // 清理事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', throttledResize)
  })
})

// sections
const sections = [
  {
    title: t('home.bilingual_translation.title'), // 双语对照网页翻译
    description: t('home.bilingual_translation.description'), // 智能识别并翻译网页内容，在原文后显示译文，实现双语对照阅读。支持各种语言的网页翻译，助您无障碍畅游全球知识海洋！
    orientation: 'horizontal', // 布局方向
    reverse: false, // 是否反转布局
    features: [
      {
        description: t('home.bilingual_translation.feature1'), // 点击扩展插件悬浮按钮，即可一键开启双语阅读。
        icon: 'i-heroicons-check-circle-16-solid'
      },
      {
        description: t('home.bilingual_translation.feature2'), // 提供多种预设译文样式，同时支持自定义译文样式。
        icon: 'i-heroicons-adjustments-horizontal-20-solid'
      },
      {
        description: t('home.bilingual_translation.feature3'), // 自由选择 AI 翻译模型，支持 100 多种语言互译。
        icon: 'i-hugeicons-artificial-intelligence-04'
      }
    ]
  },
  {
    title: t('home.ai_selected_translation.title'), // AI 择优翻译
    description: t('home.ai_selected_translation.description'), // 使用多个翻译模型对同一原文进行翻译会得到多个不同的结果，通过智能评分从中择优获取最佳译文，能大幅提升翻译结果的准确度和可靠性。
    orientation: 'horizontal', // 布局方向
    reverse: true, // 是否反转布局
    features: [
      {
        description: t('home.ai_selected_translation.feature1'), // 可设置多个翻译模型同时进行翻译。
        icon: 'i-ri-grid-fill'
      },
      {
        description: t('home.ai_selected_translation.feature2'), // 使用较强 AI 模型对多个翻译结果进行评分择优。
        icon: 'i-rivet-icons-check-all'
      },
      {
        description: t('home.ai_selected_translation.feature3'), // 每个模型的翻译结果都有直观的评分及分析说明，质量差异对比一目了然。
        icon: 'i-tabler-analyze'
      }
    ]
  },
  {
    title: t('home.input_translation.title'), // 输入翻译
    description: t('home.input_translation.description'), // 在网页输入框中输入内容即可一键翻译。让您用母语就能跨语言输入、回复和交流，轻松实现母语自由。
    orientation: 'horizontal', // 布局方向
    reverse: false, // 是否反转布局
    features: [
      {
        description: t('home.input_translation.feature1'), // 深入应用场景，摆脱使用传统翻译工具的复制、切换、粘贴等繁琐流程。
        icon: 'i-mdi-application-import'
      },
      {
        description: t('home.input_translation.feature2'), // 通过择优翻译，时刻给你最佳译文结果，让跨语言沟通和回复更自信。
        icon: 'i-tabler-thumb-up'
      },
      {
        description: t('home.input_translation.feature3'), // 翻译记录管理，方便您随时查看和复用翻译记录。
        icon: 'i-material-symbols-history-rounded'
      }
    ]
  },
  {
    title: t('home.pdf_free_translation.title'), // PDF 翻译
    description: t('home.pdf_free_translation.description'), // 完全免费的 PDF 翻译功能，日常外语文档快速翻译，满足您大部分 PDF 文档的翻译阅读需求。
    orientation: 'horizontal', // 布局方向
    reverse: true, // 是否反转布局
    features: [
      {
        description: t('home.pdf_free_translation.feature1'), // 保留原文排版：每段译文的位置与原文保持一致。
        icon: 'i-material-symbols-type-specimen-outline'
      },
      {
        description: t('home.pdf_free_translation.feature2'), // 逐页双语对照：原文档页面和译文页面左右逐页对应，轻松对比阅读。
        icon: 'i-material-symbols-light-two-pager'
      },
      {
        description: t('home.pdf_free_translation.feature3'), // 翻译结果导出：可自定义译文的显示样式和导出文档格式。
        icon: 'i-tabler-file-export'
      }
    ]
  },
  {
    title: t('home.pdf_plus_translation.title'), // PDF Plus
    description: t('home.pdf_plus_translation.description'), // PDF Plus 为学术论文和各类专业文档的翻译而设计。基于领先的 AI 视觉处理技术，解决了复杂文档中因为各类公式、图表等识别不准确导致翻译结果和排版错乱的难题。
    orientation: 'horizontal', // 布局方向
    reverse: false, // 是否反转布局
    features: [
      {
        description: t('home.pdf_plus_translation.feature1'), // 精确解析各类公式、复杂图表、代码片段等专业内容.
        icon: 'i-fluent-math-formula-16-filled'
      },
      {
        description: t('home.pdf_plus_translation.feature2'), // 支持扫描版 PDF 的解析翻译。
        icon: 'i-mdi-ocr'
      },
      {
        description: t('home.pdf_plus_translation.feature3'), // 双栏、三栏布局识别转换。
        icon: 'i-mingcute-layout-8-line'
      }
    ]
  },
  {
    title: t('home.mouse_hover_translation.title'), // 鼠标悬停翻译
    description: t('home.mouse_hover_translation.description'), // 支持【段落翻译】和【区域翻译】两种模式，鼠标悬停 + 快捷键，可快速翻译网页中单个段落文本或区域中的多个段落文本。
    orientation: 'horizontal', // 布局方向
    reverse: true, // 是否反转布局
    features: [
      {
        description: t('home.mouse_hover_translation.feature1'), // 只翻译鼠标悬停段落的单段文字。
        icon: 'i-wpf-cursor'
      },
      {
        description: t('home.mouse_hover_translation.feature2'), // 翻译鼠标悬停所选中网页区域下的所有段落，让悬停翻译更灵活。
        icon: 'i-vaadin-area-select'
      },
      {
        description: t('home.mouse_hover_translation.feature3'), // 可自定义悬停翻译的快捷键，满足您的个性化需求。
        icon: 'i-fluent-keyboard-mouse-16-filled'
      }
    ]
  },
  {
    title: t('home.highlight_translation.title'), // 划词翻译
    description: t('home.highlight_translation.description'), // 直接划选单词或文本即可翻译。给您随手、按需的轻量级翻译体验。
    orientation: 'horizontal', // 布局方向
    reverse: false, // 是否反转布局
    features: [
      {
        description: t('home.highlight_translation.feature1'), // 单词词性、音标、发音、例句，全方位掌握。
        icon: 'i-material-symbols-match-word-rounded'
      },
      {
        description: t('home.highlight_translation.feature2'), // 结合单词的上下文语境，提供详细的翻译结果分析。
        icon: 'i-material-symbols-contextual-token-add-outline-rounded'
      },
      {
        description: t('home.highlight_translation.feature3'), // 翻译模型可按需切换。
        icon: 'i-ri-openai-fill'
      }
    ]
  }
]

/**
 * 对照翻译演示动画gif.
 */
const bilingual_translation_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

/**
 * 悬停翻译演示动画gif.
 */
const hover_translate_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

/**
 * 划词翻译截图.
 */
const highlight_translation_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

/**
 * AI 择优翻译截图
 * ai_selectee_translate_2880x1800.webp
 */
const ai_selected_translate_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

/** 输入翻译演示动画: mp4 */
const input_translation_videos = ['https://assets.selecttranslate.com/web/images/home/<USER>']

/** 免费 PDF 翻译 */
const pdf_free_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

/**
 * PDF Plus 统一单栏格式展示（排版）
 */
// const pdf_plus_images = ['../../assets/images/pdf_plus/typesetting.webp'];
const pdf_plus_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

// 应用场景：视频格式 1280x800
const scenarios2 = [
  {
    slot: 'scenarios_social',
    icon: 'i-logos-discord-icon',
    label: t('home.scenarios.social.title'), // 社交互动
    content: t('home.scenarios.social.description') // 无障碍阅读，跨语言交流
  },
  {
    slot: 'scenarios_information',
    icon: 'i-bi-twitter-x',
    label: t('home.scenarios.information.title'), // 资讯浏览
    content: t('home.scenarios.information.description') // 国际资讯，实时掌握
  },
  {
    slot: 'scenarios_study',
    icon: 'i-logos-github-icon',
    label: t('home.scenarios.study.title'), // 学习研究
    content: t('home.scenarios.study.description') // 全球知识，轻松获取
  },
  {
    slot: 'scenarios_document',
    icon: 'i-material-icon-theme-pdf',
    label: t('home.scenarios.document.title'), // 文档翻译
    content: t('home.scenarios.document.description') // 学术、办公，便捷高效
  },
  {
    slot: 'scenarios_international_trade',
    icon: 'i-devicon-amazonwebservices-wordmark',
    label: t('home.scenarios.international_trade.title'), // 国际贸易
    content: t('home.scenarios.international_trade.description') // 自由沟通，服务全球
  }
]

// 应用场景视频

const scenarios_information_video = 'https://assets.selecttranslate.com/web/images/home/<USER>' // X.com 资讯浏览
const scenarios_social_video = 'https://assets.selecttranslate.com/web/images/home/<USER>' // discord.com 社交互动
const scenarios_study_video = 'https://assets.selecttranslate.com/web/images/home/<USER>' // GitHub 学习研究
const scenarios_document_video = 'https://assets.selecttranslate.com/web/images/home/<USER>' // 文档翻译
const scenarios_international_trade_images = 'https://assets.selecttranslate.com/web/images/home/<USER>' // 国际贸易

const mainVideo = ref(null)
const isPlay = ref(false)
/** 控制主视频频播放或暂停 */
function controlMainVideo() {
  // paused 属性（只读）：返回一个布尔值，表示视频是否处于暂停状态
  // isPaused = mainVideo.value.paused;
  isPlay.value = !isPlay.value
  if (isPlay.value) {
    mainVideo.value.play()
  }
  else {
    mainVideo.value.pause() // 这里有个Bug，第一次点击暂停会暂停后再次触发播放，导致第一次暂停失效
  }
}

onMounted(() => {
  // 获取视频元素
  // mainVideo.value = document.getElementById('mainVideo');
  // if (mainVideo.value){
  //   // 监听视频播放状态变化
  //   mainVideo.value.addEventListener('play', function() {
  //     console.log('Video is playing.');
  //   });
  //   mainVideo.value.addEventListener('pause', function() {
  //     console.log('Video is paused.');
  //   });
  // }
})
</script>

<style scoped>
.video_container {
  box-shadow: #0003 0 0px 8px 0px;
}

.color_text {
  background: -webkit-linear-gradient(120deg, #bd34fe 30%, #00a3d7);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.color_text_title_a {
  background: -webkit-linear-gradient(120deg, #ff2020 20%, #7245f6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.landing-grid {
  background-size: 100px 100px;
  background-image: linear-gradient(to right, rgb(var(--color-gray-200)) 1px, transparent 1px), linear-gradient(to bottom, rgb(var(--color-gray-200)) 1px, transparent 1px);
}

.dark {
  .landing-grid {
    background-image: linear-gradient(to right, rgb(var(--color-gray-800)) 1px, transparent 1px), linear-gradient(to bottom, rgb(var(--color-gray-800)) 1px, transparent 1px);
  }
}

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}
</style>
