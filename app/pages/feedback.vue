<template>
  <div class="mx-auto mt-4 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <!-- <div class="font-bold text-3xl">
        {{ t('feedback.title') }}
      </div> -->
    </div>

    <UCard class="mt-6 pb-16">
      <!-- 创建 问题 / 建议 -->
      <div class="text-3xl font-bold">
        {{ t('feedback.form.header') }}
      </div>
      <div class="mt-12">
        <!-- 类型  -->
        <label for="feedback_type" class="mb-2 block font-semibold">{{ t('feedback.form.type') }}</label>
        <div class="flex space-x-6">
          <URadioGroup
            v-model="feedback_type"
            orientation="horizontal"
            value-key="id"
            :items="items"
          />
        </div>
      </div>
      <div class="mt-10">
        <div class="mb-2 flex w-full justify-between">
          <!-- 内容 -->
          <label for="feedback_content" class="font-semibold">{{ t('feedback.form.content.label') }}</label>
          <!-- 字数限制: xxx/500  -->
          <span class="text-xs" :class="characterCountClass">{{ feedback_content.length }}/{{ maxContentLength }}</span>
        </div>
        <!-- 请输您要反馈/建议的内容（500字以内） -->
        <UTextarea
          v-model="feedback_content"
          size="lg"
          :placeholder="t('feedback.form.content.placeholder')"
          class="w-full"
          :rows="4"
          :maxlength="maxContentLength"
          @input="handleContentInput"
        />
      </div>
      <!-- 原生图片上传组件 -->
      <div class="mt-10">
        <div class="mb-2 flex w-full justify-between">
          <!-- 截图 -->
          <label for="images" class="font-semibold">{{ t('feedback.form.screenshot.label') }}</label>
          <!-- 图片数限制，如: 1/5 -->
          <span class="text-xs font-medium text-neutral-500">{{ fileList.length }}/{{ maxLimit }}</span>
        </div>
        <div
          class="resource-image-uploader"
          :class="{ 'border-primary-500 border-2 bg-gray-50': dragActive }"
          @dragover.prevent
          @dragenter.prevent="dragActive = true"
          @dragleave.prevent="dragActive = false"
          @drop.prevent="handleFileDrop"
        >
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="image/jpeg,image/png"
            style="display: none"
            @change="handleNativeFileChange"
          />
          <!-- 拖放区域 -->
          <div>
            <div v-if="dragActive">
              {{ t('feedback.form.screenshot.drop_text') }}
            </div>
            <div v-else class="flex">
              <!-- 点击或拖拽到此处添加图片 -->
              <UButton
                icon="i-heroicons-plus-16-solid"
                class="rounded-md"
                size="lg"
                color="neutral"
                variant="subtle"
                :disabled="fileList.length >= maxLimit"
                @click="fileInput?.click()"
              >
                {{ t('feedback.form.screenshot.upload_text') }}
              </UButton>
            </div>
          </div>
          <!-- 图片预览区域 -->
          <div v-if="fileList.length > 0" class="mt-4 flex flex-wrap gap-4">
            <div v-for="(file, index) in filePreviewList" :key="index" class="relative">
              <img :src="file.preview" class="h-24 w-24 cursor-pointer rounded border object-cover" @click="openImageViewer(index)" />
              <UButton
                icon="i-heroicons-x-mark"
                color="error"
                variant="soft"
                size="xs"
                class="absolute top-1 right-1"
                @click="removeImage(index)"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="mt-10">
        <div class="mb-2 flex w-full justify-between">
          <!-- 联系方式 -->
          <label for="email" class="font-semibold">{{ t('feedback.form.contact.label') }}</label>
          <span class="text-xs" :class="emailCountClass">{{ email.length }}/100</span>
        </div>
        <!-- 可提供：邮箱 / 微信 / QQ等，以方便我们快速联系 -->
        <UInput
          v-model="email"
          size="lg"
          class="w-full"
          :placeholder="t('feedback.form.contact.placeholder')"
          :maxlength="100"
          @input="handleEmailInput"
        />
      </div>

      <!-- <div class="mt-3" v-if="false">
        <UInput v-model="feedback_no" size="lg" :placeholder="t('feedback.form.feedback_no')" />
      </div> -->

      <div class="mt-20">
        <!-- 提交按钮 -->
        <UButton
          color="primary"
          variant="solid"
          class="flex w-full items-center justify-center rounded-md text-xl font-bold sm:float-right sm:w-36"
          :loading="isSubmitting"
          @click="feedBack"
        >
          {{ isSubmitting ? t('feedback.form.submitting') : t('feedback.form.submit') }}
        </UButton>
      </div>

      <!-- 微信交流群 -->
      <div class="mt-36 ml-2">
        <div class="mb-2 flex w-full justify-between">
          <!-- 建议加入微信交流群快速反馈 -->
          <label class="text-sm font-normal">{{ t('feedback.form.wechat_group_tip') }}</label>
        </div>
        <div class="ml-8 flex items-center">
          <div class="w-36">
            <img src="https://assets.selecttranslate.com/statics/ztsl_wecom_qrcode.jpg" alt="WeCom" class="size-26 rounded-md" />
          </div>
        </div>
      </div>

      <!-- 图片查看器 -->
      <UModal v-model:open="isImageViewerOpen" fullscreen>
        <template #content>
          <div class="relative flex h-full flex-col items-center justify-center p-4">
            <!-- 关闭按钮 -->
            <UButton
              icon="i-heroicons-x-mark"
              size="4xl"
              color="neutral"
              variant="ghost"
              class="absolute top-10 right-10 z-10 text-5xl"
              aria-label="Close"
              @click="isImageViewerOpen = false"
            />

            <div class="image-viewer flex h-full w-full items-center justify-center">
              <UColorModeImage :light="currentImageUrl" :dark="currentImageUrl" class="max-h-full max-w-full object-contain" />
            </div>
            <div class="mt-4 flex justify-center gap-2">
              <UButton
                icon="i-heroicons-arrow-left"
                size="sm"
                :disabled="currentImageIndex === 0"
                @click="changeImage(-1)"
              />
              <span class="text-sm">{{ currentImageIndex + 1 }} / {{ viewerImages.length }}</span>
              <UButton
                icon="i-heroicons-arrow-right"
                size="sm"
                :disabled="currentImageIndex >= viewerImages.length - 1"
                @click="changeImage(1)"
              />
            </div>
          </div>
        </template>
      </UModal>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import { createFeedback, uploadFeedbackImage } from '@/api/feedback'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import type { HeadersTokenType } from '@/api/login/types'
import { storeToRefs } from 'pinia'

// definePageMeta 定义页面的布局和权限路由守卫，(此代码要放最前面，不要然会加载默认布局)
// definePageMeta({
//   layout: 'member',
//   middleware: 'auth' // 使用 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
// });

const { t, locale } = useI18n()
const toast = useToast()

const authStore = useAuthStoreWithOut()
const { member } = storeToRefs(authStore)
const router = useRouter()

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('feedback.title') + ' - ' + t('common.site_name'), // 问题反馈
  ogTitle: t('feedback.title') + ' - ' + t('common.site_name'), // 问题反馈
  description: t('common.description'),
  ogDescription: t('common.description')
})

// 输入内容最大长度
const maxContentLength = ref(500)
const maxEmailLength = ref(100)

// 处理内容输入，确保不超过最大长度
const handleContentInput = () => {
  if (feedback_content.value.length > maxContentLength.value) {
    feedback_content.value = feedback_content.value.slice(0, maxContentLength.value)
  }
}

// 处理联系方式输入，确保不超过最大长度
const handleEmailInput = () => {
  if (email.value.length > maxEmailLength.value) {
    email.value = email.value.slice(0, maxEmailLength.value)
  }
}

// 根据剩余字符数改变计数器颜色
const characterCountClass = computed(() => {
  const remaining = maxContentLength.value - feedback_content.value.length
  if (remaining <= 50) return 'text-red-500 font-medium'
  if (remaining <= 100) return 'text-orange-500'
  return 'text-neutral-500'
})

// 联系方式字数计数器样式
const emailCountClass = computed(() => {
  const remaining = maxEmailLength.value - email.value.length
  if (remaining <= 10) return 'text-red-500 font-medium'
  if (remaining <= 20) return 'text-orange-500'
  return 'text-neutral-500'
})

const feedback_content = ref('')
const platform = 'ztsl'
const email = ref('')
// 反馈类型选项
const items = computed(() => {
  return [
    {
      label: t('feedback.form.type_options.feedback'), // 问题反馈
      id: '1'
    },
    {
      label: t('feedback.form.type_options.suggestion'), // 建议反馈
      id: '0'
    }
  ]
})

const feedback_type = ref('1')
// const feedback_no = ref('');
// 添加用于跟踪提交状态的变量
const isSubmitting = ref(false)

// 图片上传相关变量
const maxLimit = ref(5)
const fileList = ref<File[]>([])
const filePreviewList = ref<{ file: File, preview: string }[]>([])
const fileInput = ref<HTMLInputElement | null>(null)
const dragActive = ref(false)

// 新增图片查看器变量 - 与reply_list一致
const isImageViewerOpen = ref(false)
const viewerImages = ref<any[]>([])
const currentImageIndex = ref(0)
const uploadedImages = ref<string[]>([])

// 确保URL包含https://前缀的函数
const ensureHttps = (url) => {
  if (!url) return ''
  // 添加对 blob: 和 data: 开头的URL的支持，避免修改这些特殊格式
  if (url.startsWith('blob:') || url.startsWith('data:') || url.startsWith('https://') || url.startsWith('http://')) {
    return url
  }
  return `https://${url}`
}

// 计算当前图片URL
const currentImageUrl = computed(() => {
  if (viewerImages.value.length === 0) return ''
  return ensureHttps(viewerImages.value[currentImageIndex.value]?.image_url || '')
})

// 处理原生文件输入变化
const handleNativeFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || []) as File[]
  console.log('原生文件输入:', files)
  handleFileChange(files)
  // 重置input以允许选择同一文件
  target.value = ''
}

// 处理文件拖放
const handleFileDrop = (event: DragEvent) => {
  dragActive.value = false
  const files = Array.from(event.dataTransfer?.files || []) as File[]
  console.log('拖放文件:', files)
  handleFileChange(files)
}

// 处理文件更改
const handleFileChange = (files: File[]) => {
  console.log('handleFileChange:', files)

  // 添加新文件到现有文件列表(合并处理)
  const combinedFiles = [...fileList.value]
  for (const file of files) {
    if (combinedFiles.length < maxLimit.value) {
      combinedFiles.push(file)
    }
  }

  // 限制文件数量
  if (combinedFiles.length > maxLimit.value) {
    toast.add({
      title: t('feedback.messages.upload_limit_exceeded'), // 超出最大上传限制
      description: t('feedback.messages.max_images', { count: maxLimit.value }), // 最多只能上传{count}张图片
      color: 'red'
    })
    // 只保留最大数量的文件
    combinedFiles.splice(maxLimit.value)
  }

  // 验证文件类型和大小
  const validFiles = combinedFiles.filter((file) => {
    const isValidType = ['image/jpeg', 'image/png'].includes(file.type)
    const isValidSize = file.size / 1024 / 1024 < 3 // 3MB限制

    if (!isValidType) {
      toast.add({
        title: t('feedback.messages.invalid_file_type'), // 文件格式错误
        description: t('feedback.messages.only_jpg_png'), // 只能上传JPG或PNG格式图片
        color: 'red'
      })
    }

    if (!isValidSize) {
      toast.add({
        title: t('feedback.messages.file_too_large'), // 文件过大
        description: t('feedback.messages.max_file_size'), // 图片大小不能超过3MB
        color: 'red'
      })
    }

    return isValidType && isValidSize
  })

  fileList.value = validFiles

  // 生成预览
  filePreviewList.value = validFiles.map(file => ({
    file,
    preview: URL.createObjectURL(file)
  }))
}

// 移除图片
const removeImage = (index: number) => {
  filePreviewList.value.splice(index, 1)
  fileList.value.splice(index, 1)
}

// 添加图片缩放相关变量
const zoomLevel = ref(1)

// 缩放图片函数
const zoom = (delta: number) => {
  const newZoom = zoomLevel.value + delta
  // 限制缩放范围从0.4到3倍
  if (newZoom >= 0.4 && newZoom <= 3) {
    zoomLevel.value = newZoom
  }
}

// 重置缩放
const resetZoom = () => {
  zoomLevel.value = 1
}

// 修改openImageViewer函数，在打开查看器时重置缩放
const openImageViewer = (index: number) => {
  if (!filePreviewList.value || filePreviewList.value.length === 0) {
    console.warn('没有图片可查看')
    return
  }

  // 将 filePreviewList 转换为图片数组格式
  const images = filePreviewList.value.map(file => ({
    image_url: file.preview
  }))

  viewerImages.value = images
  currentImageIndex.value = index
  zoomLevel.value = 1 // 重置缩放级别
  isImageViewerOpen.value = true
  console.log(`打开图片查看器: 总计${images.length}张图片，从索引${index}开始查看`)
}

// 添加图片切换函数
const changeImage = (direction: number) => {
  const newIndex = currentImageIndex.value + direction
  if (newIndex >= 0 && newIndex < viewerImages.value.length) {
    currentImageIndex.value = newIndex
    zoomLevel.value = 1 // 切换图片时重置缩放
  }
}

// 上传图片
const uploadImages = async (feedbackId: number): Promise<string[]> => {
  // 如果用户已登录，使用token，否则使用空字符串
  const headers: HeadersTokenType = {
    token: authStore.getToken && authStore.getToken !== '' ? authStore.getToken.slice(7) : ''
  }

  const uploadPromises = fileList.value.map(async (file) => {
    try {
      // 获取member_id
      const memberId = member.value?.member_id
      const response = await uploadFeedbackImage(headers, file, feedbackId, undefined, memberId)
      if (response.code === 200) {
        return response.data.image_url
      }
      else {
        throw new Error(response.message || t('feedback.messages.submit_failed')) // 提交失败
      }
    }
    catch (error) {
      console.error('上传图片失败:', error)
      throw error
    }
  })

  return Promise.all(uploadPromises)
}

const feedBack = async () => {
  // 开始提交前设置为loading状态
  isSubmitting.value = true

  // 如果用户已登录，使用token，否则使用空字符串
  const headers: HeadersTokenType = {
    token: authStore.getToken && authStore.getToken !== '' ? authStore.getToken.slice(7) : ''
  }

  if (!feedback_content.value || feedback_content.value.length < 1) {
    toast.add({
      title: t('feedback.messages.content_required') // 请输入反馈内容
    })
    isSubmitting.value = false // 恢复按钮状态
  }
  else {
    try {
      const feedbackRes = await createFeedback(headers, {
        feedback_type: parseInt(feedback_type.value), // 反馈类型
        feedback_content: feedback_content.value, // 问题内容
        platform: platform, // 平台
        email: email.value, // 邮箱
        member_id: member.value?.member_id
        // feedback_no: feedback_no.value // 问题编号
      })

      if (feedbackRes.code === 200) {
        // 问题创建成功，获取问题ID并上传图片
        const feedbackId = feedbackRes.data.id

        // 如果有图片需要上传
        if (fileList.value.length > 0) {
          try {
            const imageUrls = await uploadImages(feedbackId)
            uploadedImages.value = imageUrls

            toast.add({
              title: t('feedback.messages.success'), // 成功
              description: t('feedback.messages.submit_with_images_success'), // 您的反馈和图片已成功提交！
              color: 'green'
            })

            // 短暂延迟后跳转到问题列表页面
            setTimeout(() => {
              router.push(`/${locale.value}/feedback_list`)
            }, 500)
          }
          catch (error) {
            toast.add({
              title: t('feedback.messages.image_upload_failed'), // 图片上传失败
              description: t('feedback.messages.partial_success'), // 反馈已提交，但图片上传失败
              color: 'yellow'
            })

            // 尽管图片上传失败，反馈已提交，仍然跳转
            setTimeout(() => {
              router.push(`/${locale.value}/feedback_list`)
            }, 500)
          }
        }
        else {
          toast.add({
            title: t('feedback.messages.success'), // 成功
            description: t('feedback.messages.submit_success'), // 您的反馈已成功提交！
            color: 'green'
          })

          // 短暂延迟后跳转到问题列表页面
          setTimeout(() => {
            router.push(`/${locale.value}/feedback_list`)
          }, 500)
        }

        // 重置表单
        feedback_content.value = ''
        email.value = ''
        feedback_type.value = '1'
        // feedback_no.value = '';
        fileList.value = []
        filePreviewList.value = []
        uploadedImages.value = []
      }
      else {
        toast.add({
          title: feedbackRes.message
        })
      }
    }
    catch (error: any) {
      toast.add({
        title: t('feedback.messages.submit_failed'), // 提交失败
        description: error.message || t('feedback.messages.unknown_error'), // 未知错误
        color: 'red'
      })
    }
    finally {
      // 无论成功还是失败，都恢复按钮状态
      isSubmitting.value = false
    }
  }
}
</script>

<style scoped>
.resource-image-uploader {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  transition: border-color 0.3s;
}

.resource-image-uploader:hover {
  border-color: #409eff;
}

.image-viewer {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
}

.image-viewer img {
  object-fit: contain;
  transform-origin: center;
  cursor: zoom-in;
}
</style>
