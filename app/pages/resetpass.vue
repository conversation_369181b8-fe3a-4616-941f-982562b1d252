<template>
  <UCard class="mt-24 mb-24 w-10/12 max-w-sm bg-white/75 backdrop-blur lg:mt-32 lg:w-full dark:bg-white/5">
    <!-- 重置密码 -->
    <UAuthForm
      :fields="fields"
      :schema="schema"
      icon="i-heroicons-arrow-path-rounded-square-solid"
      :title="t('auth.reset_password.title')"
      :submit="{
        label: t('auth.reset_password.title'), // 重置密码
        size: 'lg',
        loading: loading
      }"
      @submit="onSubmit"
    >
      <template #description>
        <!-- 已经有账号？登录 -->
        {{ t('auth.common.have_an_account') }}
        <NuxtLink :to="'/' + locale + '/login'" class="font-medium text-(--ui-primary)">{{ t('auth.login.title') }}</NuxtLink>
      </template>

      <template #verify_code-hint>
        <!-- 获取验证码 -->
        <NuxtLink
          to="#"
          class="font-medium text-(--ui-primary)"
          :class="canResend ? 'text-sky-500 hover:text-sky-700 dark:text-sky-400 dark:hover:text-sky-300' : 'text-gray-400 dark:text-gray-500'"
          @click="sendEmailVerifyCode"
        >
          {{ canResend ? t('auth.common.verify_code_send_label') : t('auth.common.verify_code_countdown_retry', { seconds: countdown }) }}
        </NuxtLink>
      </template>
    </UAuthForm>
  </UCard>
</template>

<script setup lang="ts">
import * as z from 'zod' // 引入zod库（ https://zod.dev ）用于表单数据校验
import type { FormSubmitEvent } from '@nuxt/ui' // 引入表单提交事件类型

import { sendEmailVerifyCodeApi, resetPassApi } from '@/api/login'
import type { ResetPassType, SendEmailVerifyCodeType } from '@/api/login/types'

// definePageMeta 定义页面的布局 (此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'authentication' // 使用 layouts/authentication.vue 布局
})

const { t, locale } = useI18n()
const loading = ref(false)

useSeoMeta({
  titleTemplate: '',
  title: t('auth.reset_password.title') + ' - ' + t('common.site_name') // 重置密码
})

const fields = [
  {
    name: 'email',
    label: t('auth.common.email_label'), // 邮箱地址
    type: 'text' as const,
    size: 'lg',
    placeholder: t('auth.common.email_label') // 邮箱地址
  },
  {
    name: 'verify_code',
    label: t('auth.common.verify_code_label'), // 验证码
    type: 'text' as const,
    size: 'lg',
    placeholder: t('auth.common.verify_code_label') // 验证码
  },
  {
    name: 'new_pass',
    label: t('auth.reset_password.new_password_label'), // 新密码
    type: 'password' as const,
    size: 'lg',
    placeholder: t('auth.reset_password.new_password_placeholder') // 设置新密码
  }
]

// 定义邮箱地址的正则表达式
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

// 表单数据校验 https://zod.dev
const schema = z.object({
  // 邮箱地址校验
  email: z
    .string({
      required_error: t('auth.common.messages.email_required') // 请输入邮箱地址
    })
    .email(t('auth.common.messages.email_format_incorrect')), // 请输入正确的邮箱地址

  // 邮件验证码校验
  verify_code: z.string({
    required_error: t('auth.common.messages.verify_code_required') // 请输入邮件验证码
  }),

  // 密码校验
  new_pass: z
    .string(
      {
        required_error: t('auth.common.messages.new_password_required')
      } // 请输入密码
    )
    .min(6, t('auth.common.messages.password_format_incorrect_2')) // 密码长度为6到20个字符
    .max(20, t('auth.common.messages.password_format_incorrect_2')) // 密码长度为6到20个字符
    .regex(/^[A-Za-z0-9!@#$%^&*(),.?":{}|<>]+$/, t('auth.common.messages.password_format_incorrect_1')) // 密码必须是字母、数字或符号组成
})
// 表单数据校验模式
type Schema = z.output<typeof schema>

// 添加计时器相关变量
const canResend = ref(true)
const countdown = ref(60)
let timer = null

async function sendEmailVerifyCode(formData: SendEmailVerifyCodeType) {
  const emailInput = document.getElementsByName('email')[0]
  if (!canResend.value) return

  const submitData: SendEmailVerifyCodeType = {
    email: emailInput.value,
    platform: 'web',
    interface_language: locale.value
  }

  const toast = useToast()

  try {
    // 1. 先设置倒计时值
    countdown.value = 60
    // 2. 再禁用按钮
    canResend.value = false

    const res = await sendEmailVerifyCodeApi(submitData)

    if (res.message === 'success') {
      toast.add({
        title: t('auth.common.verify_code_has_been_send'),
        description: t('auth.common.messages.verify_code_email_send_tip_1') + emailInput.value + t('auth.common.messages.verify_code_email_send_tip_2'),
        icon: 'i-heroicons-envelope'
      })

      // 3. 开始倒计时
      if (timer) clearInterval(timer)
      timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          canResend.value = true
          clearInterval(timer)
          timer = null
        }
      }, 1000)
    }
  }
  catch (error) {
    canResend.value = true
    countdown.value = 0
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
async function onSubmit(formData: FormSubmitEvent<Schema>) {
  try {
    // console.log('Submitted', formData);
    loading.value = true

    // 表单数据在formData.data中
    const formValues = formData.data || {}

    const submitData: ResetPassType = {
      email: formValues.email,
      verify_code: formValues.verify_code,
      new_pass: formValues.new_pass,
      platform: 'web', // 用户在注册时使用的入口平台（web、app等）
      method: 'email' // 邮箱方式
    }

    const res = await resetPassApi(submitData)
    // alert('res：' + res)
    if (res.message === 'success') {
      // console.log('验证成功，Email:', res.data.email)
      alert(t('auth.common.messages.reset_password_success'))
      // alert('res：' + res)
      navigateTo({ path: '/' + locale.value + '/login' }) // 跳转到登录页面，带上邮箱账号
    }
    else {
      // alert(res.message)
      console.log('reset pass failed!') // 密码重置失败！
    }
  }
  finally {
    loading.value = false
  }
}
</script>
