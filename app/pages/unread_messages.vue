<template>
  <UChip
    id="unread-messages"
    :text="totalCount"
    size="3xl"
    inset
    color="error"
    :show="totalCount > 0"
  >
    <UButton
      icon="i-heroicons-bell"
      size="lg"
      color="neutral"
      variant="ghost"
      class="p-1 pt-1"
      :to="`/${locale}/member_message`"
    />
  </UChip>
</template>

<script setup lang="ts">
import { getMemberMessageList } from '@/api/member_message'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import type { HeadersTokenType } from '@/api/login/types'

const { t, locale } = useI18n()
const messages = ref([])
const totalCount = ref(0)
const queryParams = reactive<any>({
  is_read: false,
  message_type: 2
})

const handleQuery = () => {
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }

  getMemberMessageList(headers, queryParams).then((res) => {
    if (res.code === 200 && res.data) {
      messages.value = res.data
      totalCount.value = res.count || 0
    }
  })
}

onMounted(() => {
  handleQuery()

  // 添加事件监听
  document.querySelector('#unread-messages')?.addEventListener('update-count', handleQuery)
})
</script>
