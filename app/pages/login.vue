<template>
  <UCard class="mt-24 mb-24 w-10/12 max-w-sm bg-white/75 backdrop-blur lg:mt-32 lg:w-full dark:bg-white/5">
    <!-- title: 登录 、 separator: 或使用以下方式登录 -->
    <UAuthForm
      :fields="fields"
      :schema="schema"
      :providers="providers"
      icon="i-heroicons-arrow-right-end-on-rectangle-20-solid"
      :title="t('auth.login.title')"
      :separator="t('auth.common.or_login_using')"
      :submit="{
        label: t('auth.login.title'), // 登录
        size: 'lg',
        loading: loading
      }"
      @submit="onSubmit"
    >
      <template #description>
        <!-- 还没有账号? 注册 -->
        {{ t('auth.common.no_account_yet') }}
        <NuxtLink :to="'/' + locale + '/signup'" class="font-medium text-(--ui-primary)">{{ t('auth.signup.title') }}</NuxtLink>
      </template>

      <template #password-hint>
        <!-- 重置密码 -->
        <NuxtLink :to="'/' + locale + '/resetpass'" class="font-medium text-(--ui-primary)">{{ t('auth.reset_password.title') }}</NuxtLink>
      </template>

      <template #footer>
        <!-- 注册即表示您同意《使用条款》和《隐私政策》 -->
        {{ t('auth.common.confirm_agreement') }}《
        <NuxtLink :to="'/' + locale + '/terms/terms-of-service'" target="_blank" class="font-medium text-(--ui-primary)">{{ t('auth.common.terms_of_service') }}</NuxtLink>
        》 {{ t('auth.common.and') }}《
        <NuxtLink :to="'/' + locale + '/terms/privacy-policy'" target="_blank" class="font-medium text-(--ui-primary)">{{ t('auth.common.privacy_policy') }}</NuxtLink>
        》
      </template>
    </UAuthForm>
  </UCard>
</template>

<script setup lang="ts">
import * as z from 'zod' // 引入zod库（ https://zod.dev ）用于表单数据校验
import type { FormSubmitEvent } from '@nuxt/ui' // 引入表单提交事件类型
import type { GoogleSignInPlugin } from '@/types/google-signin'

import { COOKIE_EXPIRES_DATE, useAuthStoreWithOut } from '@/store/modules/auth'
import type { EmailLoginType, GoogleLoginType } from '@/api/login/types'
// 使用Google登录 https://vue3-google-signin.wavezync.com/guide/creating-custom-buttons.html
// import { useCodeClient, type ImplicitFlowSuccessResponse, type ImplicitFlowErrorResponse } from 'vue3-google-signin';

const { $googleSignIn } = useNuxtApp() as unknown as { $googleSignIn: GoogleSignInPlugin }
const googleLoading = ref(false)

const authStore = useAuthStoreWithOut()
const router = useRouter()
const route = useRoute()
const { t, locale } = useI18n()

// definePageMeta (此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'authentication' // 使用 layouts/authentication.vue 布局
})

// const expiresDate = new Date()
// expiresDate.setTime(expiresDate.getTime() + COOKIE_EXPIRES_DATE * 24 * 60 * 60 * 1000) // 设置cookie有效时间（要与后端服务设置的token有效时长一致）
// const ztsl_token = useCookie('ztsl_token', { expires: expiresDate })
const expiresDate = new Date()
expiresDate.setDate(expiresDate.getDate() + COOKIE_EXPIRES_DATE) // 设置cookie有效天数（要与后端服务设置的token有效时长一致）
expiresDate.setHours(expiresDate.getHours() + 8) // 调整为 东八区 CST 时间，8小时时差
const ztsl_token = useCookie('ztsl_token', { expires: expiresDate })

watch(
  () => ztsl_token.value,
  (newToken) => {
    ztsl_token.value = newToken
  }
)

useSeoMeta({
  titleTemplate: '',
  title: t('auth.login.title') + ' - ' + t('common.site_name') // 登录
})

/** 登录来源uri */
const skipToUri = ref('')

/** 加载 */
const loading = ref(false)

/** 登录来源终端，extension：扩展程序 */
const fromEndpoint = ref('')

/** 登录来源终端的页面：如page=popup */
const fromEndpointPage = ref('')

const isMobile = ref(false)

// 页面挂载时加载,判断用户Token是否存在，存在则跳转到用户主页面
onMounted(() => {
  // console.log({
  //   layout: route.meta.layout,
  //   path: route.path,
  //   locale: locale.value,
  //   fullPath: route.fullPath
  // });

  // 初始化移动端检测
  // isMobile.value = isMobileDevice();

  // 如果没有会员信息, 则清除token
  if (!authStore.getIsMember) {
    ztsl_token.value = ''
  }

  // route.meta.layout = 'authentication';
  skipToUri.value = route.query.skipToUri as string // 登录来源uri
  fromEndpoint.value = route.query.from as string // 登录来源终端，extension：扩展程序
  fromEndpointPage.value = route.query.page as string // 登录来源终端的页面：如page=popup
  const token = ztsl_token.value
  if (token) {
    router.push({ path: '/' + locale.value + '/account', replace: true })
  }
})

const handleOnSuccess = async () => {
  try {
    googleLoading.value = true
    console.log('开始Google登录流程')

    // 初始化Google Sign-In
    await $googleSignIn.init()
    console.log('Google Sign-In初始化成功')

    // 请求授权码
    const response = await $googleSignIn.requestCode({})
    console.log('获取到Google授权码:', response)
    // 将授权码发送到后端服务器进行验证
    const loginData: GoogleLoginType = {
      code: response.code ? response.code.toString() : '',
      state: response.state ? response.state.toString() : '',
      method: 'google', // 方式，email:邮箱，phone:手机号，wechat：微信，google：谷歌
      platform: 'web' // 平台，web:门户系统，app：移动端
    }

    // 请求后端服务器进行验证
    const authStore = useAuthStoreWithOut() // 要在函数内部使用，不能放文件头部统一创建
    const res = await authStore.googleLogin(loginData)
    if (res.code === 200) {
      ztsl_token.value = `${res.data.access_token}` // 将token保存到cookie,供扩展插件调用
      if (fromEndpoint.value === 'extension') {
        // 从扩展插件登录成功后
        if (fromEndpointPage.value === 'popup') {
          // 如果是从插件的popup页面发起的登录请求，直接进入web的账户中心
          navigateTo({ path: '/' + locale.value + '/account' })
        }
        else {
          // 如果是从插件的options页面发起的登录请求，返回成功消息给扩展插件（插件管理页接收到成功消息后会自动刷新账户信息）
          // window.opener.postMessage('success', '*');
          if (isMobile.value) {
            // 如果是移动端，则跳转到账户中心
            navigateTo({ path: '/' + locale.value + '/account' })
          }
          else {
            // 如果是PC端，则关闭窗口
            window.close()
          }
        }
      }
      else {
        // 登录成功，并提示,跳转到用户主页面
        navigateTo({ path: '/' + locale.value + '/account' })
      }
    }
    else {
      // 登录失败
      console.log('login failed', res.message)
      alert(res.message)
      navigateTo({ path: '/' + locale.value + '/login' })
    }
  }
  catch (error) {
    console.error('Google登录过程中发生错误:', error)
  }
  finally {
    googleLoading.value = false
  }
}

/** 表单字段 */
const fields = [
  {
    name: 'email',
    label: t('auth.common.email_label'), // 邮箱地址
    type: 'text' as const,
    size: 'lg',
    placeholder: t('auth.common.email_label') // 邮箱地址
  },
  {
    name: 'password',
    label: t('auth.common.password_label'), // 密码
    type: 'password' as const,
    size: 'lg',
    placeholder: t('auth.common.password_label') // 密码
  }
]

// 表单数据校验 https://zod.dev
const schema = z.object({
  email: z
    .string({
      required_error: t('auth.common.messages.email_required') // 请输入邮箱地址
    })
    .email(t('auth.common.messages.email_format_incorrect')), // 请输入正确的邮箱地址

  password: z.string({ required_error: t('auth.common.messages.password_required') }) // 请输入密码
})
// 表单数据校验模式
type Schema = z.output<typeof schema>

// 第三方登录，注意要使用计算属性， 否则 loading 状态不生效
const providers = computed(() => [
  {
    label: t('auth.common.login_with_wechat'), // 使用微信账号登录
    icon: 'i-simple-icons-wechat',
    size: 'lg',
    onClick: () => {
      navigateTo(
        {
          path: '/' + locale.value + '/wechat_login'
        },
        {
          replace: true // true:浏览器路由被替换，从而刷新页面
        }
      )
    }
  },
  {
    label: t('auth.common.login_with_google'), // 使用 Google 账号登录
    icon: 'i-ri-google-fill',
    size: 'lg',
    loading: googleLoading.value,
    disabled: googleLoading.value,
    style: { marginTop: '15px' }, // 添加顶部间距
    onClick: () => {
      handleOnSuccess()
    }
  }
])

// 登录表单提交事件
async function onSubmit(formData: FormSubmitEvent<Schema>) {
  // console.log('Submitted', formData)
  try {
    loading.value = true

    // 表单数据在formData.data中
    const formValues = formData.data || {}

    // 创建包含所有必需字段的登录数据对象
    const loginData: EmailLoginType = {
      email: formValues.email,
      password: formValues.password,
      method: 'email',
      platform: 'web'
    }

    const res = await authStore.emailLogin(loginData)
    if (res) {
      ztsl_token.value = `${res.data.access_token}` // 将token保存到cookie,供扩展插件调用
      if (fromEndpoint.value === 'extension') {
        // 从扩展插件登录成功后
        if (fromEndpointPage.value === 'popup') {
          // 如果是从插件的popup页面发起的登录请求，直接进入web的账户中心
          navigateTo({ path: '/' + locale.value + '/account' })
        }
        else {
          // 如果是从插件的options页面发起的登录请求，返回成功消息给扩展插件（插件管理页接收到成功消息后会自动刷新账户信息）
          // window.opener.postMessage('success', '*');
          // if (isMobile.value) {
          // 如果是移动端，则跳转到账户中心
          // navigateTo({ path: '/' + locale.value + '/account' });
          // } else {
          // 如果是PC端，则关闭窗口
          window.close()
          // }
        }
      }
      else {
        if (skipToUri.value) {
          // 如果带有skipToUri，则重定向到skipToUri
          navigateTo({ path: skipToUri.value })
        }
        else {
          // 登录成功，并提示,跳转到用户中心主页面
          navigateTo({ path: '/' + locale.value + '/account' })
        }
      }
    }
    else {
      console.log('res is null')
    }
  }
  catch (e: any) {
    console.log(e)
  }
  finally {
    loading.value = false // 结束加载
  }
}
</script>
