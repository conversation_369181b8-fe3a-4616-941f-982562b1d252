<template>
  <div class="text-center">
    <div class="mb-4">
      <input
        ref="fileInput"
        type="file"
        class="hidden"
        accept=".pdf,.doc,.docx,.ppt,.pptx,.png,.jpg,.jpeg"
        @change="handleFileChange"
      />
      <!-- 上传文件 -->
      <UButton
        v-if="memberHasLogin"
        class="rounded-lg px-12 py-2 whitespace-nowrap sm:text-base md:text-xl"
        size="xl"
        @click="handleTriggerFileInput"
      >
        {{ t('pdf_plus.upload_file') }}
      </UButton>
      <!-- 请先登录 -->
      <UButton
        v-else
        size="xl"
        class="rounded-lg px-12 py-2 whitespace-nowrap sm:text-base md:text-xl"
        @click="goLogin"
      >
        {{ t('pdf_plus.please_log_in_first') }}
      </UButton>
    </div>
    <!-- 请上传 PDF 格式文件 -->
    <!-- <p v-if="memberHasLogin" class="text-gray-500">{{ t('pdf_plus.upload_file_tips') }}</p> -->
    <!-- 用于高度占位（无内容） -->
    <!-- <p v-else class="min-h-6 text-gray-500"></p> -->
    <!-- 错误信息 -->
    <div v-if="errorMessage && errorMessage.length > 0" class="bg-gray-20 mt-7 flex justify-center">
      <div class="flex flex-col items-center">
        <span class="mb-3 text-base text-red-600 dark:text-red-400">
          <!-- 上传文件大小不能超过 100MB -->
          {{ errorMessage }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PDFDocument } from 'pdf-lib'
import { parseFile } from '@/utils/file'

// 获取MinerU配置
import { useMinerUConfig } from '@/composables/usePluginData'

// 定义组件props
defineProps({
  memberHasLogin: {
    type: Boolean,
    default: false
  }
})

// 定义组件emits
const emit = defineEmits(['fileChange'])

// 国际化
const { t, locale } = useI18n()
// 提示框
const toast = useToast()

onMounted(async () => {
  // 初始化数据
  const minerUConfig = await useMinerUConfig()
  if (!minerUConfig.minerUTranslateToken || minerUConfig.minerUTranslateToken.length <= 0) {
    errorMessage.value = '请先在插件的【文档翻译】设置中填入有效的 MinerU 的 API Token'
  }
})

// 跳转登录页面
function goLogin() {
  navigateTo({ path: '/' + locale.value + '/login' })
}

// 文件输入框引用
const fileInput = ref(null)
// 点击选择文件
const handleTriggerFileInput = () => {
  // 清空错误信息
  errorMessage.value = ''
  fileInput.value?.click()
}

// 文件超大提示信息
const isTheFileSizeIsOversized = ref(false)
// 错误信息
const errorMessage = ref('')

// 选择文件
async function handleFileChange(event: Event) {
  const targetFile = (event.target as HTMLInputElement)?.files?.[0]
  if (!targetFile) {
    emit('fileChange', { parse_success: false })
    return
  }

  try {
    const fileSizeMb = targetFile.size / 1024 / 1024
    // 显示文件大小超出限制
    isTheFileSizeIsOversized.value = fileSizeMb > 100
    if (isTheFileSizeIsOversized.value) {
      errorMessage.value = '上传文件大小不能超过 100MB' // 上传文件大小不能超过 100MB
      return
    }

    const fileInfo = await parseFile(targetFile)

    if (fileInfo.fileType === 'pdf') {
      // 读取文件内容
      const arrayBuffer = await targetFile.arrayBuffer()
      const pdf = await PDFDocument.load(arrayBuffer, {
        ignoreEncryption: true,
        throwOnInvalidObject: false
      })
      if (pdf.isEncrypted) {
        // 如果是加密的PDF文件，尝试解密
        errorMessage.value = '该文件已加密，请解密后重新上传'
        return
      }
    }

    // 返回文件信息
    emit('fileChange', {
      parse_success: fileInfo.parse_success,
      file: targetFile,
      fileType: fileInfo.fileType,
      fileName: fileInfo.fileName,
      fileSize: fileInfo.fileSize,
      fileSizeMb: fileInfo.fileSizeMb,
      pageCount: fileInfo.pageCount,
      isEncrypted: fileInfo.isEncrypted
    })
  }
  catch (error) {
    if (error.message.includes('PDFDict2') || error.message.includes('undefined')) {
      toast.add({
        title: t('pdf_plus.messages.parsing_failed'), // 解析失败
        description: t('pdf_plus.messages.parsing_failed_tips_2'), // 無法解析文件-文件可能已損壞或加密方式不受支持
        color: 'error'
      })
    }
    else if (error.message.includes('encrypted')) {
      toast.add({
        title: t('pdf_plus.messages.parsing_failed'), // 解析失败
        description: t('pdf_plus.messages.parsing_failed_tips_3'), // '无法解析加密文件'
        color: 'error'
      })
    }
    else {
      console.log('解析PDF文件失败: ' + error.message)
    }
    emit('fileChange', {
      parse_success: false,
      file: targetFile,
      fileName: targetFile.name,
      fileSize: targetFile.size,
      fileSizeMb: targetFile.size / 1024 / 1024,
      pageCount: 0,
      isEncrypted: false
    })
  }
}
</script>

<style scoped></style>
