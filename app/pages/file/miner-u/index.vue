<template>
  <div class="w-full pt-6">
    <!-- 检测是否安装浏览器插件 -->
    <TranslateType :type="'miner_u'" />

    <!-- 如果有htmlContent，则显示解析后的内容 -->
    <div v-if="step === 1 || step === 2" class="mx-auto mb-2 min-h-[480px] max-w-7xl p-6 pt-0">
      <main class="rounded-md bg-gray-100 dark:bg-gray-800">
        <div class="flex max-h-96 min-h-92 flex-col items-center justify-center rounded-lg border-1 border-solid border-gray-200 p-6 sm:p-6 md:p-8 lg:p-10 xl:p-12 dark:border-gray-700">
          <div v-if="!targetFile" class="mx-auto mb-4 h-32 justify-items-center">
            <!-- 支持的文件类型 -->
            <div v-for="item in supportedFileTypes" :key="item.type" class="inline-flex flex-col items-center">
              <img :src="item.src" class="mx-5 size-12 sm:size-12 md:flex md:size-16 lg:flex lg:size-20 xl:flex xl:size-20" />
              <span class="mt-3 text-xs sm:text-xs md:text-sm lg:text-sm text-gray-600">{{ item.label }}</span>
            </div>
          </div>

          <ExtensionInstalled :is-show-product="false" loading-type="button">
            <!-- 第一步：文件选择 -->
            <upload-section v-if="step === 1" :member-has-login="memberHasLogin" @file-change="handleFileSelectedChange" />

            <!-- 第二步：设置翻译配置 -->
            <translate-config
              v-else
              ref="translateConfigRef"
              @reset-file="resetFile"
              @start-upload="startUpload"
            />
          </ExtensionInstalled>
        </div>
      </main>
    </div>

    <!-- MinerU 解析翻译介绍 -->
    <UPageSection
      :ui="{
        container: 'py-10 sm:py-10 md:py-10 lg:py-10 xl:py-10',
        title: 'text-2xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #title>
        <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">精挑翻译 + MinerU</span>
      </template>
      <template #description>
        <span>
          <a
            href="https://mineru.net/"
            target="_blank"
            rel="noopener noreferrer"
            class="text-blue-500"
          >MinerU</a>
          是上海人工智能实验室旗下「OpenDataLab」开源的一款功能极其强大的文档解析工具，诞生于【书生-浦语】大语言模型的预训练过程中。专注于攻克科技文献、专业报告等复杂文档中的符号转化与内容理解难题。在
          <a
            href="https://github.com/opendatalab/mineru"
            target="_blank"
            rel="noopener noreferrer"
            class="text-blue-500"
          >GitHub</a>
          上的 Star 超 40K，并且可以免费使用。
        </span>
        <br />
        <span class="font-semibold">精挑翻译与 MinerU 深度结合，为用户提供高质量的文档解析翻译体验。</span>
      </template>
    </UPageSection>

    <!-- 核心功能展示 -->
    <UPageSection
      v-for="(section, index) in sections"
      :key="index"
      :title="section.title"
      :description="section.description"
      :orientation="section.orientation"
      :reverse="section.reverse"
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-24 gap-0 sm:gap-0',
        title: 'text-xl sm:text-xl md:text-xl lg:text-2xl xl:text-3xl',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg',
        features: 'text-sm sm:text-sm md:text-base lg:text-base xl:text-base'
      }"
    >
      <template #title>
        <span class="">{{ section.title }}</span>
      </template>
      <template #features />
      <!-- 双语对照 + 双页联动 -->
      <UCarousel
        v-if="index === 0"
        v-slot="{ item }"
        :items="mineru_translation_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img
          :src="String(item)"
          class="w-full"
          draggable="false"
          alt="MinerU"
        />
        <!-- <video
          :src="String(item)"
          muted
          autoplay
          loop
          playsinline
          preload="none"
          class="w-full rounded-md border-2 border-gray-400 dark:border-gray-600"
        /> -->
      </UCarousel>
      <!-- 复杂元素精准提取 -->
      <UCarousel
        v-if="index === 1"
        v-slot="{ item }"
        :items="mineru_complex_content_parsing_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
      <!-- 扫描版文档解析翻译 -->
      <UCarousel
        v-if="index === 2"
        v-slot="{ item }"
        :items="mineru_scan_document_translation_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-sm"
        indicators
      >
        <img :src="String(item)" class="w-full" draggable="false" />
      </UCarousel>
    </UPageSection>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'

// 初始化数据
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'

// 组件相关
import UploadSection from '@/pages/file/miner-u/UploadSection.vue'
import TranslateConfig from '@/pages/file/miner-u/TranslateConfig.vue'
import ExtensionInstalled from '@/pages/components/ExtensionInstalled/index.vue'
import { useAuthStoreWithOut } from '@/store/modules/auth'

// 翻译类型
import TranslateType from '@/pages/components/TranslateType.vue'

// 文档翻译设置存储
import { useDocumentTranslateStore } from '@/store/documentTranslate'

import { useFileStore } from '@/store/FileStore.ts'
import { parseFile } from '@/utils/file.ts'

const documentTranslateStore = useDocumentTranslateStore()
const { translateEngine } = storeToRefs(documentTranslateStore)

const fileStore = useFileStore()

// 国际化
const { t, locale } = useI18n()

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('mineru_doc_translation.title') + ' - ' + t('common.site_name'), // MinerU 文档翻译
  ogTitle: t('mineru_doc_translation.title') + ' - ' + t('common.site_name'),
  // MinerU 文档翻译，使用 MinerU 精确解析提取文档中的文本、图片、表格、公式等元素内容，再由精挑翻译对解析后的文本进行智能翻译。文档在线浏览与文档解析翻译完美结合，为您提供极致的文档翻译体验！
  description: t('mineru_doc_translation.description'),
  ogDescription: t('mineru_doc_translation.description')
})

/**
 * 步骤1: 选择文件
 * 步骤2: 设置翻译配置
 */
const step = ref(1) // 当前步骤
const memberHasLogin = ref(false) // 是否登录
let targetFile: File = null // 目标文件

// 支持的文件类型配置
interface FileTypeConfig {
  type: string
  label: string
  src: string
}

const supportedFileTypes: FileTypeConfig[] = [
  {
    type: 'pdf',
    label: t('document_translation.pdf.label'), // pdf 文件
    src: '/assets/images/document/bi--file-earmark-pdf.svg'
  },
  {
    type: 'word',
    label: t('document_translation.docx.label'), // word 文件
    src: '/assets/images/document/bi--file-earmark-word2.svg'
  },
  {
    type: 'ppt',
    label: t('document_translation.ppt.label'), // ppt 文件
    src: '/assets/images/document/bi--file-earmark-ppt2.svg'
  },
  {
    type: 'image',
    label: t('document_translation.image.label'), // 图片文件
    src: '/assets/images/document/bi--file-earmark-image.svg'
  }
]

// 文件信息
const fileInfo = ref({
  fileType: '', // 文件类型
  pageCount: 0, // 文档页数
  fileName: '', // 文件名
  fileSize: 0 // 文件大小
})

const authStore = useAuthStoreWithOut()

onBeforeMount(async () => {
  await sendMessageToChromeExtension(locale.value, translateEngine.value.value) // 发送消息到浏览器插件
  memberHasLogin.value = authStore.getToken && authStore.getToken !== '' // 登录状态
})
// 首先先判断是否有翻译引擎
onMounted(async () => {
  if (fileStore.uploadedFile) {
    const fileInfo = await parseFile(fileStore.uploadedFile)
    await handleFileSelectedChange(fileInfo)
    // 清空待上传文件
    fileStore.uploadedFile = null
  }
})

// 翻译配置组件
const translateConfigRef = ref(null)

/**
 * 文件选择回调
 * @param targetFileInfo 文件信息
 */
async function handleFileSelectedChange(targetFileInfo: any) {
  // 选择的文件不存在
  if (!targetFileInfo.file) {
    return
  }
  // 设置步骤为2，表示进入翻译配置阶段
  step.value = 2

  // 文件信息
  targetFile = targetFileInfo.file
  // test()

  // 文件类型
  fileInfo.value.fileType = targetFileInfo.fileType
  // 文件名
  fileInfo.value.fileName = targetFileInfo.fileName
  // 获取文件页数
  fileInfo.value.pageCount = targetFileInfo.pageCount
  // 文件大小
  fileInfo.value.fileSize = targetFileInfo.fileSize

  await nextTick()
  // 初始化翻译配置
  translateConfigRef.value.init(fileInfo.value)
}

// 重置文件
const resetFile = () => {
  step.value = 1 // 重置步骤为1,表示回到文件选择阶段
  targetFile = undefined // 清空已选择的文件
}
/**
 * 开始上传文件
 */
const startUpload = async (inputPages: string) => {
  await nextTick()
  fileStore.setFile(targetFile)
  await useRouter().push({
    path: `/${locale.value}/file/miner-u/view`,
    query: {
      inputPages: inputPages
    }
  })
}

//  双语对照+双页联动
const mineru_translation_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']
// 扫描文档翻译
const mineru_scan_document_translation_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']
// 复杂内容解析
const mineru_complex_content_parsing_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

// sections
type Section = {
  title: string
  description: string
  orientation: 'horizontal' | 'vertical'
  reverse: boolean
}
const sections: Section[] = [
  {
    title: '双语对照 + 双页联动',
    description: '文档在线浏览与文档解析翻译完美结合，可点击原文快速定位译文，也可通过译文联动找到原文，让学习浏览更高效，让对比校验更便捷。',
    orientation: 'vertical', // 布局方向
    reverse: false // 是否反转布局
  },
  {
    title: '复杂元素精准提取',
    description: '基于多模态智能解析技术，精准提取文本、数学公式、图片、表格、分子结构、化学反应式等复杂元素。原样保留公式和图表，智能翻译文本内容，轻松应对各种专业学术文档的解析翻译！',
    orientation: 'vertical', // 布局方向
    reverse: false // 是否反转布局
  },
  {
    title: '扫描版文档翻译',
    description: 'OCR 光学字符识别与多模态智能解析技术相结合，精确识别和提取扫描版文档中的内容，让扫描版文档也能实现高质量的翻译。',
    orientation: 'vertical', // 布局方向
    reverse: false // 是否反转布局
  }
]
</script>

<style scoped></style>
