<template>
  <!-- 显示文件信息 -->
  <div class="w-full">
    <div class="mb-6 flex items-center justify-between">
      <div class="flex items-center">
        <img :src="getFileIcon(fileInfo.fileType)" :alt="fileInfo.fileName" class="mr-4 h-12 w-12" />
        <div>
          <!-- 文件名 -->
          <h3 class="font-medium text-gray-900 dark:text-gray-200">
            {{ t('pdf_plus.upload_file_name') }}：{{ fileInfo.fileName }}
          </h3>
          <!-- 共 XX 页 -->
          <p class="text-gray-500">
            {{ t('pdf_plus.total') }}&nbsp;{{ fileInfo.pageCount }}&nbsp;{{ t('pdf_plus.pages') }}
          </p>
        </div>
      </div>
      <!-- 取消选中文件 -->
      <UButton
        color="neutral"
        variant="ghost"
        class="whitespace-nowrap"
        trailing-icon="i-heroicons-x-mark"
        @click="cancelSelectedFile"
      >
        {{ t('common.cancel') }}
      </UButton>
    </div>
    <!-- 翻译模型选择区域 -->
    <div class="rounded-md bg-gray-50 p-6 dark:bg-gray-900">
      <div
        class="grid grid-cols-1 justify-between gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-2 xl:grid-cols-3"
        :class="{
          'xl:grid-cols-4': fileInfo.fileType === 'pdf' || fileInfo.fileType === 'ppt' || fileInfo.fileType === 'word'
        }"
      >
        <!-- 翻译模型 -->
        <div class="flex-1 basis-1/10">
          <!-- 翻译模型 -->
          <label class="mb-2 block text-gray-500">{{ t('pdf_plus.translation_model') }}</label>
          <DocumentModelSelect
            :translate-engine-config="documentTranslateEngine"
            :variant="'outline'"
            :enabled-engines-list="enabledEnginesList"
            @update:translate-engine-config="onSelectionChangeTranslateEngine"
          />
        </div>
        <!-- 目标语言选择区域 -->
        <div class="flex-1">
          <!-- 目标语言 -->
          <label class="mb-2 block text-gray-500">{{ t('pdf_plus.target_language') }}</label>
          <USelectMenu
            v-model="targetLanguageZingCode"
            size="xl"
            value-key="value"
            :items="languageList"
            class="w-full"
            :ui="{ content: 'w-full' }"
            @update:model-value="onSelectionChangeTargetLanguage"
          />
        </div>
        <!-- 译文显示选择区域 -->
        <div class="flex-1">
          <!-- 译文显示 -->
          <label class="mb-2 block text-gray-500">{{ t('pdf_plus.translation_mode') }}</label>
          <USelectMenu
            v-model="translationsDisplay"
            size="xl"
            value-key="value"
            :search-input="false"
            :items="displayOptions"
            class="w-full"
            :ui="{ content: 'w-full' }"
            @update:model-value="onSelectionChangeTranslationsDisplay"
          />
        </div>
        <div v-if="fileInfo.fileType === 'pdf' || fileInfo.fileType === 'ppt' || fileInfo.fileType === 'word'" class="flex-1">
          <!-- 页面范围 -->
          <label class="mb-2 block text-gray-500">{{ t('pdf_plus.page_range') }}</label>
          <UPopover mode="click">
            <template #default>
              <UButton color="neutral" variant="outline" class="bg-default flex h-[40px] w-full items-center justify-center rounded-md border-gray-300 p-1">
                <span
                  :class="{
                    'flex-grow text-center text-base': true,
                    'text-red-500 dark:text-red-400': inputPagesError // 当 inputPagesError 为 true 时应用红色字体
                  }"
                >
                  {{ selectedPages }}
                </span>
                <UIcon class="mr-2 size-5 bg-gray-400" name="i-lucide:chevron-down" />
              </UButton>
            </template>
            <template #content>
              <div class="translation-tip bg-default rounded-md p-3 text-sm whitespace-pre-line">
                <div class="mb-1 flex justify-start">
                  <!-- 指定页面范围 -->
                  <label class="text-smmr-10 flex text-gray-500">{{ t('pdf_plus.specify_page_range') }}</label>
                  <UPopover
                    mode="hover"
                    class="cursor-pointer align-bottom"
                    arrow
                    :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
                  >
                    <UIcon name="heroicons:information-circle" class="size-5 shrink-0 text-neutral-400" />
                    <template #content>
                      <div class="block max-w-100 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                        <!-- 1、不设置则，则默认翻译整个 PDF -->
                        <p class="mb-2">
                          1、{{ t('pdf_plus.specify_page_range_tips_1') }}
                        </p>
                        <!-- 2、指定页码，使用英文逗号分隔：1,3,-1 -->
                        <p class="mb-2">
                          2、{{ t('pdf_plus.specify_page_range_tips_2') }}
                        </p>
                        <!-- 3、使用"-"指定范围，如第1页到第5页：1-5 -->
                        <p class="mb-2">
                          3、{{ t('pdf_plus.specify_page_range_tips_3') }}
                        </p>
                        <!-- 4、第5页到倒数第3页：5--3 -->
                        <p class="mb-2">
                          4、{{ t('pdf_plus.specify_page_range_tips_4') }}
                        </p>
                        <!-- 5、倒数第5页到倒数第2页：-5--2 -->
                        <p class="mb-2">
                          5、{{ t('pdf_plus.specify_page_range_tips_5') }}
                        </p>
                        <!-- 6、组合使用页码及范围，使用英文逗号分隔：1,3,5-8,-4--2 -->
                        <p>6、{{ t('pdf_plus.specify_page_range_tips_6') }}</p>
                      </div>
                    </template>
                  </UPopover>
                </div>
                <input
                  v-model="inputPages"
                  type="text"
                  :class="{
                    'bg-waite h-[40px] w-full rounded-md border border-gray-200 p-3 text-center text-base dark:bg-gray-800': true,
                    'text-red-500 dark:text-red-400': inputPagesError // 当 inputPagesError 为 true 时应用红色字体
                  }"
                  @update:model-value="handleTranslationPagesChange"
                />
              </div>
            </template>
          </UPopover>
          <span class="w-full text-center text-sm break-words text-gray-400 dark:text-gray-600">{{ inputPages }}</span>
        </div>
      </div>
    </div>
    <div class="bg-gray-20 mt-7 flex justify-center">
      <div class="flex flex-col items-center">
        <!-- 当前文件仅有 {{ fileInfo.pageCount }} 页，请输入正确页码范围 -->
        <!-- The current file has only XX pages, please enter the correct page range -->
        <span v-if="isShowExceedHint" class="mb-3 text-base text-red-500 dark:text-red-400">
          {{ t('pdf_plus.page_range_limit_tips_1') }}&nbsp;{{ fileInfo.pageCount }}&nbsp;{{ t('pdf_plus.page_range_limit_tips_2') }}
        </span>
        <!-- 立即翻译 -->
        <UButton
          size="xl"
          color="primary"
          class="rounded-md px-10 py-2 whitespace-nowrap"
          :disabled="inputPagesError"
          @click="startTranslate"
        >
          {{ t('pdf_plus.translate_now') }}
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 文件信息
// 初始化数据
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'

// 文档翻译设置存储
import { useDocumentTranslateStore } from '@/store/documentTranslate'

interface FileInfo {
  fileType: string // 文件类型
  pageCount: number // PDF页数
  fileName: string // 文件名
  fileSize: number // 文件大小
}
const fileInfo = ref<FileInfo>({
  fileType: '', // 文件类型
  pageCount: 0, // 文档页数
  fileName: '', // 文件名
  fileSize: 0 // 文件大小
})

// 定义组件emits
const emit = defineEmits(['resetFile', 'startUpload'])

// 国际化
const { t, locale } = useI18n()

const documentTranslateStore = useDocumentTranslateStore()
const { enabledEnginesList, translateEngine, translationsDisplay, targetLanguageZingCode, targetLanguage, targetLanguageList } = storeToRefs(documentTranslateStore)

// 取消选中文件
const cancelSelectedFile = () => {
  emit('resetFile')
}

// 更新目标语言配置
function updateTargetLanguage() {
  // 从语言集合获取对应的lang_code
  const languageItem = languageList.value.find((item) => {
    return item.value === targetLanguageZingCode.value
  })

  if (languageItem) {
    targetLanguage.value = languageItem.lang_code
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
  else {
    // 如果找不到，则将目标语言设置为简体中文
    targetLanguage.value = 'zh-Hans'
    targetLanguageZingCode.value = 'zh-Hans'

    documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value) // 更新目标语言智应编码
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
}

// 选择的翻译引擎
const documentTranslateEngine = computed({
  get() {
    return translateEngine.value // 从 Store 中获取值
  },
  set(newValue) {
    // 通过 Store 方法或直接修改响应式变量
    translateEngine.value = newValue
  }
})
// 操作切换翻译引擎（同步插件）
async function onSelectionChangeTranslateEngine(data) {
  if (!data) return

  await sendMessageToChromeExtension(locale.value, data.value) // 发送消息到浏览器插件

  // 更新目标语言配置
  updateTargetLanguage()
}

// 翻译选项数据- 语言集
const languageList = computed({
  get() {
    return targetLanguageList.value
  },
  set(newValue) {
    // 通过 Store 方法或直接修改响应式变量
    targetLanguageList.value = newValue
  }
})
// 操作切换目标语言
function onSelectionChangeTargetLanguage() {
  // 更新本地存储目标语言
  documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value)

  // 更新目标语言配置
  updateTargetLanguage()
}

// 译文显示选项
const displayOptions = ref([
  { value: 'bilingual', label: t('pdf_plus.bilingual_translation') }, // 双语对照（灰色背景）
  { value: 'fulltext', label: t('pdf_plus.fulltext_translation') } // 全文翻译（仅译文）
])
// 操作切换译文显示
function onSelectionChangeTranslationsDisplay(value) {
  documentTranslateStore.setTranslationsDisplay(value)
}

const selectedPages = ref('') // 翻译页数范围3:6
const totalPages = ref(0) // 实际要求解析的总页数
const inputPages = ref('') // 输入页数范围 3,6,-2
const inputPagesError = ref(false) // 输入页数范围错误
const error = ref('') // 错误信息
const isShowHint = ref(false) // 是否显示提示信息
const isShowExceedHint = ref(false) // 是否超出显示提示信息
const timer = ref(null) // 用户输入页数-防抖定时任务

/**
 * 初始化
 * @param info 文件信息
 */
function init(info: FileInfo): void {
  fileInfo.value = { ...info }

  // 获取文件页数
  totalPages.value = info.pageCount
  // 翻译页数的范围，默认为所有页
  selectedPages.value = info.pageCount + ' / ' + info.pageCount
  // 输入页数错误，默认为false
  inputPagesError.value = false
  // 显示提示，默认为false
  isShowHint.value = false
}

// 输入翻译页数范围
function handleTranslationPagesChange() {
  clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    // 用户停止输入后的操作
    isShowExceedHint.value = false
    isShowHint.value = false
    inputPagesError.value = false

    const successInputPages = new Set()
    if (inputPages.value !== null && inputPages.value !== '') {
      // 正则表达式，匹配数字、数字范围、负数形式的页码
      const regex = /^(-?\d+(-\d+)?|-\d+|(-?\d+)-(-?\d+))(,(-?\d+(-\d+)?|-\d+|(-?\d+)-(-?\d+)))*$/
      // 测试输入字符串
      if (!regex.test(inputPages.value)) {
        // 如果输入不合法，显示错误信息
        inputPagesError.value = true
        // error.value = '请输入正确的页面范围';
        isShowExceedHint.value = true
        return
      }
      else {
        inputPagesError.value = false
        error.value = ''
      }
      // 清除翻译结果
      // 分割字符串
      const parts = inputPages.value.split(',')
      parts.forEach((part) => {
        if (part.includes('-')) {
          // 判断是不是负数
          const negativeRegex = /^-\d+$/
          if (negativeRegex.test(part)) {
            const negativePart = parseInt(part, 10)
            if (Math.abs(negativePart) > fileInfo.value.pageCount) {
              // 如果负数超出范围
              inputPagesError.value = true
              // error.value = '请输入正确的页面范围';
              isShowExceedHint.value = true
              return
            }
            // 判断是不是负数,例如：-3
            const page = fileInfo.value.pageCount + negativePart + 1
            successInputPages.add(page)
            // 退出循环
            return
          }

          let partList = []
          if (part.includes('--')) {
            const parts = part.split('--')
            if (parts.length !== 2) return [] // 无效格式
            partList = [parseInt(parts[0], 10), -parseInt(parts[1], 10)]
          }
          else if (part[0] !== '-') {
            const parts = part.split('-')
            if (parts.length !== 2) return [] // 无效格式
            partList = [parseInt(parts[0], 10), parseInt(parts[1], 10)]
          }
          else if (part[0] === '-') {
            const match = part.match(/^(-?\d+)-(\d+)$/)
            if (!match) return [] // 格式无效
            partList = [parseInt(match[1], 10), parseInt(match[2], 10)]
          }

          const originalStart = partList[0]
          const originalEnd = partList[1]
          if (Math.abs(originalStart) > fileInfo.value.pageCount) {
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }
          if (Math.abs(originalEnd) > fileInfo.value.pageCount) {
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }
          // 处理负数范围
          if (originalStart < 0 && originalEnd < 0) {
            if (originalStart > originalEnd) {
              // 负数范围(结束页码大于开始页码)
              inputPagesError.value = true
              // error.value = '请输入正确的页面范围';
              isShowExceedHint.value = true
              return
            }
          }
          if (originalStart < 0 && originalEnd > 0) {
            // 开始页数为负数，结束页数为正数
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }

          // 处理范围
          const [start, end] = partList.map((num) => {
            if (Math.abs(num) > fileInfo.value.pageCount) {
              inputPagesError.value = true
              // error.value = '请输入正确的页面范围';
              isShowExceedHint.value = true
              return
            }
            if (num < 0) num = fileInfo.value.pageCount + num + 1 // 负数转换
            return Math.max(1, Math.min(num, fileInfo.value.pageCount)) // 限制在有效范围内
          })

          // 添加范围内的所有页码
          for (let i = Math.min(start, end); i <= Math.max(start, end); i++) {
            successInputPages.add(i)
          }
        }
        else {
          // 单个页码
          let page = parseInt(part, 10)
          if (page > fileInfo.value.pageCount) {
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }
          if (isNaN(page)) return // 忽略无效的页码
          if (page < 0) page = fileInfo.value.pageCount + page + 1 // 负数转换
          if (1 <= page && page <= fileInfo.value.pageCount) {
            successInputPages.add(page)
          }
        }
      })
      selectedPages.value = successInputPages.size + ' / ' + fileInfo.value.pageCount
      if (successInputPages.size > fileInfo.value.pageCount || successInputPages.size == 0) {
        // 如果翻译页数大于剩余页数 禁用翻译按钮 则允许翻译去除禁用翻译按钮
        // 请修改翻译页数，最多不超过
        inputPagesError.value = true
        isShowExceedHint.value = true
      }
      totalPages.value = successInputPages.size
    }
    else {
      error.value = ''
      // 清除所有内容
      selectedPages.value = fileInfo.value.pageCount + ' / ' + fileInfo.value.pageCount
      totalPages.value = fileInfo.value.pageCount
    }
  }, 100) // 设置延迟时间，单位为毫秒
}

/**
 * 启动翻译
 */
function startTranslate() {
  emit('startUpload', inputPages.value)
}

/**
 * 根据文件类型获取对应的图标路径
 * @param fileType 文件类型
 * @returns 图标路径
 */
function getFileIcon(fileType: string): string {
  // 类型映射
  // 图片：.png,.jpg,.jpeg
  // ppt：.ppt,.pptx
  // doc：.doc,.docx
  // pdf：.pdf

  const iconMap: { [key: string]: string } = {
    pdf: '/assets/images/document/bi--file-earmark-pdf.svg',
    ppt: '/assets/images/document/bi--file-earmark-ppt2.svg',
    word: '/assets/images/document/bi--file-earmark-word2.svg',
    image: '/assets/images/document/bi--file-earmark-image.svg'
  }

  // 将文件类型转换为小写进行匹配
  const lowerFileType = fileType?.toLowerCase() || ''

  // 返回对应的图标，如果找不到则返回默认的文档图标
  return iconMap[lowerFileType] || '/assets/images/document/bi--file-earmark-text.svg'
}

defineExpose({
  init
})
</script>

<style scoped>
.translation-tip {
  position: absolute;
  top: 60%; /* 垂直居中对齐图标 */
  left: 80%; /* 显示在图标右侧 */
  transform: translateX(-50%);
  padding: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 190px;
  z-index: 1000;
  word-break: break-all;
}
</style>
