<template>
  <div class="flex h-screen flex-col bg-gray-200 dark:bg-gray-900">
    <client-only>
      <!-- 头部栏 -->
      <DocumentTransHeader
        document-type="miner-u"
        :show-switch-bilingual="true"
        :initial-open-comparison="openComparison"
        @file-open="handleOpenFileSelect"
        @open-comparison="handleOpenComparison"
      >
        <template #right>
          <UDropdownMenu size="xl" :items="exportItem">
            <!-- 导出文件 -->
            <UButton
              :label="t('document_translation.export_file')"
              size="xl"
              color="secondary"
              variant="outline"
              class="rounded-md"
              :disabled="!docFile"
              trailing-icon="i-heroicons-chevron-down-20-solid"
            />
          </UDropdownMenu>
        </template>
      </DocumentTransHeader>
      <div class="h-(--ui-header-height) min-h-(--ui-header-height)" />

      <ExtensionInstalled
        translate-type="miner-u"
        loading-type="icon"
        :is-show-product="true"
        size="large"
      >
        <!-- 上传进度条 -->
        <div
          v-show="uploadFileState"
          class="absolute top-1/2 left-1/2 flex h-40 w-80 -translate-x-1/2 -translate-y-1/2 flex-col items-center justify-center"
        >
          <UProgress size="2xl" :value="uploadProgress" />
          <!-- 正在解析文件... -->
          <p class="mt-1 text-xl">
            {{ t("pdf_plus.parsing_the_file") }}&nbsp;{{ uploadProgress }}%
          </p>
          <div
            v-if="uploadErrMsg && uploadErrMsg.length > 0"
            class="bg-gray-20 mt-5 flex justify-center"
          >
            <div class="flex flex-col items-center">
              <!-- 当前文件仅有 {{ fileInfo.pageCount }} 页，请输入正确页码范围 -->
              <span class="mb-3 text-base text-red-500 dark:text-red-400">{{
                uploadErrMsg
              }}</span>
            </div>
          </div>
        </div>
        <!-- 文档区域 -->
        <div v-show="!uploadFileState" class="flex h-[calc(100vh-64px)] flex-row">
          <!-- 原文件区域 -->
          <div
            class="flex-1 overflow-hidden transition-all duration-500 ease-in-out"
            :class="{
              '-ml-[100%]': !openComparison
            }"
          >
            <pdf-viewer
              id="pdfViewer"
              ref="pdfViewerRef"
              :mode="'reference'"
              @ready="pdfViewerReady"
              @loaded="handleBrowserTitle"
              @page-loaded="handlePageLoaded"
            />
          </div>
          <!-- 翻译文件区域 -->
          <div
            class="flex-1 overflow-auto box-border"
            :class="{ 'bg-white': docContent.length > 0 }"
          >
            <MarkdownRenderer
              v-if="docContent.length > 0"
              :id="'miner-u-renderer'"
              :markdown="docContent"
              class="mx-auto my-20"
              @value-updated="handleDocContentUpdate"
            />
          </div>
        </div>
      </ExtensionInstalled>

      <input
        ref="fileInputRef"
        type="file"
        class="hidden"
        accept=".pdf,.doc,.docx,.ppt,.pptx,.png,.jpg,.jpeg"
        @change="handleFileChange"
      />

      <!-- 用于导出文档 -->
      <iframe ref="printFrame" class="hidden" />
    </client-only>
  </div>
</template>

<script setup lang="ts">
import { default as PdfViewer } from '@/components/FreePdf/PdfViewer.vue'
import { default as DocumentTransHeader } from '@/pages/components/DocumentTransHeader/index.vue'
import { useFileStore } from '@/store/FileStore'
import { useMinerUConfig } from '@/composables/usePluginData'
import { getFileType } from '@/utils/file'
/**
 * 文件上传处理
 */
import {
  fileUrlsBatch,
  uploadFile,
  downloadFileContent,
  extractTaskBatch
} from '@/api/external/mineru'
import { default as MarkdownRenderer } from '@/pages/components/MarkdownRenderer.vue'
import { default as ExtensionInstalled } from '@/pages/components/ExtensionInstalled/index.vue'
import { usePluginTranslate } from '@/composables/usePluginTranslate'
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { storeToRefs } from 'pinia'
import type { PDFPageView } from 'pdfjs-dist/types/web/pdf_page_view'

const fileStore = useFileStore()

// 国际化
const { t, locale } = useI18n()

const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()

const documentTranslateStore = useDocumentTranslateStore()
const {
  translateEngine,
  translationsDisplay,
  targetLanguage,
  targetLanguageList,
  targetLanguageZingCode
} = storeToRefs(documentTranslateStore)

// 禁用默认布局
definePageMeta({
  layout: false
})

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('mineru_doc_translation.title') + ' - ' + t('common.site_name'), // MinerU 文档翻译
  ogTitle: t('mineru_doc_translation.title') + ' - ' + t('common.site_name'),
  // MinerU 文档翻译，使用 MinerU 精确解析提取文档中的文本、图片、表格、公式等元素内容，再由精挑翻译对解析后的文本进行智能翻译。文档在线浏览与文档解析翻译完美结合，为您提供极致的文档翻译体验！
  description: t('mineru_doc_translation.description'),
  ogDescription: t('mineru_doc_translation.description')
})

// 组件挂载完成事件
onMounted(async () => {
  sendMessageToChromeExtension(locale.value, translateEngine.value.value)
  // 如果存在待上传文件，则直接读取文件
  if (fileStore.uploadedFile) {
    docFile.value = fileStore.uploadedFile
    // 清空待上传文件
    fileStore.uploadedFile = null

    // 获取文件类型
    const fileType = getFileType(docFile.value)

    // 如果是 PDF、PPT、Word 文档
    if (fileType === 'pdf' || fileType === 'ppt' || fileType === 'word') {
      // 获取页数
      const pages = useRoute().query.inputPages
      if (pages && pages != 'undefined') {
        inputPages.value = pages as string
      }
    }
    else {
      inputPages.value = ''
    }
  }
})

// 文档相关
const inputPages = ref('') // 页码范围
const fileInputRef = ref() // 文件选择框引用
const docFile = ref<File | null>(null) // 当前上传的文件
const uploadFileState = ref(false) // 上传文件状态
const docContent = ref('') // 文档内容
const docLayoutContent = ref<any>('') // 文档布局内容
const sourceFile = ref('') // 源文件内容
let rectBboxMap = new Map() // 页面矩形框

const exportItem = computed(() => [{
  // 导出 HTML
  label: t('document_translation.export_html'),
  value: 'html',
  onSelect: () => {
    handleExportHtml()
  }
},
{
  // 导出 PDF
  label: t('document_translation.export_pdf'),
  value: 'pdf',
  onSelect: () => {
    handleExportPDF()
  }
}])

/**
 * 打开文件选择
 */
const handleOpenFileSelect = () => {
  fileInputRef.value.click()
}
/**
 * 文件选择处理
 */
const handleFileChange = (event: Event) => {
  if (
    !event.target
    || !(event.target as HTMLInputElement).files
    || (event.target as HTMLInputElement).files.length === 0
  ) {
    return
  }

  fileStore.setFile((event.target as HTMLInputElement).files[0])
  useRouter().push({
    path: `/${locale.value}/file/miner-u`
  })

  // 获取选中文件
  // docFile.value = (event.target as HTMLInputElement).files[0]

  // 处理文件上传
  // handleFileUpload(docFile.value)
}

const uploadErrMsg = ref('')
const uploadProgress = ref(0)
const handleFileUpload = async (file: File) => {
  // 清除之前的错误信息
  uploadErrMsg.value = ''
  // 清空上传进度
  uploadProgress.value = 0
  // 设置上传状态
  uploadFileState.value = true
  // 清空文档内容
  docContent.value = ''
  // 清空文档布局内容
  docLayoutContent.value = ''
  // 清空页面矩形框
  rectBboxMap = new Map()

  try {
    // MinerU配置
    const minerUConfig = await useMinerUConfig()
    if (!minerUConfig.minerUTranslateToken) {
      uploadErrMsg.value = '请先配置MinerU的翻译服务'
      return
    }
    const minerUToken = minerUConfig.minerUTranslateToken

    // 获取文件上传地址
    const fileUrls = await fileUrlsBatch(
      {
        files: [
          {
            name: file.name,
            page_ranges: inputPages.value.length > 0 ? inputPages.value : undefined
          }
        ],
        model_version: 'v2'
      },
      minerUToken
    )
    // 获取上传地址失败，提示错误并返回
    if (fileUrls.code !== 0) {
      uploadErrMsg.value = '获取文件上传地址失败' // 获取文件上传地址失败
      return
    }
    // 获取上传地址成功，设置上传进度
    uploadProgress.value = 20

    // 上传地址
    const uploadUrl = fileUrls.data.file_urls[0]
    // 上传任务ID
    const batchId = fileUrls.data.batch_id

    // 上传文件
    const uploadRes = await uploadFile(file, uploadUrl)
    if (!uploadRes.success) {
      uploadErrMsg.value = '上传文件失败' // 上传文件失败
      return
    }
    // 上传成功，设置上传进度
    uploadProgress.value = 50

    // 处理文件上传结果
    await handleFileUploadResult(batchId, minerUToken)
  }
  catch (error) {
    console.error('Error fetching file upload URL:', error)
    uploadErrMsg.value = '上传文件失败'
  }
}

// 组件卸载前
onBeforeUnmount(() => {
  // 组件卸载之前必须要取消翻译否则会翻译整个网站
  usePluginWebDocumentTranslate({
    type: 'cancelCurrentPageTranslate',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: 'bilingual',
    documentType: 'miner-u'
  })

  // 重置 PDF 查看器状态
  pdfViewerReadyState.value = false
})

/**
 * 处理文件上传结果
 * @param batchId 任务ID
 * @param minerUToken 翻译服务token
 */
const handleFileUploadResult = async (batchId: string, minerUToken: string) => {
  const timer = setInterval(async () => {
    // 获取文件解析结果
    const extractTaskRes = await extractTaskBatch(batchId, minerUToken)

    if (!extractTaskRes || extractTaskRes.code !== 0) {
      // 获取文件解析结果失败，提示错误并返回
      return
    }

    if (
      !extractTaskRes.data.extract_result
      || extractTaskRes.data.extract_result.length === 0
    ) {
      // 文件解析结果为空
      return
    }

    // 判断是否解析成功
    if (extractTaskRes.data.extract_result[0].state === 'failed') {
      // 清空定时任务
      clearInterval(timer)

      // 解析失败，提示错误信息
      uploadErrMsg.value = extractTaskRes.data.extract_result[0].err_msg
    }
    else if (extractTaskRes.data.extract_result[0].state === 'done') {
      uploadProgress.value = 70

      // 清空定时任务
      clearInterval(timer)

      const fullZipUrl = extractTaskRes.data.extract_result[0].full_zip_url
      const content = await downloadFileContent(fullZipUrl)
      if (content) {
        docContent.value = content.docContent
        docLayoutContent.value = JSON.parse(content.docLayout)
        // 处理定位坐标集合
        handleParseCoordinateIndex()
        // 处理源文件
        if (content.sourceContent) {
          sourceFile.value = content.sourceContent
        }
      }

      // 打开文件预览
      if (pdfViewerReadyState.value && pdfViewerRef.value) {
        try {
          // 处理源文件
          let targetDocFile = docFile.value
          if (sourceFile.value) {
            const blob = dataUrlToBlob(sourceFile.value)
            targetDocFile = new File([blob], docFile.value?.name || 'source.pdf', { type: 'application/pdf' })
          }

          // 延迟打开文件，确保 PDF 查看器完全就绪
          setTimeout(() => {
            if (pdfViewerRef.value && targetDocFile) {
              pdfViewerRef.value.openFile(targetDocFile)
            }
          }, 200)
        } catch (error) {
          console.error('打开 PDF 文件失败:', error)
        }
      } else {
        console.warn('PDF 查看器未就绪，无法打开文件')
      }

      // 设置上传状态为完成
      uploadFileState.value = false
    }
  }, 2000)
}

// 用 data URL 生成真实的 Blob（二进制），而不是返回 blob: 的字符串
const dataUrlToBlob = (dataUrl: string): Blob => {
  const [meta, base64] = dataUrl.split(',')
  const mime = (meta.match(/data:(.*);base64/) || [])[1] || 'application/pdf'
  const binary = atob(base64)
  const bytes = new Uint8Array(binary.length)
  for (let i = 0; i < binary.length; i++) bytes[i] = binary.charCodeAt(i)
  return new Blob([bytes], { type: mime })
}

// const dataUrlToBlobUrl = (dataUrl: string): string => {
//   const [meta, base64] = dataUrl.split(',')
//   const mime = (meta.match(/data:(.*);base64/) || [])[1] || 'application/pdf'
//   const binary = atob(base64)
//   const bytes = new Uint8Array(binary.length)
//   for (let i = 0; i < binary.length; i++) bytes[i] = binary.charCodeAt(i)
//   const blob = new Blob([bytes], { type: mime })
//   return URL.createObjectURL(blob)
// }

// 解析坐标索引
const handleParseCoordinateIndex = () => {
  if (!docLayoutContent.value || docLayoutContent.value.length === 0) {
    return
  }

  // 块索引
  let blockIndex = 0
  const blockBboxMap = new Map<number, any>()
  const lineBboxMap = new Map<number, any>()

  // 分页处理
  const pageLayoutList = docLayoutContent.value.pdf_info
  for (let i = 0; i < pageLayoutList.length; i++) {
    const pageLayout = pageLayoutList[i]
    const pageIndex = pageLayout.page_idx

    // 页面块列表
    const blockList = handlePageLayoutBlockParse(pageLayout.para_blocks)

    // 保存页面结果
    const pageBlockBboxMap = new Map<string, number>()
    const pageLineBboxMap = new Map<string, number>()
    const pageRectBlockMap = new Map<any, number>()

    for (let j = 0; j < blockList.length; j++) {
      const block = blockList[j]
      const bbox = block.bbox
      const bbox_key = bbox[0] + '_' + bbox[1] + '_' + bbox[2] + '_' + bbox[3]

      // 从块中获取索引值
      let targetIndex = pageBlockBboxMap.get(bbox_key)

      // 不存在，从行中获取块索引
      if (!targetIndex) {
        targetIndex = pageLineBboxMap.get(bbox_key)
      }

      // 不存在，和上一页进行匹配
      if (!targetIndex) {
        // 判断是否存在上一页, 存在则从上一页最后元素去匹配
        if (i > 0) {
          const prePageLayout = pageLayoutList[i]
          const preblockList = handlePageLayoutBlockParse(
            prePageLayout.para_blocks
          )
          // 获取上一页最后一个
          const preLastBlock = preblockList[preblockList.length - 1]
          // 获取行
          const lastLineList = preLastBlock.lines

          // let bbox = block.bbox;

          let overlap = false
          for (let k = 0; k < lastLineList.length; k++) {
            const line = lastLineList[k]
            if (
              line.bbox[0] == bbox[0]
              && line.bbox[1] == bbox[1]
              && line.bbox[2] == bbox[2]
              && line.bbox[3] == bbox[3]
            ) {
              overlap = true
              break
            }
          }

          if (overlap) {
            const prePageBlockBboxMap = blockBboxMap.get(i - 1)
            targetIndex = prePageBlockBboxMap.get(preLastBlock.bbox)
          }
        }
      }

      // 如果还是没有找到索引，则使用当前索引
      if (!targetIndex) {
        // 如果当前块没有内容, 则跳过
        if (block.lines_deleted || !block.lines || block.lines.length === 0) {
          continue
        }
        targetIndex = blockIndex
        blockIndex++
      }
      pageBlockBboxMap.set(bbox_key, targetIndex)
      pageRectBlockMap.set(block, targetIndex)

      // 保存行索引
      const lineList = block.lines
      if (lineList && lineList.length > 0) {
        for (let k = 0; k < lineList.length; k++) {
          const lineBbox = lineList[k].bbox
          const line_key
            = lineBbox[0]
              + '_'
              + lineBbox[1]
              + '_'
              + lineBbox[2]
              + '_'
              + lineBbox[3]
          pageLineBboxMap.set(line_key, targetIndex)
        }
      }
    }

    blockBboxMap.set(pageIndex, pageBlockBboxMap)
    lineBboxMap.set(pageIndex, pageLineBboxMap)
    rectBboxMap.set(pageIndex, pageRectBlockMap)
  }
}

const pdfViewerRef = ref()
const pdfViewerReadyState = ref(false)

/**
 * PDF查看器就绪事件
 */
const pdfViewerReady = () => {
  console.log('PDF 查看器就绪')
  pdfViewerReadyState.value = true // 标记组件已就绪

  // 组件就绪后再打开文件
  if (docFile.value) {
    // 延迟一下确保 PDF 查看器完全初始化
    setTimeout(() => {
      // 处理文件上传
      handleFileUpload(docFile.value)
    }, 100)
  }
}

const handlePageLoaded = async (source: PDFPageView) => {
  await nextTick()

  // 获取当前页码
  const pageNumber = source.id - 1
  if (!docLayoutContent.value) {
    return
  }

  // 当前页面布局信息
  const pageLayout = docLayoutContent.value.pdf_info[pageNumber]

  // key: 块布局信息，value: 块索引
  const bboxIndexMap = rectBboxMap.get(pageNumber)

  // 计算坐标开始位置
  // let blockIndex = getPageLayoutBlockStartIndex(pageNumber);

  // 创建SVG
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
  svg.classList.add('pdf-annotation-layer')
  svg.style.position = 'absolute'
  svg.style.left = '0'
  svg.style.top = '0'
  svg.style.zIndex = '999'
  // 设置SVG大小
  const pageSize = pageLayout.page_size
  svg.style.width = 'round(down, var(--scale-factor) * ' + pageSize[0] + 'px, var(--scale-round-x, 1px))'
  svg.style.height = 'round(down, var(--scale-factor) * ' + pageSize[1] + 'px, var(--scale-round-y, 1px))'
  svg.setAttribute('viewBox', `0 0 ${pageSize[0]} ${pageSize[1]}`)

  // 绘制svg矩形框
  const blockList = handlePageLayoutBlockParse(pageLayout.para_blocks)
  for (let i = 0; i < blockList.length; i++) {
    const block = blockList[i]
    const bbox = block.bbox

    const targetIndex = bboxIndexMap.get(block)

    // 判断目标索引
    const rectElement = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
    rectElement.setAttribute('x', bbox[0])
    rectElement.setAttribute('y', bbox[1])
    rectElement.setAttribute('width', (bbox[2] - bbox[0]).toString())
    rectElement.setAttribute('height', (bbox[3] - bbox[1]).toString())
    rectElement.setAttribute('fill', 'rgba(0, 51, 255)')
    rectElement.setAttribute('opacity', '0')
    rectElement.setAttribute('stroke', 'rgba(0, 51, 255)')
    rectElement.setAttribute('stroke-width', '1')
    rectElement.setAttribute('vector-effect', 'non-scaling-stroke')
    rectElement.setAttribute('data-type', block.type)
    rectElement.setAttribute('data-block-index', targetIndex.toString())
    svg.appendChild(rectElement)

    handleRectListener(rectElement, targetIndex)
  }

  // 绘制svg文本
  source.div.appendChild(svg)
}

/**
 * 递归处理页面布局块，获取所有段落块
 * @param paraBlockList 页面布局
 */
const handlePageLayoutBlockParse = (paraBlockList: any) => {
  const blockList = []

  // 获取当前页面的段落块
  if (!paraBlockList || paraBlockList.length === 0) {
    return blockList
  }

  for (let i = 0; i < paraBlockList.length; i++) {
    const block = paraBlockList[i]

    if (block.blocks && block.blocks.length > 0) {
      // 如果存在子块，则递归处理
      const subBlocks = handlePageLayoutBlockParse(block.blocks)
      subBlocks.sort((a, b) => {
        const aY = a.bbox[1]
        const bY = b.bbox[1]
        return aY - bY
      })
      blockList.push(...subBlocks)
    }
    else {
      // 如果没有子块，则直接添加当前块
      blockList.push(block)
    }
  }

  return blockList
}

/**
 * 添加矩形点击事件
 * @param rect SVG矩形元素
 * @param blockIndex 块索引
 */
const handleRectListener = (rect: SVGRectElement, blockIndex: number) => {
  // 添加点击事件
  rect.addEventListener('click', (event: PointerEvent) => {
    // 阻止默认事件
    event.preventDefault()

    // 清除当前元素同层级其他元素的激活样式
    const rects = document.querySelectorAll('rect')
    rects.forEach((rect) => {
      rect.classList.remove('rect-active')
    })

    // 清除目标元素的激活样式
    const targetElements = document.querySelectorAll('.parse-content-active')
    targetElements.forEach((element) => {
      element.classList.remove('parse-content-active')
    })

    // 设置当前点击的rect元素样式
    rect.classList.add('rect-active')

    const targetElement = blockElementList.value[blockIndex]
    targetElement.classList.add('parse-content-active')

    // 滚动到浏览范围内
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
  })
}

// 翻译选项数据- 语言集
const languageList = computed({
  get() {
    return targetLanguageList.value
  },
  set(newValue) {
    // 通过 Store 方法或直接修改响应式变量
    targetLanguageList.value = newValue
  }
})

// 导出为HTML文件
function handleExportHtml() {
  const htmlContent = document.getElementById('miner-u-renderer').innerHTML
  // 定义预览区域的样式
  const previewStyle = `
    <style>
      @page {
        margin: 1cm;    /* 移除默认边距 */
      }
      h1, h2, h3, h4, h5, h6 {
        font-size: inherit;
      }
      #preview {
        font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif;
        padding: 2em;
        margin: auto;
        max-width: 800px;
      }
    </style>
  `
  // 生成完整的 HTML 字符串，将预览样式添加到头部
  docHtmlDownloadContent.value = generateFullHtml(previewStyle, htmlContent)

  const blob = new Blob([docHtmlDownloadContent.value], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  const languageItem = languageList.value.find((item) => {
    return item.value === targetLanguageZingCode.value
  })
  const fileNameWithoutExtension = docFile.value.name.replace(/\.[^/.]+$/, '') // 去除文件扩展名
  a.download = `${fileNameWithoutExtension}-${languageItem.lang_code}.html`
  a.click()
  URL.revokeObjectURL(url)
}

// 定义一个函数来生成完整的 HTML 字符串
const generateFullHtml = (headContent: string, bodyContent: string) => {
  return `
    <!DOCTYPE html>
    <html lang="en">
        ${headContent}
      <body>
        ${bodyContent}
      </body>
    </html>
  `
}

// 处理对照页面
const openComparison = ref(true)
const handleOpenComparison = (value: boolean) => {
  openComparison.value = value
}

// 导出为PDF文件
const printFrame = ref(null)

function handleExportPDF() {
  if (import.meta.client) {
    const htmlContent = document.getElementById('miner-u-renderer').innerHTML
    const styleEl = document.getElementById('select_translate_style')
    const head = styleEl ? styleEl.outerHTML : ''

    const mathpixStyleEl = document.getElementById('Mathpix-styles')
    const mathpixHead = mathpixStyleEl ? mathpixStyleEl.outerHTML : ''

    // 添加打印样式，包含背景颜色打印支持
    const printStyles = `
      <style>
        @page {
          margin: 1cm;    /* 移除默认边距 */
        }
        h1, h2, h3, h4, h5, h6 {
          font-size: inherit;
        }
        #preview {
          font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif;
          padding: 2em;
          margin: auto;
          max-width: 800px;
        }
        /* 强制打印背景颜色和图像 */
        html, body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
      </style>
    `
    // 生成完整的 HTML 字符串
    docHtmlDownloadContent.value = generateFullHtml(
      head + mathpixHead + printStyles,
      htmlContent
    )
    const frame = printFrame.value
    if (frame) {
      // 确保这里做了null检查
      // 创建一个新的文档对象
      const doc = frame.contentDocument || frame.contentWindow.document
      // 设置文档标题，作为打印时的文件名
      const fileNameWithoutExtension = docFile.value.name.replace(/\.[^/.]+$/, '') // 去除文件扩展名
      doc.title = `${fileNameWithoutExtension}-${targetLanguage.value}.pdf`
      frame.srcdoc = docHtmlDownloadContent.value
      setTimeout(() => {
        frame.contentWindow.print()
      }, 100)
    }
  }
}

/**
 * 接收子组件的值html值
 * @param value
 */
const docHtmlContent = ref('') // 文档HTML内容
const docHtmlDownloadContent = ref('') // 文档HTML下载内容
const blockElementList = ref<HTMLElement[]>()
const handleDocContentUpdate = async (value: string) => {
  await nextTick()

  /**
   * 先取消翻译, 防止再次触发翻译流程（会导致插件翻译的页面被取消翻译）
   * 发送给插件告诉插件 取消翻译将currentTabTransEnable 设置为 false
   * 这样用户如果已经翻译了当前的 pdf 再打开新的 pdf 的时候不会被取消翻译
   */
  usePluginWebDocumentTranslate({
    type: 'cancelCurrentPageTranslate',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: translationsDisplay.value,
    documentType: 'miner-u'
  })

  // 发起翻译监控
  initTranslationProgressMonitor()

  docHtmlContent.value = value
  docHtmlDownloadContent.value = value

  setTimeout(() => {
    // 执行扩展翻译
    usePluginWebDocumentTranslate({
      type: 'translateFilePage',
      translationEngine: translateEngine.value.value,
      targetLanguage: targetLanguage.value,
      transDisplayMode: translationsDisplay.value,
      documentType: 'miner-u'
    })
  }, 1000)

  // 处理节点
  const parseContent = document.querySelector('#miner-u-renderer #setText')
  const childrenList = parseContent.children
  const elementList = handleParseRendererArea(childrenList)
  blockElementList.value = elementList

  // 设置点击事件
  handleTranslationAreaRectListener(elementList)
}

const handleParseRendererArea = (
  elementList: HTMLCollection
): HTMLElement[] => {
  const resultList: HTMLElement[] = []

  for (let i = 0; i < elementList.length; i++) {
    const element = elementList[i] as HTMLElement
    if (element.nodeName == 'OL' || element.nodeName == 'UL') {
      // 如果是有序列表或无序列表，则直接添加
      const children = element.children
      for (let j = 0; j < children.length; j++) {
        resultList.push(children[j] as HTMLElement)
      }
    }
    else {
      resultList.push(element)
    }
  }

  return resultList
}

const handleTranslationAreaRectListener = (blockElementList: HTMLElement[]) => {
  for (let i = 0; i < blockElementList.length; i++) {
    const child = blockElementList[i]

    child.addEventListener('click', (event: PointerEvent) => {
      // 阻止默认事件
      event.preventDefault()

      // 清除当前元素同层级其他元素的激活样式
      const childrenList = child.parentElement.children
      for (const element of childrenList) {
        element.classList.remove('parse-content-active')
      }

      // 晴空rect元素的激活
      const rects = document.querySelectorAll('rect')
      rects.forEach((rect) => {
        rect.classList.remove('rect-active')
      })

      // 显示选中
      child.classList.add('parse-content-active')
      const targetRects = document.querySelectorAll(
        `rect[data-block-index="${i}"]`
      )
      targetRects.forEach((rect) => {
        rect.classList.add('rect-active')

        rect.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      })
    })
  }
}

// 处理浏览器title
const handleBrowserTitle = () => {
  document.title = docFile.value.name
}
</script>

<style scoped>
:deep(.pdf-annotation-layer rect) {
  cursor: pointer;
  rx: 2px;
  ry: 2px;
  fill: #0033ff14 !important; /* 半透明填充 */
}

:deep(.pdf-annotation-layer rect:hover) {
  opacity: 1;
}

:deep(.rect-active) {
  opacity: 1;
  stroke-width: 2px !important; /* 增加边框宽度 */
  stroke-dasharray: 0 !important; /* 确保边框是实线 */
  stroke-linecap: round !important; /* 圆角线帽 */
  stroke-linejoin: round !important; /* 圆角连接点 */
}

:deep(.parse-content-active) {
  border: #03f 2px solid; /* 与rect-active的stroke-width一致 */
  border-radius: 4px; /* 与rect的rx/ry保持一致 */
  /* 使用与rect-active相同的边框颜色 */
  background-color: rgba(0, 51, 255, 0.1); /* 与rect的fill颜色一致 */
}

:deep(#miner-u-renderer #setText > *) {
  cursor: pointer;
  /* 不添加黑暗模式下会变成白色字体 */
  color: #000;
}

:deep(#miner-u-renderer #setText > *:hover) {
  border: #03f 1px solid;
  background-color: rgba(0, 51, 255, 0.1); /* 与rect的fill颜色一致 */
  border-radius: 4px;
}

/* 滚动条容器 */
/* 隐藏默认滚动条 */
.scroll-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 滚动条轨道 - 始终透明 */
.scroll-container::-webkit-scrollbar-track {
  background: transparent;
}

/* 滚动条滑块 - 默认透明（隐藏） */
.scroll-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

/* 鼠标移入容器时显示滚动条滑块 */
.scroll-container:hover::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5); /* 半透明灰色 */
}

/* 滑块 hover 时加深颜色 */
.scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7); /* 深色半透明 */
}

/* 边角样式 */
.scroll-container::-webkit-scrollbar-corner {
  background: transparent;
}
</style>
