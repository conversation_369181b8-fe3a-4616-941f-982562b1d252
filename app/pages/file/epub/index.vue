<template>
  <DocumentLayouts
    document-type="epub"
    :show-switch-bilingual="true"
    :loading="isLoading"
    :parse-error="error"
    @file-selected="handleFileSelected"
    @retry-parse="parseFile"
  >
    <template #right>
      <UButton
        :disabled="!document"
        size="xl"
        color="secondary"
        variant="outline"
        class="rounded-md"
        @click="handleExport"
      >
        <!-- 导出 EPUB -->
        {{ t('document_translation.export_epub') }}
      </UButton>
    </template>
    <!-- EPUB 阅读器 -->
    <EpubViewer v-if="hasDocument && document" ref="epubViewerRef" :document="document" />
  </DocumentLayouts>
</template>

<script setup lang="ts">
import DocumentLayouts from '../components/DocumentLayouts.vue'
import EpubViewer from './components/EpubViewer.vue'
import { useEpubParser } from './composables/useEpubParser'
import { useFileStore } from '@/store/FileStore'
import { exportEpub } from './utils/export'

const { document, isLoading, error, hasDocument, parseFile } = useEpubParser()

const { t } = useI18n()

// 响应式的文档标题
const documentTitle = ref('')

// 计算完整的页面标题
const pageTitle = computed(() => {
  // 电子书 EPUB 翻译
  const baseTitle = t('document_translation.epub.title')
  const siteName = t('common.site_name')

  if (documentTitle.value) {
    return `${documentTitle.value} - ${siteName}`
  }
  return `${baseTitle} - ${siteName}`
})

useSeoMeta({
  titleTemplate: '%s',
  title: pageTitle, // 这里不要使用 .value ，否则会导致 SEO 标题变回默认的，动态标题会不生效
  ogTitle: pageTitle
})

const fileStore = useFileStore()

// EpubViewer 组件引用
const epubViewerRef = ref()

// 禁用默认布局
definePageMeta({
  layout: false
})

/**
 * 选择打开文件后回调
 */
const handleFileSelected = async (val: File) => {
  fileStore.setFile(val)
  const result = await parseFile(val)
  if (result.success && document.value) {
    fileStore.setFile(document.value)
    // 更新文档标题
    if (document.value.metadata?.title) {
      documentTitle.value = document.value.metadata.title
    }
  }
}

// 如果组件加载的时候存在文件，直接解析
onMounted(() => {
  if (fileStore.uploadedFile) {
    handleFileSelected(fileStore.uploadedFile)
  }
})

/**
 * 导出 epub
 */
const handleExport = async () => {
  if (!document.value) return

  try {
    // 创建文档副本
    const exportDoc = { ...document.value }

    // 从子组件引用中获取暴露的元素
    if (!epubViewerRef.value) {
      return
    }

    const { documentTitle, documentCreator, tocItemLabels, chapterTitles, chapterContents } = epubViewerRef.value

    // 更新元数据
    if (documentTitle) {
      exportDoc.metadata.title = documentTitle.textContent
    }
    if (documentCreator) {
      exportDoc.metadata.creator = documentCreator.textContent
    }

    // 更新目录标签
    if (tocItemLabels) {
      exportDoc.toc.forEach((item, index) => {
        if (tocItemLabels[index]) {
          item.label = tocItemLabels[index].textContent
        }
      })
    }

    // 更新章节内容
    if (chapterTitles && chapterContents) {
      exportDoc.chapters.forEach((chapter, index) => {
        if (chapterTitles[index]) {
          chapter.title = chapterTitles[index].textContent
        }
        if (chapterContents[index]) {
          chapter.content = chapterContents[index].innerHTML
        }
      })
    }

    // 导出文档
    await exportEpub(exportDoc)
  }
  catch (error) {
    console.error('导出失败:', error)
  }
}
</script>

<style scoped>
/* EPUB 文档翻译样式 */
</style>
