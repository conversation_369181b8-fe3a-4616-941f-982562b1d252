/**
 * 电子书 解析器
 * 如果需要使用 i18n ，请使用 useNuxtApp 获取 i18n 实例
 * 使用方法如：$i18n.t('document_translation.export_epub')
 */

import { EpubParser } from '../utils/epubParser'
import type { EpubDocument } from '../types/epub.d'

export function useEpubParser() {
  const parser = ref<EpubParser | null>(null)
  const document = ref<EpubDocument | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 使用 useNuxtApp 获取 i18n 实例
  const { $i18n } = useNuxtApp()

  /**
   * 解析 EPUB 文件
   */
  const parseFile = async (file: File) => {
    try {
      isLoading.value = true
      error.value = null

      parser.value = new EpubParser()
      const result = await parser.value.parse(file)

      if (result.success && result.document) {
        document.value = result.document
      }
      else {
        error.value = result.error || $i18n.t('document_translation.parse_failed')
      }

      return result
    }
    catch (err) {
      const errorMessage = err instanceof Error ? err.message : $i18n.t('document_translation.parse_failed')
      error.value = errorMessage
      return { success: false, error: errorMessage }
    }
    finally {
      isLoading.value = false
    }
  }

  const cleanup = () => {
    if (parser.value) {
      parser.value.destroy()
      parser.value = null
    }
    document.value = null
    error.value = null
  }

  const hasDocument = computed(() => document.value !== null)

  return {
    document,
    isLoading,
    error,
    hasDocument,
    parseFile,
    cleanup
  }
}
