<template>
  <!-- 这个data-translation="true" 是给翻译插件用的，表示这个元素需要被翻译 -->
  <div class="epub-viewer space-y-6">
    <!-- 文档标题 -->
    <div class="border-b border-gray-200 dark:border-gray-700 py-4">
      <div class="flex items-center justify-between">
        <div class="flex-1 text-center" data-translation="true">
          <div class="text-2xl font-bold text-gray-900 dark:text-white">
            <div ref="documentTitle">
              {{ document.metadata.title }}
            </div>
          </div>
          <div class="mt-2 text-gray-600 dark:text-gray-300">
            <div ref="documentCreator">
              {{ document.metadata.creator }}
            </div>
          </div>
        </div>

        <!-- 导出按钮组 -->
        <div class="flex space-x-2">
          <!-- 开始翻译 -->
          <UButton class="rounded-md" @click="handleStartTranslate">
            <!-- 开始翻译/取消翻译 -->
            {{ isEnableTranslate ? $t('document_translation.cancel_translation') : $t('document_translation.start_translation') }}
          </UButton>
        </div>
      </div>
    </div>

    <!-- 目录区域 -->
    <div class="toc-section rounded-lg bg-gray-50 dark:bg-gray-800 p-6" data-translation="true">
      <div class="toc-content space-y-2">
        <div v-for="tocItem in document.toc" :key="tocItem.id">
          <span class="cursor-pointer text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
            <div ref="tocItemLabels">{{ tocItem.label }}</div>
          </span>
        </div>
      </div>
    </div>

    <!-- 章节内容区域 -->
    <div class="chapters-container space-y-8" data-translation="true">
      <div v-for="chapter in document.chapters" :key="chapter.id" class="chapter-section rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm">
        <!-- 章节标题 -->
        <div class="chapter-header rounded-t-lg border-b border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-700 px-6 py-4">
          <div class="text-lg font-semibold text-gray-800 dark:text-gray-200">
            <div ref="chapterTitles">
              {{ chapter.title }}
            </div>
          </div>
        </div>

        <!-- 章节内容 -->
        <div class="chapter-content px-6 py-4">
          <div class="chapter-text prose prose-gray max-w-none">
            <div ref="chapterContents" v-html="chapter.content" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EpubDocument } from '../types/epub.d'
import { usePluginTranslate } from '@/composables/usePluginTranslate'
import { useDocumentTranslateStore } from '@/store/documentTranslate'

const { usePluginWebDocumentTranslate } = usePluginTranslate()
const documentTranslateStore = useDocumentTranslateStore()
const { translateEngine, translationsDisplay, targetLanguage } = storeToRefs(documentTranslateStore)

const { initTranslationProgressMonitor } = useTranslationProgress()
interface Props {
  document: EpubDocument
}

const props = defineProps<Props>()

// 监听文档变化
watch(
  () => props.document,
  (newDocument) => {
    if (newDocument) {
      // console.log('文档已更新:', newDocument)
    }
  },
  { immediate: true }
)

const documentTitle = ref<HTMLDivElement>()
const documentCreator = ref<HTMLDivElement>()
const tocItemLabels = ref<HTMLDivElement[]>()
const chapterTitles = ref<HTMLDivElement[]>()
const chapterContents = ref<HTMLDivElement[]>()

// 开始翻译/取消翻译
const isEnableTranslate = ref(false)

const handleStartTranslate = () => {
  // 开启翻译监控
  initTranslationProgressMonitor()

  // 开启翻译
  usePluginWebDocumentTranslate({
    type: 'translateFilePage',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: translationsDisplay.value,
    documentType: 'epub'
  })

  isEnableTranslate.value = !isEnableTranslate.value
}
onMounted(() => {
  // 接受到插件发送过来的 翻译状态同步
  window.addEventListener('message', (e) => {
    if (e.data.type === 'UPDATE_TRANSLATION_STATE' && e.data.source === 'content_script') {
      isEnableTranslate.value = e.data.state
    }
  })
})

// 暴露给父组件使用
defineExpose({
  documentTitle,
  documentCreator,
  tocItemLabels,
  chapterTitles,
  chapterContents
})
</script>

<style scoped>
.epub-viewer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.toc-section {
  max-height: 400px;
  overflow-y: auto;
}

.toc-child-item {
  transition: all 0.2s ease;
}

.toc-child-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 4px;
  padding: 4px 8px;
  margin: 0 -8px;
}

.dark .toc-child-item:hover {
  background-color: rgba(59, 130, 246, 0.15);
}

.chapter-section {
  max-height: 600px;
  overflow-y: auto;
}

.chapter-content {
  line-height: 1.6;
}

.chapter-text {
  font-size: 16px;
  color: #374151;
}

.dark .chapter-text {
  color: #d1d5db;
}

.chapter-text :deep(h1),
.chapter-text :deep(h2),
.chapter-text :deep(h3),
.chapter-text :deep(h4),
.chapter-text :deep(h5),
.chapter-text :deep(h6) {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #111827;
}

.dark .chapter-text :deep(h1),
.dark .chapter-text :deep(h2),
.dark .chapter-text :deep(h3),
.dark .chapter-text :deep(h4),
.dark .chapter-text :deep(h5),
.dark .chapter-text :deep(h6) {
  color: #f9fafb;
}

.chapter-text :deep(p) {
  margin-bottom: 1em;
  text-align: justify;
}

.chapter-text :deep(blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: #6b7280;
}

.dark .chapter-text :deep(blockquote) {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.chapter-text :deep(ul),
.chapter-text :deep(ol) {
  margin: 1em 0;
  padding-left: 2em;
}

.chapter-text :deep(li) {
  margin-bottom: 0.5em;
}

.chapter-text :deep(strong),
.chapter-text :deep(b) {
  font-weight: 600;
  color: #111827;
}

.dark .chapter-text :deep(strong),
.dark .chapter-text :deep(b) {
  color: #f9fafb;
}

.chapter-text :deep(em),
.chapter-text :deep(i) {
  font-style: italic;
}

.chapter-text :deep(code) {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.dark .chapter-text :deep(code) {
  background-color: #374151;
  color: #e5e7eb;
}

.chapter-text :deep(pre) {
  background-color: #f3f4f6;
  padding: 1em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1em 0;
}

.dark .chapter-text :deep(pre) {
  background-color: #374151;
  color: #e5e7eb;
}

.chapter-text :deep(pre code) {
  background: none;
  padding: 0;
}

.chapter-text :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}

.chapter-text :deep(th),
.chapter-text :deep(td) {
  border: 1px solid #d1d5db;
  padding: 0.5em;
  text-align: left;
}

.chapter-text :deep(th) {
  background-color: #f9fafb;
  font-weight: 600;
}

.dark .chapter-text :deep(th),
.dark .chapter-text :deep(td) {
  border-color: #4b5563;
}

.dark .chapter-text :deep(th) {
  background-color: #374151;
  color: #f9fafb;
}

.chapter-text :deep(img) {
  display: none;
}

.chapter-text :deep(a) {
  color: #2563eb;
  text-decoration: underline;
}

.chapter-text :deep(a:hover) {
  color: #1d4ed8;
}

.dark .chapter-text :deep(a) {
  color: #60a5fa;
}

.dark .chapter-text :deep(a:hover) {
  color: #93c5fd;
}

.chapter-text.rtl :deep(p) {
  text-align: right;
}

.chapter-text.rtl :deep(blockquote) {
  border-left: none;
  border-right: 4px solid #e5e7eb;
  padding-left: 0;
  padding-right: 1em;
}

.dark .chapter-text.rtl :deep(blockquote) {
  border-right-color: #4b5563;
}

.chapter-text.rtl :deep(ul),
.chapter-text.rtl :deep(ol) {
  padding-left: 0;
  padding-right: 2em;
}

.chapter-text.rtl :deep(th),
.chapter-text.rtl :deep(td) {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .epub-viewer {
    padding: 10px;
  }

  .toc-section,
  .chapter-section {
    max-height: 500px;
  }
}

/* 滚动条样式 */
.toc-section::-webkit-scrollbar,
.chapter-section::-webkit-scrollbar {
  width: 6px;
}

.toc-section::-webkit-scrollbar-track,
.chapter-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.toc-section::-webkit-scrollbar-thumb,
.chapter-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.toc-section::-webkit-scrollbar-thumb:hover,
.chapter-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 黑暗模式滚动条样式 */
.dark .toc-section::-webkit-scrollbar-track,
.dark .chapter-section::-webkit-scrollbar-track {
  background: #374151;
}

.dark .toc-section::-webkit-scrollbar-thumb,
.dark .chapter-section::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .toc-section::-webkit-scrollbar-thumb:hover,
.dark .chapter-section::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
