// EPUB 基础类型定义
export interface EpubMetadata {
  title: string // 书名
  creator: string // 作者
  language: string // 语言
  identifier: string // 唯一标识符
  publisher?: string // 出版社
  date?: string // 出版日期
  description?: string // 描述
  cover?: string // 封面图片文件名
}

export interface EpubManifest {
  id: string // 资源ID
  href: string // 资源路径
  mediaType: string // 资源类型
  properties?: string[] // 资源属性
}

export interface EpubSpine {
  idref: string // 资源ID引用
  linear: boolean // 是否线性
}

export interface EpubTocItem {
  id: string // 资源ID
  href: string // 资源路径
  label: string // 目录项标签
  level: number // 目录项层级
  children?: EpubTocItem[]
}

export interface EpubChapter {
  id: string // 资源ID
  href: string // 资源路径
  title: string // 章节标题
  content: string // 章节内容
  index: number // 章节索引
}

export interface EpubDocument {
  metadata: EpubMetadata // 元数据
  manifest: EpubManifest[]
  spine: EpubSpine[] // 资源线性顺序
  toc: EpubTocItem[] // 目录项
  images: Map<string, Blob> // 图片资源
  resources: Map<string, string> // 其他资源
  chapters: EpubChapter[] // 章节资源
  currentChapter: number // 当前章节索引
  totalChapters: number // 总章节数
  version: string // EPUB 版本
}
