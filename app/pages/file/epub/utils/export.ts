import type { EpubDocument, EpubTocItem } from '../types/epub.d'
import J<PERSON>Z<PERSON> from 'jszip'

// HTML转义函数
function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// 辅助函数：获取封面的清单项
function getCoverManifestItem(exportDoc: EpubDocument): string {
  if (!exportDoc.metadata.cover) return ''

  const coverName = exportDoc.metadata.cover

  if (!exportDoc.images.has(coverName)) {
    // console.error(`封面图片 ${coverName} 不存在`)
    return ''
  }
  const extension = coverName.split('.').pop()?.toLowerCase() || 'jpg'
  const mediaType = extension === 'svg' ? 'image/svg+xml' : `image/${extension}`

  return `<item id="cover-image" href="images/${coverName}" media-type="${mediaType}" properties="cover-image"/>`
}

function fixSelfClosingTags(html: string): string {
  const selfClosingTags = ['img', 'br', 'hr', 'input', 'meta', 'link', 'col', 'area']

  return html.replace(new RegExp(`<\\s*(${selfClosingTags.join('|')})([^>]*?)(?<!/)>`, 'gi'), '<$1$2 />')
}

// 递归生成TOC的HTML结构
function generateTocHtml(tocItems: EpubTocItem[], level = 0): string {
  if (!tocItems || tocItems.length === 0) return ''

  const indent = '  '.repeat(level + 2)
  let html = `${indent}<ol>\n`

  tocItems.forEach((item) => {
    html += `${indent}  <li>\n`
    html += `${indent}    <a href="${item.href}">${escapeHtml(item.label)}</a>\n`

    // 处理子目录
    if (item.children && item.children.length > 0) {
      html += generateTocHtml(item.children, level + 2)
    }

    html += `${indent}  </li>\n`
  })

  html += `${indent}</ol>\n`
  return html
}

export async function exportEpub(exportDoc: EpubDocument) {
  const zip = new JSZip()

  // console.log('exportDoc', exportDoc);

  // 添加调试信息
  // console.log('封面信息:', exportDoc.metadata.cover);
  // console.log('图片资源:', Array.from(exportDoc.images.keys()));

  // 1. mimetype (必须第一个文件且不压缩)
  zip.file('mimetype', 'application/epub+zip', { compression: 'STORE' })

  // 2. 容器配置
  zip.file(
    'META-INF/container.xml',
    `<?xml version="1.0"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
  <rootfiles>
    <rootfile full-path="OEBPS/content.opf" media-type="application/oebps-package+xml"/>
  </rootfiles>
</container>`
  )

  // 3. 生成导航文件 (EPUB3 必需)
  const navContent = `<?xml version="1.0" encoding="UTF-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops">
  <body>
    <nav epub:type="toc">
${
  exportDoc.toc.length > 0
    ? generateTocHtml(exportDoc.toc)
    : generateTocHtml(
        exportDoc.chapters.map(c => ({
          id: c.id,
          href: c.href,
          label: c.title,
          level: 0
        }))
      )
}
    </nav>
  </body>
</html>`

  zip.file('OEBPS/nav.xhtml', navContent)

  // 4. 内容清单 (content.opf)
  const manifestItems = [
    // 添加导航文件
    `<item id="nav" href="nav.xhtml" media-type="application/xhtml+xml" properties="nav"/>`,

    // 添加样式表
    `<item id="style" href="styles.css" media-type="text/css"/>`,

    // 添加封面（如果存在）
    ...(exportDoc.metadata.cover ? [getCoverManifestItem(exportDoc)] : []),

    // 添加封面页章节
    `<item id="cover-page" href="cover.xhtml" media-type="application/xhtml+xml"/>`,

    // 添加图片资源
    ...Array.from(exportDoc.images.entries()).map(([name]) => {
      const extension = name.split('.').pop()?.toLowerCase() || 'jpg'
      const mediaType = extension === 'svg' ? 'image/svg+xml' : `image/${extension}`
      return `<item id="img-${name.replace(/[^a-zA-Z0-9]/g, '_')}" href="images/${name}" media-type="${mediaType}"/>`
    }),

    // 添加章节
    ...exportDoc.chapters.map(chapter => `<item id="${chapter.id}" href="${chapter.href}" media-type="application/xhtml+xml"/>`)
  ].join('\n    ')

  const spineItems = [
    '<itemref idref="cover-page"/>', // 封面页作为第一项
    ...exportDoc.chapters.map((chapter) => {
      console.log('chapter', chapter)
      return `<itemref idref="${chapter.id}"/>`
    })
  ].join('\n    ')

  // const spineItems = ['<itemref idref="nav"/>', ...exportDoc.chapters.map((chapter) => `<itemref idref="${chapter.id}"/>`)].join('\n    ');

  zip.file(
    'OEBPS/content.opf',
    `<?xml version="1.0" encoding="UTF-8"?>
<package xmlns="http://www.idpf.org/2007/opf" 
         unique-identifier="bookid" 
         version="3.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:title>${escapeHtml(exportDoc.metadata.title)}</dc:title>
    <dc:creator>${escapeHtml(exportDoc.metadata.creator)}</dc:creator>
    <dc:publisher>${escapeHtml(exportDoc.metadata.publisher)}</dc:publisher>
    <dc:date>${new Date().toISOString()}</dc:date>
    <dc:language>${exportDoc.metadata.language}</dc:language>
    <dc:identifier id="bookid">${escapeHtml(exportDoc.metadata.identifier)}</dc:identifier>
    <meta property="dcterms:modified">${new Date().toISOString().split('.')[0]}Z</meta>
    ${exportDoc.metadata.cover ? '<meta name="cover" content="cover-image"/>' : ''}
  </metadata>
  <manifest>
    ${manifestItems}
  </manifest>
  <spine>
    ${spineItems}
  </spine>
</package>`
  )

  // 5. 创建封面页
  if (exportDoc.metadata.cover) {
    zip.file(
      'OEBPS/cover.xhtml',
      `<?xml version="1.0" encoding="UTF-8"?>
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <link rel="stylesheet" href="styles.css"/>
  </head>
  <body>
    <div class="cover-page">
      <img src="images/${exportDoc.metadata.cover}"/>
    </div>
  </body>
</html>`
    )
  }

  // 5. 样式表将在章节文件后添加

  // 6. 添加章节文件
  exportDoc.chapters.forEach((chapter) => {
    // 确保章节路径正确
    const chapterPath = chapter.href.startsWith('OEBPS/') ? chapter.href : `OEBPS/${chapter.href}`

    zip.file(
      chapterPath,
      `<?xml version="1.0" encoding="UTF-8"?>
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <title>${escapeHtml(chapter.title)}</title>
    <link rel="stylesheet" href="styles.css"/>
  </head>
  <body>
    <div class="chapter-content">
       ${fixSelfClosingTags(chapter.content)}
    </div>
  </body>
</html>`
    )
  })

  // 添加样式表
  zip.file('OEBPS/styles.css')

  // 7. 添加图片资源
  if (exportDoc.images.size > 0) {
    const imagesFolder = zip.folder('OEBPS/images')!
    exportDoc.images.forEach((blob, name) => {
      console.log('添加图片:', name)
      imagesFolder.file(name, blob)
    })
  }

  // 8. 生成并下载
  const blob = await zip.generateAsync({
    type: 'blob',
    compression: 'DEFLATE',
    compressionOptions: { level: 6 }
  })

  // 生成安全的文件名
  const safeTitle
    = exportDoc.metadata.title
      .normalize('NFD')
      .replace(/[^\w\u4e00-\u9fa5]/gi, '_')
      .replace(/\s+/g, '_')
      .substring(0, 100) || 'epub_export'

  console.log('safeTitle', safeTitle)

  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `${safeTitle}.epub`
  document.body.appendChild(link)
  link.click()

  // 清理资源
  setTimeout(() => {
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)
  }, 100)
}
