/**
 * EPUB 解析工具
 */
import JSZip from 'jszip'
import type { EpubDocument, EpubMetadata, EpubManifest, EpubSpine, EpubTocItem, EpubChapter } from '../types/epub.d'

export class EpubParser {
  private zip: JSZip | null = null
  private document: EpubDocument | null = null

  /**
   * 解析 EPUB 文件
   */
  async parse(file: File): Promise<{ success: boolean, document?: EpubDocument, error?: string }> {
    try {
      // 1. 加载 ZIP 文件
      this.zip = await JSZip.loadAsync(file)

      // 2. 解析容器文件
      const containerXml = await this.parseContainer()
      if (!containerXml) {
        return { success: false, error: '无法解析容器文件' }
      }

      // 3. 解析包文档
      const packagePath = containerXml.rootfile['@_full-path']
      console.log('packagePath', packagePath)
      const packageDoc = await this.parsePackage(packagePath)
      console.log('packageDoc', packageDoc)
      if (!packageDoc) {
        return { success: false, error: '无法解析包文档' }
      }

      // 4. 解析目录
      const toc = await this.parseToc(packageDoc, packagePath)

      // 5.1 提取资源（图片等）
      const images = await this.extractImages(packageDoc, packagePath)

      // 5. 解析章节内容
      const chapters = await this.parseChapters(packageDoc, packagePath)

      // 6. 构建文档对象
      this.document = {
        // 元数据
        metadata: packageDoc.metadata,
        // 清单
        manifest: packageDoc.manifest,
        // 书脊
        spine: packageDoc.spine,
        // 目录
        toc,
        // 资源
        resources: new Map(),
        // 图片资源
        images,
        // 章节
        chapters,
        // 当前章节
        currentChapter: 0,
        // 总章节数
        totalChapters: chapters.length,
        // 版本
        version: packageDoc.version || '2.0'
      }

      return { success: true, document: this.document }
    }
    catch (error) {
      return {
        success: false,
        error: `解析失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  /**
   * 解析容器文件 (META-INF/container.xml)
   */
  private async parseContainer(): Promise<any> {
    try {
      const containerFile = this.zip?.file('META-INF/container.xml')
      if (!containerFile) {
        throw new Error('容器文件不存在')
      }

      const containerContent = await containerFile.async('text')
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(containerContent, 'text/xml')

      const rootfile = xmlDoc.querySelector('rootfile')
      if (!rootfile) {
        throw new Error('根文件信息不存在')
      }

      return {
        rootfile: {
          '@_full-path': rootfile.getAttribute('full-path'),
          '@_media-type': rootfile.getAttribute('media-type')
        }
      }
    }
    catch (error) {
      throw new Error(`容器文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 解析包文档 (*.opf)
   */
  private async parsePackage(packagePath: string): Promise<any> {
    try {
      const packageFile = this.zip?.file(packagePath)
      if (!packageFile) {
        throw new Error('包文档不存在')
      }

      const packageContent = await packageFile.async('text')
      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(packageContent, 'text/xml')

      const metadata = this.parseMetadata(xmlDoc)
      const manifest = this.parseManifest(xmlDoc)
      const spine = this.parseSpine(xmlDoc)

      return {
        metadata,
        manifest,
        spine,
        version: xmlDoc.documentElement.getAttribute('version') || '2.0'
      }
    }
    catch (error) {
      throw new Error(`包文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 解析元数据
   */
  private parseMetadata(xmlDoc: Document): EpubMetadata {
    const metadata: EpubMetadata = {
      title: '',
      creator: '',
      language: 'en',
      identifier: ''
    }

    // 解析标题
    const titleElement = xmlDoc.querySelector('title, dc\\:title')
    if (titleElement) {
      metadata.title = titleElement.textContent?.trim() || ''
    }

    // 解析创建者
    const creatorElement = xmlDoc.querySelector('creator, dc\\:creator')
    if (creatorElement) {
      metadata.creator = creatorElement.textContent?.trim() || ''
    }

    // 解析语言
    const languageElement = xmlDoc.querySelector('language, dc\\:language')
    if (languageElement) {
      metadata.language = languageElement.textContent?.trim() || 'en'
    }

    // 解析标识符
    const identifierElement = xmlDoc.querySelector('identifier, dc\\:identifier')
    if (identifierElement) {
      metadata.identifier = identifierElement.textContent?.trim() || ''
    }

    // 解析封面
    const metaElements = xmlDoc.querySelectorAll('meta[name="cover"], meta[property="cover"]')
    if (metaElements.length > 0) {
      const coverId = metaElements[0].getAttribute('content') || ''
      if (coverId) {
        // 根据 ID 在 manifest 中查找对应的 href
        const coverItem = xmlDoc.querySelector(`manifest item[id="${coverId}"]`)
        if (coverItem) {
          const href = coverItem.getAttribute('href')
          if (href) {
            // 提取文件名
            metadata.cover = href.split('/').pop() || href
          }
        }
      }
    }

    return metadata
  }

  /**
   * 解析清单
   */
  private parseManifest(xmlDoc: Document): EpubManifest[] {
    const manifest: EpubManifest[] = []
    const manifestElements = xmlDoc.querySelectorAll('manifest item')

    manifestElements.forEach((item) => {
      manifest.push({
        id: item.getAttribute('id') || '',
        href: item.getAttribute('href') || '',
        mediaType: item.getAttribute('media-type') || '',
        properties: item.getAttribute('properties')?.split(' ') || []
      })
    })

    return manifest
  }

  // 新增图片提取方法
  private async extractImages(packageDoc: any, packagePath: string): Promise<Map<string, Blob>> {
    const images = new Map<string, Blob>()
    // 提取包所在目录
    const packageDir = packagePath.substring(0, packagePath.lastIndexOf('/') + 1)

    // 遍历 manifest 中的图片资源
    for (const item of packageDoc.manifest) {
      if (item.mediaType.startsWith('image/')) {
        const imagePath = packageDir + item.href
        const imageFile = this.zip?.file(imagePath)

        if (imageFile) {
          try {
            const blob = await imageFile.async('blob')
            // 使用相对路径作为 key
            images.set(item.href, blob)
          }
          catch (error) {
            // console.warn(`图片加载失败: ${imagePath}`, error)
          }
        }
      }
    }
    return images
  }

  /**
   * 解析书脊
   */
  private parseSpine(xmlDoc: Document): EpubSpine[] {
    const spine: EpubSpine[] = []
    const spineElements = xmlDoc.querySelectorAll('spine itemref')

    spineElements.forEach((item) => {
      spine.push({
        idref: item.getAttribute('idref') || '',
        linear: item.getAttribute('linear') !== 'no'
      })
    })

    return spine
  }

  /**
   * 解析目录
   */
  private async parseToc(packageDoc: any, packagePath: string): Promise<EpubTocItem[]> {
    try {
      // 查找目录文件
      const tocManifest = packageDoc.manifest.find((item: EpubManifest) => item.mediaType === 'application/x-dtbncx+xml' || item.properties?.includes('nav'))
      // console.log('目录文件:', tocManifest);

      if (!tocManifest) {
        return []
      }

      // 计算目录文件的完整路径
      const packageDir = packagePath.substring(0, packagePath.lastIndexOf('/') + 1)
      const tocPath = packageDir + tocManifest.href
      // console.log('目录文件路径:', tocPath);

      const tocFile = this.zip?.file(tocPath)
      // console.log('目录文件内容:', tocFile);

      if (!tocFile) {
        // console.warn('未找到目录文件:', tocPath);
        return []
      }

      const tocContent = await tocFile.async('text')
      // console.log('tocContent', tocContent);

      return this.parseTocContent(tocContent, tocManifest.mediaType)
    }
    catch (error) {
      // console.warn('目录解析失败:', error);
      return []
    }
  }

  /**
   * 解析目录内容
   */
  private parseTocContent(content: string, mediaType: string): EpubTocItem[] {
    if (mediaType === 'application/x-dtbncx+xml') {
      return this.parseNcxToc(content)
    }
    else {
      return this.parseNavToc(content)
    }
  }

  /**
   * 解析 NCX 格式目录
   */
  private parseNcxToc(content: string): EpubTocItem[] {
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(content, 'text/xml')

    // 只选择顶级 navPoint
    const topLevelNavPoints = xmlDoc.querySelectorAll('navMap > navPoint')
    const toc: EpubTocItem[] = []

    topLevelNavPoints.forEach((navPoint) => {
      const tocItem = this.parseNavPoint(navPoint, 0)
      if (tocItem) {
        toc.push(tocItem)
      }
    })

    return toc
  }

  // 新增方法：递归解析 navPoint
  private parseNavPoint(navPoint: Element, level: number): EpubTocItem | null {
    const label = navPoint.querySelector('navLabel text')
    const content = navPoint.querySelector('content')

    if (!label || !content) {
      return null
    }

    const tocItem: EpubTocItem = {
      id: navPoint.getAttribute('id') || '',
      href: content.getAttribute('src') || '',
      label: label.textContent || '',
      level: level
    }

    // 处理子级 navPoint
    const childNavPoints = navPoint.querySelectorAll(':scope > navPoint')
    if (childNavPoints.length > 0) {
      tocItem.children = []
      childNavPoints.forEach((childNavPoint) => {
        const childTocItem = this.parseNavPoint(childNavPoint, level + 1)
        if (childTocItem) {
          tocItem.children!.push(childTocItem)
        }
      })
    }

    return tocItem
  }

  /**
   * 解析 NAV 格式目录
   */
  private parseNavToc(content: string): EpubTocItem[] {
    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(content, 'text/xml')

    // 找到导航容器
    const navElement = xmlDoc.querySelector('nav[epub\\:type="toc"], nav')
    if (!navElement) return []

    // 只选择顶级 ol 或 ul 的直接子 li
    const topLevelList = navElement.querySelector('ol, ul')
    if (!topLevelList) return []

    const topLevelItems = topLevelList.querySelectorAll(':scope > li')
    const toc: EpubTocItem[] = []

    topLevelItems.forEach((li) => {
      const tocItem = this.parseNavItem(li, 0)
      if (tocItem) {
        toc.push(tocItem)
      }
    })

    return toc
  }

  private parseNavItem(li: Element, level: number): EpubTocItem | null {
    const link = li.querySelector(':scope > a')
    if (!link) return null

    const tocItem: EpubTocItem = {
      id: link.getAttribute('id') || '',
      href: link.getAttribute('href') || '',
      label: link.textContent?.trim() || '',
      level: level
    }

    // 查找子级列表
    const childList = li.querySelector(':scope > ol, :scope > ul')
    if (childList) {
      const childItems = childList.querySelectorAll(':scope > li')
      if (childItems.length > 0) {
        tocItem.children = []
        childItems.forEach((childLi) => {
          const childTocItem = this.parseNavItem(childLi, level + 1)
          if (childTocItem) {
            tocItem.children!.push(childTocItem)
          }
        })
      }
    }

    return tocItem
  }

  /**
   * 解析章节内容
   */
  private async parseChapters(packageDoc: any, packagePath: string): Promise<EpubChapter[]> {
    const chapters: EpubChapter[] = []

    // 计算包文档所在目录
    const packageDir = packagePath.substring(0, packagePath.lastIndexOf('/') + 1)

    for (let i = 0; i < packageDoc.spine.length; i++) {
      const spineItem = packageDoc.spine[i]
      const manifestItem = packageDoc.manifest.find((item: EpubManifest) => item.id === spineItem.idref)

      if (manifestItem && manifestItem.mediaType.includes('html')) {
        // 过滤封面章节
        if (this.isCoverChapter(manifestItem)) {
          // console.log(`跳过封面章节: ${manifestItem.href}`)
          continue
        }

        try {
          const chapter = await this.parseChapter(manifestItem.href, i, packageDir)
          if (chapter) {
            chapters.push(chapter)
          }
        }
        catch (error) {
          // console.warn(`章节 ${i} 解析失败:`, error);
        }
      }
    }

    return chapters
  }

  /**
   * 解析单个章节
   */
  private async parseChapter(href: string, index: number, packageDir: string): Promise<EpubChapter | null> {
    try {
      // 计算章节文件的完整路径
      const chapterPath = packageDir + href
      const chapterFile = this.zip?.file(chapterPath)
      if (!chapterFile) {
        // console.warn('未找到章节文件:', chapterPath);
        return null
      }

      const content = await chapterFile.async('text')

      const parser = new DOMParser()
      const xmlDoc = parser.parseFromString(content, 'text/html')

      // 提取标题 - 优先从body中的h1获取，其次从title标签
      let title = xmlDoc.querySelector('body h1, body h2')?.textContent?.trim()
      if (!title) {
        title = xmlDoc.querySelector('title')?.textContent?.trim()
      }

      // 提取正文内容
      const body = xmlDoc.querySelector('body')

      if (!body) {
        // console.warn(`章节 ${href} 没有body标签`)
        return null
      }

      // 克隆body内容以避免修改原始DOM
      const bodyClone = body.cloneNode(true) as Element

      // 移除可能的标题重复（如果已经提取了标题）
      const firstHeading = bodyClone.querySelector('h1, h2')
      if (firstHeading && firstHeading.textContent?.trim() === title) {
        firstHeading.remove()
      }

      const correctedContent = this.correctImagePaths(bodyClone.innerHTML, href)

      return {
        id: `chapter-${index}`,
        href,
        title,
        content: correctedContent,
        index
      }
    }
    catch (error) {
      // console.warn(`章节内容解析失败:`, error);
      return null
    }
  }

  /**
   * 判断是否为封面章节（基于 manifest 信息）
   */
  private isCoverChapter(manifestItem: EpubManifest): boolean {
    const href = manifestItem.href.toLowerCase()
    const id = manifestItem.id.toLowerCase()
    const properties = manifestItem.properties || []

    // 检查文件路径是否包含封面关键词
    const coverKeywords = ['cover', 'titlepage', 'title-page', 'front-cover']
    const hasCoverInPath = coverKeywords.some(keyword => href.includes(keyword) || id.includes(keyword))

    // 检查 properties 是否包含封面相关属性
    const hasCoverProperty = properties.some((prop: string) => prop.includes('cover') || prop.includes('title-page'))

    return hasCoverInPath || hasCoverProperty
  }

  // 修正图片路径
  private correctImagePaths(html: string, chapterPath: string): string {
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')

    // 处理所有图片
    doc.querySelectorAll('img').forEach((img) => {
      const src = img.getAttribute('src')
      if (src) {
        let relativePath = src

        // 如果是相对路径，需要基于章节目录解析
        if (!src.startsWith('/') && !src.startsWith('http')) {
          const chapterDir = chapterPath.substring(0, chapterPath.lastIndexOf('/') + 1)
          relativePath = chapterDir + src
          // 规范化路径
          relativePath = relativePath.replace(/\/\.\/|\/[^/]+\/\.\.\//g, '/')
        }

        // 确保图片路径指向正确的 images 目录
        // 如果路径不是以 images/ 开头，则添加 images/ 前缀
        if (!relativePath.startsWith('images/') && !relativePath.startsWith('/') && !relativePath.startsWith('http')) {
          // 提取文件名
          const fileName = relativePath.split('/').pop() || relativePath
          relativePath = `images/${fileName}`
        }

        img.setAttribute('src', relativePath)
      }
    })

    return this.cleanHtmlContent(doc.body)
  }

  /**
   * 清理 HTML 内容
   */
  private cleanHtmlContent(body: Element): string {
    // 移除脚本标签
    const scripts = body.querySelectorAll('script')
    scripts.forEach(script => script.remove())

    // 移除样式标签
    const styles = body.querySelectorAll('style')
    styles.forEach(style => style.remove())

    // 获取清理后的 HTML
    return body.innerHTML
  }

  /**
   * 获取文档对象
   */
  getDocument(): EpubDocument | null {
    return this.document
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.zip = null
    this.document = null
  }
}
