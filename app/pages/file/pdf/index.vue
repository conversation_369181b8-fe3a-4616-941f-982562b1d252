<template>
  <DocumentLayouts
    document-type="pdf"
    @file-selected="handleFileSelected"
  >
    <template #right>
      <!-- 翻译并下载 PDF 文件 -->
      <UButton
        :label="t('free_pdf_translation.translate_all_and_download')"
        size="xl"
        color="secondary"
        :disabled="!hasFile"
        class="cursor-pointer rounded-md"
        @click="downloadDialogOpen"
      />
    </template>
    <!-- PDF 预览 - 有文件时显示 -->
    <pdf-viewer
      v-show="hasFile"
      id="pdfView"
      ref="pdfViewerRef"
      @loaded="fileLoaded"
      @page-loaded="pageLoaded"
      @ready="pdfViewerReady"
    />

    <!-- 翻译下载拟态框 -->
    <UModal
      v-model:open="openDownloadModal"
      :close="{
        color: 'neutral',
        variant: 'outline',
        size: 'xs'
      }"
      :ui="{ footer: 'justify-end' }"
    >
      <!-- 翻译全部并下载 -->
      <template #title>
        <!-- 翻译并下载 PDF 文件 -->
        <span class="text-lg">{{ t('free_pdf_translation.download_pdf_dialog_title') }}</span>
      </template>
      <template #description>
        <!-- 请先检查翻译质量和输出格式 -->
        <span>{{ t('free_pdf_translation.download_pdf_dialog_description') }}</span>
      </template>
      <template #body>
        <div class="">
          <!-- 添加错误信息区域 -->
          <div v-if="translationProgress.hasError" class="mb-4">
            <div class="rounded-md bg-red-50 p-3 dark:bg-red-900/30">
              <div class="flex items-start">
                <UIcon name="i-heroicons-exclamation-triangle" class="mt-0.5 mr-2 h-5 w-5 flex-shrink-0 text-red-500" />
                <div>
                  <p class="text-sm font-medium text-red-800 dark:text-red-200">
                    {{ errorPagesList }}
                  </p>
                  <p class="mt-1 text-xs text-red-700 dark:text-red-300">
                    {{ t('检测到部分页面在翻译过程中出现错误，可能导致下载内容不完整或质量下降。') }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <!-- 已翻译：2页 / 共 4 页 -->
            <!-- Translated: 2 pages / Total 4 pages -->
            <p class="text-sm font-medium">
              {{ t('free_pdf_translation.translated') }}：{{ translationProgress.completedPages }}&nbsp;{{ t('free_pdf_translation.pages') }}&nbsp;/&nbsp;{{ t('free_pdf_translation.total') }}&nbsp;{{
                translationProgress.totalPages
              }}&nbsp;{{ t('free_pdf_translation.pages') }}
            </p>
            <!-- 翻译所有页面后再下载（ Translate all pages before downloading ） -->
            <UCheckbox
              v-model="translationAll"
              :label="t('free_pdf_translation.translate_all_pages_before_downloading')"
              class="mt-5"
              :ui="{ label: 'text-sm font-medium', container: 'h-5' }"
            />
          </div>
          <div class="mt-5">
            <!-- 翻译模式 -->
            <URadioGroup
              v-model="translationMode"
              :legend="t('free_pdf_translation.translation_mode') + ':'"
              orientation="horizontal"
              variant="list"
              :items="translationModes"
              :ui="{ legend: 'text-sm font-medium float-left' }"
            />
          </div>
          <div class="mt-4">
            <!-- 导出格式 -->
            <URadioGroup
              v-model="exportFormat"
              :legend="t('free_pdf_translation.export_format') + ':'"
              orientation="horizontal"
              variant="list"
              :items="exportFormats"
              :ui="{ legend: 'text-sm font-medium float-left' }"
            />
          </div>
          <div v-if="exportFormat === 'text'" class="bg-primary-50 dark:bg-primary-900/30 mt-5 flex items-center rounded-md p-4">
            <p class="text-sm">
              <!-- 文字版 PDF 下载 -->
              <span class="font-medium">{{ t('free_pdf_translation.text_based_pdf_download_tips_1') }}：</span>
              <!-- 点击【下载】会显示浏览器的打印界面，在【目标打印机】中选择【另存为PDF】，点击【保存】即可。 -->
              {{ t('free_pdf_translation.text_based_pdf_download_tips_2') }}
            </p>
          </div>
          <!-- 注意：建议避免下载超过 300 页或内容过大的 PDF 文件，以免因系统资源不足导致下载异常。 -->
          <div class="mt-5 text-sm">
            {{ t('free_pdf_translation.download_pdf_dialog_tips') }}
          </div>
          <!-- 统一进度条：渲染翻译进度和下载进度 -->
          <div v-show="renderingProgress.show || exportHandleProgressShow" class="mt-5">
            <div class="mb-2 text-sm font-medium">
              <!-- 根据当前阶段显示不同的文本 -->
              <span v-if="renderingProgress.show && !exportHandleDownload">
                <!-- 正在翻译，请勿关闭窗口... 已翻译 X/X页 -->
                {{ t('free_pdf_translation.translating_please_wait') }}
                {{ t('free_pdf_translation.translated') }} {{ translationProgress.completedPages }} {{ t('free_pdf_translation.page') }}/{{ t('doc_trans_progress.total') }}
                {{ translationProgress.totalPages }} {{ t('free_pdf_translation.page') }}
              </span>
              <span v-else-if="exportHandleDownload">
                <!-- 正在下载，请勿关闭窗口... 已加载 X/X页 -->
                {{ t('free_pdf_translation.downloading_please_wait') }}
                {{ t('free_pdf_translation.loaded') }} {{ Math.round((exportHandleProgress / 100) * totalPages) }}/{{ totalPages }} {{ t('doc_trans_progress.page') }}
              </span>
            </div>
            <!-- 统一进度条，根据阶段显示不同进度和颜色 -->
            <UProgress :model-value="exportHandleDownload ? exportHandleProgress : renderingProgress.value" :color="exportHandleDownload ? 'info' : 'primary'" size="xl" />
          </div>
        </div>
      </template>

      <template #footer>
        <!-- 取消 -->
        <UButton
          size="lg"
          :label="t('common.cancel')"
          color="neutral"
          variant="outline"
          class="mr-4 cursor-pointer rounded-md px-4 font-medium"
          @click="handleCloseDownloadModal"
        />
        <!-- 下载 -->
        <UButton
          size="lg"
          :label="t('free_pdf_translation.download')"
          color="secondary"
          class="cursor-pointer rounded-md px-4 font-medium"
          :disabled="exportHandleDownload || renderingProgress.show"
          @click="handlePdfDownload"
        />
      </template>
    </UModal>

    <!-- PDF导出 -->
    <iframe ref="pdfExportView" style="display: none" />
  </DocumentLayouts>
</template>

<script setup lang="ts">
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { storeToRefs } from 'pinia'
import { usePluginTranslate } from '@/composables/usePluginTranslate.ts'
import { useFileStore } from '@/store/FileStore.ts'
// 组件
import PdfViewer from '@/components/FreePdf/PdfViewer.vue'
import DocumentLayouts from '@/pages/file/components/DocumentLayouts.vue'

// import { useCheckCurrentTabTransEnable } from '@/composables/useCheckCurrentTabTransEnable.ts';
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'
import { useDocTranslationProgressStore } from '@/store/documentTranslate/translationProgress'

// ------------------------------------------------- PDF 功能操作 -------------------------------------------------
import {
  getElementScaleXValue,
  getElementRect,
  paragraphCheck,
  paragraphRowHandle,
  calculateRowFontSize,
  checkSubTag,
  checkSupTag,
  computeElementWidth,
  computeRowWidth,
  htmlEscape,
  formattingOutlineContentText,
  getContentOutlineItem,
  getTextIndent,
  isSequenceNumber,
  extractLeftValue,
  replaceLeftValue,
  calculateRowInfo
} from './utils'
import type { PDFPageView } from 'pdfjs-dist/types/web/pdf_page_view'
import html2canvas from 'html2canvas-oklch'

const { translationProgress } = storeToRefs(useDocTranslationProgressStore())

const { t, locale } = useI18n()
const documentTranslateStore = useDocumentTranslateStore()
const { enabledEnginesList, translateEngine, targetLanguage, targetLanguageList } = storeToRefs(documentTranslateStore)

// 禁用默认布局
definePageMeta({
  layout: false
})

useSeoMeta({
  titleTemplate: '',
  title: t('free_pdf_translation.title') + ' - ' + t('common.site_name'), // PDF 文档翻译
  ogTitle: t('free_pdf_translation.title') + ' - ' + t('common.site_name'), // PDF 文档翻译
  description: t('free_pdf_translation.description'), // 完全免费的 PDF 双语对照翻译功能，满足您大部分外语 PDF 文档的翻译阅读需求。
  ogDescription: t('free_pdf_translation.description') // 完全免费的 PDF 双语对照翻译功能，满足您大部分外语 PDF 文档的翻译阅读需求。
})

// PDF 组件对象
const pdfViewerRef = ref(null)

// 总页数
const totalPages = ref(0)

// 计算是否有文件
const hasFile = computed(() => {
  return fileStore.uploadedFile !== null
})

/**
 * 页面渲染进度
 */
const renderingProgress = ref({
  show: false,
  value: 0 // 渲染进度
})

// ------------------------------------------------- 切换语言 / 翻译引擎 -------------------------------------------------

const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()

const textLayerSelector = '.textLayer'
const annotationLayerSelector = '.annotationLayer'

// 调整获取 pdf 文件
const fileStore = useFileStore()
onMounted(async () => {
  // 使用该方法可以更新切换的翻译引擎的 语言集配置 以及模型列表
  await sendMessageToChromeExtension(locale.value, translateEngine.value.value)

  if (!translateEngine.value || !translateEngine.value.value) {
    // 使用默认值初始化
    translateEngine.value = enabledEnginesList.value[0]
  }
})

// PDF 组件初始化完成
const pdfViewerReadyFlag = ref(false)
const pdfViewerReady = (source: any) => {
  pdfViewerReadyFlag.value = true // 标记组件已就绪

  // 组件就绪后再打开文件
  if (fileStore.uploadedFile) {
    console.log('检测到已上传文件，自动打开:', fileStore.uploadedFile.name)
    nextTick(() => {
      pdfViewerRef.value.openFile(fileStore.uploadedFile)
    })
  }
}

// 组件卸载前
onBeforeUnmount(() => {
  // 清理文件状态
  fileStore.setFile(null)
  // 重置组件状态
  pdfViewerReadyFlag.value = false
  // 取消翻译
  usePluginWebDocumentTranslate({
    type: 'cancelCurrentPageTranslate',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: 'fulltext',
    documentType: 'pdf'
  })
})

// 打开 PDF
const fileName = ref()

const dragCounter = ref(0)

// 处理FileUploadArea组件的文件选择
const handleFileSelected = async (file: File) => {
  fileStore.setFile(file)
  // 如果组件未就绪，等待一段时间后重试
  if (!pdfViewerRef.value || !pdfViewerReadyFlag.value) {
    await new Promise(resolve => setTimeout(resolve, 100))
    if (!pdfViewerRef.value || !pdfViewerReadyFlag.value) {
      console.warn('PDF组件初始化超时')
      return
    }
  }

  pdfViewerRef.value.openFile(file)
}

// 处理FileUploadArea组件的错误
const handleFileError = (message: string) => {
  console.error(message)
  // 这里可以添加用户友好的错误提示
}

// PDF 加载完成
const pdfViewerApplication = ref(null)
// PDF 加载完成回调
const fileLoaded = async (source) => {
  // 重置翻译进度
  translationProgress.value = {
    completedPages: 0, // 已翻译页数
    overallProgress: 0, // 总进度
    pagesStatus: [], // 页码状态
    totalPages: 0, // 总页数
    hasError: false // 是否翻译错误
  }
  /**
   * 先取消翻译, 防止再次触发翻译流程（会导致插件翻译的页面被取消翻译）
   * 发送给插件告诉插件 取消翻译将currentTabTransEnable 设置为 false
   * 这样用户如果已经翻译了当前的 pdf 再打开新的 pdf 的时候不会被取消翻译
   */
  usePluginWebDocumentTranslate({
    type: 'cancelCurrentPageTranslate',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: 'fulltext',
    documentType: 'pdf'
  })

  // 每次加载都重置一次（防止上次加载的 pdf 翻译的状态没有重置）
  translationStarted.value = false

  /** 初始化翻译状态监控-开始监控 */
  initTranslationProgressMonitor(source.pdfDocument.numPages)

  console.log('PDF 加载完成', source)
  pdfViewerApplication.value = source
  fileName.value = source._docFilename

  // 设置总页数
  totalPages.value = source.pdfDocument.numPages

  // PDF语言检测
  try {
    const detectedLanguage = await detectPdfLanguage(source.pdfDocument)
    if (detectedLanguage) {
      // 将检测到的语言代码发送给content脚本和Chrome扩展
      window.postMessage(
        {
          type: 'PDF_LANGUAGE_DETECTED',
          languageCode: detectedLanguage
        },
        '*'
      )

      console.log('PDF语言检测完成:', detectedLanguage)
    }
  }
  catch (error) {
    console.error('PDF语言检测失败:', error)
  }

  const viewerContainer = source.appConfig.viewerContainer

  for (const child of viewerContainer.children) {
    if (child.classList.contains('page')) {
      // 创建一个页面区域
      const pageAreaWrapper = document.createElement('div') // 创建一个页面区域

      pageAreaWrapper.className = 'page-area' // 添加样式类
      // 用包裹容器替换原始元素
      child.replaceWith(pageAreaWrapper)
      // 添加元素
      pageAreaWrapper.appendChild(child) // 添加原始页面
    }
  }
}

// 添加一个标志位来控制翻译是否已经启动
const translationStarted = ref(false)

// 页面加载完成回调
const pageLoaded = async (source: PDFPageView) => {
  const pageDiv = source.div

  // 等待页面渲染完成
  await nextTick()

  if (source.renderingState !== 2) {
    // 等待文本层渲染完成
    if (source.textLayer) {
      try {
        // 使用 setTimeout 给文本层一些时间来完成渲染
        await new Promise<void>((resolve) => {
          const checkTextLayer = () => {
            if (source.textLayer.div && source.textLayer.div.children.length > 0) {
              resolve()
            }
            else {
              setTimeout(checkTextLayer, 100)
            }
          }
          checkTextLayer()
        })
      }
      catch (error) {
        console.error('等待文本层渲染完成时出错:', error)
      }
    }
  }

  // 等待页面克隆流程完成
  await pageClone(pageDiv)

  // 只在第一个页面渲染完成后启动翻译
  if (!translationStarted.value && source.id === 1) {
    translationStarted.value = true
    await usePluginWebDocumentTranslate({
      type: 'translateFilePage',
      translationEngine: translateEngine.value.value,
      targetLanguage: targetLanguage.value,
      transDisplayMode: 'fulltext',
      totalPages: totalPages.value,
      documentType: 'pdf'
    })
  }

  // -------------------------------------------------
  // 如果当前页是最后一页，且存在等待的回调，则触发它继续后续流程
  // -------------------------------------------------
  if (
    pdfViewerApplication.value
    && pdfViewerApplication.value.pdfDocument
    && source.id === pdfViewerApplication.value.pdfDocument.numPages
    && typeof pdfViewerApplication.value.pageLoaded === 'function'
  ) {
    pdfViewerApplication.value.pageLoaded()
  }
}

// 页面拷贝
const pageClone = async (originalPageElement: HTMLElement) => {
  try {
    // 紧接在当前节点之后的兄弟节点已是翻译副本
    const maybeClone = originalPageElement.nextElementSibling as HTMLElement | null
    if (maybeClone && maybeClone.dataset.translation === 'true') {
      return
    }

    // 复制节点
    const clonedElement = originalPageElement.cloneNode(true) as HTMLElement
    clonedElement.dataset.translation = 'true' // 标识复制页面需要翻译

    // 将clonedElement插入到originalPageElement下方
    originalPageElement.parentNode.insertBefore(clonedElement, originalPageElement.nextSibling)

    // 因为文本层可能不存在，所以需要判断
    const textLayerElement = originalPageElement.querySelector(textLayerSelector)
    if (textLayerElement) {
      // 处理画布
      await pageCanvasClearContent(originalPageElement, clonedElement)

      // 处理文本合并
      pageTextLayerMerge(originalPageElement, clonedElement)
    }

    // 清空标注层
    clonedElement.querySelectorAll(annotationLayerSelector).forEach(el => (el.innerHTML = ''))
  }
  catch (error) {
    console.error('页面克隆处理失败:', error)
    throw error // 重新抛出错误让上层处理
  }
}

/**
 * 页面画布清空内容
 * @param originalPageElement 原始页面元素
 * @param clonedElement 克隆的页面元素
 */
const pageCanvasClearContent = async (originalPageElement: HTMLElement, clonedElement: HTMLElement) => {
  // 获取原始页面画布
  const originalCanvas = originalPageElement.querySelector('canvas')
  if (!originalCanvas) {
    return
  }
  const originalCanvasWidth = originalCanvas.width // 获取原始页面宽度
  const originalCanvasHeight = originalCanvas.height // 获取原始页面高度

  // 获取原始页面文字层信息
  const textLayerElement = originalPageElement.querySelector(textLayerSelector) as HTMLElement // 文字层元素
  const textLayerWidth = textLayerElement.offsetWidth // 文字层宽度
  const textLayerHeight = textLayerElement.offsetHeight // 文字层高度

  // 获取复制页面画布
  const clonedCanvas = clonedElement.querySelector('canvas') as HTMLCanvasElement
  const context = clonedCanvas.getContext('2d') // 获取画布上下文

  // 创建水印画布
  const watermarkCanvas = document.createElement('canvas') // 创建水印画布
  watermarkCanvas.width = originalCanvasWidth // 设置水印画布宽度
  watermarkCanvas.height = originalCanvasHeight // 设置水印画布高度
  const watermarkContext = watermarkCanvas.getContext('2d') // 获取水印画布上下文

  // 绘制原始 canvas 的内容到 watermarkCanvas
  watermarkContext.drawImage(originalCanvas, 0, 0)

  // 绘制水印
  context.drawImage(watermarkCanvas, 0, 0)

  // 获取画布和内容层缩放系数
  const widthPercentage = originalCanvasWidth / textLayerWidth // 获取画布宽度缩放系数
  const heightPercentage = originalCanvasHeight / textLayerHeight // 获取画布高度缩放系数

  // 使用 TreeWalker 获取所有有文字内容的元素
  const treeWalker = document.createTreeWalker(textLayerElement, NodeFilter.SHOW_ELEMENT, {
    acceptNode: function (node) {
      // 获取textLayerElement中的所有子元素，不限于直属子元素
      if (node.nodeType === Node.ELEMENT_NODE && node !== textLayerElement) {
        return NodeFilter.FILTER_ACCEPT
      }
      return NodeFilter.FILTER_SKIP
    }
  })

  // 根据文字层内容位置信息，清空画布文字内容
  let currentEle = treeWalker.nextNode() as HTMLElement
  while (currentEle) {
    // 跳过换行符和没有文字内容的元素
    if (currentEle.nodeName === 'BR' || !currentEle.textContent || currentEle.textContent.trim().length === 0) {
      currentEle = treeWalker.nextNode() as HTMLElement
      continue
    }

    // 获取元素缩放系数
    const scaleX = getElementScaleXValue(currentEle)
    const { x1, y1, width, height } = getElementRect(currentEle, textLayerWidth, textLayerHeight, scaleX) // 获取元素位置和宽高

    // 绘制背景色，把文字内容覆盖，避免覆盖不完整，宽度和高度都加 10%
    context.fillStyle = '#FFFFFF'
    context.fillRect(x1 * widthPercentage, y1 * heightPercentage, width * 1.3 * widthPercentage, height * 1.1 * heightPercentage)

    // 移动到下一个节点
    currentEle = treeWalker.nextNode() as HTMLElement
  }
}

/**
 * 页面文本层合并
 * @param originalPageElement 原始页面元素
 * @param clonedElement 克隆的页面元素
 */
const pageTextLayerMerge = (originalPageElement: HTMLElement, clonedElement: HTMLElement) => {
  // 获取文本节点
  const textLayerElement = originalPageElement.querySelector(textLayerSelector) as HTMLElement

  // 把文本层内容进行行拆分
  const rowList = pageTextLayerContentRowSplit(textLayerElement)

  // 进行段落内容合并
  const paragraphList = pageTextLayerParagraphMerge(rowList)
  console.log('paragraphList', paragraphList)

  // 创建新的文本层
  const textLayerNewElement = pageTextLayerCreate(paragraphList, textLayerElement)

  // 添加样式
  textLayerNewElement.classList.add(...textLayerElement.classList)
  textLayerNewElement.classList.add('textTranslationLayer')

  const cloneTextDiv = clonedElement.querySelector('.textLayer')
  // 插入到克隆文本层之后
  cloneTextDiv.after(textLayerNewElement)

  // 删除图文层
  cloneTextDiv.remove()
}

/**
 * 页面文本层内容行拆分
 * @param textLayerElement
 */
const pageTextLayerContentRowSplit = (textLayerElement: HTMLElement): HTMLElement[][] => {
  // 按 BR 标签拆分文本层，拆分成句子
  const rowList: HTMLElement[][] = []
  let rowContentList: HTMLElement[] = []
  for (const child of textLayerElement.children) {
    if (child.classList.contains('markedContent')) {
      const collection = child.children
      if (collection.length <= 0) {
        continue
      }

      for (const collectionElement of collection) {
        // 遇到 BR 标签，将当前行内容添加到行列表中
        if (collectionElement.nodeName == 'BR') {
          if (rowContentList.length > 0) {
            rowList.push(rowContentList)
            rowContentList = []
          }
          continue
        }

        if (collectionElement.innerHTML.trim() === '') {
          continue
        }

        // 设置父类ID作为一个属性，用于进行合并
        const element = collectionElement as HTMLElement
        element.dataset.parentId = child.id
        rowContentList.push(element)
      }

      if (rowContentList.length > 0) {
        rowList.push(rowContentList)
        rowContentList = []
      }
    }
    else {
      // 遇到 BR 标签，将当前行内容添加到行列表中
      if (child.nodeName == 'BR') {
        if (rowContentList.length > 0) {
          rowList.push(rowContentList)
          rowContentList = []
        }
        continue
      }

      rowContentList.push(child as HTMLElement)
    }
  }
  // 添加最后一行
  if (rowContentList.length > 0) {
    rowList.push(rowContentList)
  }
  return rowList
}

/**
 * 页面文本层段落合并
 * @param rowList 行列表
 */
const pageTextLayerParagraphMerge = (rowList: HTMLElement[][]) => {
  const paragraphList = [] // 段落列表
  let paragraphRowList: HTMLElement[][] = [] // 段落行列表

  // 遍历每一行, 进行段落处理
  const rowListLength = rowList.length
  for (let i = 0; i < rowListLength; i++) {
    const row = rowList[i]

    let nextRow = null
    if (i + 1 < rowListLength) {
      nextRow = rowList[i + 1]
    }

    if (i == 0) {
      // 第一行
      paragraphRowList = paragraphRowHandle(paragraphList, paragraphRowList, null, row, nextRow)
      continue
    }

    // 获取上一行的第一个元素
    let preRow = rowList[i - 1]
    if (paragraphRowList.length > 0) {
      const paragraphRow = paragraphRowList[paragraphRowList.length - 1]
      const index = paragraphRow.indexOf(preRow[0])
      if (index > 0) {
        preRow = paragraphRow
      }
    }
    const newParagraph = paragraphCheck(row, preRow, paragraphRowList)
    if (newParagraph) {
      if (paragraphRowList.length > 0) {
        paragraphList.push(paragraphRowList)
        paragraphRowList = []
      }
    }
    paragraphRowList = paragraphRowHandle(paragraphList, paragraphRowList, preRow, row, nextRow)
  }

  if (paragraphRowList.length > 0) {
    paragraphList.push(paragraphRowList)
  }

  return paragraphList
}

/**
 * 页面文本层创建
 * @param paragraphs 段落列表
 * @param textLayerElement 文本层元素
 */
const pageTextLayerCreate = (paragraphs: HTMLElement[][][], textLayerElement: HTMLElement): HTMLDivElement => {
  const pageWidth = textLayerElement.offsetWidth

  const pageDiv = document.createElement('div')
  pageDiv.style.cssText = textLayerElement.style.cssText

  for (let i = 0; i < paragraphs.length; i++) {
    const rows = paragraphs[i]

    let leftVal = extractLeftValue(rows[0][0])
    let paragraphWidth = 0
    let paragraphFontSize = 0

    let text = ''
    let firstElement = rows[0][0]
    let startTraversal = false

    for (let j = 0; j < rows.length; j++) {
      // 段落行集合处理
      let rowWidth = 0
      let rowContent = ''
      const contents = rows[j]
      let tempEle = undefined
      let tempText = ''
      let tempTextLabel = ''

      let rowStartElement = contents[0]
      const rowEndElement = contents[contents.length - 1]

      let emptyEle = undefined
      for (let k = 0; k < contents.length; k++) {
        const currentEle = contents[k]
        if (currentEle === firstElement) {
          startTraversal = true
          rowStartElement = firstElement
        }
        if (!startTraversal) {
          continue
        }

        // 处理段落第一行
        if (j === 0 && k === 0) {
          // 判断是否为需要开头，为需要开头单独处理成一个模块
          const filterContents = contents.filter(item => item.innerHTML.trim().length > 0)
          if (filterContents && filterContents.length > 1) {
            if (isSequenceNumber(filterContents[0].innerText)) {
              const targetEle = filterContents[0]

              // 计算段落宽度
              const paragraphWidth = computeElementWidth(targetEle)

              // 计算行宽度
              const rowWidth = computeRowWidth(targetEle, targetEle)
              const fontSize = calculateRowFontSize(rowWidth, targetEle.innerText, targetEle)

              // 创建段落
              pageTextLayerParagraphElementCreate(pageDiv, targetEle.innerHTML, targetEle, [[targetEle]], extractLeftValue(targetEle), paragraphWidth, fontSize, pageWidth)
              firstElement = filterContents[1]
              startTraversal = false
              leftVal = extractLeftValue(firstElement)
              continue
            }

            if (rows.length > 1) {
              const nextContents = rows[1]
              const computedStyle = window.getComputedStyle(filterContents[1])
              const nextComputedStyle = window.getComputedStyle(nextContents[0])
              if (Math.abs(parseFloat(nextComputedStyle.left) - parseFloat(computedStyle.left)) < parseFloat(computedStyle.fontSize)) {
                const targetEle = filterContents[0]

                // 计算段落宽度
                const paragraphWidth = computeElementWidth(targetEle)

                // 计算行宽度
                const rowWidth = computeRowWidth(targetEle, targetEle)
                const fontSize = calculateRowFontSize(rowWidth, targetEle.innerText, targetEle)

                // 创建段落
                pageTextLayerParagraphElementCreate(pageDiv, targetEle.innerHTML, targetEle, [[targetEle]], extractLeftValue(targetEle), paragraphWidth, fontSize, pageWidth)
                firstElement = filterContents[1]
                startTraversal = false
                leftVal = extractLeftValue(firstElement)
                continue
              }
            }
          }
        }

        rowWidth += computeElementWidth(currentEle)

        // 当前元素为空，处理临时内容
        if (currentEle.innerText.trim().length == 0) {
          emptyEle = currentEle

          if (tempEle) {
            rowContent += `<${tempTextLabel}>${tempText}</${tempTextLabel}>`

            tempEle = undefined
            tempText = ''
            tempTextLabel = ''
          }
          continue
        }

        if (emptyEle) {
          const fontWidth = currentEle.offsetWidth / currentEle.innerText.length

          // 空格占据的宽度百分比
          const offsetWidth = parseFloat(currentEle.style.left) - parseFloat(emptyEle.style.left)
          // 空格占据的宽度
          const width = pageWidth * (offsetWidth / 100)

          const fontCount = Math.round(width / fontWidth)
          if (fontCount >= 1) {
            for (let i = 0; i < fontCount; i++) {
              rowContent += '&#8201;'
            }
          }

          emptyEle = undefined
        }

        // 行内 span 内容处理
        if (k === 0) {
          if (currentEle.innerText.trim().length > 0) {
            rowContent += htmlEscape(currentEle.innerText)
          }
        }
        else {
          const preEle = contents[k - 1]
          if (checkSupTag(currentEle, preEle)) {
            // 上标
            tempEle = preEle
            tempText += htmlEscape(currentEle.innerText)
            tempTextLabel = 'sup'
          }
          else if (checkSubTag(currentEle, preEle)) {
            // 下标
            tempEle = preEle
            tempText += htmlEscape(currentEle.innerText)
            tempTextLabel = 'sub'
          }
          else {
            if (tempEle) {
              if (checkSupTag(currentEle, tempEle)) {
                // 上标
                tempText += htmlEscape(currentEle.innerText)
              }
              else if (checkSubTag(currentEle, tempEle)) {
                // 下标
                tempText += htmlEscape(currentEle.innerText)
              }
              else {
                rowContent += `<${tempTextLabel}>${tempText}</${tempTextLabel}>`

                rowContent += htmlEscape(currentEle.innerText)
                tempEle = undefined
                tempText = ''
                tempTextLabel = ''
              }
            }
            else {
              // 同级前一个元素
              const previousEle = currentEle.previousElementSibling as HTMLElement
              if (previousEle && previousEle.innerText.trim().length === 0) {
                const prePreviousEle = previousEle.previousElementSibling as HTMLElement
                if (prePreviousEle && prePreviousEle.innerText.trim().length > 0) {
                  rowContent += ' '
                }

                // rowContent += ' ';
              }
              else {
                const preComputedStyle = window.getComputedStyle(preEle)
                const currentComputedStyle = window.getComputedStyle(currentEle)
                const interval = parseFloat(currentComputedStyle.left) - parseFloat(preComputedStyle.left) - parseFloat(preComputedStyle.width)
                if (interval > parseFloat(currentComputedStyle.fontSize) / 2 || interval > parseFloat(preComputedStyle.fontSize) / 2) {
                  rowContent += ' '
                }
              }
              rowContent += htmlEscape(currentEle.innerText)
            }
          }
        }

        if (j > 0) {
          const currentLeftVal = extractLeftValue(contents[0])
          if (leftVal > currentLeftVal) {
            leftVal = currentLeftVal
          }
        }

        if (k === contents.length - 1) {
          if (tempEle) {
            // 段落行处理
            rowContent += `<${tempTextLabel}>${tempText}</${tempTextLabel}>`
          }
        }
      }

      rowWidth = computeRowWidth(rowStartElement, rowEndElement)
      if (rowWidth > paragraphWidth) {
        // 计算段落字体大小
        // paragraphFontSize = calculateRowFontSize(rowWidth, rowContent, firstElement);

        const rowFontSize = calculateRowFontSize(rowWidth, rowContent, firstElement)
        if (!paragraphFontSize || rowFontSize < paragraphFontSize) {
          paragraphFontSize = rowFontSize
        }

        // 段落宽度
        paragraphWidth = rowWidth
      }

      // 把行内容赋值到段落内容
      text += rowContent
      // 非最后一行才添加空格占位符
      if (!text.endsWith('-') && j !== rows.length - 1) {
        text += '&#8201;'
      }
    }

    // 排除空内容
    if (text.replaceAll('&#8201;', ' ').trim().length === 0) {
      continue
    }

    // 创建页面元素
    pageTextLayerParagraphElementCreate(pageDiv, text, firstElement, rows, leftVal, paragraphWidth, paragraphFontSize, pageWidth)
  }

  return pageDiv
}

/**
 * 创建页面段落元素（加入到页面元素）
 * @param pageDiv 页面元素
 * @param htmlContent HTML内容
 * @param firstElement 首元素
 * @param paragraphRowList 行元素列表
 * @param paragraphLeftValue 段落左边偏移量
 * @param paragraphWidth 段落宽度
 * @param paragraphFontSize 段落字体大小
 * @param pageWidth 页面宽度
 */
const pageTextLayerParagraphElementCreate = (
  pageDiv: HTMLElement,
  htmlContent: string,
  firstElement: HTMLElement,
  paragraphRowList: HTMLElement[][],
  paragraphLeftValue: number,
  paragraphWidth: number,
  paragraphFontSize: number,
  pageWidth: number
) => {
  // 放大段落宽度，防止刚刚好宽度导致换行
  paragraphWidth = paragraphWidth * 1.01

  // 替换原始中的字体设置
  let fontSize = firstElement.style.fontSize.replaceAll('calc(var(--scale-factor)', 'calc(var(--scale-factor)* var(--font-scale)')
  const match = fontSize.match(/(\d+\.\d+|\.\d+|\d+)(?=px)/)
  if (match) {
    fontSize = fontSize.replaceAll(match[1], paragraphFontSize.toString())
  }

  // 如果是大纲标题内容，则进行特殊格式处理
  if (getContentOutlineItem(firstElement).length > 0) {
    htmlContent = formattingOutlineContentText(htmlContent)
  }
  htmlContent = htmlContent.replace(/\r?\n/g, ' ')

  // 创建段落元素
  const pEle = document.createElement('p')
  pEle.innerHTML = htmlContent
  pEle.style.whiteSpace = 'pre-line'
  pEle.style.marginBottom = '0'
  pEle.style.fontSize = fontSize
  pEle.style.textAlign = 'justify'
  pEle.dataset.ztsl_free_pdf = 'true'

  // 替换段落字体
  const originalFontFamily = window.getComputedStyle(firstElement).fontFamily
  pEle.style.fontFamily = '"Microsoft YaHei", ' + (originalFontFamily === 'serif' ? originalFontFamily : 'system-ui')

  // 处理行高
  if (paragraphRowList.length > 1) {
    const styles = window.getComputedStyle(firstElement)
    const scaleFactor = parseFloat(styles.getPropertyValue('--scale-factor')) || 1

    const rowElement = paragraphRowList[paragraphRowList.length - 1][0]
    const lineHeight = rowElement.offsetTop + rowElement.offsetHeight - firstElement.offsetTop
    const height = lineHeight / paragraphRowList.length
    pEle.style.lineHeight = height / (paragraphFontSize * scaleFactor) + ''
  }

  // 处理首行缩进
  const textIndent = getTextIndent(firstElement, paragraphLeftValue, pageWidth)
  if (textIndent) {
    pEle.style.textIndent = textIndent
  }

  // 创建段落元素
  const divEle = document.createElement('div')
  divEle.style.top = firstElement.style.top
  divEle.style.left = replaceLeftValue(firstElement, paragraphLeftValue.toFixed(2))
  divEle.style.fontSize = fontSize
  divEle.style.fontFamily = firstElement.style.fontFamily
  divEle.style.position = 'absolute'
  divEle.style.color = 'black'

  // 处理旋转
  if (firstElement.style.transform) {
    const strictMatch = firstElement.style.transform.match(/rotate\(\s*([+-]?\d*\.?\d+)(deg|rad|turn)\s*\)/)
    if (strictMatch) {
      divEle.style.transform = strictMatch[0]
      divEle.style.transformOrigin = '0 0'
    }
  }

  // 段落宽度
  const percentage = (paragraphWidth / pageWidth) * 100
  const roundedUp = Math.ceil(percentage * 100) / 100
  divEle.style.width = roundedUp.toFixed(2) + '%'

  // 创建段落元素
  divEle.append(pEle)

  // 将段落元素添加到页面元素
  try {
    pageDiv.appendChild(divEle)
  }
  catch (e) {
    console.log('error', e, divEle)
  }
}

// ------------------------------------------------- PDF 导出操作 -------------------------------------------------
const openDownloadModal = ref(false)
const exportHandleProgress = ref(0)
const exportHandleProgressShow = ref(false)
const exportHandleDownload = ref(false)
// 控制下载按钮禁用
const downloadButtonDisabled = ref(false)
watch(openDownloadModal, (val) => {
  if (val) {
    // 获取需要打印的区域
    const printArea = document.getElementById('viewer')
    if (!printArea) {
      showToast(t('free_pdf_translation.no_content_to_print'), '')
      openDownloadModal.value = false
      return
    }

    // 打开，设置默认值
    translationAll.value = true // 翻译全部页面后再下载
    translationMode.value = 'bilingual'
    exportFormat.value = 'text'

    exportHandleProgress.value = 0
    exportHandleProgressShow.value = false
    exportHandleDownload.value = false

    // 清空
    renderingProgress.value.value = 0
    renderingProgress.value.show = false
  }
})
// 打开下载弹窗
const downloadDialogOpen = async () => {
  if (!pdfViewerApplication.value || pdfViewerApplication.value.pagesCount === 0) {
    return
  }

  openDownloadModal.value = true
}
// 关闭弹窗
const handleCloseDownloadModal = () => {
  openDownloadModal.value = false
  exportHandleDownload.value = false
  exportHandleProgress.value = 0
  exportHandleProgressShow.value = false
  renderingProgress.value.show = false
  renderingProgress.value.value = 0
}
// 翻译全部后再下载
const translationAll = ref(true)
// 翻译模式
const translationMode = ref('bilingual')
const translationModes = ref([
  { value: 'bilingual', label: t('free_pdf_translation.bilingual_translation') }, // 双语对照（灰色背景）
  { value: 'fulltext', label: t('free_pdf_translation.fulltext_translation') } // 全文翻译（仅译文）
])
// 导出格式
const exportFormat = ref('text')
const exportFormats = ref([
  { value: 'text', label: t('free_pdf_translation.text_based_pdf') }, // 文字版 PDF
  { value: 'image', label: t('free_pdf_translation.image_based_pdf') } // 图片版 PDF
])

// 添加客户端条件判断
const { jsPDF } = import.meta.client ? await import('jspdf') : { jsPDF: null }
const pdfExportView = ref()

watch(translationProgress, (val, oldVal) => {
  // 浅拷贝防止重复触发
  const newProgress = { ...val }
  const oldProgress = oldVal ? { ...oldVal } : { overallProgress: -1 }

  // 只有当 overallProgress 真正发生变化时才更新
  if (newProgress.overallProgress !== oldProgress.overallProgress) {
    renderingProgress.value.value = newProgress.overallProgress
    if (newProgress.overallProgress === 100 && renderingProgress.value.show && !exportHandleDownload.value) {
      /**
       * 这里添加延迟事件是确保 renderingProgress.value.value 的值更新成功再打开下载 （iframe）
       * 否则就会出现 有 20 页，加载到 19 就打开下载（iframe），用户体验不好
       */
      // 继续下载流程
      setTimeout(() => {
        continueDownloadProcess()
      }, 500)
    }
  }
})

/**
 * 处理 PDF 下载
 */
const handlePdfDownload = async () => {
  // 统计已渲染页面数量
  const loadedPageCount = countLoadedPage()

  renderingProgress.value.show = true

  // 判断是否渲染所有页面
  if (pdfViewerApplication.value.pagesCount === loadedPageCount) {
    console.log('已渲染所有页面')

    // 直接继续下载流程
    await continueDownloadProcess()
  }
  else {
    // 渲染所有页面
    console.log('未渲染所有页面，当前已渲染页面数：', loadedPageCount, '，总页面数：', pdfViewerApplication.value.pagesCount)

    await renderAllPages()
  }
}

// 文字翻译，仅翻译
const handleTextTranslationDownload = async (onlyTranslation: boolean = false) => {
  // 获取需要打印的区域
  const printArea = document.querySelector('#pdfView #viewer')
  if (!printArea) {
    // 没有需要打印的内容
    showToast(t('free_pdf_translation.no_content_to_print'), '')
    return
  }

  // 如果下载弹窗未打开，则不进行下载
  if (!openDownloadModal.value) {
    return
  }

  let title = undefined
  try {
    // 创建用于打印 Iframe 页面
    const iframe = pdfExportView.value
    const iframeDocument = iframe.contentDocument || iframe.contentWindow.document

    // 复制所有样式节点至 iframe，避免跨域/沙箱限制导致 cssRules 读取失败
    document.querySelectorAll('style, link[rel="stylesheet"]').forEach((node) => {
      iframeDocument.head.appendChild(node.cloneNode(true))
    })

    // 创建 Iframe 页面内容区域，用于设置页面缩放系数
    const iframeContainer = document.createElement('div')
    iframeContainer.id = 'viewer'
    iframeContainer.className = 'pdfViewer'
    // 同步字体缩放和整体缩放，使打印宽度与预览一致
    const computedStyles2 = window.getComputedStyle(printArea)
    const fontScale2 = computedStyles2.getPropertyValue('--font-scale') || '1'
    const scaleFactor2 = computedStyles2.getPropertyValue('--scale-factor') || '1'
    console.log('scaleFactor2', computedStyles2.getPropertyValue('--scale-factor'), scaleFactor2, document.body.clientWidth, printArea.clientWidth)
    const scaleFactorNew = (document.body.clientWidth / printArea.clientWidth) * parseFloat(scaleFactor2)
    iframeContainer.style.setProperty('--font-scale', fontScale2.trim())
    iframeContainer.style.setProperty('--scale-factor', scaleFactorNew.toString())
    iframeContainer.style.display = 'flex'
    iframeContainer.style.flexDirection = 'column'
    iframeContainer.style.alignItems = 'center'
    iframeContainer.style.overflow = 'hidden'
    iframeDocument.body.appendChild(iframeContainer)

    // 打印时确保每个 .page 独占一张纸
    const printPageStyle2 = document.createElement('style')
    printPageStyle2.textContent = `
      @media print {
        .pdfViewer .page {
          page-break-after: always;
          break-after: page;
        }
      }
    `
    iframeDocument.head.appendChild(printPageStyle2)

    // 复制元素到 Iframe 页面内容区域
    const childrenList = printArea.children
    for (let i = 0; i < childrenList.length; i++) {
      // 如果下载弹窗未打开，则不进行下载
      if (!openDownloadModal.value) {
        return
      }

      // 处理页面
      const child = childrenList[i] as HTMLElement
      if (child.classList.contains('page-area')) {
        const children = child.children
        for (let j = 0; j < children.length; j++) {
          handleTextDownloadContent(iframeContainer, children[j] as HTMLElement, onlyTranslation)
        }
      }
      else {
        handleTextDownloadContent(iframeContainer, child, onlyTranslation)
      }

      // 计算进度
      exportHandleProgress.value = ((i + 1) / childrenList.length) * 100

      // 增加延迟是为了防止内存爆满
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    // 处理文件名称
    if (fileName.value && fileName.value.length > 0) {
      title = window.document.title
      window.document.title = fileName.value
    }

    // 打印
    // TODO 调试先注释掉
    iframe.contentWindow?.focus()
    iframe.contentWindow?.print()
  }
  finally {
    if (title) {
      window.document.title = title
    }
    // 移除 Iframe 页面内容区域
    const iframe = pdfExportView.value
    const iframeDocument = iframe.contentDocument || iframe.contentWindow.document
    iframeDocument.innerHTML = '' // 清空整个文档节点
    iframeDocument.write('<html lang="zh"><head></head><body></body></html>') // 重建基础结构
    pdfExportView.value.src = 'about:blank' // 重置iframe地址
  }
}

const handleTextDownloadContent = (iframeContainer: HTMLElement, child: HTMLElement, onlyTranslation: boolean = false) => {
  if (onlyTranslation) {
    // 处理页面翻页标识，标识是否为翻页页面
    const translation = child.dataset.translation
    if (translation !== 'true') {
      return
    }
  }

  // ----------------------------------- 复制页面 -----------------------------------
  // 处理页面复制
  const clonePageElement = child.cloneNode(true) as HTMLElement
  // 移除 PDF.js 注入的平移/缩放，避免文本层在打印时错位到上一页
  clonePageElement.style.transform = 'none'
  // 修正文本层自身 transform/top 偏移
  clonePageElement.querySelectorAll('.textLayer').forEach((tl) => {
    const layer = tl as HTMLElement
    layer.style.transform = 'none'
    layer.style.top = '0'
    layer.style.left = '0'
  })
  clonePageElement.style.position = 'relative'
  iframeContainer.append(clonePageElement)

  // 处理画布复制，需要重新绘制，否则无法显示
  const pageCanvasList = child.querySelectorAll('canvas')
  const canvasList = clonePageElement.querySelectorAll('canvas')
  for (let j = 0; j < canvasList.length; j++) {
    // 同步画布尺寸
    const srcCanvas = pageCanvasList[j]
    const destCanvas = canvasList[j]

    destCanvas.width = srcCanvas.width // 关键修复点
    destCanvas.height = srcCanvas.height
    destCanvas.style.zIndex = '0'

    // 重新绘制内容
    const ctx = destCanvas.getContext('2d')!
    ctx.drawImage(srcCanvas, 0, 0)
  }

  // ----------------------------------- 处理页面文字 -----------------------------------
  // 获取页面翻页标识，标识是否为翻页页面
  const translation = clonePageElement.dataset.translation

  // 处理页面文字内容，原页面需要将文字显示然后放在画布下面，否则在导出 PDF 后无法复制文职
  const textLayerList = clonePageElement.querySelectorAll('.textLayer')
  for (let j = 0; j < textLayerList.length; j++) {
    if (translation !== 'true') {
      const textLayer = textLayerList[j] as HTMLElement
      if (textLayer.classList.contains('textTranslationLayer')) {
        continue
      }
      textLayer.classList.add('textTranslationLayer')
      textLayer.style.zIndex = '-1'
    }
  }
}

// 图片版翻译，仅翻译
const handleImageTranslationDownload = async (onlyTranslation: boolean = false, suffix: string = '-only-') => {
  // 获取需要打印的区域
  const printArea = document.querySelector('#pdfView #viewer')
  if (!printArea) {
    // 没有需要打印的内容
    showToast(t('free_pdf_translation.no_content_to_print'), '')
    return
  }

  // 如果下载弹窗未打开，则不进行下载
  if (!openDownloadModal.value) {
    return
  }

  // 循环打印区域页面，生成图片并直接写入 PDF
  const pdf = new jsPDF()
  let isFirstPage = true

  const appendImgToPdf = (imgData: string) => {
    const imgProps = pdf.getImageProperties(imgData)
    const pdfWidth = pdf.internal.pageSize.getWidth()
    const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width

    if (!isFirstPage) {
      pdf.addPage()
    }
    pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight)
    isFirstPage = false
  }

  // 遍历页面并即时添加到 PDF
  const childrenList = printArea.children
  for (let i = 0; i < childrenList.length; i++) {
    // 如果下载弹窗未打开，则不进行下载
    if (!openDownloadModal.value) {
      return
    }

    const child = childrenList[i] as HTMLElement

    if (child.classList.contains('page-area')) {
      console.log('page-area', child)
      const innerChildren = child.children
      for (let j = 0; j < innerChildren.length; j++) {
        const subChild = innerChildren[j] as HTMLElement
        const imgData = await handleImageDownloadContent(subChild, onlyTranslation)
        if (imgData) appendImgToPdf(imgData)
      }
    }
    else {
      const imgData = await handleImageDownloadContent(child, onlyTranslation)
      if (imgData) appendImgToPdf(imgData)
    }

    // 计算进度
    exportHandleProgress.value = ((i + 1) / childrenList.length) * 100
  }

  const exportFileName = processFileName(fileName.value, suffix)
  pdf.save(exportFileName)
}

/**
 * 处理图片版翻译内容
 * @param iframeContainer
 * @param child
 * @param onlyTranslation
 * @returns
 */
const handleImageDownloadContent = async (child: HTMLElement, onlyTranslation: boolean = false): Promise<string> => {
  if (onlyTranslation && child.dataset.translation !== 'true') {
    return null
  }

  const scale = Math.min(window.devicePixelRatio, 1)
  const canvas = await html2canvas(child, {
    scale: scale, // 适当降低分辨率
    useCORS: true,
    allowTaint: true
  })
  // 现代浏览器使用WebP
  // const imgData = canvas.toDataURL('image/webp');
  const imgData = canvas.toDataURL('image/jpeg', 0.85)
  return imgData
}

// 处理文件名称
const processFileName = (originalName: string, suffix: string = '') => {
  // 使用正则表达式分割文件名和扩展名（处理多.号的情况）
  const match = originalName.match(/(.*?)(\.[^.]*?|)$/)

  if (!match) return originalName // 无效文件名保护

  const [_, baseName, extension] = match
  return `${baseName}${suffix}${extension}`
}

// 错误页面列表
const errorPagesList = computed(() => {
  if (!translationProgress.value || !translationProgress.value.pagesStatus) {
    return t('未发现错误页面')
  }

  const errorPages = translationProgress.value.pagesStatus.filter(page => page.failedNodes > 0).map(page => page.pageNumber)

  if (errorPages.length === 0) {
    return t('未发现错误页面')
  }

  // 格式化页码列表，例如: "第 1, 3, 5 页出现翻译错误"
  return t('第') + errorPages.join(', ') + t('页出现翻译错误')
})

// 实际下载处理逻辑
const continueDownloadProcess = async () => {
  // 隐藏渲染进度，开始显示下载进度
  renderingProgress.value.show = false
  exportHandleDownload.value = true // 设置下载状态为 true
  exportHandleProgress.value = 0 // 设置下载进度为 0
  exportHandleProgressShow.value = true // 设置下载进度显示为 true

  if (exportFormat.value === 'text') {
    // 文字版 PDF
    if (translationMode.value === 'fulltext') {
      // 全文翻译（仅译文）
      await handleTextTranslationDownload(true)
    }
    else {
      // 双语对照
      await handleTextTranslationDownload(false)
    }
  }
  else {
    // 图片版 PDF
    if (translationMode.value === 'fulltext') {
      // 全文翻译（仅译文）
      await handleImageTranslationDownload(true, '-only-')
    }
    else {
      // 双语对照
      await handleImageTranslationDownload(false, '-dual-')
    }
  }

  // 下载完成后重置状态
  exportHandleDownload.value = false
  exportHandleProgressShow.value = false
  exportHandleProgress.value = 0
}

// 统计已渲染页面数量
const countLoadedPage = (): number => {
  const viewer = document.querySelector('#pdfView #viewer')
  let loadedPageCount = 0
  if (viewer) {
    const children = viewer.children
    console.log('children', children)

    for (let i = 0; i < children.length; i++) {
      const child = children[i] as HTMLElement
      if (!child.classList.contains('page-area')) {
        continue
      }
      console.log('child', child)

      if (child.children.length !== 2) {
        continue
      }

      // 判断子节点是否为空，为空返回
      Array.from(child.children).forEach((item) => {
        if ((item as HTMLElement).children.length === 0) {
          console.log('子节点为空')
          return
        }
      })

      // 判断是否包含翻译页面
      Array.from(child.children).forEach((item) => {
        if ((item as HTMLElement).dataset.translation === 'true') {
          console.log('包含翻译页面')

          loadedPageCount++
        }
      })
    }
  }

  return loadedPageCount
}

// ------------------------------------------------- PDF 页面渲染 -------------------------------------------------
/**
 * 渲染所有 PDF 页面（移植自 viewer.mjs）
 */
const renderAllPages = async () => {
  const app = toRaw(pdfViewerApplication.value)
  // 保证 pdfViewer 及其页面数组存在
  if (!app || !app.pdfViewer || !app.pdfViewer._pages || !Array.isArray(app.pdfViewer._pages)) {
    console.warn('PDFViewer 未初始化或页面数据不存在')
    return
  }

  // 等待所有 pdfPage 加载完成
  await app.pdfViewer._pagesCapability.promise

  // 解除 Vue Proxy，确保使用原始的 PageView 对象，避免访问私有字段报错
  const pages = toRaw(app.pdfViewer._pages)

  // 覆盖 destroy 方法，防止 PDFPageViewBuffer 触发 destroy 后清空已渲染内容
  for (const p of pages) {
    const pv = toRaw(p)
    if (pv && typeof pv.destroy === 'function' && !pv.__overrideDestroy) {
      // 先缓存原始 destroy 方法，后续渲染完成后再恢复
      pv.__originalDestroy = pv.destroy
      pv.destroy = () => {}
      pv.__overrideDestroy = true // 标记已覆盖，避免重复
    }
  }

  // RenderingStates 常量
  const RenderingStatesConst = (window as any)?.PDFViewerApplicationConstants?.RenderingStates ?? app?.RenderingStates ?? (window as any)?.PDFViewerApplication?.RenderingStates

  for (const rawPage of pages) {
    const pageView = toRaw(rawPage)
    if (typeof pageView.draw === 'function') {
      // 只有在页面尚未开始渲染的 INITIAL 状态时才调用 draw，避免 "Must be in new state before drawing" 错误
      if (pageView.renderingState === RenderingStatesConst?.INITIAL) {
        // 临时禁用 renderingQueue，避免 draw 过程因优先级判断而暂停
        const originalQueue = pageView.renderingQueue
        pageView.renderingQueue = null
        try {
          // draw 内部会触发 pagerendered 事件
          await pageView.draw()

          const closeFailurePromptEvent = new CustomEvent('checkNewTranslationNodes', {
            detail: {
              pageNumber: pageView.id
            }
          })
          document.dispatchEvent(closeFailurePromptEvent)

          // 添加延迟控制渲染速度
          await new Promise(resolve => setTimeout(resolve, 1000)) // 每页间隔1s
        }
        catch (e) {
          console.error(`渲染第 ${pageView.id} 页失败:`, e)
        }
        finally {
          // 还原 renderingQueue
          pageView.renderingQueue = originalQueue
        }
      }
    }
  }

  // 渲染完成后恢复 destroy 方法，避免影响后续内存管理
  for (const p of pages) {
    const pv = toRaw(p)
    if (pv && pv.__overrideDestroy && pv.__originalDestroy) {
      pv.destroy = pv.__originalDestroy
      delete pv.__originalDestroy
      delete pv.__overrideDestroy
    }
  }
}

// ------------------------------------------------- 通知 -------------------------------------------------
// 通知
const toast = useToast()

function showToast(title: string, description: string) {
  toast.add({
    title: title,
    description: description,
    duration: 3000
  })
}

// ------------------------------------------------- PDF 语言检测 -------------------------------------------------

/**
 * 检测PDF文档的语言
 * @param {PDFDocumentProxy} pdfDocument - PDF文档对象
 * @returns {Promise<string>} - 检测到的语言代码
 */
const detectPdfLanguage = async (pdfDocument: any): Promise<string | null> => {
  try {
    // 提取前3页的文本内容作为样本
    const maxPages = Math.min(3, pdfDocument.numPages)
    const textPromises = []

    for (let i = 1; i <= maxPages; i++) {
      textPromises.push(pdfDocument.getPage(i).then((page: any) => page.getTextContent().then((content: any) => content.items.map((item: any) => item.str).join(' '))))
    }

    const texts = await Promise.all(textPromises)
    const sampleText = texts.join(' ').trim()

    // 确保有足够的文本进行检测（至少100个字符）
    if (sampleText.length < 100) {
      console.warn('PDF文本内容太少，无法进行语言检测')
      return null
    }

    // 使用Google Translate API检测语言
    const detectedLang = await detectLanguageWithGoogle(sampleText.slice(0, 1000)) // 限制文本长度

    // 将Google Translate的语言代码转换为Zing代码
    return mapGoogleLangToZingCode(detectedLang)
  }
  catch (error) {
    console.error('PDF语言检测失败:', error)
    return null
  }
}

/**
 * 使用Google Translate API检测语言
 * @param {string} text - 要检测的文本
 * @returns {Promise<string>} - 检测到的语言代码
 */
const detectLanguageWithGoogle = async (text: string): Promise<string> => {
  try {
    // 使用Google Translate免费检测API
    const response = await fetch(`https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=en&dt=t&q=${encodeURIComponent(text)}`)
    const data = await response.json()

    // Google Translate API返回格式：[[[翻译结果, 原文, null, null, 检测到的语言代码]], null, 检测到的语言代码]
    if (data && data.length > 2 && data[2]) {
      return data[2] // 返回检测到的语言代码
    }

    return 'en' // 默认返回英语
  }
  catch (error) {
    console.error('Google语言检测API调用失败:', error)
    return 'en' // 默认返回英语
  }
}

/**
 * 将Google Translate语言代码映射为Zing代码
 * @param {string} googleLangCode - Google Translate语言代码
 * @returns {string} - Zing语言代码
 */
const mapGoogleLangToZingCode = (googleLangCode: string): string => {
  const langMapping: Record<string, string> = {
    'zh-cn': 'zh-Hans',
    'zh-tw': 'zh-Hant',
    'zh': 'zh-Hans',
    'en': 'en',
    'ja': 'ja',
    'ko': 'ko',
    'fr': 'fr',
    'de': 'de',
    'es': 'es',
    'pt': 'pt',
    'ru': 'ru',
    'ar': 'ar',
    'th': 'th',
    'vi': 'vi',
    'it': 'it',
    'nl': 'nl',
    'pl': 'pl',
    'tr': 'tr',
    'sv': 'sv',
    'da': 'da',
    'no': 'no',
    'fi': 'fi',
    'cs': 'cs',
    'hu': 'hu',
    'ro': 'ro',
    'bg': 'bg',
    'hr': 'hr',
    'sk': 'sk',
    'sl': 'sl',
    'et': 'et',
    'lv': 'lv',
    'lt': 'lt',
    'uk': 'uk',
    'el': 'el',
    'he': 'he',
    'fa': 'fa',
    'hi': 'hi',
    'bn': 'bn',
    'ur': 'ur',
    'ms': 'ms',
    'id': 'id',
    'tl': 'tl'
  }

  return langMapping[googleLangCode.toLowerCase()] || googleLangCode
}
</script>

<style scoped>
#pdfView {
  --font-scale: 1;
}

.transition-div {
  transition: width 0.5s ease;
}
</style>

<style>
.main-header-logo .cursor-pointer div {
  height: 40px !important;
  /* width: 125px !important; */
}

/* .textTranslationLayer :is(span, br) {
  color: inherit !important;
} */

/* 修复术语高亮重叠问题 */
.textTranslationLayer .ztsl_result_output {
  /** display: inline-block !important; */
  width: 100% !important;
  word-wrap: break-word !important;
  white-space: normal !important;
  line-height: normal !important;
  vertical-align: baseline !important;
}

.textTranslationLayer .ztsl_result_output .ztsl_terminology_highlight {
  display: inline !important;
  position: static !important;
  float: none !important;
  z-index: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
}

.textTranslationLayer .ztsl_result_output .ztsl_terminology_dashed_underline {
  display: inline !important;
  position: static !important;
  float: none !important;
  z-index: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
}

.textTranslationLayer .ztsl_result_output .ztsl_terminology_none {
  display: inline !important;
  position: static !important;
  float: none !important;
  z-index: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  vertical-align: baseline !important;
  box-sizing: border-box !important;
}

/* 确保术语后的文本正常显示 */
.textTranslationLayer .ztsl_result_output span + span,
.textTranslationLayer .ztsl_result_output span + text,
.textTranslationLayer .ztsl_result_output .ztsl_terminology_highlight + *,
.textTranslationLayer .ztsl_result_output .ztsl_terminology_dashed_underline + *,
.textTranslationLayer .ztsl_result_output .ztsl_terminology_none + * {
  display: inline !important;
  position: static !important;
  float: none !important;
  clear: none !important;
}

.textLayer .highlight.selected {
  background-color: var(--highlight-selected-bg-color);
}

.textLayer :is(span, br) {
  position: absolute;
  text-align: initial;
}

[data-translation] .linkAnnotation {
  border: none !important;
}

@media print {
  @page {
    size: auto;
    margin: 0;
  }

  body {
    margin: 0;
    width: 100%;
    height: 100%;
  }
}
</style>
