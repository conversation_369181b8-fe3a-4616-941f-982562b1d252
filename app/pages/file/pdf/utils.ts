// 注释层选择器
import nlp from 'compromise'

const annotationLayerSelector = '.annotationLayer'

/**
 * 获取元素缩放系数
 * @param element HTML 元素
 */
export const getElementScaleXValue = (element: HTMLElement): number => {
  // 获取缩放系数
  const transform = element.style.transform
  // 使用正则表达式匹配 scaleX 的值
  const scaleXMatch = transform.match(/scaleX\(([\d.]+)\)/)

  // 如果匹配成功，返回 scaleX 的值
  if (scaleXMatch && scaleXMatch[1]) {
    return parseFloat(scaleXMatch[1])
  }
  return 1
}

/**
 * 获取元素矩形信息
 * @param element DOM 元素
 * @param pageWidth 页面宽度
 * @param pageHeight 页面高度
 * @param scaleX 缩放系数
 */
export const getElementRect = (
  element: HTMLElement,
  pageWidth: number,
  pageHeight: number,
  scaleX: number
): {
  x1: number
  y1: number
  x2: number
  y2: number
  width: number
  height: number
} => {
  const elementComputedStyle = window.getComputedStyle(element)
  // 计算坐标
  const x1 = parseFloat(elementComputedStyle.left)
  const y1 = parseFloat(elementComputedStyle.top)

  // 获取元素的变换矩阵
  const transformMatrix = elementComputedStyle.transform
  const matrix = new DOMMatrix(transformMatrix)

  // 计算元素四个顶点的坐标
  const rectWidth = element.offsetWidth * scaleX
  const rectHeight = element.offsetHeight

  const points = [
    { x: 0, y: 0 },
    { x: rectWidth, y: 0 },
    { x: rectWidth, y: rectHeight },
    { x: 0, y: rectHeight }
  ]

  // 应用变换矩阵计算旋转后的顶点坐标
  const transformedPoints = points.map((point) => {
    const transformed = matrix.transformPoint(new DOMPoint(point.x, point.y))
    return { x: transformed.x + x1, y: transformed.y + y1 }
  })

  // 计算旋转后的边界框
  const minX = Math.min(...transformedPoints.map(p => p.x))
  const minY = Math.min(...transformedPoints.map(p => p.y))
  const maxX = Math.max(...transformedPoints.map(p => p.x))
  const maxY = Math.max(...transformedPoints.map(p => p.y))

  const rotatedWidth = maxX - minX
  const rotatedHeight = maxY - minY

  return {
    x1: minX,
    y1: minY,
    x2: maxX,
    y2: maxY,
    width: rotatedWidth,
    height: rotatedHeight
  }
}

/**
 * 判断是否为为新段落
 * @param rowElementList 当前行元素集合
 * @param preRowElementList 上一个行元素集合
 * @param paragraphRows 段落行元素集合
 */
export const paragraphCheck = (rowElementList: HTMLElement[], preRowElementList: HTMLElement[], paragraphRows: HTMLElement[][]): boolean => {
  // 当传入的前一行为空，有可能为首行元素，认为是新段落
  if (!preRowElementList || preRowElementList.length === 0) {
    return true
  }

  const preParentId = preRowElementList[preRowElementList.length - 1].dataset.parentId
  const currentParentId = rowElementList[0].dataset.parentId
  if (currentParentId && currentParentId === preParentId) {
    return false
  }

  // 当前行首元素
  const firstElement = rowElementList[0]
  // 当前行首元素的计算样式
  const firstElementStyle = window.getComputedStyle(firstElement)
  // 当前行首元素的距离左侧的距离
  const firstElementLeft = parseFloat(firstElementStyle.left)
  // 当前行首元素的字体大小
  const firstElementFontSize = parseFloat(firstElementStyle.fontSize)

  // 获取首行元素
  if (paragraphRows && paragraphRows.length > 0) {
    const minTop = getComputedMinTop(preRowElementList)
    const preMostFrequentTop = getComputedMostFrequentTop(preRowElementList)

    const tempParagraphRows: HTMLElement[][] = []
    for (let i = 0; i < paragraphRows.length; i++) {
      const paragraphRow = paragraphRows[i]
      if (paragraphRow[0] === preRowElementList[0]) {
        tempParagraphRows.push(paragraphRow)
        continue
      }

      // 获取出现次数最多的 top 值
      const mostFrequentTop = getComputedMostFrequentTop(paragraphRow)
      if (mostFrequentTop === minTop || preMostFrequentTop == mostFrequentTop) {
        tempParagraphRows.push(paragraphRow)
      }
    }

    if (tempParagraphRows.length > 0) {
      tempParagraphRows.sort((a, b) => {
        const aLeft = parseFloat(a[0].style.left)
        const bLeft = parseFloat(b[0].style.left)
        return aLeft - bLeft
      })
      let mergedList: HTMLElement[] = []
      for (const row of tempParagraphRows) {
        mergedList = mergedList.concat(row)
      }
      preRowElementList = mergedList
    }
  }

  // 前一行首元素
  const preFirstElement = preRowElementList[0]
  // 前一行首元素的计算样式
  const preFirstElementStyle = window.getComputedStyle(preFirstElement)
  // 前一行首元素的距离左侧的距离
  const preFirstElementLeft = parseFloat(preFirstElementStyle.left)
  // 前一行首元素首元素的字体大小
  const preFirstElementFontSize = parseFloat(preFirstElementStyle.fontSize)

  const topInterval = parseFloat(preFirstElementStyle.top) - parseFloat(firstElementStyle.top)

  // 增强判断: 当top值出现倒挂时，认定为新段落
  if (topInterval >= preFirstElementFontSize || topInterval >= firstElementFontSize) {
    return true
  }

  // 段落最后一行行元素集合
  let paragraphLatestRowElementList: HTMLElement[] = []
  if (paragraphRows && paragraphRows.length > 0) {
    paragraphLatestRowElementList = paragraphRows[paragraphRows.length - 1]
  }

  // 当前行首元素相对应临时元素，缩进超过 1/2个字符，判断为新段落
  if (firstElementLeft - preFirstElementLeft > firstElementFontSize / 2) {
    // 当前行和前一行是否居中对齐显示，如果是居中对齐，有可能为标题，判断为同一段
    if (rowAlignCenterCheck(preRowElementList, rowElementList)) {
      // 增强判断：是否为邮箱地址
      if (emailCheck(preRowElementList) || emailCheck(rowElementList)) {
        return true
      }
      return false
    }

    // 当前行对于在注释层中是否存在对于的标注内容，有可能为大纲目录，认定为新段落
    const checkRes = getContentOutlineItem(firstElement).length > 0
    if (checkRes) {
      return true
    }

    // 判断上一行的最后一个元素和当前行第一个元素的位置
    const preLastElement = preRowElementList[preRowElementList.length - 1]
    const preLastElementStyle = window.getComputedStyle(preLastElement)
    const preLastElementLeft = parseFloat(preLastElementStyle.left)
    const preLastElementFontSize = parseFloat(preLastElementStyle.fontSize)

    const preLastElementScaleX = getElementScaleXValue(preLastElement)
    const preOffset = preLastElementLeft + parseFloat(preLastElementStyle.width) * preLastElementScaleX
    if (Math.abs(preOffset - firstElementLeft) < preLastElementFontSize * 0.5 && Math.abs(topInterval) < parseFloat(preFirstElementStyle.fontSize) * 0.7) {
      return false
    }

    // 增强判断：判断是否为公式的上下坐标
    // 1、判断前一行最后一个元素和当前元素在X轴方向的间隔是否小于等于当前元素字体大小
    // 2、在Y轴方向，判断当前元素和前一行最后一个元素是否存在高度差，大于0.5个字体大小且小于等于当前元素字体大小1.5倍
    if (Math.abs(firstElementLeft - preLastElementLeft) < preLastElementFontSize * 0.5) {
      const interval = Math.abs(parseFloat(preLastElementStyle.top) - parseFloat(firstElementStyle.top))
      if (interval > preLastElementFontSize * 0.5 && interval < preLastElementFontSize * 1.5) {
        return false
      }
    }

    // 对前一行元素的空元素进行过滤
    const preNotEmptyRowList = preRowElementList.filter(item => item.innerHTML.trim().length > 0)
    if (preNotEmptyRowList && preNotEmptyRowList.length > 1) {
      const preSecondElementStyle = window.getComputedStyle(preNotEmptyRowList[1])
      // 前一行的首元素为序号符号，且前一行第二个元素和当前行元素的左边距小于等于当前行字体大小的一半，判断为同一行
      return !(isSequenceNumber(preNotEmptyRowList[0].innerText) && Math.abs(parseFloat(preSecondElementStyle.left) - firstElementLeft) < parseFloat(firstElementStyle.fontSize) / 2)
    }
    else {
      // 所有情况都不符合，判断为新段落
      return true
    }
  }

  if (preFirstElementLeft - firstElementLeft > preFirstElementFontSize * 0.75) {
    // 判断当前行左边偏移量小于前一行，但是从所有非空元素中，如果第二个元素偏移量和前一行开始元素基本一致，判断为列表开始元素
    const targetElementList = rowElementList.filter(item => item.textContent.trim().length > 0)
    if (targetElementList && targetElementList.length > 1) {
      const secondElementStyle = window.getComputedStyle(targetElementList[1])
      if (Math.abs(parseFloat(secondElementStyle.left) - preFirstElementLeft) < parseFloat(preFirstElementStyle.fontSize)) {
        return true
      }
    }
  }

  // 当前后两行的第一个非空元素都是序号时，判断为新段落内容
  const rowNotEmptyElementList = rowElementList.filter(item => item.innerText.trim().length > 0)
  const preRowNotEmptyElementList = preRowElementList.filter(item => item.innerText.trim().length > 0)
  if (rowNotEmptyElementList && rowNotEmptyElementList.length > 0 && preRowNotEmptyElementList && preRowNotEmptyElementList.length > 0) {
    if (isSequenceNumber(rowNotEmptyElementList[0].innerText) && isSequenceNumber(preRowNotEmptyElementList[0].innerText)) {
      return true
    }
  }

  // 当前行首元素相对应上一行的首元素，顶部距离超过 1.5 倍字体大小，判断为新段落
  if (Math.abs(topInterval) >= parseFloat(firstElementStyle.fontSize) * 1.5) {
    return true
  }

  const firstFontSize = parseFloat(firstElementStyle.fontSize)
  const preFirstFontSize = parseFloat(preFirstElementStyle.fontSize)
  // 当前行字体和上一行字体相差小于 1/4 倍，判断大小一致
  if (Math.abs(firstFontSize - preFirstFontSize) < Math.max(firstFontSize, preFirstFontSize) / 4) {
    if (Math.abs(firstElementLeft - preFirstElementLeft) < firstFontSize / 2) {
      // 当前行相对应上一行存在缩进小于 1/2 倍字体大小，有可能为同一段落

      // 当段落集合存在内容且最后一行的内容不为空
      if (paragraphLatestRowElementList && paragraphLatestRowElementList.length > 0) {
        // 计算前一行的内容长度
        const preRowWidth = computeRowWidth(preRowElementList[0], preRowElementList[preRowElementList.length - 1])

        // 计算当前行的内容长度
        const rowWidth = computeRowWidth(rowElementList[0], rowElementList[rowElementList.length - 1])

        // 前一行的内容宽度小于当前行的内容宽度，且当前行的内容宽度比上一行内容宽度大1.5个字符大小，判断为新段落
        if (preRowWidth < rowWidth && rowWidth - preRowWidth > parseFloat(firstElementStyle.fontSize) * 1.5) {
          // 增强判断：
          // 前一行最后一个元素不为结束符号
          const preNotEmptyElementList = preRowElementList.filter(item => item.innerText && item.innerText.trim().length > 0)
          if (preNotEmptyElementList && preNotEmptyElementList.length > 0) {
            const lastElement = preNotEmptyElementList[preNotEmptyElementList.length - 1]
            if (!hasSentenceEndMarker(lastElement.innerText)) {
              return false
            }

            // 增强判断：
            // 1、当前行内容宽度大于前一行宽度，不小于两个字符大小
            // 2、当前行内容宽度小于前行宽度，且当前行内容宽度比前行宽度大25%
            if (rowWidth - preRowWidth > parseFloat(firstElementStyle.fontSize) * 2 && rowWidth - preRowWidth > rowWidth * 0.25) {
              return true
            }
          }

          const firstContent = preRowElementList.map(element => element.textContent || '').join(' ')
          const secondContent = rowElementList.map(element => element.textContent || '').join(' ')
          const checkRes = analyzeSentence(firstContent, secondContent)
          if (!checkRes) {
            return true
          }
        }

        // 前一行最后一个元素不为结束符号
        const preNotEmptyElementList = preRowElementList.filter(item => item.innerText && item.innerText.trim().length > 0)
        if (preNotEmptyElementList && preNotEmptyElementList.length > 0) {
          const lastElement = preNotEmptyElementList[preNotEmptyElementList.length - 1]
          if (!hasSentenceEndMarker(lastElement.innerText)) {
            return false
          }
        }

        // 如果当前行只有一个元素
        // if (rowElementList.length === 1) {
        //   const firstContent = preRowElementList.map((element) => element.textContent || '').join('');
        //   const secondContent = rowElementList.map((element) => element.textContent || '').join('');
        //   let checkRes = analyzeSentence(firstContent, secondContent);
        //   return !checkRes;
        // }
      }

      // // 增强判断：
      // // 当前后两行的第一个非空元素都是序号时，判断为新段落内容
      // let rowNotEmptyElementList = rowElementList.filter((item) => item.innerHTML.trim().length > 0);
      // let preRowNotEmptyElementList = preRowElementList.filter((item) => item.innerHTML.trim().length > 0);
      // if (isSequenceNumber(rowNotEmptyElementList[0].innerText) && isSequenceNumber(preRowNotEmptyElementList[0].innerText)) {
      //   return true;
      // }

      // 增强判断：
      // 1、前一行不为空的元素中，最后一个元素内容结尾带有结束符号
      // 2、存在前一行的前一行，且内容宽度比前一行要宽，认定前一行已经为段落的结尾行，当前行为新段落内容
      const preNotEmptyElementList = preRowElementList.filter(item => item.innerText && item.innerText.trim().length > 0)
      if (preNotEmptyElementList && preNotEmptyElementList.length > 0) {
        const lastElement = preNotEmptyElementList[preNotEmptyElementList.length - 1]

        // 判断是个结束语句
        if (hasSentenceEndMarker(lastElement.innerText)) {
          if (paragraphRows.length > 1) {
            const prevPrevRow = paragraphRows[paragraphRows.length - 2]

            // 计算前一行的内容长度
            const preRowWidth = computeRowWidth(preRowElementList[0], preRowElementList[preRowElementList.length - 1])
            // 计算上上行的内容长度
            const prevPrevRowWidth = computeRowWidth(prevPrevRow[0], prevPrevRow[prevPrevRow.length - 1])

            if (prevPrevRowWidth - preRowWidth > preFirstFontSize * 2) {
              return true
            }
          }
        }
      }
      return false
    }

    // 判断且其中一个是邮箱地址，认定为新段落
    if (emailCheck(preRowElementList) || emailCheck(rowElementList)) {
      return true
    }

    // 获取当前行的字体大小
    const rowFontSize = parseFloat(firstElementStyle.fontSize)
    if (firstElementLeft - parseFloat(preFirstElementStyle.left) >= rowFontSize * 2) {
      // 当前行左边距相对应上一行的左边距，缩进超过 2 个字符，判断为新段落
      return true
    }

    // 获取上一行字体
    const preFontSize = parseFloat(preFirstElementStyle.fontSize)
    if (Math.abs(firstElementLeft - (parseFloat(preFirstElementStyle.left) - 2 * preFontSize)) < preFontSize) {
      // 当前一行减去连个字符的缩进和当前行左边距，缩进小于 1 个字符，有可能为同一个段落

      const rowNotEmptyElementList = rowElementList.filter(item => item.innerHTML.trim().length > 0)
      if (rowNotEmptyElementList.length > 1) {
        // 如果当前行移除所有空元素后，第一个元素为序号，判断为新段落
        return isSequenceNumber(rowNotEmptyElementList[0].innerText)
      }
      return false
    }

    // 前一行最后一个元素不为结束符号
    const preNotEmptyElementList = preRowElementList.filter(item => item.innerText && item.innerText.trim().length > 0)
    if (preNotEmptyElementList && preNotEmptyElementList.length > 0) {
      const lastElement = preNotEmptyElementList[preNotEmptyElementList.length - 1]
      if (!hasSentenceEndMarker(lastElement.innerText)) {
        return false
      }
    }

    return true
  }

  return true
}

/**
 * 段落行处理
 * @param paragraphList 段落集合
 * @param paragraphRowList 段落行集合
 * @param preContentList 前一个内容集合
 * @param rowContentList 行内容集合
 * @param nextContentList 下一个内容集合
 * @returns 段落行集合
 */
export const paragraphRowHandle = (
  paragraphList: HTMLElement[][][],
  paragraphRowList: HTMLElement[][],
  preContentList: HTMLElement[],
  rowContentList: HTMLElement[],
  nextContentList: HTMLElement[]
): HTMLElement[][] => {
  // 判断是否为同一行
  if (preContentList && preContentList.length > 0) {
    // 判断是否和上一行内容为同一行
    const minTop = getComputedMinTop(rowContentList)

    // 获取出现次数最多的 top 值
    const mostFrequentTop = getComputedMostFrequentTop(preContentList)

    const lastPreElement = preContentList[preContentList.length - 1]
    if (lastPreElement) {
      const lastPreStyle = window.getComputedStyle(lastPreElement)
      if (Math.abs(mostFrequentTop - minTop) <= parseFloat(lastPreStyle.fontSize) * 0.5) {
        if (paragraphRowList.length > 0) {
          paragraphRowList[paragraphRowList.length - 1].push(...rowContentList)
        }
        else {
          paragraphRowList.push(rowContentList)
        }
        return paragraphRowList
      }

      const offsetSize = parseFloat(lastPreStyle.left) + lastPreElement.getBoundingClientRect().width
      const firstElement = rowContentList[0]
      const computedStyle = window.getComputedStyle(firstElement)
      const firstLeft = parseFloat(computedStyle.left)
      if (Math.abs(firstLeft - offsetSize) <= parseFloat(lastPreStyle.fontSize) && Math.abs(parseFloat(lastPreStyle.top) - parseFloat(computedStyle.top)) < parseFloat(lastPreStyle.fontSize)) {
        if (paragraphRowList.length > 0) {
          paragraphRowList[paragraphRowList.length - 1].push(...rowContentList)
        }
        else {
          paragraphRowList.push([lastPreElement])
        }
        return paragraphRowList
      }
    }
  }

  // 检查是否为目录大纲
  const contentOutlineItemList = getContentOutlineItem(rowContentList[0])
  if (contentOutlineItemList.length > 0) {
    // 进行目录大纲分割
    return paragraphOutlineSplit(paragraphList, paragraphRowList, rowContentList, contentOutlineItemList)
  }

  // 增强判断：
  // 1. 当存在前一行和后一行时，且前一行和后一行的左边距小于当前行的左边距
  if (preContentList && preContentList.length > 0 && nextContentList && nextContentList.length > 0) {
    const preFirstElement = preContentList[0]
    const preFirstElementStyle = window.getComputedStyle(preFirstElement)

    const nextFirstElement = nextContentList[0]
    const nextFirstElementStyle = window.getComputedStyle(nextFirstElement)

    const rowFirstElement = rowContentList[0]
    const rowFirstElementStyle = window.getComputedStyle(rowFirstElement)

    const preLeft = parseFloat(rowFirstElementStyle.left) - parseFloat(preFirstElementStyle.left)
    const nextLeft = parseFloat(rowFirstElementStyle.left) - parseFloat(nextFirstElementStyle.left)
    const rowFontSize = parseFloat(rowFirstElementStyle.fontSize)

    if (preLeft > rowFontSize * 0.75 && nextLeft > rowFontSize * 0.75) {
      // 段落行集合为空，则进行普通分割
      return paragraphCommonSplitHandle(paragraphList, paragraphRowList, rowContentList)
    }
  }

  // 检查是否为表格
  const tableRow = paragraphTableRowCheck(rowContentList, paragraphRowList)
  if (tableRow) {
    if (paragraphRowList.length > 0) {
      paragraphList.push(paragraphRowList) // 添加段落行集合到段落集合，表示为一个新的段落集合
    }

    for (const element of rowContentList) {
      if (element.innerText.trim().length === 0) {
        continue
      }
      paragraphList.push([[element]])
    }
    return []
  }

  // 段落行集合为空，则进行普通分割
  return paragraphCommonSplitHandle(paragraphList, paragraphRowList, rowContentList)
}

/**
 * 处理目录大纲分割段落
 * @param paragraphList 段落集合
 * @param paragraphRowList 段落行集合
 * @param rowContentList 内容集合
 * @param contentOutlineItemList 目录大纲元素集合
 * @returns 段落行集合
 */
const paragraphOutlineSplit = (paragraphList: HTMLElement[][][], paragraphRowList: HTMLElement[][], rowContentList: HTMLElement[], contentOutlineItemList: HTMLElement[]): HTMLElement[][] => {
  // 标注层存在目录大纲元素，表示当前行为目录大纲
  const annotationContentList = getAnnotationContent(contentOutlineItemList, rowContentList)

  let paragraphRowContentList: HTMLElement[] = [] // 段落行内容集合
  let annotationContentStart = true // 标注内容开始
  for (let i = 0; i < rowContentList.length; i++) {
    const element = rowContentList[i]
    // 当前元素为标注内容元素
    if (annotationContentList.includes(element)) {
      // 大纲内容
      if (annotationContentStart) {
        if (paragraphRowContentList.length > 0) {
          // 判断为大纲内容且为大纲开始，如果原来存在内容，则添加段落行集合到段落集合，表示为一个新的段落集合
          paragraphRowList.push(paragraphRowContentList) // 添加段落行内容集合到段落行集合
          paragraphList.push(paragraphRowList) // 添加段落行集合到段落集合，表示为一个新的段落集合

          paragraphRowList = [] // 清空上一个段落行集合
          paragraphRowContentList = [element] // 添加段落行内容集合到段落行集合
        }
        else {
          paragraphRowContentList = [element]
        }
        annotationContentStart = false
      }
      else {
        // 判断为大纲内容且不是开始内容则进行合并
        paragraphRowContentList.push(element)
      }
    }
    else {
      // 不是大纲内容
      if (!annotationContentStart) {
        // 判断为大纲内容且为大纲开始，如果原来存在内容，则添加段落行集合到段落集合，表示为一个新的段落集合
        paragraphRowList.push(paragraphRowContentList) // 添加段落行内容集合到段落行集合
        paragraphList.push(paragraphRowList) // 添加段落行集合到段落集合，表示为一个新的段落集合

        paragraphRowList = [] // 清空上一个段落行集合
        if (element.innerText.trim().length > 0 && formattingOutlineContent(element).trim().length > 0) {
          paragraphRowContentList = [element] // 添加段落行内容集合到段落行集合
        }
        else {
          paragraphRowContentList = []
        }
        annotationContentStart = true
      }
      else {
        if (element.innerText.trim().length === 0 && formattingOutlineContent(element).trim().length === 0 && paragraphRowContentList.length === 0) {
          continue
        }
        paragraphRowContentList.push(element)
      }
    }
  }

  // 行内容遍历结束后，判断段落行内容不为空，则加入段落行集合
  if (paragraphRowContentList.length > 0) {
    paragraphRowList.push(paragraphRowContentList) // 添加段落行内容集合到段落行集合
    paragraphList.push(paragraphRowList)
  }
  return []
}

/**
 * 获取注释内容
 * @param annotationElementList 注释元素列表
 * @param contentElementList 内容元素列表
 */
export const getAnnotationContent = (annotationElementList: HTMLElement[], contentElementList: HTMLElement[]): HTMLElement[] => {
  const targetElementList = []
  for (const contentElement of contentElementList) {
    // 获取内容元素在注释层中的位置
    const contentRect = contentElement.getBoundingClientRect()
    for (const annotationElement of annotationElementList) {
      // 判断内容元素和注释层元素是否重叠且内容元素没有被添加过
      if (isRectanglesOverlap(annotationElement.getBoundingClientRect(), contentRect) && !targetElementList.includes(contentElement)) {
        targetElementList.push(contentElement)
      }
    }
  }
  return targetElementList
}

/**
 * 获取内容元素在注释层中的位置
 * @param contentElement 内容元素
 */
export const getContentOutlineItem = (contentElement: HTMLElement): HTMLElement[] => {
  // 从上级中找到.page的div元素
  const pageElement = contentElement.closest('.page')
  if (!pageElement) {
    return []
  }

  const annotationLayerEle = pageElement.querySelector(annotationLayerSelector)
  if (!annotationLayerEle) {
    return []
  }

  // 获取 section 集合
  const sectionList = annotationLayerEle.querySelectorAll('section')
  if (!sectionList || sectionList.length === 0) {
    return []
  }
  const intersectingElements = Array.from(sectionList).filter((element) => {
    const htmlEle = element as HTMLElement

    // 判断没有a标签，则跳过
    const aElement = htmlEle.querySelector('a')
    if (!aElement) {
      return false
    }

    // 判断 a 标签的 href 属性，是否为 #section 或者 #subsubsection 开头
    const href = aElement.getAttribute('href')
    if (!href || !/^#(?:sub)*(?:section|appendix)/.test(href)) {
      return false
    }

    // 判断内容元素是否和当前元素存在重叠
    return (
      contentElement.offsetLeft < htmlEle.offsetLeft + htmlEle.offsetWidth
      && contentElement.offsetLeft + contentElement.offsetWidth > htmlEle.offsetLeft
      && contentElement.offsetTop < htmlEle.offsetTop + htmlEle.offsetHeight
      && contentElement.offsetTop + contentElement.offsetHeight > htmlEle.offsetTop
    )
  })

  // 获取注释元素集合
  return intersectingElements as HTMLElement[]
}

/**
 * 格式化大纲内容
 * @param contentElement 内容元素
 */
const formattingOutlineContent = (contentElement: HTMLElement): string => {
  const textContent = contentElement.textContent || ''

  // 或者更严格的版本（必须全是句号，不允许其他字符）
  const cleaned = textContent.replace(/\s+/g, '') // 移除所有空白字符
  if (cleaned.length > 0 && /^\.+$/.test(cleaned)) {
    return ''
  }

  // 对结尾也进行清除
  return textContent.replace(/[\s.]+$/g, '')
}

/**
 * 格式化大纲内容
 * @param textContent 内容元素
 */
export const formattingOutlineContentText = (textContent: string): string => {
  // 或者更严格的版本（必须全是句号，不允许其他字符）
  const cleaned = textContent.replace(/\s+/g, '') // 移除所有空白字符
  if (cleaned.length > 0 && /^\.+$/.test(cleaned)) {
    return ''
  }

  // 对结尾也进行清除
  return textContent.replace(/[\s.]+$/g, '')
}

/**
 * 判断是否是表格行
 * @param rowContentList 行内容集合
 * @param paragraphRowList 段落行集合
 */
const paragraphTableRowCheck = (rowContentList: HTMLElement[], paragraphRowList: HTMLElement[][]): boolean => {
  if (!rowContentList || rowContentList.length <= 1) {
    return false
  }

  if (paragraphRowList && paragraphRowList.length > 0) {
    const latestRowList = paragraphRowList[paragraphRowList.length - 1]
    const latestRowElementStyle = window.getComputedStyle(latestRowList[0])
    const rowElementStyle = window.getComputedStyle(rowContentList[0])
    // 增强判断，如果为表格行，因为表格边框的原因，开头元素和段落内容的元素的左边距会存在偏移，不对齐
    if (Math.abs(parseFloat(latestRowElementStyle.left) - parseFloat(rowElementStyle.left)) < parseFloat(rowElementStyle.fontSize) / 3) {
      return false
    }
  }

  // 统计空格数量
  const emptyCount = rowContentList.filter(element => element.innerText.trim().length === 0).length
  let emptyResult: boolean = false
  if (emptyCount > 2) {
    if (rowContentList.length % 2 === 0) {
      emptyResult = emptyCount >= rowContentList.length / 2 - 1
    }
    else {
      emptyResult = emptyCount >= Math.floor((rowContentList.length - 1) / 2) - 1
    }
  }

  let emptyVal = rowContentList[0].innerText.trim().length === 0
  for (let i = 1; i < rowContentList.length; i++) {
    const res = rowContentList[i].innerText.trim().length === 0
    if (res) {
      // 当前内容为空格，判断前后连个元素之间间隔小于一个字符的，判定为同一行，不为表格
      if (i + 1 < rowContentList.length) {
        const preElement = rowContentList[i - 1]
        const nextElement = rowContentList[i + 1]

        // 相隔不到一个字符，判定为同一行，不为表格
        if (Math.abs(nextElement.offsetLeft - (preElement.offsetLeft + preElement.offsetWidth)) <= nextElement.offsetHeight) {
          if (!emptyResult) {
            return false
          }
        }

        // 判断字体相差太远，判定为同一行，不为表格
        if (emptyResult) {
          const nextStyle = window.getComputedStyle(nextElement)
          const nextFontSize = parseFloat(nextStyle.fontSize)

          const preStyle = window.getComputedStyle(preElement)
          const preFontSize = parseFloat(preStyle.fontSize)
          const fontSizeInterval = Math.abs(nextFontSize - preFontSize)
          if (fontSizeInterval > preFontSize / 3 && fontSizeInterval > nextFontSize / 3) {
            return false
          }
        }
      }
    }

    if (emptyVal !== res) {
      // 相邻的两个格子有一个为空，有可能表格行
      emptyVal = res
    }
    else {
      // 相邻的两个格子都为空或非空，则不是表格行
      return false
    }
  }

  // 计算内容宽度

  return true
}

/**
 * 处理普通分割段落
 * @param paragraphList 段落集合
 * @param paragraphRowList 段落行集合
 * @param rowContentList 内容集合
 * @returns 段落行集合
 */
const paragraphCommonSplitHandle = (paragraphList: HTMLElement[][][], paragraphRowList: HTMLElement[][], rowContentList: HTMLElement[]): HTMLElement[][] => {
  let paragraphRowContentList: HTMLElement[] = [] // 段落行内容集合
  for (let i = 0; i < rowContentList.length; i++) {
    const element = rowContentList[i]

    if (element.dataset.parentId && paragraphRowList.length === 0 && paragraphList.length > 0) {
      const paragraphContentList = paragraphList[paragraphList.length - 1]
      const rowContentList = paragraphContentList[paragraphContentList.length - 1]
      const lastElement = rowContentList[rowContentList.length - 1]
      if (element.dataset.parentId === lastElement.dataset.parentId) {
        rowContentList.push(element)
        continue
      }
    }

    if (element.offsetHeight === 0 || element.offsetWidth === 0) {
      continue
    }

    // 第一个元素
    if (i === 0) {
      paragraphRowContentList.push(element)
      continue
    }

    // 校验是否属于同一个内容模块
    const lastElement = paragraphRowContentList[paragraphRowContentList.length - 1]
    if (element.dataset.parentId && lastElement?.dataset?.parentId === element.dataset.parentId) {
      paragraphRowContentList.push(element)
      continue
    }

    // 判断两个元素距离
    if (lastElement) {
      const lastStyle = window.getComputedStyle(lastElement)
      const computedStyle = window.getComputedStyle(element)
      const offsetSize = parseFloat(lastStyle.left) + lastElement.getBoundingClientRect().width
      if (Math.abs(parseFloat(computedStyle.left) - offsetSize) > parseFloat(lastStyle.fontSize) * 2) {
        // 在统一垂直方向上，判定为不是同一段落内容（判断依据：当前元素和前一个元素左边偏移量相同或者小于前一个元素字体的一半）
        paragraphRowList.push(paragraphRowContentList) // 添加段落行内容集合到段落行集合
        paragraphList.push(paragraphRowList) // 添加段落行集合到段落集合，表示为一个新的段落集合

        paragraphRowList = [] // 清空上一个段落行集合
        paragraphRowContentList = [element] // 添加段落行内容集合到段落行集合
        continue
      }
    }

    const preElement = rowContentList[i - 1] as HTMLElement

    if (preElement.offsetHeight === 0 || preElement.offsetWidth === 0 || preElement.textContent.trim().length === 0) {
      paragraphRowContentList.push(element)
      continue
    }

    const preOffsetLeft = preElement.offsetLeft // 前一个元素左边偏移量
    const offsetLeft = element.offsetLeft // 当前元素左边偏移量
    const preFontSize = Math.abs(preOffsetLeft - offsetLeft) / preElement.innerText.length // 前一个元素字体大小
    if (preOffsetLeft === offsetLeft || Math.abs(offsetLeft - preOffsetLeft) < preFontSize / 2) {
      // 在统一垂直方向上，判定为不是同一段落内容（判断依据：当前元素和前一个元素左边偏移量相同或者小于前一个元素字体的一半）
      paragraphRowList.push(paragraphRowContentList) // 添加段落行内容集合到段落行集合
      paragraphList.push(paragraphRowList) // 添加段落行集合到段落集合，表示为一个新的段落集合

      paragraphRowList = [] // 清空上一个段落行集合
      paragraphRowContentList = [element] // 添加段落行内容集合到段落行集合
      continue
    }

    let preOffsetTop: number
    let offsetTop: number
    if (element.offsetTop > preElement.offsetTop) {
      // 可能为下标或正常内容
      // 判断依据：
      // 1、当前元素的顶部偏移量大于前一个元素，表示当前元素位置偏下。
      // 2、因为下标字体偏小，所以（当前元素顶部偏移量+当前元素高度）减去（前一个元素顶部偏移量+前一个元素高度）偏差小于当前元素的一半高度，有可能为下标
      // 3、因为同一行的不同元素的偏移量会存在偏差，且元素内容的内容也会存在偏差（例如：顶部偏移量一致，但是当前元素的文字内容只占据了元素内的一半高度），所以有可能是正常内容或者下标
      preOffsetTop = preElement.offsetTop + preElement.offsetHeight
      offsetTop = element.offsetTop + element.offsetHeight
    }
    else {
      // 可能为上标或正常内容
      // 判断依据：
      // 1、当前元素的顶部偏移量小于或等于前一个元素，表示当前元素位置偏上或持平。
      // 2、因为上标字体偏小，所以（当前元素顶部偏移量-当前元素高度）减去（前一个元素顶部偏移量-前一个元素高度）偏差小于当前元素的一半高度，有可能为上标
      // 3、因为同一行的不同元素的偏移量会存在偏差，且元素内容的内容也会存在偏差（例如：顶部偏移量一致，但是当前元素的文字内容只占据了元素内的一半高度），所以有可能是正常内容或者上标

      const preComputedStyle = window.getComputedStyle(preElement)
      const computedStyle = window.getComputedStyle(element)
      if (parseFloat(preComputedStyle.fontSize) < parseFloat(computedStyle.fontSize)) {
        // 当前一个字符小于当前字符内容，有可能为前面内容的下标
        preOffsetTop = preElement.offsetTop + preElement.offsetHeight
        offsetTop = element.offsetTop + element.offsetHeight
      }
      else {
        preOffsetTop = preElement.offsetTop - preElement.offsetHeight
        offsetTop = element.offsetTop - element.offsetHeight
      }
    }

    // 偏移量偏差（当前元素的一半）
    const offset = element.offsetHeight / 2

    if (Math.abs(preElement.offsetTop - element.offsetTop) < offset || Math.abs(preOffsetTop - offsetTop) < offset) {
      // 判断为同一行
      // 判断依据：
      // 1、当前元素和前一个元素的顶部偏移量相差小于当前元素高度的一半。
      // 2、当前元素和前一元素按规则获取的偏移量差小于当前元素的一半高度。
      paragraphRowContentList.push(element)
    }
    else {
      // 不符合以上的情况，判定为不是同一段落内容
      paragraphRowList.push(paragraphRowContentList) // 添加段落行内容集合到段落行集合
      paragraphList.push(paragraphRowList) // 添加段落行集合到段落集合，表示为一个新的段落集合

      paragraphRowList = [] // 清空上一个段落行集合
      paragraphRowContentList = [element] // 添加段落行内容集合到段落行集合
    }
  }

  // 行内容遍历结束后，判断段落行内容不为空，则加入段落行集合
  if (paragraphRowContentList.length > 0) {
    paragraphRowList.push(paragraphRowContentList) // 添加段落行内容集合到段落行集合
  }
  return paragraphRowList
}

/**
 * 获取最小的 top 值
 * @param rowElementList 行元素列表
 */
const getComputedMinTop = (rowElementList: HTMLElement[]): number => {
  return Math.min(
    ...rowElementList.map((el) => {
      const computedStyle = window.getComputedStyle(el)
      return parseFloat(computedStyle.top)
    })
  )
}

/**
 * 检查两行是否居中，判断为同一行
 * @param preRowList 上一行元素集合
 * @param currentRowList 当前行元素集合
 */
export const rowAlignCenterCheck = (preRowList: HTMLElement[], currentRowList: HTMLElement[]): boolean => {
  // 计算前一行的中心点
  const preLeft = Math.min(
    ...preRowList.map((item) => {
      const computedStyle = window.getComputedStyle(item)
      return parseFloat(computedStyle.left)
    })
  )
  const preRight = Math.min(
    ...preRowList.map((item) => {
      const computedStyle = window.getComputedStyle(item)
      return parseFloat(computedStyle.left) + parseFloat(computedStyle.width)
    })
  )
  const preCenterX = preLeft + (preRight - preLeft) / 2
  // 计算当前行的中心点
  const currentLeft = Math.min(
    ...currentRowList.map((item) => {
      const computedStyle = window.getComputedStyle(item)
      return parseFloat(computedStyle.left)
    })
  )
  const currentRight = Math.min(
    ...currentRowList.map((item) => {
      const computedStyle = window.getComputedStyle(item)
      return parseFloat(computedStyle.left) + parseFloat(computedStyle.width)
    })
  )
  const currentCenterX = currentLeft + (currentRight - currentLeft) / 2

  const fontSize = parseFloat(window.getComputedStyle(currentRowList[0]).fontSize)

  // 当左右两边边距相差小于半个字符大小，判断为居中
  if (Math.abs(preCenterX - currentCenterX) > fontSize / 3) {
    return false
  }

  // 判空处理，如果前一行为空元素，直接返回false，判断不是同一行
  const preFilterRowList = preRowList.filter((item) => {
    if (item.innerText.trim().length === 0) {
      return false
    }

    // 移除控制字符
    const cleanText = item.innerText.replace(/[^\x20-\x7E]/g, '')
    return cleanText.length !== 0
  })
  if (preFilterRowList.length === 0) {
    return false
  }

  let preRowWidth = 0
  for (const element of preRowList) {
    preRowWidth += element.offsetWidth
  }

  let currentRowWidth = 0
  for (const element of currentRowList) {
    currentRowWidth += element.offsetWidth
  }

  // 判断上下两行的宽度是否小于一半字体大小
  const preFirstElement = preRowList[0]
  const currentFirstElement = currentRowList[0]
  if (Math.abs(preFirstElement.offsetTop + preFirstElement.offsetHeight - currentFirstElement.offsetTop) > currentFirstElement.offsetHeight / 2) {
    return false
  }
  // 相差大于 1/3个字体大小
  if (Math.abs(preFirstElement.offsetHeight - currentFirstElement.offsetHeight) > preFirstElement.offsetHeight / 2) {
    return false
  }

  // 相差小于半个字体大小
  return Math.abs(preRowList[0].offsetLeft + preRowWidth / 2 - (currentRowList[0].offsetLeft + currentRowWidth / 2)) <= currentRowList[0].offsetHeight * 2
}

/**
 * 检查是否为上标
 * @param currentElement 当前元素
 * @param preElement 前一个元素
 */
export const checkSupTag = (currentElement: HTMLElement, preElement: HTMLElement): boolean => {
  // 判断是否为上标
  // 判断依据：当前元素上边框位置相对于前一个元素位置偏上，且当前元素小于前一个元素高度的 1/4
  return preElement.offsetTop > currentElement.offsetTop && preElement.offsetHeight - currentElement.offsetHeight > preElement.offsetHeight / 4
}

/**
 * 检查是否为下标
 * @param currentElement 当前元素
 * @param preElement 前一个元素
 */
export const checkSubTag = (currentElement: HTMLElement, preElement: HTMLElement): boolean => {
  // 判断是否为下标
  // 判断依据：当前元素下边框位置相对于前一个元素位置偏下，且当前元素小于前一个元素高度的 1/4
  return preElement.offsetTop <= currentElement.offsetTop && preElement.offsetHeight - currentElement.offsetHeight > preElement.offsetHeight / 4
}

/**
 * 分析句子
 * @param firstContent 第一个内容
 * @param secondContent 第二个内容
 */
export const analyzeSentence = (firstContent: string, secondContent: string): boolean => {
  if (!firstContent.trim() || !secondContent.trim()) {
    return false
  }

  const sentence = combinedSentence(firstContent, secondContent)

  // 使用Compromise进行NLP分析
  const content = nlp(sentence)

  // 获取句子数量
  const sentenceCount = content.sentences().length

  // 检查语法完整性
  const isComplete = content.sentences().fullSentences().out('array').length > 0

  // 获取词性标注
  const tags = []
  const terms = content.sentences().json()[0]?.terms || []
  terms.forEach((term) => {
    tags.push(...term.tags)
  })

  // 判断是否属于同一个句子
  if (isComplete) {
    if (sentenceCount === 1) {
      const firstDoc = nlp(firstContent)
      const secondDoc = nlp(secondContent)

      const firstPeopleList = firstDoc.people().out('array')
      const secondPeopleList = secondDoc.people().out('array')
      const firstNounsList = firstDoc.nouns().out('array')
      const secondNounsList = secondDoc.nouns().out('array')
      if (
        ((firstPeopleList.length === 1 && firstPeopleList[0] === firstContent) || (firstNounsList.length === 1 && firstNounsList[0] === firstContent))
        && ((secondPeopleList.length === 1 && secondPeopleList[0] === secondContent) || (secondNounsList.length === 1 && secondNounsList[0] === secondContent))
      ) {
        return false
      }

      return true
    }

    const sentences = content.sentences().out('array')
    let tempContent = firstContent
    for (let i = 0; i < sentences.length; i++) {
      let sentenceContent = sentences[i].trim()
      if (tempContent.trim().includes(sentenceContent)) {
        tempContent = tempContent.replace(sentenceContent, '').trim()
        continue
      }

      if (sentenceContent.includes(tempContent.trim())) {
        sentenceContent = sentenceContent.replace(tempContent, '')

        if (secondContent.startsWith(sentenceContent.trim())) {
          return true
        }
      }
    }
    return false
  }
  else {
    return false
  }
}

/**
 * 组合句子
 * @param str1 第一个句子
 * @param str2 第二个句子
 */
const combinedSentence = (str1: string, str2: string) => {
  if (!str1 && !str2) return ''
  return `${str1} ${str2}`.replace(/\s+/g, ' ').trim()
}

/**
 * 计算元素宽度
 * @param element
 */
export const computeElementWidth = (element: HTMLElement): number => {
  if (element.style.transform) {
    const transformStr = element.style.transform
    const scaleXMatch = transformStr.match(/scaleX\(([\d.]+)\)/)
    let scaleX = 1

    if (scaleXMatch && scaleXMatch[1]) {
      scaleX = parseFloat(scaleXMatch[1])
    }

    const width = Math.ceil(element.offsetWidth * scaleX * 100) / 100
    return parseFloat(width.toFixed(2))
  }
  else {
    return element.offsetWidth
  }
}

/**
 * 计算两个元素之间的行宽度，考虑元素的旋转和缩放变换
 * @param startElement 起始元素
 * @param endElement 结束元素
 * @returns 行的实际宽度
 */
export const computeRowWidth = (startElement: HTMLElement, endElement: HTMLElement): number => {
  // 获取两个元素的边界矩形
  const startRect = startElement.getBoundingClientRect()
  const endRect = endElement.getBoundingClientRect()

  // 获取两个元素的变换信息（旋转和缩放）
  const startTransform = getElementTransformInfo(startElement)
  const endTransform = getElementTransformInfo(endElement)

  // 计算考虑缩放后的实际尺寸
  const startScaledWidth = startRect.width / startTransform.scaleX
  const startScaledHeight = startRect.height / startTransform.scaleY
  const endScaledWidth = endRect.width / endTransform.scaleX
  const endScaledHeight = endRect.height / endTransform.scaleY

  // 计算旋转后的实际宽度（考虑缩放）
  const startActualWidth = calculateRotatedWidth(startScaledWidth, startScaledHeight, startTransform.rotation) * startTransform.scaleX
  const endActualWidth = calculateRotatedWidth(endScaledWidth, endScaledHeight, endTransform.rotation) * endTransform.scaleX

  // 计算元素的X轴中心点
  const startCenterX = startRect.left + startRect.width / 2
  const endCenterX = endRect.left + endRect.width / 2

  // 计算两个中心点之间的距离
  const distanceBetweenCenters = Math.abs(endCenterX - startCenterX)

  // 计算总宽度（包含两个元素的实际宽度和它们之间的距离）
  return distanceBetweenCenters + startActualWidth / 2 + endActualWidth / 2
}

// 获取元素的变换信息（旋转角度和缩放比例）
interface TransformInfo {
  rotation: number
  scaleX: number
  scaleY: number
}

/**
 * 获取元素的变换信息（旋转角度和缩放比例）
 * @param element HTML元素
 * @returns 包含旋转角度和缩放比例的对象
 */
const getElementTransformInfo = (element: HTMLElement): TransformInfo => {
  const style = window.getComputedStyle(element)
  const transform = style.getPropertyValue('transform')

  let rotation = 0
  let scaleX = 1
  let scaleY = 1

  if (transform && transform !== 'none') {
    try {
      // 解析变换矩阵
      const matrix = new DOMMatrix(transform)

      // 从变换矩阵中提取旋转角度
      rotation = Math.atan2(matrix.b, matrix.a)

      // 从变换矩阵中提取缩放比例
      // 计算缩放因子需要考虑旋转，这里简化处理
      scaleX = Math.sqrt(matrix.a * matrix.a + matrix.b * matrix.b)
      scaleY = Math.sqrt(matrix.c * matrix.c + matrix.d * matrix.d)
    }
    catch (error) {
      console.error('Failed to parse transform matrix:', error)
    }
  }

  return { rotation, scaleX, scaleY }
}

/**
 * 计算旋转后的实际宽度
 * @param width 原始宽度
 * @param height 原始高度
 * @param rotation 旋转角度
 */
const calculateRotatedWidth = (width: number, height: number, rotation: number): number => {
  // 使用三角函数计算旋转后的宽度
  return Math.abs(width * Math.cos(rotation)) + Math.abs(height * Math.sin(rotation))
}

/**
 * 计算行字体大小
 * @param rowWidth 行宽度
 * @param rowContent
 * @param rowFirstElement
 */
export const calculateRowFontSize = (rowWidth: number, rowContent: string, rowFirstElement: HTMLElement): number => {
  const styles = window.getComputedStyle(rowFirstElement)
  const scaleFactor = parseFloat(styles.getPropertyValue('--scale-factor')) || 1

  // 创建临时元素用于测量
  const tempSpan = document.createElement('span')
  tempSpan.style.position = 'absolute' // 防止影响布局
  tempSpan.style.visibility = 'hidden' // 隐藏元素
  tempSpan.style.whiteSpace = 'nowrap' // 禁止换行
  tempSpan.style.fontFamily = styles.fontFamily
  tempSpan.style.fontWeight = styles.fontWeight
  tempSpan.style.fontStyle = styles.fontStyle
  tempSpan.style.letterSpacing = styles.letterSpacing
  tempSpan.innerHTML = rowContent

  // 添加临时元素
  document.body.appendChild(tempSpan)

  // 初始字体大小
  const originalFontSize = parseFloat(styles.fontSize) || 16
  let fontSize = originalFontSize
  tempSpan.style.fontSize = `${fontSize.toFixed(2)}px`
  let currentWidth = tempSpan.offsetWidth

  // 安全系数：预留 1% 的空间
  const safeRowWidth = rowWidth * 0.98

  // 按比例调整字体大小
  const ratio = safeRowWidth / currentWidth
  fontSize = fontSize * ratio
  tempSpan.style.fontSize = `${fontSize.toFixed(2)}px`
  currentWidth = tempSpan.offsetWidth

  // 逐步减小字体直到适应
  while (currentWidth > safeRowWidth && fontSize > 0) {
    fontSize = fontSize - 0.1
    tempSpan.style.fontSize = `${fontSize.toFixed(2)}px`
    currentWidth = tempSpan.offsetWidth
  }

  // 清理临时元素
  document.body.removeChild(tempSpan)

  fontSize = fontSize / scaleFactor

  return Math.min(fontSize, originalFontSize)
}

/**
 * 计算行信息
 * @param rowContent 行内容
 * @param rowFirstElement 行第一个元素
 */
export const calculateRowInfo = (
  rowContent: string,
  rowFirstElement: HTMLElement
): {
  fontSize: number
  rowWidth: number
} => {
  const styles = window.getComputedStyle(rowFirstElement)
  const scaleFactor = parseFloat(styles.getPropertyValue('--scale-factor')) || 1

  // 创建临时元素用于测量
  const tempSpan = document.createElement('span')
  tempSpan.style.position = 'absolute' // 防止影响布局
  tempSpan.style.visibility = 'hidden' // 隐藏元素
  tempSpan.style.whiteSpace = 'nowrap' // 禁止换行
  tempSpan.style.fontFamily = styles.fontFamily
  tempSpan.style.fontWeight = styles.fontWeight
  tempSpan.style.fontStyle = styles.fontStyle
  tempSpan.style.letterSpacing = styles.letterSpacing
  tempSpan.innerHTML = rowContent

  // 添加临时元素
  document.body.appendChild(tempSpan)

  // 初始字体大小
  const fontSize = parseFloat(styles.fontSize) || 16
  tempSpan.style.fontSize = `${fontSize.toFixed(2)}px`
  return {
    fontSize: fontSize / scaleFactor,
    rowWidth: tempSpan.offsetWidth
  }
}

/**
 * 获取当前元素的缩进
 * @param firstElement 当前段落的第一个元素
 * @param offsetLeft 段落最小左边偏移百分比
 * @param pageWidth 页面宽度
 */
export const getTextIndent = (firstElement: HTMLElement, offsetLeft: number, pageWidth: number): string | undefined => {
  const firstElementOffsetLeft = parseFloat(firstElement.style.left)
  if (firstElementOffsetLeft <= offsetLeft) {
    return
  }

  // 获取缩进的百分比
  const interval = firstElementOffsetLeft - offsetLeft
  // 获取缩进的宽度
  const intervalWidth = pageWidth * (interval / 100)

  // 计算缩进字数
  const fontCount = intervalWidth / firstElement.offsetHeight
  return fontCount + 'em'
}

/**
 * 获取最小的 top 值
 * @param rowElementList 行元素列表
 */
const getMinTop = (rowElementList: HTMLElement[]): number => {
  return Math.min(...rowElementList.map(el => parseFloat(el.style.top)))
}

/**
 * 获取行元素列表中元素最常用的顶部位置
 * @param rowElementList 行元素列表
 */
const getMostFrequentTop = (rowElementList: HTMLElement[]): number => {
  // 统计 top 值出现的次数，获取出现次数最多的 top 值
  const topCounts = rowElementList.reduce((acc, el) => {
    const top = parseFloat(el.style.top)
    acc[top] = (acc[top] || 0) + 1
    return acc
  }, {})

  const mostFrequentTop = Object.keys(topCounts).reduce((a, b) => (topCounts[a] > topCounts[b] ? a : b))
  return parseFloat(mostFrequentTop)
}

/**
 * 获取行元素列表中元素最常用的顶部位置
 * @param rowElementList 行元素列表
 */
const getComputedMostFrequentTop = (rowElementList: HTMLElement[]): number => {
  // 统计 top 值出现的次数，获取出现次数最多的 top 值
  const topCounts = rowElementList.reduce((acc, el) => {
    const computedStyle = window.getComputedStyle(el)
    const top = parseFloat(computedStyle.top)
    acc[top] = (acc[top] || 0) + 1
    return acc
  }, {})

  const mostFrequentTop = Object.keys(topCounts).reduce((a, b) => (topCounts[a] > topCounts[b] ? a : b))
  return parseFloat(mostFrequentTop)
}

/**
 * 判断是否是序号
 * @param text 文本
 */
export const isSequenceNumber = (text: string): boolean => {
  if (!text || text.trim().length === 0) {
    return false
  }
  return /^(?:\(\d+(?:\.\d+)*\)|\[\d+(?:\.\d+)*]|\d+(?:\.\d+)*\.?|\d+(?:\.\d+)*\)|[-.]|[•●])$/.test(text.trim())
}

/**
 * 判断两个矩形是否重叠
 * @param annotationLayerRect 注释层矩形
 * @param contentRect 内容元素矩形
 */
const isRectanglesOverlap = (annotationLayerRect: DOMRect, contentRect: DOMRect): boolean => {
  return !(
    annotationLayerRect.right < contentRect.left
    || annotationLayerRect.left > contentRect.right
    || annotationLayerRect.bottom < contentRect.top
    || annotationLayerRect.top > contentRect.bottom
  )
}

/**
 * 转义HTML
 * @param unsafe HTML
 */
export const htmlEscape = (unsafe: string): string => {
  // 转义HTML
  return unsafe.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#039;')
}

/**
 * 提取元素left样式中的数值
 * @param element HTML元素
 * @returns 提取的数值，如果无法提取则返回0
 */
export const extractLeftValue = (element: HTMLElement): number => {
  const leftStyle = element.style.left

  if (!leftStyle) {
    return 0
  }

  // 处理百分比格式：left: 8%;
  if (leftStyle.includes('%')) {
    const percentMatch = leftStyle.match(/([\d.]+)%/)
    if (percentMatch && percentMatch[1]) {
      return parseFloat(percentMatch[1])
    }
  }

  // 处理calc函数格式：left: calc(var(--total-scale-factor) *46.80px); 或 calc(var(--total-scale-factor) *46.80%);
  if (leftStyle.includes('calc(')) {
    // 提取calc函数中的数值部分（px单位）
    const calcPxMatch = leftStyle.match(/calc\([^)]*\*\s*([\d.]+)px\s*\)/)
    if (calcPxMatch && calcPxMatch[1]) {
      return parseFloat(calcPxMatch[1])
    }

    // 提取calc函数中的数值部分（%单位）
    const calcPercentMatch = leftStyle.match(/calc\([^)]*\*\s*([\d.]+)%\s*\)/)
    if (calcPercentMatch && calcPercentMatch[1]) {
      return parseFloat(calcPercentMatch[1])
    }

    // 处理其他calc格式，提取所有数值
    const numberMatches = leftStyle.match(/([\d.]+)/g)
    if (numberMatches && numberMatches.length > 0) {
      // 返回最后一个数值（通常是像素值或百分比值）
      return parseFloat(numberMatches[numberMatches.length - 1])
    }
  }

  // 处理直接的像素值：left: 46.80px;
  if (leftStyle.includes('px')) {
    const pxMatch = leftStyle.match(/([\d.]+)px/)
    if (pxMatch && pxMatch[1]) {
      return parseFloat(pxMatch[1])
    }
  }

  // 尝试直接解析数值
  const directNumber = parseFloat(leftStyle)
  if (!isNaN(directNumber)) {
    return directNumber
  }

  return 0
}

/**
 * 替换元素left样式中的数值
 * @param element HTML元素
 * @param newValue 新的数值
 * @returns 替换后的left样式字符串
 */
export const replaceLeftValue = (element: HTMLElement, newValue: number | string): string => {
  const leftStyle = element.style.left

  if (!leftStyle) {
    return `${newValue}px`
  }

  // 处理百分比格式：left: 8%; → left: newValue%;
  if (leftStyle.includes('%') && !leftStyle.includes('calc(')) {
    return leftStyle.replace(/([\d.]+)%/, `${newValue}%`)
  }

  // 处理calc函数格式（px单位）：left: calc(var(--total-scale-factor) *46.80px); → left: calc(var(--total-scale-factor) *newValue px);
  if (leftStyle.includes('calc(') && leftStyle.includes('px')) {
    return leftStyle.replace(/(\*\s*)([\d.]+)(px\s*\))/, `$1${newValue}$3`)
  }

  // 处理calc函数格式（%单位）：left: calc(var(--total-scale-factor) *46.80%); → left: calc(var(--total-scale-factor) *newValue %);
  if (leftStyle.includes('calc(') && leftStyle.includes('%')) {
    return leftStyle.replace(/(\*\s*)([\d.]+)(%\s*\))/, `$1${newValue}$3`)
  }

  // 处理直接的像素值：left: 46.80px; → left: newValue px;
  if (leftStyle.includes('px')) {
    return leftStyle.replace(/([\d.]+)px/, `${newValue}px`)
  }

  // 处理直接的百分比值：left: 46.80%; → left: newValue %;
  if (leftStyle.includes('%')) {
    return leftStyle.replace(/([\d.]+)%/, `${newValue}%`)
  }

  // 处理纯数字：left: 100; → left: newValue;
  const directNumberMatch = leftStyle.match(/^[\d.]+$/)
  if (directNumberMatch) {
    return `${newValue}`
  }

  // 如果无法识别格式，返回像素值
  return `${newValue}px`
}

/**
 * 判断文本是否以句子结束符号结尾（支持全语言）
 * @param text 要检查的文本内容
 * @returns 如果以句子结束符号结尾返回 true，否则返回 false
 */
export const hasSentenceEndMarker = (text: string): boolean => {
  if (!text || typeof text !== 'string') {
    return false
  }

  // 全语言句子结束符号集合
  const sentenceEndMarkers = [
    // 英文、德语、法语等拉丁语系
    '.',
    '!',
    '?',
    ';',
    ':',
    // 中文
    '。',
    '！',
    '？',
    '；',
    '：',
    '…',
    // 日文
    '。',
    '！',
    '？',
    '…',
    // 韩文
    '.',
    '!',
    '?',
    '…',
    // 阿拉伯语
    '؟',
    '؛',
    '.',
    // 俄语、乌克兰语等西里尔字母语系
    '.',
    '!',
    '?',
    ';',
    '…',
    // 泰文
    '฿',
    // 希腊语
    '.',
    ';',
    '·',
    // 其他特殊符号
    '‽',
    '⁇',
    '⁈',
    '⁉', // 疑问感叹号组合
    '¿',
    '¡', // 西班牙语倒置标点
    '…',
    '⋯',
    '⋰',
    '⋱', // 各种省略号
    '｡',
    '｡',
    '！',
    '？' // 全角符号
  ]

  const trimmedText = text.trim()

  // 检查基本句子结束符号
  const hasBasicEndMarker = sentenceEndMarkers.some(marker => trimmedText.endsWith(marker))

  // 检查连续标点组合（如 "....", "!!", "??" 等）
  const hasRepeatedPunctuation = /[.!?。！？]{2,}$/.test(trimmedText)

  // 检查括号后的句号等情况
  const hasParenthesesEnd = /[)）】』」〉》】〕〗〉⟩⟧⟫⟭⟯⟰⟱⟲⟳\]}>}\'""][.!?。！？；：…]+$/.test(trimmedText)

  return hasBasicEndMarker || hasRepeatedPunctuation || hasParenthesesEnd
}

/**
 * 校验是否为邮箱
 */
const emailCheck = (contentList: HTMLElement[]) => {
  // 合并内容
  const content = contentList.map(item => item.textContent).join('')
  // 校验是否为邮箱
  return /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(content)
}
