<template>
  <div class="w-full">
    <!-- 文件信息头部（与 MinerU TranslateConfig 统一样式） -->
    <div v-if="fileInfo && fileInfo.fileName" class="mb-6 flex items-center justify-between">
      <div class="flex items-center">
        <img :src="getFileIcon(fileInfo.fileType)" :alt="fileInfo.fileName" class="mr-4 h-12 w-12" />
        <div>
          <!-- 文件名 -->
          <h3 class="font-medium text-gray-900 dark:text-gray-200">
            {{ t('pdf_plus.upload_file_name') }}：{{ fileInfo.fileName }}
          </h3>
          <!-- 共 XX 页 -->
          <p class="text-gray-500">
            {{ t('pdf_plus.total') }}&nbsp;{{ fileInfo.pageCount }}&nbsp;{{ t('pdf_plus.pages') }}
          </p>
        </div>
      </div>
      <!-- 取消选中文件 -->
      <UButton
        color="neutral"
        variant="ghost"
        class="whitespace-nowrap"
        trailing-icon="i-heroicons-x-mark"
        @click="cancelSelectedFile"
      >
        {{ t('common.cancel') }}
      </UButton>
    </div>

    <!-- 翻译模型/目标语言/译文显示 配置区域（与 MinerU TranslateConfig 统一排版） -->
    <div class="rounded-md bg-gray-50 p-6 dark:bg-gray-900">
      <div class="grid grid-cols-1 justify-between gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-2 xl:grid-cols-3">
        <!-- 翻译模型 -->
        <div class="flex-1 basis-1/10">
          <label class="mb-2 block text-gray-500">{{ t('pdf_plus.translation_model') }}</label>
          <DocumentModelSelect
            :translate-engine-config="documentTranslateEngine"
            :variant="'outline'"
            :enabled-engines-list="enabledEnginesList"
            @update:translate-engine-config="onSelectionChangeTranslateEngine"
          />
        </div>

        <!-- 目标语言选择 -->
        <div class="flex-1">
          <label class="mb-2 block text-gray-500">{{ t('pdf_plus.target_language') }}</label>
          <USelectMenu
            v-model="targetLanguageZingCode"
            size="xl"
            value-key="value"
            :items="languageList"
            class="w-full"
            :ui="{ content: 'w-full' }"
            @update:model-value="onSelectionChangeTargetLanguage"
          />
        </div>

        <!-- 译文显示 -->
        <div class="flex-1">
          <label class="mb-2 block text-gray-500">{{ t('pdf_plus.translation_mode') }}</label>
          <USelectMenu
            v-model="translationsDisplay"
            size="xl"
            value-key="value"
            :search-input="false"
            :items="displayOptions"
            class="w-full"
            :ui="{ content: 'w-full' }"
            @update:model-value="onSelectionChangeTranslationsDisplay"
          />
        </div>
      </div>
    </div>

    <!-- 翻译按钮区域（与 MinerU TranslateConfig 一致） -->
    <div class="bg-gray-20 mt-7 flex justify-center">
      <div class="flex flex-col items-center">
        <!-- 立即翻译 -->
        <UButton
          size="xl"
          color="primary"
          class="rounded-md px-10 py-2 whitespace-nowrap"
          @click="startTranslate"
        >
          {{ t('pdf_plus.translate_now') }}
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'
import { useDocumentTranslateStore } from '@/store/documentTranslate'

// i18n
const { t, locale } = useI18n()

// props / emits
interface FileInfo {
  fileType: string // 支持所有文件类型字符串
  pageCount: number
  fileName: string
  fileSize: number
}

defineProps<{ fileInfo?: FileInfo }>()
const emit = defineEmits(['resetFile', 'startTranslate'])

// Store
const documentTranslateStore = useDocumentTranslateStore()
const { enabledEnginesList, translateEngine, translationsDisplay, targetLanguageZingCode, targetLanguage, targetLanguageList } = storeToRefs(documentTranslateStore)

// 语言列表（双向绑定 Store）
const languageList = computed({
  get() {
    return targetLanguageList.value
  },
  set(newValue) {
    targetLanguageList.value = newValue
  }
})

// 更新目标语言：根据选择的 Zing 编码同步实际 lang_code
function updateTargetLanguage() {
  const languageItem = languageList.value.find(item => item.value === targetLanguageZingCode.value)
  if (languageItem) {
    targetLanguage.value = languageItem.lang_code
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
  else {
    targetLanguage.value = 'zh-Hans'
    targetLanguageZingCode.value = 'zh-Hans'
    documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value)
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
}

// 翻译引擎（双向绑定 Store）
const documentTranslateEngine = computed({
  get() {
    return translateEngine.value
  },
  set(newValue) {
    translateEngine.value = newValue
  }
})

// 切换翻译引擎（同步到浏览器插件 + 更新语言）
async function onSelectionChangeTranslateEngine(data) {
  if (!data) return
  await sendMessageToChromeExtension(locale.value, data.value)
  updateTargetLanguage()
}

// 切换目标语言
function onSelectionChangeTargetLanguage() {
  documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value)
  updateTargetLanguage()
}

// 译文显示选项
const displayOptions = ref([
  { value: 'bilingual', label: t('pdf_plus.bilingual_translation') },
  { value: 'fulltext', label: t('pdf_plus.fulltext_translation') }
])

// 切换译文显示
function onSelectionChangeTranslationsDisplay(value) {
  documentTranslateStore.setTranslationsDisplay(value)
}

// 取消选中文件
function cancelSelectedFile() {
  emit('resetFile')
}

// 启动翻译
function startTranslate() {
  emit('startTranslate')
}

// 根据文件类型获取图标
function getFileIcon(fileType: string): string {
  const iconMap: { [key: string]: string } = {
    pdf: '/assets/images/document/bi--file-earmark-pdf.svg',
    ppt: '/assets/images/document/bi--file-earmark-ppt2.svg',
    word: '/assets/images/document/bi--file-earmark-word2.svg',
    image: '/assets/images/document/bi--file-earmark-image.svg',
    epub: '/assets/images/document/bi--file-earmark-epub.svg',
    html: '/assets/images/document/bi--file-earmark-html.svg',
    markdown: '/assets/images/document/bi--file-earmark-markdown2.svg',
    text: '/assets/images/document/bi--file-earmark-text2.svg',
    subtitle: '/assets/images/document/bi--file-earmark-cc.svg'
  }
  const lowerFileType = fileType?.toLowerCase() || ''
  return iconMap[lowerFileType] || '/assets/images/document/bi--file-earmark-text.svg'
}
</script>

<style scoped></style>
