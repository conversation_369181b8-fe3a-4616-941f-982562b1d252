import MarkdownIt from 'markdown-it'

export const useMarkdownParser = () => {
  // 使用 useNuxtApp 获取 i18n 实例
  const { $i18n } = useNuxtApp()

  // 初始化 markdown-it 实例
  const md = new MarkdownIt({
    html: true, // 启用 HTML 标签
    linkify: true, // 自动检测链接
    typographer: true, // 启用一些语言中性的替换 + 引号美化
    breaks: false // 不转换换行符为 <br>，保持原始换行
  })

  // KaTeX 加载状态
  const katexLoaded = false

  // 数学公式预处理 - 处理所有格式的数学公式
  const processMathFormulas = async (markdown: string): Promise<string> => {
    // 使用更简单的方法，先分割代码块，然后分别处理
    const parts = markdown.split(/(```[\s\S]*?```|`[^`]*`|``[^`]*``)/g)
    let processed = ''

    // 动态导入 KaTeX
    let katex: any = null
    try {
      const katexModule = await import('katex')
      katex = katexModule.default
    }
    catch (error) {
      console.warn('KaTeX 导入失败，将使用原始格式:', error)
    }

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i]

      // 如果是代码块（奇数索引），直接保留
      if (i % 2 === 1) {
        processed += part
        continue
      }

      // 如果不是代码块，处理数学公式
      let processedPart = part

      // 处理 $$ $$ 块级公式
      processedPart = processedPart.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
        const trimmedFormula = formula.trim()
        if (katex) {
          try {
            // 直接使用 KaTeX 渲染
            const rendered = katex.renderToString(trimmedFormula, {
              displayMode: true,
              throwOnError: false,
              errorColor: '#cc0000',
              macros: {
                '\\RR': '\\mathbb{R}',
                '\\NN': '\\mathbb{N}',
                '\\ZZ': '\\mathbb{Z}',
                '\\QQ': '\\mathbb{Q}',
                '\\CC': '\\mathbb{C}'
              }
            })
            return `<div class="katex-display" data-math="${trimmedFormula}" data-original-math="${trimmedFormula}">${rendered}</div>`
          }
          catch (error) {
            console.warn('KaTeX 渲染失败:', error)
          }
        }
        return `<div class="katex-display" data-math="${trimmedFormula}">$$${trimmedFormula}$$</div>`
      })

      // 处理 $ $ 行内公式
      processedPart = processedPart.replace(/\$([^\n$]+?)\$/g, (match, formula) => {
        const trimmedFormula = formula.trim()
        if (katex) {
          try {
            // 直接使用 KaTeX 渲染
            const rendered = katex.renderToString(trimmedFormula, {
              displayMode: false,
              throwOnError: false,
              errorColor: '#cc0000',
              macros: {
                '\\RR': '\\mathbb{R}',
                '\\NN': '\\mathbb{N}',
                '\\ZZ': '\\mathbb{Z}',
                '\\QQ': '\\mathbb{Q}',
                '\\CC': '\\mathbb{C}'
              }
            })
            return `<span class="katex" data-math="${trimmedFormula}" data-original-math="${trimmedFormula}">${rendered}</span>`
          }
          catch (error) {
            console.warn('KaTeX 渲染失败:', error)
          }
        }
        return `<span class="katex" data-math="${trimmedFormula}">$${trimmedFormula}$</span>`
      })

      // 处理 \[ \] 块级公式
      processedPart = processedPart.replace(/\\\[([\s\S]*?)\\\]/g, (match, formula) => {
        const trimmedFormula = formula.trim()
        if (katex) {
          try {
            // 直接使用 KaTeX 渲染
            const rendered = katex.renderToString(trimmedFormula, {
              displayMode: true,
              throwOnError: false,
              errorColor: '#cc0000',
              macros: {
                '\\RR': '\\mathbb{R}',
                '\\NN': '\\mathbb{N}',
                '\\ZZ': '\\mathbb{Z}',
                '\\QQ': '\\mathbb{Q}',
                '\\CC': '\\mathbb{C}'
              }
            })
            return `<div class="katex-display" data-math="${trimmedFormula}" data-original-math="${trimmedFormula}">${rendered}</div>`
          }
          catch (error) {
            console.warn('KaTeX 渲染失败:', error)
          }
        }
        return `<div class="katex-display" data-math="${trimmedFormula}">\\[${trimmedFormula}\\]</div>`
      })

      // 处理 \( \) 行内公式
      processedPart = processedPart.replace(/\\\(([^\n]+?)\\\)/g, (match, formula) => {
        const trimmedFormula = formula.trim()
        if (katex) {
          try {
            // 直接使用 KaTeX 渲染
            const rendered = katex.renderToString(trimmedFormula, {
              displayMode: false,
              throwOnError: false,
              errorColor: '#cc0000',
              macros: {
                '\\RR': '\\mathbb{R}',
                '\\NN': '\\mathbb{N}',
                '\\ZZ': '\\mathbb{Z}',
                '\\QQ': '\\mathbb{Q}',
                '\\CC': '\\mathbb{C}'
              }
            })
            return `<span class="katex" data-math="${trimmedFormula}" data-original-math="${trimmedFormula}">${rendered}</span>`
          }
          catch (error) {
            console.warn('KaTeX 渲染失败:', error)
          }
        }
        return `<span class="katex" data-math="${trimmedFormula}">\\(${trimmedFormula}\\)</span>`
      })

      processed += processedPart
    }

    return processed
  }

  // 解析状态
  const isLoading = ref(false)
  const parseError = ref('')
  const parseProgress = ref(0)
  const docContent = ref('')
  const originalRawContent = ref('') // 保存原始文本内容

  /**
   * 解析 Markdown 文件
   */
  const parseMarkdownFile = async (file: File) => {
    isLoading.value = true
    parseError.value = ''
    parseProgress.value = 0
    docContent.value = ''

    try {
      // 检查文件类型
      if (!file.name.toLowerCase().endsWith('.md') && !file.name.toLowerCase().endsWith('.markdown')) {
        throw new Error($i18n.t('document_translation.unsupported_file_type'))
      }

      parseProgress.value = 10
      const raw = await file.text()

      if (!raw || raw.trim().length === 0) {
        throw new Error($i18n.t('document_translation.content_empty'))
      }

      parseProgress.value = 30
      // 保存原始内容用于导出
      originalRawContent.value = raw

      // 将 Markdown 转换为 HTML
      const htmlContent = await convertMarkdownToHtml(raw)
      docContent.value = htmlContent

      parseProgress.value = 90

      if (!docContent.value || docContent.value.trim().length === 0) {
        throw new Error($i18n.t('document_translation.parse_failed'))
      }

      parseProgress.value = 100
    }
    catch (error: any) {
      console.error('Markdown 解析失败:', error)
      parseError.value = error?.message || $i18n.t('document_translation.parse_failed')
    }
    finally {
      isLoading.value = false
    }
  }

  /**
   * 将 Markdown 转换为 HTML
   */
  const convertMarkdownToHtml = async (markdown: string): Promise<string> => {
    try {
      // 预处理：清理内容
      let cleanedMarkdown = markdown
        .replace(/\r\n/g, '\n') // 统一换行符
        .replace(/\r/g, '\n') // 处理旧式换行符
        .trim() // 去除首尾空白

      // 检查是否包含数学公式
      const hasMath = /(\$\$.*?\$\$|\$[^\n$]+?\$|\\\[.*?\\\]|\\\(.*?\\\))/s.test(cleanedMarkdown)

      // 如果包含数学公式，先进行自定义处理以生成 data-math 属性
      if (hasMath) {
        cleanedMarkdown = await processMathFormulas(cleanedMarkdown)
      }

      // 使用 markdown-it 进行解析
      const html = md.render(cleanedMarkdown)

      // 后处理：清理 HTML 输出
      const cleanedHtml = html
        .replace(/\n\s*\n\s*\n/g, '\n\n') // 清理多余的空行
        .replace(/>\s+</g, '><') // 清理标签间的多余空白
        .trim()

      return cleanedHtml
    }
    catch (error) {
      console.error('Markdown 转换失败:', error)
      throw new Error(t('document_translation.conversion_failed'))
    }
  }

  /**
   * 重试解析
   */
  const retryParse = (file: File) => {
    parseMarkdownFile(file)
  }

  /**
   * 清理状态
   */
  const clearState = () => {
    isLoading.value = false
    parseError.value = ''
    parseProgress.value = 0
    docContent.value = ''
    originalRawContent.value = ''
  }

  return {
    // 状态
    isLoading: readonly(isLoading),
    parseError: readonly(parseError),
    parseProgress: readonly(parseProgress),
    docContent: readonly(docContent),
    originalRawContent: readonly(originalRawContent),

    // 方法
    parseMarkdownFile,
    retryParse,
    clearState
  }
}
