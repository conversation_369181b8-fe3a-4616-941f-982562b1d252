export const useMarkdownExport = () => {
  const isExporting = ref(false)

  /**
   * 改进的数学公式提取函数，专门处理复杂的数学公式
   */
  const extractMathFromKatexImproved = (katexHtml: string): string | null => {
    try {
      // 创建临时 DOM 元素来解析 KaTeX HTML
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = katexHtml

      // 1. 优先从 data-original-math 属性获取原始表达式
      const katexElement = tempDiv.querySelector('.katex')
      if (katexElement && katexElement.getAttribute('data-original-math')) {
        return katexElement.getAttribute('data-original-math')
      }

      // 2. 从 data-math 属性获取原始表达式
      if (katexElement && katexElement.getAttribute('data-math')) {
        return katexElement.getAttribute('data-math')
      }

      // 3. 尝试从 aria-label 获取
      const ariaLabel = katexElement?.getAttribute('aria-label')
      if (ariaLabel) {
        return ariaLabel
      }

      // 4. 尝试从 KaTeX 的文本内容中提取
      const textContent = katexElement?.textContent?.trim()
      if (textContent) {
        // 清理文本内容，移除多余的空白字符
        return textContent.replace(/\s+/g, ' ').trim()
      }

      // 5. 尝试从整个 HTML 内容中提取
      const fullText = tempDiv.textContent?.trim()
      if (fullText) {
        return fullText.replace(/\s+/g, ' ').trim()
      }

      return null
    }
    catch (error) {
      console.warn('提取 KaTeX 数学表达式失败:', error)
      return null
    }
  }

  /**
   * 生成文件名
   */
  const generateFileName = (originalName: string, format: string, language?: string): string => {
    const baseName = originalName.replace(/\.[^/.]+$/, '') // 移除扩展名
    const extension = format.toLowerCase()
    const suffix = language ? `-${language}` : ''
    return `${baseName}${suffix}.${extension}`
  }

  /**
   * 导出为 Markdown
   */
  const exportAsMarkdown = (content: string, fileName: string, language?: string): void => {
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = generateFileName(fileName, 'md', language)
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 导出为 HTML
   */
  const exportAsHtml = (content: string, fileName: string, language?: string): void => {
    // 定义预览区域的样式
    const previewStyle = `
      <style>
        @page {
          margin: 1cm;
        }
        body {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          line-height: 1.6;
          color: #24292e;
        }
        /* 隐藏多余数学公式 */
        .katex-html {
          display: none;
        }
        /* 数学公式样式 */
        .katex,
        .katex-display,
        [class*="katex"],
        [data-math],
        [data-original-math] {
          margin: 1.5rem 0;
        }
      </style>
    `

    // 生成完整的 HTML 字符串
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>${fileName.replace('.html', '')}-${language || 'export'}.html</title>
          ${previewStyle}
        </head>
        <body>
          <div id="preview">
            ${content}
          </div>
        </body>
      </html>
    `

    // 创建下载链接
    const blob = new Blob([fullHtml], { type: 'text/html;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = generateFileName(fileName, 'html', language)
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 导出为 PDF
   */
  // 生成完整的 HTML 字符串，将预览样式添加到头部
  const generateFullHtml = (head: string, htmlContent1: string) => {
    return head + htmlContent1
  }


  
// 导出为PDF文件
const handleExportPDF = (content: string, fileName: string, language?: string, appliedStyles?: string) => {
  if (import.meta.client) {
    // 检查内容是否为空
    if (!content || content.trim().length === 0) {
      console.error('PDF 导出失败: 内容为空')
      return
    }

    // 构建完整的样式 - 直接使用项目中的样式
    const fullStyles = `
      <style>
        /* 基础重置样式 */
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          font-size: 14px;
          line-height: 1.6;
          color: #333;
          background: white;
        }

        /* 代码块样式 - 使用更高优先级 */
        pre,
        pre[class*="language-"],
        pre[class*="hljs"],
        .markdown-content pre,
        #preview pre,
        body pre {
          background: #f5f5f5 !important;
          padding: 10px !important;
          border-radius: 4px !important;
          overflow-x: auto !important;
          border: 1px solid #e1e4e8 !important;
          margin: 1rem 0 !important;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
          font-size: 14px !important;
          line-height: 1.5 !important;
          color: #333 !important;
        }

        /* 代码块内的代码样式 */
        pre code,
        pre[class*="language-"] code,
        pre[class*="hljs"] code,
        .markdown-content pre code,
        #preview pre code,
        body pre code {
          background: transparent !important;
          padding: 0 !important;
          border-radius: 0 !important;
          border: none !important;
          font-family: inherit !important;
          font-size: inherit !important;
          color: inherit !important;
        }

        /* 导入KaTeX样式 */
        @import url('https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css');
        
        /* 强制打印背景颜色和图像 */
        html, body {
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
        
        /* 确保代码块在打印时保持样式 */
        @media print {
          pre,
          pre[class*="language-"],
          pre[class*="hljs"],
          .markdown-content pre,
          #preview pre,
          body pre {
            background: #f5f5f5 !important;
            padding: 10px !important;
            border-radius: 4px !important;
            overflow-x: auto !important;
            border: 1px solid #e1e4e8 !important;
            margin: 1rem 0 !important;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            color: #333 !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
        }
        
        /* 页面实际应用的样式 */
        ${appliedStyles || ''}

      </style>
    `
    
    // 生成完整的 HTML 字符串
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>${fileName.replace('.pdf', '')}-${language || 'export'}.pdf</title>
          ${fullStyles}
        </head>
        <body>
          <div id="preview">
            <div class="markdown-content">
              ${content}
            </div>
          </div>
        </body>
      </html>
    `
        
    // 创建隐藏的 iframe
    const frame = document.createElement('iframe')
    frame.style.position = 'absolute'
    frame.style.left = '-9999px'
    frame.style.top = '-9999px'
    frame.style.width = '100%'
    frame.style.height = '100%'
    
    // 将 iframe 添加到 DOM 中
    document.body.appendChild(frame)
    
    // 等待 iframe 加载完成后执行打印
    frame.onload = () => {
      try {
        if (frame.contentWindow && frame.contentDocument) {
          // 设置文档内容
          frame.contentDocument.open()
          frame.contentDocument.write(fullHtml)
          frame.contentDocument.close()
          
          // 延迟执行打印，确保内容完全加载
          setTimeout(() => {
            if (frame.contentWindow) {
              frame.contentWindow.print()
            }
            // 打印完成后移除 iframe
            setTimeout(() => {
              if (frame.parentNode) {
                frame.parentNode.removeChild(frame)
              }
            }, 1000)
          }, 500)
        } else {
          console.error('iframe contentWindow 或 contentDocument 不可用')
        }
      } catch (error) {
        console.error('PDF 导出失败:', error)
        // 清理 iframe
        if (frame.parentNode) {
          frame.parentNode.removeChild(frame)
        }
      }
    }
    
    // 设置 iframe 的 src 来触发 onload 事件
    frame.src = 'about:blank'
  }
}


  /**
   * 将 HTML 内容转换回 Markdown 格式
   */
  const convertHtmlToMarkdown = (htmlContent: string, originalMarkdown?: string): string => {
    try {
      if (!htmlContent || htmlContent.trim().length === 0) {
        return ''
      }

      let markdown = htmlContent

      // 处理数学公式 - 必须在移除class属性之前处理
      // 1. 首先处理带 data-original-math 属性的公式（这是我们专门为矩阵设置的）
      markdown = markdown.replace(/<div[^>]*class="katex-display"[^>]*data-original-math="([^"]*)"[^>]*>.*?<\/div>/gis, (match, mathContent) => {
        return `$$\n${mathContent}\n$$\n\n`
      })

      markdown = markdown.replace(/<span[^>]*class="katex"[^>]*data-original-math="([^"]*)"[^>]*>.*?<\/span>/gi, (match, mathContent) => {
        return `$${mathContent}`
      })

      // 2. 处理带 data-math 属性的数学公式
      markdown = markdown.replace(/<div[^>]*class="katex-display"[^>]*data-math="([^"]*)"[^>]*>.*?<\/div>/gis, (match, mathContent) => {
        return `$$\n${mathContent}\n$$\n\n`
      })

      markdown = markdown.replace(/<span[^>]*class="katex"[^>]*data-math="([^"]*)"[^>]*>.*?<\/span>/gi, (match, mathContent) => {
        return `$${mathContent}`
      })

      // 3. 处理其他 KaTeX 生成的数学公式（使用改进的提取函数）
      markdown = markdown.replace(/<div[^>]*class="katex-display"[^>]*>(.*?)<\/div>/gis, (match, content) => {
        const mathText = extractMathFromKatexImproved(content)
        return mathText ? `$$\n${mathText}\n$$\n\n` : match
      })

      markdown = markdown.replace(/<span[^>]*class="katex"[^>]*>(.*?)<\/span>/gis, (match, content) => {
        const mathText = extractMathFromKatexImproved(content)
        return mathText ? `$${mathText}` : match
      })

      // 现在移除 class 属性（数学公式已经处理完毕）
      markdown = markdown.replace(/\s+class\s*=\s*["'][^"']*["']/gi, '')

      // 处理标题
      markdown = markdown.replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
      markdown = markdown.replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
      markdown = markdown.replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
      markdown = markdown.replace(/<h4[^>]*>(.*?)<\/h4>/gi, '#### $1\n\n')
      markdown = markdown.replace(/<h5[^>]*>(.*?)<\/h5>/gi, '##### $1\n\n')
      markdown = markdown.replace(/<h6[^>]*>(.*?)<\/h6>/gi, '###### $1\n\n')

      // 处理粗体
      markdown = markdown.replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
      markdown = markdown.replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')

      // 处理斜体
      markdown = markdown.replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
      markdown = markdown.replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')

      // 处理删除线
      markdown = markdown.replace(/<del[^>]*>(.*?)<\/del>/gi, '~~$1~~')
      markdown = markdown.replace(/<s[^>]*>(.*?)<\/s>/gi, '~~$1~~')

      // 处理代码块
      markdown = markdown.replace(/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/gis, '```\n$1\n```\n\n')
      markdown = markdown.replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')

      // 处理引用块
      markdown = markdown.replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gis, (match, content) => {
        const lines = content.split('\n').filter(line => line.trim())
        return lines.map(line => `> ${line}`).join('\n') + '\n\n'
      })

      // 处理列表
      markdown = markdown.replace(/<ul[^>]*>(.*?)<\/ul>/gis, (match, content) => {
        const items = content.match(/<li[^>]*>(.*?)<\/li>/gis) || []
        return (
          items
            .map((item) => {
              const text = item.replace(/<li[^>]*>(.*?)<\/li>/is, '$1').trim()
              return `- ${text}`
            })
            .join('\n') + '\n\n'
        )
      })

      markdown = markdown.replace(/<ol[^>]*>(.*?)<\/ol>/gis, (match, content) => {
        const items = content.match(/<li[^>]*>(.*?)<\/li>/gis) || []
        return (
          items
            .map((item, index) => {
              const text = item.replace(/<li[^>]*>(.*?)<\/li>/is, '$1').trim()
              return `${index + 1}. ${text}`
            })
            .join('\n') + '\n\n'
        )
      })

      // 处理链接
      markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')

      // 处理图片
      markdown = markdown.replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)')
      markdown = markdown.replace(/<img[^>]*alt="([^"]*)"[^>]*src="([^"]*)"[^>]*>/gi, '![$1]($2)')

      // 处理段落
      markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/gis, '$1\n\n')

      // 处理换行
      markdown = markdown.replace(/<br\s*\/?>/gi, '\n')

      // 处理水平分割线
      markdown = markdown.replace(/<hr[^>]*>/gi, '---\n\n')

      // 处理表格（简化版本）
      markdown = markdown.replace(/<table[^>]*>(.*?)<\/table>/gis, (match, content) => {
        const rows = content.match(/<tr[^>]*>(.*?)<\/tr>/gis) || []
        const tableRows = rows.map((row) => {
          const cells = row.match(/<t[dh][^>]*>(.*?)<\/t[dh]>/gis) || []
          return (
            '| '
            + cells
              .map((cell) => {
                const text = cell.replace(/<t[dh][^>]*>(.*?)<\/t[dh]>/is, '$1').trim()
                return text
              })
              .join(' | ')
              + ' |'
          )
        })

        if (tableRows.length > 0) {
          const headerRow = tableRows[0]
          const separatorRow = headerRow.replace(/\|[^|]*/g, '| ---')
          return tableRows.join('\n') + '\n' + separatorRow + '\n\n'
        }
        return match
      })

      // 移除剩余的 HTML 标签
      markdown = markdown.replace(/<[^>]*>/g, '')

      // 处理 HTML 实体
      markdown = markdown.replace(/&nbsp;/g, ' ')
      markdown = markdown.replace(/&amp;/g, '&')
      markdown = markdown.replace(/&lt;/g, '<')
      markdown = markdown.replace(/&gt;/g, '>')
      markdown = markdown.replace(/&quot;/g, '"')

      // 清理多余的换行
      markdown = markdown.replace(/\n{3,}/g, '\n\n')
      markdown = markdown.trim()

      return markdown
    }
    catch (error) {
      console.error('HTML 转 Markdown 失败:', error)
      return htmlContent
    }
  }

  /**
   * 提取翻译内容（保持 Markdown 格式）
   */
  const extractTranslatedContent = (element: Element, originalMarkdown?: string): string | null => {
    try {
      const htmlContent = element.innerHTML || ''

      if (!htmlContent || htmlContent.trim().length === 0) {
        return null
      }

      // 将 HTML 转换为 Markdown 格式
      return convertHtmlToMarkdown(htmlContent, originalMarkdown)
    }
    catch (error) {
      return null
    }
  }

  return {
    // 状态
    isExporting,

    // 方法
    generateFileName,
    exportAsMarkdown,
    exportAsHtml,
    handleExportPDF,
    generateFullHtml,
    convertHtmlToMarkdown,
    extractTranslatedContent
  }
}
