<!-- Markdown 文档翻译页面 -->
<template>
  <DocumentLayouts
    document-type="markdown"
    :loading="isLoading"
    :show-switch-bilingual="true"
    :parse-error="parseError"
    @file-selected="handleFileSelected"
    @retry-parse="retryParseHandler"
  >
    <template #right>
      <UDropdownMenu size="xl" :items="exportItem">
        <!-- 导出文件 -->
        <UButton
          :label="t('document_translation.export_file')"
          size="xl"
          color="secondary"
          variant="outline"
          class="rounded-md"
          :disabled="!hasFile"
          trailing-icon="i-heroicons-chevron-down-20-solid"
        />
      </UDropdownMenu>
    </template>
    <div v-if="hasFile" class="flex h-full flex-col">
      <div v-if="docContent" class="p-4">
        <!-- Markdown 内容渲染区域 -->
        <div id="markdown-render-root" class="w-full">
          <div class="flex justify-center" data-translation="true">
            <div class="markdown-content w-full max-w-4xl" v-html="docContent" />
          </div>
        </div>
      </div>
    </div>
  </DocumentLayouts>
</template>

<script setup lang="ts">
// Markdown 文档翻译逻辑
import DocumentLayouts from '../components/DocumentLayouts.vue'
import { useFileStore } from '@/store/FileStore.ts'
import { useMarkdownParser } from './composables/useMarkdownParser'
import { useMarkdownExport } from './composables/useMarkdownExport'
import './style/index.css'

import { useDocumentTranslateStore } from '@/store/documentTranslate'

const documentTranslateStore = useDocumentTranslateStore()
const { translateEngine, translationsDisplay, targetLanguage } = storeToRefs(documentTranslateStore)
const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()

const { t } = useI18n()

// 禁用默认布局
definePageMeta({
  layout: false
})

// 状态管理
const fileStore = useFileStore()

// 使用组合式函数
const { isLoading, parseError, parseProgress, docContent, originalRawContent, parseMarkdownFile, retryParse } = useMarkdownParser()

const { isExporting, exportAsMarkdown, exportAsHtml, handleExportPDF, extractTranslatedContent } = useMarkdownExport()

// 页面状态
const hasFile = computed(() => fileStore.uploadedFile !== null)

// 数学公式检测状态
const hasMathFormulasDetected = ref(false)

// 响应式的文档标题
const documentTitle = ref('')

// 计算完整的页面标题
const pageTitle = computed(() => {
  // Markdown 文件翻译
  const baseTitle = t('document_translation.markdown.title')
  const siteName = t('common.site_name')

  if (documentTitle.value) {
    return `${documentTitle.value} - ${siteName}`
  }
  return `${baseTitle} - ${siteName}`
})

useSeoMeta({
  titleTemplate: '',
  title: pageTitle,
  ogTitle: pageTitle
})

const exportItem = [
  {
    label: t('document_translation.export_markdown'),
    value: 'markdown',
    onSelect: () => {
      handleExport('markdown')
    }
  },
  {
    label: t('document_translation.export_html'),
    value: 'html',
    onSelect: () => {
      handleExport('html')
    }
  },
  {
    label: t('document_translation.export_pdf'),
    value: 'pdf',
    onSelect: () => {
      handleExport('pdf')
    }
  }
]

/**
 * 检测文档是否包含数学公式
 * @param content 文档内容
 * @returns 是否包含数学公式
 */
const hasMathFormulas = (content: string): boolean => {
  // 检测块级数学公式 $$ $$
  const blockMathRegex = /\$\$([\s\S]*?)\$\$/g
  if (blockMathRegex.test(content)) {
    return true
  }

  // 检测行内数学公式 $ $
  const inlineMathRegex = /\$([^\n$]+?)\$/g
  if (inlineMathRegex.test(content)) {
    return true
  }

  // 检测 LaTeX 格式的数学公式
  const latexBlockRegex = /\\\[([\s\S]*?)\\\]/g
  if (latexBlockRegex.test(content)) {
    return true
  }

  const latexInlineRegex = /\\\(([\s\S]*?)\\\)/g
  if (latexInlineRegex.test(content)) {
    return true
  }

  return false
}

/**
 * 为包含数学公式的元素添加跳过翻译标记
 */
const markMathElementsForSkip = () => {
  // 等待 DOM 渲染完成
  nextTick(() => {
    const container = document.querySelector('#markdown-render-root')
    if (!container) return

    // 查找所有数学公式元素
    const mathElements = container.querySelectorAll('.katex, .katex-display, [data-math], [data-original-math]')

    mathElements.forEach((element) => {
      // 为数学公式元素添加跳过翻译标记
      element.setAttribute('data-skip-translation', 'true')
    })
  })
}

/**
 * 选择打开文件后回调
 */
const handleFileSelected = async (file: File) => {
  try {
    // 设置文件到 store
    fileStore.setFile(file)

    await parseMarkdownFile(file)

    // 设置标题
    documentTitle.value = file.name

    await nextTick()

    initTranslationProgressMonitor()

    // 检查是否包含数学公式
    const containsMathFormulas = hasMathFormulas(originalRawContent.value || '')
    hasMathFormulasDetected.value = containsMathFormulas

    // 如果包含数学公式，为数学公式元素添加跳过翻译标记
    if (containsMathFormulas) {
      markMathElementsForSkip()

      // 延迟开启翻译，确保 DOM 处理完成
      setTimeout(() => {
        usePluginWebDocumentTranslate({
          type: 'translateFilePage',
          translationEngine: translateEngine.value.value,
          targetLanguage: targetLanguage.value,
          transDisplayMode: translationsDisplay.value,
          documentType: 'markdown'
        })
      }, 500)
    }
    else {
      // 如果没有数学公式，直接开启翻译
      usePluginWebDocumentTranslate({
        type: 'translateFilePage',
        translationEngine: translateEngine.value.value,
        targetLanguage: targetLanguage.value,
        transDisplayMode: translationsDisplay.value,
        documentType: 'markdown'
      })
    }
  }
  catch (error: any) {
    console.error('Markdown 文件解析失败:', error)
    // 错误处理已经在 useMarkdownParser 内部完成
  }
}

/**
 * 重试解析
 */
const retryParseHandler = async () => {
  if (fileStore.uploadedFile) {
    await retryParse(fileStore.uploadedFile)

    // 重新检查数学公式
    const containsMathFormulas = hasMathFormulas(originalRawContent.value || '')
    hasMathFormulasDetected.value = containsMathFormulas

    // 如果包含数学公式，为数学公式元素添加跳过翻译标记
    if (containsMathFormulas) {
      markMathElementsForSkip()

      // 延迟开启翻译，确保 DOM 处理完成
      setTimeout(() => {
        usePluginWebDocumentTranslate({
          type: 'translateFilePage',
          translationEngine: translateEngine.value.value,
          targetLanguage: targetLanguage.value,
          transDisplayMode: translationsDisplay.value,
          documentType: 'markdown'
        })
      }, 500)
    }
    else {
      // 如果没有数学公式，直接开启翻译
      usePluginWebDocumentTranslate({
        type: 'translateFilePage',
        translationEngine: translateEngine.value.value,
        targetLanguage: targetLanguage.value,
        transDisplayMode: translationsDisplay.value,
        documentType: 'markdown'
      })
    }
  }
}

/**
 * 处理导出
 */
const handleExport = async (format: string) => {
  format = format || 'markdown'
  if (!hasFile.value || !docContent.value) {
    return
  }

  isExporting.value = true

  try {
    // 获取翻译后的内容
    let content = docContent.value

    // 如果是双语模式或全文翻译模式，尝试获取翻译后的内容
    if (translationsDisplay.value === 'bilingual' || translationsDisplay.value === 'fulltext') {
      // 获取包含翻译内容的元素
      const translatedElement = document.querySelector('[data-translation="true"]')
      if (translatedElement) {
        // 根据显示模式提取不同的内容
        if (translationsDisplay.value === 'fulltext') {
          // 全文翻译模式：提取所有翻译后的内容
          const translatedContent = extractTranslatedContent(translatedElement, originalRawContent.value)
          if (translatedContent) {
            content = translatedContent
          }
        }
        else if (translationsDisplay.value === 'bilingual') {
          // 双语模式：提取翻译内容（不包含原文）
          const translatedContent = extractTranslatedContent(translatedElement, originalRawContent.value)
          if (translatedContent) {
            content = translatedContent
          }
        }
      }
    }

    // 检查是否要导出翻译后的内容
    const isTranslatedContent = translationsDisplay.value === 'bilingual' || translationsDisplay.value === 'fulltext'

    if (isTranslatedContent && content !== docContent.value) {
      // 导出翻译后的内容，内容已经在提取时处理过了
    }
    else {
      // 导出原文，直接使用原始内容，确保数学公式格式正确
      content = originalRawContent.value || content

      // 如果是原文导出，确保数学公式格式正确
      if (content === originalRawContent.value) {
        // 对数学公式进行特殊处理，确保格式正确
        content = content.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
          const trimmedFormula = formula.trim()
          // 确保矩阵公式格式正确
          if (trimmedFormula.includes('\\begin{pmatrix}') || trimmedFormula.includes('\\begin{matrix}') || trimmedFormula.includes('\\begin{vmatrix}') || trimmedFormula.includes('\\begin{bmatrix}')) {
            return `$$\n${trimmedFormula}\n$$`
          }
          return match
        })

        // 处理行内数学公式
        content = content.replace(/\$([^\n$]+?)\$/g, (match, formula) => {
          const trimmedFormula = formula.trim()
          // 确保行内矩阵公式格式正确
          if (trimmedFormula.includes('\\begin{pmatrix}') || trimmedFormula.includes('\\begin{matrix}') || trimmedFormula.includes('\\begin{vmatrix}') || trimmedFormula.includes('\\begin{bmatrix}')) {
            return `$${trimmedFormula}$`
          }
          return match
        })
      }
    }

    // 执行导出
    const exportFileName = fileStore.uploadedFile?.name || 'document'

    if (format === 'markdown') {
      exportAsMarkdown(content, exportFileName)
    }
    else if (format === 'html') {
      // 获取解析后的 HTML 内容和样式
      const parsedHtml = document.querySelector('#markdown-render-root')?.innerHTML
      console.log('parsedHtml', parsedHtml)
      exportAsHtml(parsedHtml, exportFileName)
    }
    else if (format === 'pdf') {
      // 获取 markdown 内容区域的实际 HTML，包括所有渲染效果
      const markdownElement = document.querySelector('#markdown-render-root .markdown-content')
      console.log('markdownElement', markdownElement)

      if (!markdownElement) {
        console.error('无法找到 markdown 内容元素')
        return
      }

      // 获取完整的HTML内容，包括所有子元素和样式
      const parsedHtml = markdownElement.innerHTML

      if (!parsedHtml) {
        console.error('无法获取 markdown 内容用于 PDF 导出')
        return
      }

      // 获取页面中实际应用的样式
      const getAppliedStyles = () => {
        const styles = []

        // 获取所有样式表
        Array.from(document.styleSheets).forEach((sheet) => {
          try {
            Array.from(sheet.cssRules || sheet.rules || []).forEach((rule) => {
              if (rule.cssText.includes('.markdown-content')
                || rule.cssText.includes('katex')
                || rule.cssText.includes('hljs')
                || rule.cssText.includes('markdown')
                || rule.cssText.includes('prose')
                || rule.cssText.includes('code')
                || rule.cssText.includes('pre')
                || rule.cssText.includes('img')
                || rule.cssText.includes('table')
                || rule.cssText.includes('blockquote')) {
                styles.push(rule.cssText)
              }
            })
          }
          catch (e) {
            // 跨域样式表会抛出错误，忽略
          }
        })

        return styles.join('\n')
      }

      // 将样式信息传递给PDF导出函数
      handleExportPDF(parsedHtml, exportFileName, targetLanguage.value, getAppliedStyles())
    }
    else {
      throw new Error(`不支持的导出格式: ${format}`)
    }
  }
  catch (error) {
    // 导出失败处理
  }
  finally {
    isExporting.value = false
  }
}

onMounted(async () => {
  if (fileStore.uploadedFile) {
    await handleFileSelected(fileStore.uploadedFile)
  }
})

// 组件卸载时重置状态和清理观察器
onBeforeUnmount(() => {
  hasMathFormulasDetected.value = false
})
</script>

<style scoped>
/* Markdown 文档翻译样式 */

/* 确保内容区域不产生滚动条 */
:deep(.markdown-content) {
  overflow: visible !important;
}

:deep(#markdown-render-root) {
  overflow: visible !important;
}

/* 自定义滚动条样式 */
:deep(.h-full.overflow-y-auto::-webkit-scrollbar) {
  width: 8px;
}

:deep(.h-full.overflow-y-auto::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.h-full.overflow-y-auto::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.h-full.overflow-y-auto::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}
</style>
