/* Markdown 文档翻译样式 */

/* Markdown 内容样式 */
.markdown-content {
  line-height: 1.6;
  color: inherit;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.25;
  page-break-after: avoid;
}

.markdown-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content h3 {
  font-size: 1.25em;
}

.markdown-content h4 {
  font-size: 1em;
}

.markdown-content h5 {
  font-size: 0.875em;
}

.markdown-content h6 {
  font-size: 0.85em;
  color: #6a737d;
}

.markdown-content p {
  margin-bottom: 1em;
  text-align: justify;
  orphans: 3;
  widows: 3;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1em;
  padding-left: 2em;
  page-break-inside: avoid;
}

.markdown-content li {
  margin-bottom: 0.25em;
  line-height: 1.6;
}

.markdown-content li > ul,
.markdown-content li > ol {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.markdown-content blockquote {
  margin: 1em 0;
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  background-color: #f6f8fa;
  page-break-inside: avoid;
}

.markdown-content pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin: 1em 0;
  page-break-inside: avoid;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.markdown-content code {
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-size: 85%;
  margin: 0;
  padding: 0.2em 0.4em;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
  font-size: 100%;
}

.markdown-content a {
  color: #0366d6;
  text-decoration: none;
  word-break: break-word;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
  page-break-inside: avoid;
  font-size: 0.9em;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #dfe2e5;
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
  word-wrap: break-word;
  max-width: 300px;
}

.markdown-content th {
  background-color: #f6f8fa;
  font-weight: 600;
  white-space: nowrap;
}

.markdown-content tr:nth-child(even) {
  background-color: #f8f9fa;
}

.markdown-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
}

/* 翻译相关样式 */
.markdown-content [data-translation="true"] {
  position: relative;
}

.markdown-content .translation-content {
  margin-top: 0.5em;
  padding: 0.5em;
  background-color: #f8f9fa;
  border-left: 3px solid #0366d6;
  border-radius: 3px;
}

/* 暗色主题适配 */
.dark .markdown-content {
  color: #e1e4e8;
}

.dark .markdown-content h1,
.dark .markdown-content h2 {
  border-bottom-color: #30363d;
}

.dark .markdown-content blockquote {
  color: #8b949e;
  border-left-color: #30363d;
  background-color: #21262d;
}

.dark .markdown-content pre {
  background-color: #21262d;
}

.dark .markdown-content code {
  background-color: rgba(110, 118, 129, 0.4);
}

.dark .markdown-content a {
  color: #58a6ff;
}

.dark .markdown-content table th,
.dark .markdown-content table td {
  border-color: #30363d;
}

.dark .markdown-content table th {
  background-color: #21262d;
}

.dark .markdown-content table tr:nth-child(even) {
  background-color: #161b22;
}

.dark .markdown-content .translation-content {
  background-color: #21262d;
  border-left-color: #58a6ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 0.9em;
  }

  .markdown-content table {
    font-size: 0.8em;
  }

  .markdown-content th,
  .markdown-content td {
    padding: 6px 8px;
  }

  .markdown-content pre {
    padding: 12px;
  }

  /* 移动端数学公式编号样式调整 */
  .markdown-content .katex-display {
    padding-right: 5em;
  }

  .markdown-content .katex-display .katex .tag {
    right: -1.5em;
    font-size: 0.8em;
  }

  .markdown-content .katex-display + span,
  .markdown-content .katex-display + em,
  .markdown-content .katex-display + strong {
    right: -1.5em;
    font-size: 0.8em;
  }
}

/* KaTeX 数学公式样式 */
.markdown-content .katex {
  font-size: 1.1em;
  line-height: 1.4; /* 增加行高，防止公式被切掉 */
  text-rendering: auto;
  vertical-align: baseline; /* 确保基线对齐 */
  display: inline-block; /* 确保正确的显示模式 */
  overflow: visible; /* 完全显示，不要滚动条 */
  height: auto; /* 自动调整高度 */
  max-height: none; /* 移除最大高度限制 */
}

.markdown-content .katex-display {
  text-align: center;
  margin: 1em 0;
  overflow: visible; /* 桌面端完全显示，不要滚动条 */
  position: relative;
  padding-right: 6em; /* 增加更多右边距，确保编号和公式有足够间距 */
  padding-top: 0.5em; /* 增加顶部内边距，防止公式被切掉 */
  padding-bottom: 0.5em; /* 增加底部内边距，确保公式完整显示 */
  height: auto; /* 自动调整高度 */
  min-height: auto; /* 移除最小高度限制 */
}

/* 移动端适配：当宽度不够时显示左右滚动条 */
@media (max-width: 768px) {
  .markdown-content .katex-display {
    overflow-x: auto; /* 移动端允许水平滚动 */
    overflow-y: visible; /* 垂直方向仍然完全显示 */
    padding-right: 2em; /* 移动端减少右边距 */
    padding-left: 0.5em; /* 移动端添加左边距 */
  }

  .markdown-content .katex-display .katex {
    min-width: max-content; /* 确保公式不会被压缩 */
  }
}

.markdown-content .katex-display .katex {
  display: inline-block;
  text-align: left;
  max-width: 100%;
  line-height: 1.4; /* 确保块级公式也有足够的行高 */
  vertical-align: baseline; /* 确保基线对齐 */
  overflow: visible; /* 完全显示，不要滚动条 */
  height: auto; /* 自动调整高度 */
}

/* 数学公式编号样式 - 垂直居中对齐 */
.markdown-content .katex-display .katex .tag {
  position: absolute;
  right: -6em; /* 增加更多间距 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 精确垂直居中 */
  color: #666;
  font-size: 0.9em;
  font-weight: normal;
  font-style: normal;
  text-align: left;
  white-space: nowrap;
  vertical-align: baseline;
  border: 0;
  background: transparent;
  margin-right: 0;
}

/* 对于多行公式，编号对齐到第一行 */
.markdown-content .katex-display .katex .base .tag {
  top: 0;
  transform: none;
}

/* 处理数学公式后面的编号文本 */
.markdown-content .katex-display + span,
.markdown-content .katex-display + em,
.markdown-content .katex-display + strong {
  position: absolute;
  right: -6em; /* 增加更多间距 */
  top: 50%; /* 垂直居中 */
  transform: translateY(-50%); /* 精确垂直居中 */
  color: #666;
  font-size: 0.9em;
  font-weight: normal;
  font-style: normal;
  text-align: left;
  white-space: nowrap;
  vertical-align: baseline;
  background: transparent;
  z-index: 1;
  margin-right: 0;
}

/* 移动端适配：数学公式编号样式调整 */
@media (max-width: 768px) {
  .markdown-content .katex-display .katex .tag {
    right: -2em; /* 移动端减少编号间距 */
    font-size: 0.8em; /* 移动端稍微减小编号字体 */
  }

  .markdown-content .katex-display + span,
  .markdown-content .katex-display + em,
  .markdown-content .katex-display + strong {
    right: -2em; /* 移动端减少编号间距 */
    font-size: 0.8em; /* 移动端稍微减小编号字体 */
  }
}

/* 确保分数线正确显示 */
.markdown-content .katex .frac-line {
  border-bottom-width: 0.04em;
}

/* 确保根号正确显示 */
.markdown-content .katex .sqrt .sqrt-sign {
  font-size: 1.1em;
}

/* 确保数学公式完整显示，防止被切掉 */
.markdown-content .katex .mord,
.markdown-content .katex .mbin,
.markdown-content .katex .mrel,
.markdown-content .katex .mop,
.markdown-content .katex .mopen,
.markdown-content .katex .mclose {
  vertical-align: baseline;
  line-height: inherit;
  overflow: visible; /* 完全显示 */
  height: auto; /* 自动调整高度 */
}

/* 确保求和符号和积分符号正确显示 */
.markdown-content .katex .mop .large-op {
  vertical-align: baseline;
}

/* 确保上下标正确显示 */
.markdown-content .katex .sup,
.markdown-content .katex .sub {
  vertical-align: baseline;
}

/* 暗色主题下的 KaTeX 样式 */
.dark .markdown-content .katex {
  color: #e1e4e8;
}

.dark .markdown-content .katex .mord {
  color: #e1e4e8;
}

.dark .markdown-content .katex .mbin,
.dark .markdown-content .katex .mrel {
  color: #e1e4e8;
}

.dark .markdown-content .katex .mopen,
.dark .markdown-content .katex .mclose {
  color: #e1e4e8;
}

.dark .markdown-content .katex .mpunct {
  color: #e1e4e8;
}

.dark .markdown-content .katex .mop {
  color: #e1e4e8;
}

.dark .markdown-content .katex .mtext {
  color: #e1e4e8;
}

.dark .markdown-content .katex .mspace {
  color: #e1e4e8;
}

/* 暗色主题下的数学公式编号样式 */
.dark .markdown-content .katex-display .katex .tag {
  color: #8b949e;
}

/* 暗色主题下的数学公式编号文本 */
.dark .markdown-content .katex-display + span,
.dark .markdown-content .katex-display + em,
.dark .markdown-content .katex-display + strong {
  color: #8b949e;
}

/* 数学公式错误样式 */
.markdown-content .katex-error {
  color: #cc0000;
  background-color: #ffebee;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: monospace;
}

.dark .markdown-content .katex-error {
  color: #ff6b6b;
  background-color: #2d1b1b;
}
