# Markdown 文档翻译功能

## 功能特性

- ✅ 支持 Markdown 文件解析和渲染
- ✅ 支持数学公式渲染（基于 KaTeX）- **已重新启用**
- ✅ 支持双语对照翻译
- ✅ 支持全文翻译
- ✅ 支持多种导出格式（Markdown、HTML、TXT）
- ✅ 支持暗色主题
- ✅ 响应式设计

## 数学公式支持

本功能集成了 KaTeX 数学公式渲染引擎，支持以下数学公式格式：

### 行内数学公式

使用单个美元符号包围：

```markdown
这是一个行内数学公式：$E = mc^2$
```

### 块级数学公式

使用双美元符号包围：

```markdown
$$
x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}
$$
```

### LaTeX 格式

也支持标准的 LaTeX 数学公式格式：

```markdown
行内公式：\( f(x) = x^2 \)
块级公式：\[ \int\_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi} \]
```

## 支持的数学符号和功能

- 基础运算：`+`, `-`, `*`, `/`, `^`, `_`
- 希腊字母：`\alpha`, `\beta`, `\gamma`, `\pi`, `\sigma` 等
- 函数：`\sin`, `\cos`, `\tan`, `\log`, `\ln`, `\exp` 等
- 分数：`\frac{numerator}{denominator}`
- 根式：`\sqrt{x}`, `\sqrt[n]{x}`
- 积分：`\int`, `\iint`, `\iiint`
- 求和：`\sum_{i=1}^{n}`
- 矩阵：`\begin{bmatrix} ... \end{bmatrix}`
- 多行公式：`\begin{align} ... \end{align}`
- 上下标：`x^2`, `x_1`, `x^{2n+1}`, `x_{i,j}`

## 使用示例

### 基础数学公式

```markdown
# 二次方程

$$
x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}
$$

# 积分

$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

# 矩阵乘法

$$
\begin{bmatrix}
a & b \\
c & d
\end{bmatrix}
\begin{bmatrix}
x \\
y
\end{bmatrix} =
\begin{bmatrix}
ax + by \\
cx + dy
\end{bmatrix}
$$
```

### 复杂公式

```markdown
# 傅里叶变换

$$
F(\omega) = \int_{-\infty}^{\infty} f(t) e^{-i\omega t} dt
$$

# 泰勒级数

$$
f(x) = f(a) + f'(a)(x-a) + \frac{f''(a)}{2!}(x-a)^2 + \frac{f'''(a)}{3!}(x-a)^3 + \cdots
$$

# 概率论公式

$$
P(A|B) = \frac{P(B|A)P(A)}{P(B)}
$$
```

## 技术实现

### 依赖包

- `katex`: KaTeX 数学公式渲染引擎
- `@types/katex`: TypeScript 类型定义

### 配置说明

在 `useMarkdownParser.ts` 中直接使用 KaTeX 进行数学公式渲染：

```typescript
// 直接使用 KaTeX 渲染数学公式
const rendered = katex.renderToString(formula, {
  displayMode: true, // 块级公式
  throwOnError: false,
  errorColor: "#cc0000",
});
```

### 样式支持

- 支持明色和暗色主题
- 响应式设计，适配移动端
- 数学公式错误提示样式
- 自动换行和滚动支持

## 注意事项

1. 数学公式解析错误时会显示错误提示，不会影响整个文档的渲染
2. 复杂的数学公式在移动端会自动换行和滚动
3. 暗色主题下数学公式会自动适配颜色
4. 支持导出包含数学公式的 HTML 格式

## 测试

可以使用包含数学公式的 Markdown 文件来测试数学公式渲染效果。

## 最新更新

### SSR 兼容性修复

- ✅ 修复了 `ReferenceError: require is not defined` 错误
- ✅ 使用动态导入 (`import()`) 替代 `require()` 语句
- ✅ 确保在 Nuxt SSR 环境中正常工作
- ✅ 保持了客户端和服务端的兼容性

### 技术改进

- 使用 ES6 模块导入语法
- 动态加载 KaTeX 插件，避免 SSR 问题
- 优化错误处理，确保解析失败不影响基本功能
- 通过 Nuxt 配置全局导入 KaTeX CSS 样式
