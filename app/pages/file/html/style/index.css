/* HTML 文档翻译样式 */

/* 文本内容样式 */
.prose {
  line-height: 1.6;
  color: inherit;
}

.prose p {
  margin-bottom: 1em;
  text-align: justify;
}

.prose p:last-child {
  margin-bottom: 0;
}

input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #e5e7eb;
  height: 6px;
  border-radius: 3px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #3b82f6;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
}

input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 6px;
  border-radius: 3px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: #3b82f6;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.dark input[type="range"]::-webkit-slider-track {
  background: #4b5563;
}

.dark input[type="range"]::-webkit-slider-thumb {
  background: #60a5fa;
}

.dark input[type="range"]::-moz-range-track {
  background: #4b5563;
}

.dark input[type="range"]::-moz-range-thumb {
  background: #60a5fa;
}
