<!-- HTML/text 文档翻译页面 -->
<template>
  <DocumentLayouts
    :document-type="documentType"
    :show-switch-bilingual="true"
    :loading="isLoading"
    :parse-error="parseError"
    @file-selected="handleFileSelected"
    @retry-parse="retryParse"
  >
    <template #right>
      <UButton
        :loading="isExporting"
        :disabled="!docContent"
        size="xl"
        color="secondary"
        variant="outline"
        class="rounded-md"
        @click="handleExport"
      >
        <!-- 导出 HTML/ 导出 TXT -->
        {{ exportButtonText }}
      </UButton>
    </template>
    <div v-if="hasFile && docContent" class="flex w-full flex-col p-4 flex-1">
      <!-- 这个data-translation="true" 是给翻译插件用的，表示这个元素需要被翻译 -->
      <div v-if="isTextFile" class="flex h-full justify-center" data-translation="true">
        <div class="prose w-full max-w-4xl" v-html="docContent" />
      </div>
      <!-- !注意不要添加 sandbox="allow-scripts allow-same-origin" 否则插件无法访问 iframe 内的 dom -->
      <iframe
        v-else
        ref="docContentRef"
        class="w-full h-full border-0"
        :src="docContent"
      />
    </div>
  </DocumentLayouts>
</template>

<script setup lang="ts">
// HTML/text 文档翻译逻辑
import DocumentLayouts from '../components/DocumentLayouts.vue'
import { useFileStore } from '@/store/FileStore.ts'
import { useFileParser } from './composables/useFileParser'
import { useExport } from './composables/useExport'
// import './style/index.css'
import { useDocumentTranslateStore } from '@/store/documentTranslate'

const { t } = useI18n()

// 翻译
const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()
const documentTranslateStore = useDocumentTranslateStore()
const { translateEngine, translationsDisplay, targetLanguage } = storeToRefs(documentTranslateStore)

const { isLoading, parseError, parseProgress, docContent, isTextFile, originalRawContent, parseHtmlOrTextFile, retryParse } = useFileParser()

// 响应式的文档标题
const documentTitle = ref('')

// 计算完整的页面标题
const pageTitle = computed(() => {
  // HTML 文件翻译
  const baseTitle = t('document_translation.html.title')
  const siteName = t('common.site_name')

  if (documentTitle.value) {
    return `${documentTitle.value} - ${siteName}`
  }
  return `${baseTitle} - ${siteName}`
})

useSeoMeta({
  titleTemplate: '%s',
  // 文档翻译
  title: pageTitle,
  ogTitle: pageTitle // 这里不要使用 .value ，否则会导致 SEO 标题变回默认的，动态标题会不生效
  // description: t('document_translation.description'), // 免費的 Word 文件翻譯功能，支援 .doc、.docx 格式文件的智能解析和翻譯。
  // ogDescription: t('document_translation.description') // 免費的 Word 文件翻譯功能，支援 .doc、.docx 格式文件的智能解析和翻譯。
})

// 禁用默认布局
definePageMeta({
  layout: false
})

// 状态管理
const fileStore = useFileStore()

const { isExporting, exportAsHtml, exportAsHtmlWithOriginalStyles, exportAsText, extractTranslatedContent, extractBilingualTranslatedContent } = useExport()

// 页面状态
const hasFile = computed(() => fileStore.uploadedFile !== null)
const docContentRef = ref<HTMLElement>()

// 导出按钮文本
const exportButtonText = computed(() => {
  const fileName = fileStore.uploadedFile?.name || ''
  const isTextFile = fileName.toLowerCase().endsWith('.txt') || fileName.toLowerCase().endsWith('.text')
  // 导出 HTML/ 导出 TXT
  return isTextFile ? t('document_translation.export_txt') : t('document_translation.export_html')
})

// 计算类型， 用于请求翻译使用
const documentType = computed(() => {
  const fileName = fileStore.uploadedFile?.name || ''
  const isTextFile = fileName.toLowerCase().endsWith('.txt') || fileName.toLowerCase().endsWith('.text')
  return isTextFile ? 'txt' : 'html'
})

/**
 * 选择打开文件后回调
 */
const handleFileSelected = async (file: File) => {
  try {
    // 设置文件到 store
    fileStore.setFile(file)

    await parseHtmlOrTextFile(file)

    await nextTick()

    // 提取文档标题
    documentTitle.value = file.name

    // 开启翻译状态监控
    initTranslationProgressMonitor()

    // 如果是HTML文件，等待iframe加载完成后再开启翻译
    if (!isTextFile.value && docContentRef.value) {
      const iframe = docContentRef.value as HTMLIFrameElement
      if (iframe) {
        // 等待iframe加载完成后调整高度并开启翻译
        iframe.onload = () => {
          // iframe加载完成后开启翻译
          usePluginWebDocumentTranslate({
            type: 'translateFilePage',
            translationEngine: translateEngine.value.value,
            targetLanguage: targetLanguage.value,
            transDisplayMode: translationsDisplay.value,
            documentType: documentType.value
          })
        }
      }
    }
    else {
      await nextTick()

      // 如果是文本文件，直接开启翻译
      usePluginWebDocumentTranslate({
        type: 'translateFilePage',
        translationEngine: translateEngine.value.value,
        targetLanguage: targetLanguage.value,
        transDisplayMode: translationsDisplay.value,
        documentType: documentType.value
      })
    }
  }
  catch {
    // 文件解析失败处理
  }
}

/**
 * 处理导出
 */
const handleExport = async () => {
  const fileName = fileStore.uploadedFile?.name || ''
  const isTextFile = fileName.toLowerCase().endsWith('.txt') || fileName.toLowerCase().endsWith('.text')
  const format = isTextFile ? 'txt' : 'html'

  if (!hasFile.value || !docContent.value) {
    return
  }

  isExporting.value = true

  try {
    // 获取翻译后的内容
    let content = docContent.value

    // 如果是双语模式或全文翻译模式，尝试获取翻译后的内容
    if (translationsDisplay.value === 'bilingual' || translationsDisplay.value === 'fulltext') {
      // 获取包含翻译内容的元素
      const translatedElement = document.querySelector('[data-translation="true"]')
      if (translatedElement) {
        // 根据显示模式提取不同的内容
        if (translationsDisplay.value === 'fulltext') {
          // 全文翻译模式：提取所有翻译后的内容
          const translatedContent = extractTranslatedContent(translatedElement)
          if (translatedContent) {
            content = translatedContent
          }
        }
        else if (translationsDisplay.value === 'bilingual') {
          // 双语模式：提取翻译内容（不包含原文）
          const translatedContent = extractBilingualTranslatedContent(translatedElement)
          if (translatedContent) {
            content = translatedContent
          }
        }
      }
    }

    // 如果是HTML文件且需要导出翻译后的内容，从iframe中获取
    if (!isTextFile.value && (translationsDisplay.value === 'bilingual' || translationsDisplay.value === 'fulltext')) {
      if (docContentRef.value) {
        const iframe = docContentRef.value as HTMLIFrameElement
        console.log(iframe, 'iframe')
        if (iframe && iframe.contentDocument) {
          const iframeTranslatedElement = iframe.contentDocument.querySelector('[data-translation="true"]')
          console.log(iframeTranslatedElement, 'iframeTranslatedElement')
          if (iframeTranslatedElement) {
            content = iframeTranslatedElement.innerHTML
            console.log(content, 'content')
          }
        }
      }
    }

    // 如果是 .txt 文件，强制使用 text 格式导出
    if (isTextFile.value) {
      // 检查是否要导出翻译后的内容
      const isTranslatedContent = translationsDisplay.value === 'bilingual' || translationsDisplay.value === 'fulltext'

      if (isTranslatedContent && content !== docContent.value) {
        // 导出翻译后的内容，内容已经在提取时处理过了
      }
      else {
        // 导出原文，直接使用原始内容
        content = originalRawContent.value || content
      }
    }

    // 执行导出
    const exportFileName = fileStore.uploadedFile?.name || 'document'

    if (format === 'html') {
      // 如果是HTML文件，需要获取原始内容和翻译后的内容
      if (!isTextFile.value && docContentRef.value) {
        const iframe = docContentRef.value as HTMLIFrameElement
        if (iframe && iframe.contentDocument) {
          const iframeTranslatedElement = iframe.contentDocument.querySelector('[data-translation="true"]')
          if (iframeTranslatedElement) {
            // 获取原始HTML内容以提取样式
            const originalContent = originalRawContent.value
            exportAsHtmlWithOriginalStyles(iframeTranslatedElement.innerHTML, originalContent, exportFileName, undefined)
          }
          else {
            exportAsHtml(content, exportFileName, undefined, true) // 保持原始样式
          }
        }
        else {
          exportAsHtml(content, exportFileName, undefined, true) // 保持原始样式
        }
      }
      else {
        exportAsHtml(content, exportFileName, undefined, true) // 保持原始样式
      }
    }
    else if (format === 'txt') {
      exportAsText(content, exportFileName)
    }
    else {
      throw new Error(`不支持的导出格式: ${format}`)
    }
  }
  catch {
    // 导出失败处理
  }
  finally {
    isExporting.value = false
  }
}

onMounted(async () => {
  console.log('fileStore', fileStore.uploadedFile)
  if (fileStore.uploadedFile) {
    await handleFileSelected(fileStore.uploadedFile)
  }
})
</script>
