export const useExport = () => {
  const isExporting = ref(false)

  /**
   * 生成文件名
   */
  const generateFileName = (originalName: string, format: string, language?: string): string => {
    const baseName = originalName.replace(/\.[^/.]+$/, '') // 移除扩展名
    const extension = format.toLowerCase()
    const suffix = language ? `-${language}` : ''
    return `${baseName}${suffix}.${extension}`
  }

  /**
   * 创建下载链接并下载文件
   */
  const downloadFile = (content: string, fileName: string, mimeType: string): void => {
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 从原始HTML文件中提取样式
   */
  const extractOriginalStyles = (originalContent: string): string => {
    const headMatch = originalContent.match(/<head[\s\S]*?>([\s\S]*?)<\/head>/i)
    const headInner = headMatch ? headMatch[1] : ''
    const styleTags = headInner.match(/<style[\s\S]*?<\/style>/gi) || []
    const linkTags = headInner.match(/<link[^>]*rel=["']stylesheet["'][^>]*>/gi) || []
    return [...linkTags, ...styleTags].join('\n')
  }

  /**
   * 清理翻译后的内容，移除翻译插件输出标签，只保留翻译后的文本
   */
  const cleanTranslatedContent = (content: string): string => {
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = content

    // 递归清理翻译插件输出
    const cleanElement = (element: Element): void => {
      const children = Array.from(element.children)
      children.forEach(child => {
        if (child.tagName === 'OUTPUT') {
          const textContent = child.textContent || ''
          const textNode = document.createTextNode(textContent)
          element.replaceChild(textNode, child)
        } else {
          cleanElement(child)
        }
      })
    }

    cleanElement(tempDiv)

    // 移除所有data-ztsl-*属性
    const allElements = tempDiv.querySelectorAll('*')
    allElements.forEach(el => {
      const attrs = Array.from(el.attributes)
      attrs.forEach(attr => {
        if (attr.name.startsWith('data-ztsl-')) {
          el.removeAttribute(attr.name)
        }
      })
    })

    // 清理多余的空白字符
    let cleanedHtml = tempDiv.innerHTML
    cleanedHtml = cleanedHtml.replace(/\s+/g, ' ')
    cleanedHtml = cleanedHtml.replace(/>\s+</g, '><')
    return cleanedHtml.trim()
  }

  /**
   * 生成HTML文档
   */
  const generateHtmlDocument = (bodyContent: string, styles: string = ''): string => {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>Exported Document</title>
          ${styles}
        </head>
        <body>
          <div id="preview">
            ${bodyContent}
          </div>
        </body>
      </html>
    `
  }

  /**
   * 导出为 HTML
   */
  const exportAsHtml = (content: string, fileName: string, language?: string, preserveOriginalStyle: boolean = true): void => {
    const cleanedContent = cleanTranslatedContent(content)
    const styles = preserveOriginalStyle ? '' : `
      <style>
        @page { margin: 1cm; }
        h1, h2, h3, h4, h5, h6 { font-size: inherit; }
        #preview {
          font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif;
          padding: 2em;
          margin: auto;
          max-width: 800px;
        }
      </style>
    `
    const fullHtml = generateHtmlDocument(cleanedContent, styles)
    downloadFile(fullHtml, generateFileName(fileName, 'html', language), 'text/html;charset=utf-8')
  }

  /**
   * 导出为HTML，保持原始样式
   */
  const exportAsHtmlWithOriginalStyles = (content: string, originalContent: string, fileName: string, language?: string): void => {
    const cleanedContent = cleanTranslatedContent(content)
    const originalStyles = extractOriginalStyles(originalContent)
    const fullHtml = generateHtmlDocument(cleanedContent, originalStyles)
    downloadFile(fullHtml, generateFileName(fileName, 'html', language), 'text/html;charset=utf-8')
  }

  /**
   * 导出为文本文件
   */
  const exportAsText = (content: string, fileName: string, language?: string): void => {
    downloadFile(content, generateFileName(fileName, 'txt', language), 'text/plain;charset=utf-8')
  }

  /**
   * 提取翻译内容（通用函数）
   */
  const extractTranslatedContent = (element: Element): string | null => {
    try {
      const htmlContent = element.innerHTML || ''
      if (!htmlContent || htmlContent.trim().length === 0) {
        return null
      }

      let content = htmlContent
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<\/p>/gi, '\n')
        .replace(/<[^>]*>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/\n{3,}/g, '\n\n')
        .trim()

      return content
    }
    catch (error) {
      return null
    }
  }

  /**
   * 提取双语模式下的翻译内容（仅翻译部分）
   */
  const extractBilingualTranslatedContent = (element: Element): string | null => {
    // 双语模式暂时使用相同的逻辑，可以根据需要调整
    return extractTranslatedContent(element)
  }

  return {
    // 状态
    isExporting,

    // 方法
    generateFileName,
    exportAsHtml,
    exportAsHtmlWithOriginalStyles,
    exportAsText,
    extractTranslatedContent,
    extractBilingualTranslatedContent,
    cleanTranslatedContent,
    extractOriginalStyles
  }
}
