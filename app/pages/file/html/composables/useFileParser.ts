export const useFileParser = () => {
  // 使用 useNuxtApp 获取 i18n 实例
  const { $i18n } = useNuxtApp()

  // 解析状态
  const isLoading = ref(false)
  const parseError = ref('')
  const parseProgress = ref(0)
  const docContent = ref('')
  const isTextFile = ref(false)
  const originalRawContent = ref('') // 保存原始文本内容

  /**
   * 解析 HTML 或文本文件
   */
  const parseHtmlOrTextFile = async (file: File) => {
    isLoading.value = true
    parseError.value = ''
    parseProgress.value = 0
    docContent.value = ''

    try {
      parseProgress.value = 10
      const raw = await file.text()
      parseProgress.value = 40

      // 保存原始内容用于导出
      originalRawContent.value = raw

      const fileName = file.name.toLowerCase()

      if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
        // HTML文件使用iframe
        isTextFile.value = false
        docContent.value = parseHtmlFile(raw)
      }
      else {
        // 纯文本：转换为段落，直接返回HTML内容
        isTextFile.value = true
        docContent.value = parseTextFile(raw)
      }

      parseProgress.value = 90

      if (!docContent.value) {
        throw new Error($i18n.t('document_translation.content_empty'))
      }

      parseProgress.value = 100
    }
    catch (error: any) {
      parseError.value = error?.message || $i18n.t('document_translation.parse_failed')
    }
    finally {
      isLoading.value = false
    }
  }

  /**
   * 解析 HTML 文件
   */
  const parseHtmlFile = (raw: string): string => {
    // 提取 <head> 中的样式与样式表，以及 <body> 内容，尽量还原原始样式（不执行脚本）
    const headMatch = raw.match(/<head[\s\S]*?>([\s\S]*?)<\/head>/i)
    const headInner = headMatch ? headMatch[1] : ''
    const styleTags = headInner.match(/<style[\s\S]*?<\/style>/gi) || []
    const linkTags = headInner.match(/<link[^>]*rel=["']stylesheet["'][^>]*>/gi) || []
    const stylesAndLinks = [...linkTags, ...styleTags].join('\n')

    const bodyMatch = raw.match(/<body[\s\S]*?>([\s\S]*?)<\/body>/i)
    const bodyInner = bodyMatch ? bodyMatch[1] : raw

    // 创建完整的HTML文档用于iframe
    const fullHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { 
            margin: 0; 
            padding: 20px; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            min-height: 100vh;
          }
          * { box-sizing: border-box; }
          img { max-width: 100%; height: auto; }
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
          code { background: #f5f5f5; padding: 2px 4px; border-radius: 2px; }
          blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 15px; }
          [data-translation="true"] { min-height: 100%; }
        </style>
        ${stylesAndLinks}
      </head>
      <body>
        <div data-translation="true">
          ${bodyInner}
        </div>
      </body>
      </html>
    `
    // 添加这个data-translation="true"是给插件做翻译使用的，
    // !注意不要放在body 属性上否则翻译不生效
    // 插件会根据这个属性来判断是否需要翻译

    // 创建 Blob URL
    const blob = new Blob([fullHtml], { type: 'text/html' })
    return URL.createObjectURL(blob)
  }

  /**
   * 解析文本文件
   */
  const parseTextFile = (raw: string): string => {
    // 处理文本内容，转义HTML特殊字符
    const escaped = raw.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')

    // 如果文本很短或者没有双换行，按单换行分割
    let paragraphArray
    if (escaped.length < 1000) {
      // 短文本或没有双换行的文本，按单换行分割
      paragraphArray = escaped
        .split(/\n/)
        .filter(p => p.trim()) // 过滤空行
        .map(p => `<p>${p}</p>`)
    }
    else {
      // 长文本，按双换行分割段落
      paragraphArray = escaped
        .split(/\n{2,}/)
        .filter(p => p.trim()) // 过滤空段落
        .map(p => `<p>${p.replace(/\n/g, '<br/>')}</p>`)
    }

    const paragraphs = paragraphArray.join('')
    return paragraphs || '<p>空文件</p>'
  }

  /**
   * 重试解析
   */
  const retryParse = (file: File) => {
    parseHtmlOrTextFile(file)
  }

  return {
    // 状态
    isLoading: readonly(isLoading),
    parseError: readonly(parseError),
    parseProgress: readonly(parseProgress),
    docContent: readonly(docContent),
    isTextFile: readonly(isTextFile),
    originalRawContent: readonly(originalRawContent),

    // 方法
    parseHtmlOrTextFile,
    retryParse
  }
}
