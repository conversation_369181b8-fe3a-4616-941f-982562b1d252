<template>
  <div class="pt-6">
    <TranslateType :type="'document'" />
    <div class="mx-auto mb-2 min-h-[480px] max-w-7xl p-6 pt-0">
      <main class="rounded-md bg-gray-100 dark:bg-gray-800">
        <div class="flex max-h-96 min-h-92 flex-col items-center justify-center rounded-lg border-1 border-solid border-gray-200 p-5 sm:p-5 md:p-8 lg:p-12 dark:border-gray-700">
          <ExtensionInstalled :is-show-product="false" loading-type="button">
            <!-- 第一步：文件选择 -->
            <div v-if="step === 1" class="text-center">
              <div class="mx-auto mb-4 min-h-32 justify-items-center">
                <!-- 动态渲染支持的文件类型 -->
                <div class="flex flex-wrap justify-center gap-4 sm:gap-6 md:gap-8 lg:gap-9.5 xl:gap-9.5">
                  <div v-for="fileType in supportedFileTypes" :key="fileType.type" class="flex flex-col items-center">
                    <img :src="fileType.icon" :alt="fileType.label" class="size-10 sm:size-12 md:size-16 lg:size-20" />
                    <span class="mt-3 text-xs text-gray-600 sm:text-xs md:text-sm lg:text-sm text-center">{{ fileType.label }}</span>
                  </div>
                </div>
              </div>
              <div class="mb-4 mt-6 md:mt-0 space-y-3">
                <UButton
                  size="xl"
                  :label="t('document_translation.open_file')"
                  class="rounded-lg px-10 sm:px-8 md:px-12 py-2 whitespace-nowrap text-sm sm:text-base md:text-xl"
                  @click="triggerFileInput"
                />
              </div>
              <p class="text-gray-500 text-sm sm:text-sm md:text-base px-2">
                {{ t('document_translation.open_file_tips') }}
              </p>
            </div>

            <!-- 第二步：翻译配置 -->
            <div v-else class="w-full">
              <TranslateSelect :file-info="fileInfo" @reset-file="resetFile" @start-translate="startTranslate" />
            </div>
          </ExtensionInstalled>
        </div>
      </main>
    </div>

    <!-- 文档翻译介绍 -->
    <!-- 核心功能展示 -->
    <UPageSection
      v-for="(section, index) in documents"
      :key="index"
      :title="section.title"
      :description="section.description"
      :orientation="section.orientation"
      :reverse="section.reverse"
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-xl sm:text-xl md:text-xl lg:text-2xl xl:text-3xl',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg',
        features: 'text-sm sm:text-sm md:text-base lg:text-base xl:text-base'
      }"
    >
      <template #title>
        <span class="">{{ section.title }}</span>
      </template>
      <UCarousel
        v-slot="{ item }"
        :items="hover_translate_images"
        :ui="{ item: 'basis-full' }"
        class="images_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
    </UPageSection>
  </div>

  <input
    ref="fileInput"
    type="file"
    class="hidden"
    :accept="acceptedFileTypes"
    @change="onFileChange"
  />
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useFileStore } from '@/store/FileStore.ts'
import { parseFile } from '@/utils/file'
import ExtensionInstalled from '@/pages/components/ExtensionInstalled/index.vue'

// 翻译类型
import TranslateType from '@/pages/components/TranslateType.vue'
import TranslateSelect from '@/pages/file/TranslateSelect.vue'

const { t } = useI18n()

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('document_translation.title') + ' - ' + t('common.site_name'), // 文档翻译
  ogTitle: t('document_translation.title') + ' - ' + t('common.site_name'),
  description: t('document_translation.description'), // 免费的文档翻译功能，支持 PDF文件、Markdown文件、HTML文件、text文件等多种格式文档的翻译。
  ogDescription: t('document_translation.description')
})

// 文档翻译
const documents = [
  {
    title: t('document_translation.pdf_bilingual_translation'), // PDF 双语对照翻译
    description: t('document_translation.pdf_bilingual_translation_desc'), // 完全免费的 PDF 翻译功能，智能双语对照，日常外语文档快速翻译，满足大部分 PDF 翻译阅读需求。
    orientation: 'horizontal', // 布局方向 horizontal | vertical
    reverse: false // 是否反转布局
  }
]

/**
 * 免费PDF翻译.
 */
const hover_translate_images = ['https://assets.selecttranslate.com/web/images/home/<USER>']

/**
 * 打开上传文件窗口
 */
const fileInput = ref()
const triggerFileInput = () => {
  fileInput.value.click()
}

// 步骤：1 选择文件；2 翻译配置
const step = ref(1)

// 支持的文件类型配置
interface FileTypeConfig {
  type: string
  label: string
  icon: string
  extensions: string[]
  route: string
}

const supportedFileTypes: FileTypeConfig[] = [
  {
    type: 'pdf',
    label: t('document_translation.pdf.label'), // pdf 文件
    icon: '/assets/images/document/bi--file-earmark-pdf.svg',
    extensions: ['.pdf'],
    route: '/file/pdf'
  },
  {
    type: 'epub',
    label: t('document_translation.epub.label'), // epub 文件
    icon: '/assets/images/document/bi--file-earmark-epub.svg',
    extensions: ['.epub'],
    route: '/file/epub'
  },
  {
    type: 'word',
    label: t('document_translation.docx.label'), // docx 文件
    icon: '/assets/images/document/bi--file-earmark-word2.svg',
    extensions: ['.doc', '.docx'],
    route: '/file/docx'
  },
  {
    type: 'text',
    label: t('document_translation.txt.label'), // TXT 文件
    icon: '/assets/images/document/bi--file-earmark-text2.svg',
    extensions: ['.txt'],
    // 这里跳转到html页面，因为text文件是html文件的一种，放在html页面中处理
    route: '/file/html'
  },
  {
    type: 'html',
    label: t('document_translation.html.label'), // html 文件
    icon: '/assets/images/document/bi--file-earmark-html.svg',
    extensions: ['.html', '.htm'],
    route: '/file/html'
  },
  {
    type: 'markdown',
    label: t('document_translation.markdown.label'), // markdown 文件
    icon: '/assets/images/document/bi--file-earmark-markdown2.svg',
    extensions: ['.md'],
    route: '/file/markdown'
  },
  {
    type: 'subtitle',
    label: t('document_translation.subtitle.label'), // 字幕文件
    icon: '/assets/images/document/bi--file-earmark-cc.svg',
    extensions: ['.srt', '.vtt', '.ass', '.ssa', '.sbv', '.lrc'],
    route: '/file/subtitle'
  }
]

// 计算接受的文件类型
const acceptedFileTypes = computed(() => supportedFileTypes.flatMap(type => type.extensions).join(','))

const router = useRouter()
const fileStore = useFileStore()
// 文件信息（供 TranslateSelect 展示头部信息使用）
const fileInfo = ref({
  fileType: '',
  pageCount: 0,
  fileName: '',
  fileSize: 0
})

/**
 * 根据文件名获取文件类型配置
 * @param fileName 文件名
 * @returns 文件类型配置或null
 */
const getFileTypeConfig = (fileName: string): FileTypeConfig | null => {
  const lowerFileName = fileName.toLowerCase()
  return supportedFileTypes.find(type => type.extensions.some(ext => lowerFileName.endsWith(ext))) || null
}

/**
 * 处理文件选择变化事件
 * @param event 文件选择事件
 */
const onFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files?.length) {
    return
  }

  const selectedFile = files[0]

  // 验证文件类型
  const config = getFileTypeConfig(selectedFile.name)
  if (!config) {
    // TODO: 添加错误提示，文件类型不支持
    console.error('不支持的文件类型:', selectedFile.name)
    return
  }

  console.log('选择的文件:', selectedFile)

  // 存储文件到状态管理
  fileStore.setFile(selectedFile)

  try {
    // 解析文件以获取正确的页数和类型信息
    const parsedFileInfo = await parseFile(selectedFile)

    // 设置文件信息，使用解析后的真实数据
    fileInfo.value = {
      fileType: parsedFileInfo.fileType,
      pageCount: parsedFileInfo.pageCount,
      fileName: parsedFileInfo.fileName,
      fileSize: parsedFileInfo.fileSize
    }
  }
  catch (error) {
    console.error('解析文件失败:', error)

    // 如果解析失败，仍然设置基本信息，页数设为 0
    const nameLower = selectedFile.name.toLowerCase()
    let type = ''
    if (nameLower.endsWith('.pdf')) type = 'pdf'
    else if (nameLower.endsWith('.doc') || nameLower.endsWith('.docx')) type = 'word'
    else if (nameLower.endsWith('.ppt') || nameLower.endsWith('.pptx')) type = 'ppt'
    else if (nameLower.endsWith('.png') || nameLower.endsWith('.jpg') || nameLower.endsWith('.jpeg')) type = 'image'
    else if (nameLower.endsWith('.epub')) type = 'epub'
    else if (nameLower.endsWith('.html') || nameLower.endsWith('.htm')) type = 'html'
    else if (nameLower.endsWith('.md')) type = 'markdown'
    else if (nameLower.endsWith('.txt')) type = 'text'
    else if (nameLower.endsWith('.srt') || nameLower.endsWith('.vtt') || nameLower.endsWith('.ass') || nameLower.endsWith('.ssa') || nameLower.endsWith('.sbv') || nameLower.endsWith('.lrc')) type = 'subtitle'

    fileInfo.value = {
      fileType: type,
      pageCount: 0,
      fileName: selectedFile.name,
      fileSize: selectedFile.size
    }
  }

  // 确保文件信息设置完成后再跳转到第二步
  await nextTick()

  // 与 miner-u 一致：进入第二步展示翻译配置（不立即跳转）
  step.value = 2
}

// 取消选中文件，返回第一步
const resetFile = () => {
  step.value = 1
  fileStore.setFile(undefined)
  fileInfo.value = { fileType: '', pageCount: 0, fileName: '', fileSize: 0 }
}

// 启动翻译，跳转到对应的文档处理页面
const startTranslate = async () => {
  const config = getFileTypeConfig(fileInfo.value.fileName)
  if (config) {
    await router.push({ path: config.route })
  }
}
</script>

<style scoped>
.images_container {
  box-shadow: #0003 0 0 8px 0;
}
</style>
