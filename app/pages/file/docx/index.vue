<!-- DOCX 文档翻译页面 -->
<template>
  <DocumentLayouts
    document-type="docx"
    :loading="isLoading"
    :parse-error="parseError"
    :show-switch-bilingual="true"
    @file-selected="handleFileSelected"
    @retry-parse="parseDocxFileHandler"
  >
    <template #right>
      <UDropdownMenu size="xl" :items="exportItem">
        <!-- 导出文件 -->
        <UButton
          :label="t('document_translation.export_file')"
          size="xl"
          color="secondary"
          variant="outline"
          class="rounded-md"
          :disabled="!hasFile"
          trailing-icon="i-heroicons-chevron-down-20-solid"
        />
      </UDropdownMenu>
    </template>
    <!-- DOCX 文档内容显示区域 -->
    <div class="flex-1 overflow-hidden">
      <div v-if="docContent" class="h-full dark:bg-gray-100">
        <!-- 分栏模式 -->
        <div class="overflow-auto flex justify-center h-full">
          <!-- 原文区域 -->
          <!-- <div
            v-if="showOriginal"
            class="border-gray-200 p-4 dark:border-gray-700"
            :style="{ width: showTranslation ? `${splitRatio}%` : '100%' }"
            @scroll="handleOriginalScroll"
          >
            <div class="docx-content">
              <div ref="originalContentRef" class="mx-auto max-w-2xl" v-html="docContent" />
            </div>
          </div> -->

          <!-- 翻译区域 -->
          <!-- 这个data-translation="true" 是给翻译插件用的，表示这个元素需要被翻译 -->
          <div
            v-if="showTranslation"
            class="p-4"
          >
            <div class="docx-content">
              <div
                ref="translatedContentRef"
                class="mx-auto max-w-2xl"
                data-translation="true"
                v-html="docContent"
              />
            </div>
          </div>

          <!-- 当两个区域都隐藏时显示提示 -->
          <div v-if="!showOriginal && !showTranslation" class="flex flex-1 items-center justify-center ">
            <div class="text-center text-gray-500 dark:text-gray-400">
              <UIcon name="i-heroicons-eye-slash" class="mx-auto mb-4 h-12 w-12" />
              <!-- 请选择显示模式 -->
              <p>{{ t('document_translation.select_view_mode') }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <iframe ref="printFrame" class="hidden" />
  </DocumentLayouts>
</template>

<script setup lang="ts">
import DocumentLayouts from '../components/DocumentLayouts.vue'
import { useFileStore } from '@/store/FileStore.ts'
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { storeToRefs } from 'pinia'
import { usePluginTranslate } from '@/composables/usePluginTranslate.ts'
import { parseDocxFile, validateDocxFile } from '@/pages/file/docx/composables/docx-parser'
import { useDocxExport } from './composables/useDocxExport'

// DOCX 文档翻译逻辑
const { t, locale } = useI18n()

const { isExporting, generateFileName, exportAsHtml, exportAsPdf, printFrame } = useDocxExport()

// 禁用默认布局
definePageMeta({
  layout: false
})

// 响应式的文档标题
const documentTitle = ref('')

// 计算完整的页面标题
const pageTitle = computed(() => {
  const baseTitle = t('document_translation.docx_title')
  const siteName = t('common.site_name')

  if (documentTitle.value) {
    return `${documentTitle.value} - ${siteName}`
  }
  return `${baseTitle} - ${siteName}`
})

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '%s',
  // 这里不要使用 .value ，否则会导致 SEO 标题变回默认的，动态标题会不生效
  title: pageTitle, // Word 文件翻譯
  ogTitle: pageTitle, // Word 文件翻譯
  description: t('document_translation.docx_description'), // 免費的 Word 文件翻譯功能，支援 .doc、.docx 格式文件的智能解析和翻譯。
  ogDescription: t('document_translation.docx_description') // 免費的 Word 文件翻譯功能，支援 .doc、.docx 格式文件的智能解析和翻譯。
})

// 状态管理
const fileStore = useFileStore()
const documentTranslateStore = useDocumentTranslateStore()
const { translateEngine, targetLanguage, translationsDisplay } = storeToRefs(documentTranslateStore)

// 文档状态
const hasFile = computed(() => fileStore.uploadedFile !== null)
const isLoading = ref(false)
const parseError = ref('')
const parseProgress = ref(0)
const docContent = ref('')
const originalContentRef = ref<HTMLElement>()
const translatedContentRef = ref<HTMLElement>()

// 布局状态
const showOriginal = ref(true)
const showTranslation = ref(true)
const syncScroll = ref(true)

// 翻译相关
const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()

const exportItem = computed(() => [{
  // 导出 HTML
  label: t('document_translation.export_html'),
  value: 'html',
  onSelect: () => {
    handleExport('html')
  }
},
{
  // 导出 PDF
  label: t('document_translation.export_pdf'),
  value: 'pdf',
  onSelect: () => {
    handleExport('pdf')
  }
}])

/**
 * 选择打开文件后回调
 * @param file 选择的文件
 */
const handleFileSelected = async (file: File) => {
  try {
    // 设置文件到 store
    fileStore.setFile(file)
    await parseDocxFileHandler(file)
  }
  catch (error) {
    console.error('DOCX文件解析失败:', error)
    parseError.value = error.message || t('document_translation.parse_failed')
  }
}

/**
 * 解析DOCX文件处理函数
 * @param file DOCX文件
 */
const parseDocxFileHandler = async (file: File) => {
  isLoading.value = true
  parseError.value = ''
  parseProgress.value = 0
  docContent.value = ''

  try {
    // 验证文件
    const validation = validateDocxFile(file)
    if (!validation.valid) {
      throw new Error(validation.error)
    }

    // 提取文档标题
    documentTitle.value = file.name

    // 解析DOCX文件
    const result = await parseDocxFile(file, {})

    parseProgress.value = 80

    if (result.value) {
      docContent.value = result.value

      // 初始化翻译监控
      await nextTick()

      initTranslationProgressMonitor()

      // 开始翻译
      setTimeout(() => {
        // docx 只能选择全文翻译
        usePluginWebDocumentTranslate({
          type: 'translateFilePage',
          translationEngine: translateEngine.value.value,
          targetLanguage: targetLanguage.value,
          transDisplayMode: translationsDisplay.value, //  全文翻译
          documentType: 'docx'
        })
      }, 1000)
    }
    else {
      throw new Error(t('document_translation.content_empty'))
    }

    parseProgress.value = 100
  }
  catch (error) {
    console.error('DOCX解析失败:', error)
    parseError.value = error.message || t('document_translation.parse_failed')
  }
  finally {
    isLoading.value = false
  }
}

/**
 * 同步滚动处理
 */
// const handleOriginalScroll = (event: Event) => {
//   if (!syncScroll.value || !showTranslation.value) return

//   const target = event.target as HTMLElement
//   const scrollTop = target.scrollTop
//   const scrollHeight = target.scrollHeight
//   const clientHeight = target.clientHeight
//   const scrollRatio = scrollTop / (scrollHeight - clientHeight)

//   if (translatedContentRef.value?.parentElement) {
//     const translatedContainer = translatedContentRef.value.parentElement.parentElement as HTMLElement
//     const translatedScrollHeight = translatedContainer.scrollHeight
//     const translatedClientHeight = translatedContainer.clientHeight
//     const targetScrollTop = scrollRatio * (translatedScrollHeight - translatedClientHeight)
//     translatedContainer.scrollTop = targetScrollTop
//   }
// }

// const handleTranslationScroll = (event: Event) => {
//   if (!syncScroll.value || !showOriginal.value) return

//   const target = event.target as HTMLElement
//   const scrollTop = target.scrollTop
//   const scrollHeight = target.scrollHeight
//   const clientHeight = target.clientHeight
//   const scrollRatio = scrollTop / (scrollHeight - clientHeight)

//   if (originalContentRef.value?.parentElement) {
//     const originalContainer = originalContentRef.value.parentElement.parentElement as HTMLElement
//     const originalScrollHeight = originalContainer.scrollHeight
//     const originalClientHeight = originalContainer.clientHeight
//     const targetScrollTop = scrollRatio * (originalScrollHeight - originalClientHeight)
//     originalContainer.scrollTop = targetScrollTop
//   }
// }

// 组件挂载时初始化
onMounted(async () => {
  // 如果存在待上传文件，则直接解析
  if (fileStore.uploadedFile) {
    await handleFileSelected(fileStore.uploadedFile)
  }
})

/**
 * 处理导出
 */
const handleExport = async (format: string) => {
  console.log('handleExport', format, hasFile.value, docContent.value)

  if (!hasFile.value || !docContent.value) {
    return
  }
  try {
    // 获取包含翻译内容的元素
    const translatedElement = document.querySelector('[data-translation="true"]')
    const content = translatedElement?.innerHTML || ''

    // 执行导出
    const exportFileName = fileStore.uploadedFile?.name || 'document'

    const head = document.head

    console.log('head', head.outerHTML)
    if (format === 'pdf') {
      exportAsPdf(head.outerHTML, content, exportFileName, targetLanguage.value)
    }
    else if (format === 'html') {
      exportAsHtml(head.outerHTML, content, exportFileName, targetLanguage.value)
    }
    else {
      throw new Error(`不支持的导出格式: ${format}`)
    }
  }
  catch (error) {
    // 导出失败处理
  }
  finally {
    isExporting.value = false
  }
}
</script>

<style scoped>
/* 样式已通过 docx-parser.ts 中的 addCustomStyles 函数添加 */

/* 自定义滑块样式 */
input[type='range'] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type='range']::-webkit-slider-track {
  background: #e5e7eb;
  height: 6px;
  border-radius: 3px;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #3b82f6;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
}

input[type='range']::-moz-range-track {
  background: #e5e7eb;
  height: 6px;
  border-radius: 3px;
  border: none;
}

input[type='range']::-moz-range-thumb {
  background: #3b82f6;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

/* 深色模式 */
.dark input[type='range']::-webkit-slider-track {
  background: #4b5563;
}

.dark input[type='range']::-webkit-slider-thumb {
  background: #60a5fa;
}

.dark input[type='range']::-moz-range-track {
  background: #4b5563;
}

.dark input[type='range']::-moz-range-thumb {
  background: #60a5fa;
}
</style>
