export const useDocxExport = () => {
  const isExporting = ref(false)
  const printFrame = ref<HTMLIFrameElement | null>(null)

  /**
   * 生成文件名
   */
  const generateFileName = (originalName: string, format: string, language?: string): string => {
    const baseName = originalName.replace(/\.[^/.]+$/, '') // 移除扩展名
    const extension = format.toLowerCase()
    const suffix = language ? `-${language}` : ''
    return `${baseName}${suffix}.${extension}`
  }

  /**
   * 导出为 PDF
   */
  const exportAsPdf = (headContent: string, content: string, fileName: string, language?: string): void => {
    // 生成完整的 HTML 字符串
    const htmlContentDownload = generateFullHtml(headContent, content)
    const frame = printFrame.value
    if (frame) {
      // 确保这里做了null检查
      // 创建一个新的文档对象
      const doc = frame.contentDocument || frame.contentWindow?.document
      if (doc) {
        // 设置文档标题，作为打印时的文件名
        doc.title = generateFileName(fileName, 'pdf', language)
        frame.srcdoc = htmlContentDownload
        setTimeout(() => {
          frame.contentWindow?.print()
        }, 100)
      }
    }
  }

  /**
   * 导出为 HTML
   */
  const exportAsHtml = (headContent: string, content: string, fileName: string, language?: string): void => {
    // 生成完整的 HTML 字符串
    const fullHtml = generateFullHtml(headContent, content)

    // 创建下载链接
    const blob = new Blob([fullHtml], { type: 'text/html;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = generateFileName(fileName, 'html', language)
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 生成完整的 HTML 字符串
   */
  const generateFullHtml = (headContent: string, bodyContent: string): string => {
    // 获取当前页面的head标签内容（可选）
    const currentHeadContent = typeof document !== 'undefined' ? document.head.innerHTML : ''
    
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Exported Document</title>
          ${headContent}
          <!-- 可选：包含当前页面的head内容 -->
          <!-- ${currentHeadContent} -->
        </head>
        <body>
          <div id="preview">
            ${bodyContent}
          </div>
        </body>
      </html>
    `
  }

  return {
    // 状态
    isExporting,
    printFrame,
    // 方法
    generateFileName,
    exportAsPdf,
    exportAsHtml,
    generateFullHtml
  }
}
