/**
 * DOCX 文档解析工具
 * 基于 docx-preview 的前端 DOCX 解析功能
 * 参考: https://volodymyrbaydalka.github.io/docxjs/
 */

import { renderAsync } from 'docx-preview'

export interface DocxParseResult {
  value: string
  messages: any[]
  styles?: any
}

export interface DocxParseOptions {
  className?: string
  inWrapper?: boolean
  ignoreWidth?: boolean
  ignoreHeight?: boolean
  ignoreFonts?: boolean
  ignoreLastRenderedPageBreak?: boolean
  ignoreImages?: boolean
  experimental?: boolean
  trimXmlDeclaration?: boolean
  useBase64URL?: boolean
  useMathMLPolyfill?: boolean
  renderHeaders?: boolean
  renderFooters?: boolean
  renderFootnotes?: boolean
  renderEndnotes?: boolean
  breakPages?: boolean
  ignoreOutsideWidth?: boolean
  ignoreOutsideHeight?: boolean
  renderMode?: 'canvas' | 'svg'
  pageWidth?: number
  pageHeight?: number
  pageMargins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

/**
 * 获取 docx-preview 实例
 */
export const getDocxPreview = () => {
  return { renderAsync }
}

/**
 * 使用 docx-preview 解析 DOCX 文件
 * @param file DOCX 文件
 * @param options 解析选项
 * @returns 解析结果
 */
export const parseDocxWithDocxPreview = async (
  file: File,
  options: DocxParseOptions = {}
): Promise<DocxParseResult> => {
  return new Promise((resolve, reject) => {
    const { renderAsync } = getDocxPreview()
    if (!renderAsync) {
      reject(new Error('Docx-preview library not available'))
      return
    }

    const reader = new FileReader()
    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer

        // 创建临时容器
        const container = document.createElement('div')
        container.style.position = 'absolute'
        container.style.left = '-9999px'
        container.style.top = '-9999px'
        document.body.appendChild(container)

        // 配置解析选项
        const parseOptions: any = {
          inWrapper: true,
          breakPages: true,
          renderHeaders: true,
          renderFooters: true,
          renderFootnotes: true,
          renderEndnotes: true,
          ...options
        }

        // 渲染文档
        await renderAsync(arrayBuffer, container, undefined, parseOptions)

        // 获取渲染后的 HTML
        const html = container.innerHTML

        // 清理临时容器
        document.body.removeChild(container)

        resolve({
          value: html,
          messages: [],
          styles: {}
        })
      }
      catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 提取 DOCX 文件中的纯文本
 * @param file DOCX 文件
 * @returns 纯文本内容
 */
export const extractDocxText = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const { renderAsync } = getDocxPreview()
    if (!renderAsync) {
      reject(new Error('Docx-preview library not available'))
      return
    }

    const reader = new FileReader()
    reader.onload = async (event) => {
      try {
        const arrayBuffer = event.target?.result as ArrayBuffer

        // 创建临时容器
        const container = document.createElement('div')
        container.style.position = 'absolute'
        container.style.left = '-9999px'
        container.style.top = '-9999px'
        document.body.appendChild(container)

        // 渲染文档（仅文本模式）
        await renderAsync(arrayBuffer, container, undefined, {
          inWrapper: false,
          ignoreWidth: true,
          ignoreHeight: true,
          ignoreFonts: true,
          breakPages: false,
          renderHeaders: false,
          renderFooters: false,
          renderFootnotes: false,
          renderEndnotes: false
        })

        // 提取纯文本
        const text = container.textContent || container.innerText || ''

        // 清理临时容器
        document.body.removeChild(container)

        resolve(text)
      }
      catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 验证 DOCX 文件
 * @param file 文件对象
 * @returns 验证结果
 */
export const validateDocxFile = (file: File): { valid: boolean, error?: string } => {
  // 检查文件类型
  const fileName = file.name.toLowerCase()
  if (!fileName.endsWith('.docx') && !fileName.endsWith('.doc')) {
    return {
      valid: false,
      error: '不支持的文件格式，请上传 .doc 或 .docx 文件'
    }
  }

  // 检查文件大小 (50MB限制)
  const fileSizeMB = file.size / 1024 / 1024
  if (fileSizeMB > 50) {
    return {
      valid: false,
      error: '文件过大，请上传小于 50MB 的文件'
    }
  }

  // 检查文件是否为空
  if (file.size === 0) {
    return {
      valid: false,
      error: '文件为空，请选择有效的文件'
    }
  }

  return { valid: true }
}

/**
 * 清理和优化 HTML 内容
 * @param html HTML 内容
 * @returns 优化后的 HTML
 */
export const cleanAndOptimizeHtml = (html: string): string => {
  // 移除多余的空白字符
  let cleaned = html.replace(/\s+/g, ' ')

  // 修复常见的 HTML 问题
  cleaned = cleaned
    .replace(/<p>\s*<\/p>/g, '') // 移除空段落
    .replace(/<div>\s*<\/div>/g, '') // 移除空 div
    .replace(/\s*<br\s*\/?>\s*/g, '<br>') // 标准化换行标签
    .replace(/\s*<hr\s*\/?>\s*/g, '<hr>') // 标准化水平线标签

  return cleaned.trim()
}

/**
 * 添加自定义样式到解析结果
 * @param html HTML 内容
 * @returns 添加样式后的 HTML
 */
export const addCustomStyles = (html: string): string => {
  const styleBlock = `
    <style>
      .docx-content {
        font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
      }

      /* docx-preview 生成的页面结构样式 */
      .docx-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        max-width: 100%;
        background: transparent;
        font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }

      .docx-wrapper section {
        margin-bottom: 20px;
        page-break-after: always;
        background: transparent;
        overflow: hidden;
        width: 100%;
        box-shadow: none !important;
      }

      /* 覆盖 docx-preview 默认的阴影样式 */
      .docx-wrapper > section.docx {
        box-shadow: none !important;
        background: transparent !important;
        min-height: auto !important;
        height: auto !important;
      }

      /* 移除 docx-preview 生成的 0px 宽高限制 */
      .docx-wrapper div[style*="width: 0px"],
      .docx-wrapper div[style*="height: 0px"] {
        width: auto !important;
        height: auto !important;
        min-width: 0 !important;
        min-height: 0 !important;
        max-width: none !important;
      }

      /* 移除所有 min-height 限制并调整每页样式 */
      .docx-wrapper section[style*="min-height"],
      .docx-wrapper section.docx {
        min-height: auto !important;
        height: auto !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 20px !important;
        margin: 0 auto !important;
        border-radius: 8px !important;
        overflow: hidden !important;
        position: relative !important;
      }

      .docx-wrapper section:last-child {
        page-break-after: auto;
      }

      /* 页面内容样式 - 每页调整 */
      .docx-wrapper section .docx {
        padding: 20px !important;
        min-height: auto !important;
        background: transparent !important;
        box-shadow: none !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        border-radius: 8px !important;
        overflow: hidden !important;
      }

      /* 每页背景效果 */
      .docx-wrapper section .docx::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: -1;
      }

      /* 标题样式 */
      .docx-content h1, .docx-content h2, .docx-content h3,
      .docx-content h4, .docx-content h5, .docx-content h6 {
        margin: 1.5em 0 0.5em 0;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1.4;
      }

      .docx-content h1 { font-size: 2em; }
      .docx-content h2 { font-size: 1.75em; }
      .docx-content h3 { font-size: 1.5em; }
      .docx-content h4 { font-size: 1.25em; }
      .docx-content h5 { font-size: 1.1em; }
      .docx-content h6 { font-size: 1em; }

      /* 段落样式 - 只保留指定样式 */
      .docx-content p {
        margin-top: 12px !important;
        line-height: 22px !important;
        text-indent: 0 !important;
        word-break: break-word !important;
      }

      /* 表格样式 */
      .docx-content table {
        border-collapse: collapse;
        width: 100%;
        margin: 1em 0;
        border: 1px solid #ddd;
      }

      .docx-content th, .docx-content td {
        border: 1px solid #ddd;
        padding: 8px 12px;
        text-align: left;
        vertical-align: top;
      }

      .docx-content th {
        background-color: #f8f9fa;
        font-weight: 600;
      }

      /* 列表样式 */
      .docx-content ul, .docx-content ol {
        margin: 0.8em 0;
        padding-left: 2em;
      }

      .docx-content li {
        margin: 0.3em 0;
        line-height: 1.6;
      }

      /* 引用样式 */
      .docx-content blockquote {
        margin: 1em 0;
        padding: 0.5em 1em;
        border-left: 4px solid #3498db;
        background-color: #f8f9fa;
        font-style: italic;
      }

      /* 代码样式 */
      .docx-content code {
        background-color: #f1f2f6;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
      }

      .docx-content pre {
        background-color: #f8f9fa;
        padding: 1em;
        border-radius: 5px;
        overflow-x: auto;
        margin: 1em 0;
        border: 1px solid #e9ecef;
      }

      .docx-content pre code {
        background-color: transparent;
        padding: 0;
      }

      /* 图片样式 */
      .docx-content img {
        max-width: 100% !important;
        height: auto !important;
        margin: 1em 0;
        border-radius: 4px;
        display: block;
        object-fit: contain;
      }

      /* 覆盖 docx-preview 生成的图片内联样式 */
      .docx-content img[style*="height"],
      .docx img[style*="height"] {
        height: auto !important;
        max-height: none !important;
      }

      /* 覆盖 docx-preview 的默认段落样式 */
      .docx p {
        margin: 0 !important;
        min-height: auto !important;
        line-height: normal !important;
        text-align: left !important;
        margin-inline-start: 0 !important;
        margin-inline-end: 0 !important;
      }

      /* 目录样式 - 针对 docx_toc1 和 docx_toc2 类 */
      .docx p.docx_toc1,
      .docx p.docx_toc2 {
        margin-top: 12px !important;
        line-height: 22px !important;
        text-indent: 0 !important;
        word-break: break-word !important;
      }

      /* 确保目录样式在 docx-content 中也生效 */
      .docx-content p.docx_toc1,
      .docx-content p.docx_toc2 {
        margin-top: 12px !important;
        line-height: 22px !important;
        text-indent: 0 !important;
        word-break: break-word !important;
      }

      /* 链接样式 */
      .docx-content a {
        color: #3498db;
        text-decoration: none;
      }

      .docx-content a:hover {
        text-decoration: underline;
      }

      /* 文本对齐样式 */
      .docx-content .text-center { text-align: center; }
      .docx-content .text-left { text-align: left; }
      .docx-content .text-right { text-align: right; }
      .docx-content .text-justify { text-align: justify; }

      /* 字体样式 */
      .docx-content .bold { font-weight: bold; }
      .docx-content .italic { font-style: italic; }
      .docx-content .underline { text-decoration: underline; }
      .docx-content .strikethrough { text-decoration: line-through; }

      /* 深色模式适配 */
      .dark .docx-content {
        color: #e5e7eb;
      }

      .dark .docx-wrapper {
        background: transparent;
      }

      .dark .docx-wrapper section {
        background: transparent !important;
        box-shadow: none !important;
      }

      .dark .docx-wrapper > section.docx {
        box-shadow: none !important;
        background: transparent !important;
      }

      /* 深色模式下移除 0px 宽高限制
      .dark .docx-wrapper div[style*="width: 0px"],
      .dark .docx-wrapper div[style*="height: 0px"] {
        min-width: 0 !important;
        min-height: 0 !important;
        max-width: none !important;
        max-height: none !important;
      }*/

      .dark .docx-wrapper section .docx {
        background: transparent !important;
        box-shadow: none !important;
        border: 1px solid #374151 !important;
      }

      /* 深色模式下的图片高度覆盖 */
      .dark .docx-content img[style*="height"],
      .dark .docx img[style*="height"] {
        height: auto !important;
        max-height: none !important;
      }

      /* 深色模式下覆盖 docx-preview 的默认段落样式 */
      .dark .docx p {
        margin-top: 12px !important;
        line-height: 22px !important;
        text-indent: 0 !important;
        word-break: break-word !important;
      }

      .dark .docx-content h1, .dark .docx-content h2, .dark .docx-content h3,
      .dark .docx-content h4, .dark .docx-content h5, .dark .docx-content h6 {
        color: #f3f4f6;
      }

      .dark .docx-content th, .dark .docx-content td {
        border-color: #4b5563;
      }

      .dark .docx-content th {
        background-color: #374151;
      }

      .dark .docx-content blockquote {
        background-color: #374151;
        border-left-color: #60a5fa;
      }

      .dark .docx-content code {
        background-color: #374151;
      }

      .dark .docx-content pre {
        background-color: #374151;
        border-color: #4b5563;
      }

      .dark .docx-content a {
        color: #60a5fa;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .docx-wrapper {
          background: transparent;
        }

        .docx-wrapper section {
          margin: 10px;
          background: transparent !important;
          box-shadow: none !important;
        }

        .docx-wrapper > section.docx {
          box-shadow: none !important;
          background: transparent !important;
        }

        .docx-wrapper section .docx {
          padding: 15px !important;
          background: transparent !important;
          box-shadow: none !important;
          border-radius: 6px !important;
        }

        /* 响应式设计下的每页背景效果 */
        .docx-wrapper section .docx::before {
          background: linear-gradient(135deg, rgba(30, 58, 138, 0.03) 0%, rgba(55, 48, 163, 0.03) 50%, rgba(124, 58, 237, 0.03) 100%);
        }

        /* 响应式设计下的图片高度覆盖 */
        .docx-content img[style*="height"],
        .docx img[style*="height"] {
          height: auto !important;
          max-height: none !important;
        }

        /* 响应式设计下覆盖 docx-preview 的默认段落样式 */
        .docx p {
          margin: 0 !important;
          min-height: auto !important;
          line-height: normal !important;
          text-align: left !important;
          margin-inline-start: 0 !important;
          margin-inline-end: 0 !important;
        }

        /* 响应式设计下目录样式 */
        .docx p.docx_toc1,
        .docx p.docx_toc2 {
          margin-top: 12px !important;
          line-height: 22px !important;
          text-indent: 0 !important;
          word-break: break-word !important;
        }

        /* 响应式设计下确保目录样式在 docx-content 中也生效 */
        .docx-content p.docx_toc1,
        .docx-content p.docx_toc2 {
          margin-top: 12px !important;
          line-height: 22px !important;
          text-indent: 0 !important;
          word-break: break-word !important;
        }

        /* 响应式设计下移除 0px 宽高限制
        .docx-wrapper div[style*="width: 0px"],
        .docx-wrapper div[style*="height: 0px"] {
          width: auto !important;
          height: auto !important;
          min-width: 0 !important;
          min-height: 0 !important;
          max-width: none !important;
          max-height: none !important;
        }*/

        .docx-content h1 { font-size: 1.75em; }
        .docx-content h2 { font-size: 1.5em; }
        .docx-content h3 { font-size: 1.25em; }
        .docx-content h4 { font-size: 1.1em; }
        .docx-content h5 { font-size: 1em; }
        .docx-content h6 { font-size: 0.9em; }
      }
    </style>
  `

  return `<div class="docx-content">${html}</div>${styleBlock}`
}

/**
 * 完整的 DOCX 解析流程
 * @param file DOCX 文件
 * @param options 解析选项
 * @returns 解析结果
 */
export const parseDocxFile = async (
  file: File,
  options: DocxParseOptions = {}
): Promise<DocxParseResult> => {
  // 验证文件
  const validation = validateDocxFile(file)
  if (!validation.valid) {
    throw new Error(validation.error)
  }

  // 解析文件
  const result = await parseDocxWithDocxPreview(file, options)

  // 清理和优化 HTML
  result.value = cleanAndOptimizeHtml(result.value)

  // 添加自定义样式
  result.value = addCustomStyles(result.value)

  return result
}
