import { convertTimeToTime, convertTimeToSrtFormat } from '@/pages/file/subtitle/utils/convertTime.ts'
import type { ParsedSubtitle } from '@/pages/file/subtitle/types/subtitle.d.ts'

/**
 * ASS/SSA 字幕解析器
 * @param {string} content - ASS/SSA文件内容
 * @returns {Array} 解析后的字幕对象数组
 */
export const parseAss = (content) => {
  const subtitles: ParsedSubtitle[] = []

  // 查找 [Events] 部分
  const eventsSectionRegex = /\[Events\]([\s\S]*?)(?:\n\[|$)/i
  const eventsMatch = content.match(eventsSectionRegex)

  if (!eventsMatch) {
    return subtitles
  }

  // 提取 [Events] 部分的内容
  const eventsContent = eventsMatch[1]

  // 查找格式定义行
  const formatRegex = /Format:\s*(.*)/i
  const formatMatch = eventsContent.match(formatRegex)

  if (!formatMatch) {
    return subtitles
  }

  // 解析字段顺序
  const formatFields = formatMatch[1].split(',').map(field => field.trim())
  const startIndex = formatFields.indexOf('Start')
  const endIndex = formatFields.indexOf('End')
  const textIndex = formatFields.indexOf('Text')

  if (startIndex === -1 || endIndex === -1 || textIndex === -1) {
    return subtitles
  }

  // 匹配对话行 (Dialogue)
  const dialogueRegex = /Dialogue:\s*(.*)/g
  let match
  let id = 1

  while ((match = dialogueRegex.exec(eventsContent)) !== null) {
    const fields = match[1].split(',')

    if (fields.length >= Math.max(startIndex, endIndex, textIndex) + 1) {
      // 提取开始时间、结束时间和文本
      const startTime = fields[startIndex].trim()
      const endTime = fields[endIndex].trim()

      // 提取文本部分（可能包含逗号，所以需要重新组合剩余部分）
      const textParts = fields.slice(textIndex)
      const originalText = textParts.join(',').trim()

      // 处理 ASS 特殊标记（如花括号内的样式标记）
      const cleanedText = cleanAssText(originalText)

      subtitles.push({
        id: Number(id++),
        showStartTime: convertTimeToTime(startTime),
        showEndTime: convertTimeToTime(endTime),
        originalText: cleanedText,
        srtFormatStartTime: convertTimeToSrtFormat(startTime),
        srtFormatEndTime: convertTimeToSrtFormat(endTime)
      })
    }
  }

  return subtitles
}

/**
 * 清理 ASS 文本中的特殊标记
 * @param {string} text - 原始 ASS 文本
 * @returns {string} 清理后的文本
 */
const cleanAssText = (text: string): string => {
  // 移除花括号内的样式标记，如 { \pos(400,570) } 或 { \fn微软雅黑 }
  return text
    .replace(/\{[^}]*\}/g, '')
    .replace(/\\N/g, '\n') // 将 \N 替换为换行符
    .replace(/\\n/g, '\n') // 将 \n 替换为换行符
}
