import { convertTimeToSrtFormat, convertTimeToTime } from '@/pages/file/subtitle/utils/convertTime.ts'
import type { ParsedSubtitle } from '@/pages/file/subtitle/types/subtitle.d.ts'

/**
 * VTT 字幕解析器
 * @param {string} content - VTT文件内容
 * @returns {Array} 解析后的字幕对象数组
 */
export const parseVtt = (content) => {
  // 移除WEBVTT头部
  const body = content.replace(/^WEBVTT(?:\s.*)?[\r\n]+/i, '').trim()

  // 按空行分割字幕块
  const blocks = body.split(/\r?\n\r?\n/).filter(block => block.trim() !== '')

  const subtitles: ParsedSubtitle[] = []

  // VTT时间格式: 00:00:10.311 或 00:10.311
  const timePattern = /(\d{1,2}:?\d{2}:\d{2}\.\d{3})\s*-->\s*(\d{1,2}:?\d{2}:\d{2}\.\d{3})/

  blocks.forEach((block, index) => {
    const lines = block.split(/\r?\n/)

    // 查找时间行
    const timeLineIndex = lines.findIndex(line => timePattern.test(line))

    if (timeLineIndex !== -1) {
      const timeLine = lines[timeLineIndex]
      const match = timeLine.match(timePattern)

      if (match) {
        const [, startTime, endTime] = match

        // 统一时间格式并转换为 hh:mm:ss
        const showStartTime = convertTimeToTime(startTime)
        const showEndTime = convertTimeToTime(endTime)

        // 转换时间为srt格式
        const srtFormatStartTime = convertTimeToSrtFormat(startTime)
        const srtFormatEndTime = convertTimeToSrtFormat(endTime)

        // 获取字幕文本（时间行之后的所有内容）
        const textLines = lines.slice(timeLineIndex + 1)
        const cleanedText = textLines
          .map(line => line.trim())
          .filter(line => line.length > 0)
          .join('\n')

        subtitles.push({
          id: Number(index) + 1,
          showStartTime: showStartTime,
          showEndTime: showEndTime,
          originalText: cleanedText,
          srtFormatStartTime: srtFormatStartTime,
          srtFormatEndTime: srtFormatEndTime
        })
      }
    }
  })

  return subtitles
}
