/**
 * 时间码转换时间格式（支持逗号/句点）
 * @param {string} timeString - 时间码
 * @returns {string} 时间格式 hh:mm:ss
 * 支持格式示例：
 * srt：00:00:10,552
 * vtt：00:00:10.311、00:10.311
 * sbv：0:00:10.311
 * lrc：00:07.56
 * ass：0,0:00:12.21
 */
export const convertTimeToTime = (timeString: string): string => {
  // 处理ASS格式的特殊情况 (如: 0,0:00:12.21)
  let normalizedString = timeString
  if (timeString.includes(',') && timeString.split(',').length === 3) {
    // 对于ASS格式，移除第一个数字和逗号 (0,0:00:12.21 -> 0:00:12.21)
    const parts = timeString.split(',')
    normalizedString = parts.slice(1).join(',')
  }

  // 统一替换逗号为句点
  const stringWithDot = normalizedString.replace(',', '.')

  // 分离时间和毫秒部分
  const [timePart] = stringWithDot.split('.')

  // 按冒号分割时间部分
  const timeComponents = timePart.split(':')

  let hours = 0
  let minutes = 0
  let seconds = 0

  // 根据组件数量处理不同格式
  switch (timeComponents.length) {
    case 3:
      // 标准格式 HH:MM:SS (如: 00:00:10)
      hours = parseInt(timeComponents[0], 10) || 0
      minutes = parseInt(timeComponents[1], 10) || 0
      seconds = parseInt(timeComponents[2], 10) || 0
      break
    case 2:
      // 简化格式 MM:SS (如: 00:07 或 00:10)
      minutes = parseInt(timeComponents[0], 10) || 0
      seconds = parseInt(timeComponents[1], 10) || 0
      break
    case 1:
      // 只有秒数的情况
      seconds = parseInt(timeComponents[0], 10) || 0
      break
    default:
      // 异常情况，返回默认值
      return '00:00:00'
  }

  // 验证时间值的有效性
  if (minutes >= 60 || seconds >= 60) {
    // 可以选择抛出错误或调整时间值
    return '00:00:00'
  }

  // 格式化为 hh:mm:ss 格式
  const formatNumber = (num: number): string => String(num).padStart(2, '0')
  return `${formatNumber(hours)}:${formatNumber(minutes)}:${formatNumber(seconds)}`
}

/**
 * 时间码转换为SRT时间格式
 * @param {string} timeString - 时间码
 * @returns {string} SRT时间格式 hh:mm:ss,mmm
 * 支持格式示例：
 * vtt：00:00:10.311、00:10.311
 * sbv：0:00:10.311
 * lrc：00:07.56
 * ass：0,0:00:12.21
 */
export const convertTimeToSrtFormat = (timeString: string): string => {
  // 处理ASS格式的特殊情况 (如: 0,0:00:12.21)
  let normalizedString = timeString
  if (timeString.includes(',') && timeString.split(',').length === 3) {
    // 对于ASS格式，移除第一个数字和逗号 (0,0:00:12.21 -> 0:00:12.21)
    const parts = timeString.split(',')
    normalizedString = parts.slice(1).join(',')
  }

  // 提取毫秒部分
  let milliseconds = '000'
  if (normalizedString.includes(',') || normalizedString.includes('.')) {
    const separator = normalizedString.includes(',') ? ',' : '.'
    const [, msPart] = normalizedString.split(separator)
    // 确保毫秒部分是3位数
    if (msPart) {
      if (msPart.length === 1) {
        milliseconds = msPart + '00'
      }
      else if (msPart.length === 2) {
        milliseconds = msPart + '0'
      }
      else if (msPart.length >= 3) {
        milliseconds = msPart.substring(0, 3)
      }
    }
  }

  // 统一替换逗号为句点以便处理
  const stringWithDot = normalizedString.replace(',', '.')

  // 分离时间部分
  const [timePart] = stringWithDot.split('.')

  // 按冒号分割时间部分
  const timeComponents = timePart.split(':')

  let hours = 0
  let minutes = 0
  let seconds = 0

  // 根据组件数量处理不同格式
  switch (timeComponents.length) {
    case 3:
      // 标准格式 HH:MM:SS (如: 00:00:10)
      hours = parseInt(timeComponents[0], 10) || 0
      minutes = parseInt(timeComponents[1], 10) || 0
      seconds = parseInt(timeComponents[2], 10) || 0
      break
    case 2:
      // 简化格式 MM:SS (如: 00:07 或 00:10)
      minutes = parseInt(timeComponents[0], 10) || 0
      seconds = parseInt(timeComponents[1], 10) || 0
      break
    case 1:
      // 只有秒数的情况
      seconds = parseInt(timeComponents[0], 10) || 0
      break
    default:
      // 异常情况，返回默认值
      return '00:00:00,000'
  }

  // 验证时间值的有效性
  if (minutes >= 60 || seconds >= 60) {
    return '00:00:00,000'
  }

  // 格式化为 hh:mm:ss,mmm 格式
  const formatNumber = (num: number): string => String(num).padStart(2, '0')
  return `${formatNumber(hours)}:${formatNumber(minutes)}:${formatNumber(seconds)},${milliseconds}`
}
