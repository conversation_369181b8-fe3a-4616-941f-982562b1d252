import { convertTimeToTime, convertTimeToSrtFormat } from '@/pages/file/subtitle/utils/convertTime.ts'
import type { ParsedSubtitle } from '@/pages/file/subtitle/types/subtitle.d.ts'

/**
 * SBV 字幕解析器
 * @param {string} content - SBV文件内容
 * @returns {Array} 解析后的字幕对象数组
 */
export const parseSbv = (content) => {
  // 按空行分割字幕块
  const blocks = content.split(/\n\s*\n/).filter(block => block.trim() !== '')
  const subtitles: ParsedSubtitle[] = []

  for (const [index, block] of blocks.entries()) {
    // 分割时间码和文本内容
    const lines = block.trim().split('\n')
    if (lines.length >= 2) {
      const timeLine = lines[0]
      const textLines = lines.slice(1)

      // 提取开始和结束时间
      const timeParts = timeLine.split(',')
      if (timeParts.length === 2) {
        const startTime = timeParts[0].trim()
        const endTime = timeParts[1].trim()

        // 转换时间为显示格式 (hh:mm:ss)
        const showStartTime = convertTimeToTime(startTime)
        const showEndTime = convertTimeToTime(endTime)

        // 转换时间为SRT格式 (hh:mm:ss,mmm)
        const srtFormatStartTime = convertTimeToSrtFormat(startTime)
        const srtFormatEndTime = convertTimeToSrtFormat(endTime)

        // 合并文本行
        const originalText = textLines.join('\n')

        subtitles.push({
          id: Number(index) + 1,
          showStartTime,
          showEndTime,
          originalText,
          srtFormatStartTime,
          srtFormatEndTime
        })
      }
    }
  }

  return subtitles
}
