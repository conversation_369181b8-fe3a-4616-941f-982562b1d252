import { convertTimeToTime } from '@/pages/file/subtitle/utils/convertTime.ts'
import type { ParsedSubtitle } from '@/pages/file/subtitle/types/subtitle.d.ts'
/**
 * SRT 字幕解析器
 * @param {string} content - SRT文件内容
 * @returns {Array} 解析后的字幕对象数组
 */
export const parseSrt = (content) => {
  // 使用更精确的正则表达式匹配字幕块
  const pattern = /(\d+)\s*\n(\d{2}:\d{2}:\d{2}[,\.]\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2}[,\.]\d{3})\s*\n((?:(?!\r?\n\d+|\r?\n$).)*)/gs
  const subtitles: ParsedSubtitle[] = []

  let match
  while ((match = pattern.exec(content)) !== null) {
    const [, id, startTime, endTime, text] = match

    // 统一时间格式并转换为秒
    const showStartTime = convertTimeToTime(startTime)
    const showEndTime = convertTimeToTime(endTime)

    // 清理文本内容（保留换行但移除多余空白）
    const cleanedText = text
      .replace(/\r?\n/g, '\n')
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n')

    subtitles.push({
      id: Number(id),
      showStartTime: showStartTime,
      showEndTime: showEndTime,
      originalText: cleanedText,
      srtFormatStartTime: startTime,
      srtFormatEndTime: endTime
    })
  }

  return subtitles
}
