import { convertTimeToTime, convertTimeToSrtFormat } from '@/pages/file/subtitle/utils/convertTime.ts'
import type { ParsedSubtitle } from '@/pages/file/subtitle/types/subtitle.d.ts'

/**
 * LRC 歌词解析器
 * @param {string} content - LRC文件内容
 * @returns {Array} 解析后的歌词对象数组
 */
export const parseLrc = (content) => {
  const lines = content.split(/\r?\n/)
  const lyrics: ParsedSubtitle[] = []
  const timeLyricPairs = []

  // 提取所有时间标签和对应的歌词
  for (const line of lines) {
    // 匹配时间标签，支持 [mm:ss.xx] 或 [mm:ss.xxx] 格式
    const timeRegex = /\[(\d{1,2}:\d{2}\.\d{2,3})\]/g
    const timeMatches = [...line.matchAll(timeRegex)]

    // 提取歌词文本（去除所有时间标签）
    const lyricText = line.replace(/\[\d{1,2}:\d{2}\.\d{2,3}\]/g, '').trim()

    // 如果有时间标签和歌词文本，则保存
    if (timeMatches.length > 0 && lyricText) {
      for (const match of timeMatches) {
        const timeString = match[1]
        timeLyricPairs.push({
          time: timeString,
          lyric: lyricText
        })
      }
    }
  }

  // 按时间排序
  timeLyricPairs.sort((a, b) => {
    const timeAToSeconds = parseLrcTimeToSeconds(a.time)
    const timeBToSeconds = parseLrcTimeToSeconds(b.time)
    return timeAToSeconds - timeBToSeconds
  })

  // 转换为字幕格式
  for (let i = 0; i < timeLyricPairs.length; i++) {
    const current = timeLyricPairs[i]
    let endTime

    // 如果不是最后一行，结束时间是下一行的开始时间
    if (i < timeLyricPairs.length - 1) {
      endTime = timeLyricPairs[i + 1].time
    }
    else {
      // 最后一行，根据内容长度估算结束时间（假设每字符0.2秒）
      const duration = current.lyric.length * 0.2
      const endTimeSeconds = parseLrcTimeToSeconds(current.time) + duration
      endTime = convertSecondsToLrcTime(endTimeSeconds)
    }

    lyrics.push({
      id: i + 1,
      showStartTime: convertTimeToTime(current.time),
      showEndTime: convertTimeToTime(endTime),
      originalText: current.lyric,
      srtFormatStartTime: convertTimeToSrtFormat(current.time),
      srtFormatEndTime: convertTimeToSrtFormat(endTime)
    })
  }

  return lyrics
}

/**
 * 将 LRC 时间格式转换为秒数
 * @param {string} timeString - LRC 时间格式 mm:ss.xx 或 mm:ss.xxx
 * @returns {number} 秒数
 */
const parseLrcTimeToSeconds = (timeString: string): number => {
  const parts = timeString.split(':')
  if (parts.length !== 2) return 0

  const minutes = parseInt(parts[0], 10) || 0
  const secondsAndMillis = parts[1].split('.')
  const seconds = parseInt(secondsAndMillis[0], 10) || 0
  const milliseconds = parseInt(secondsAndMillis[1], 10) || 0

  // 根据毫秒位数处理（可能是 2 位或 3 位）
  const millisValue = secondsAndMillis[1].length === 2 ? milliseconds / 100 : milliseconds / 1000

  return minutes * 60 + seconds + millisValue
}

/**
 * 将秒数转换为 LRC 时间格式
 * @param {number} seconds - 秒数
 * @returns {string} LRC 时间格式 mm:ss.xx
 */
const convertSecondsToLrcTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  const secs = Math.floor(remainingSeconds)
  const millis = Math.floor((remainingSeconds - secs) * 100)

  return `${minutes}:${secs.toString().padStart(2, '0')}.${millis.toString().padStart(2, '0')}`
}
