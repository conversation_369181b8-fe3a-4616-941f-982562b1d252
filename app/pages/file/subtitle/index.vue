<!-- 字幕 文档翻译页面 -->
<template>
  <DocumentLayouts
    document-type="subtitle"
    :parse-error="parseError"
    :loading="isLoading"
    :show-switch-bilingual="false"
    @file-selected="handleFileSelected"
    @retry-parse="parseSubtitleFile"
  >
    <template #right>
      <!-- 导出译文字幕 -->
      <UButton
        size="xl"
        color="secondary"
        :disabled="!parsedSubtitles.length"
        variant="outline"
        class="mr-4 cursor-pointer rounded-md"
        :label="t('document_translation.subtitle.export_trans_subtitle')"
        @click="exportTranslatedSubtitles"
      />
      <!-- 导出双语字幕 -->
      <UButtonGroup>
        <UButton
          size="xl"
          color="secondary"
          :disabled="!parsedSubtitles.length"
          variant="outline"
          class="cursor-pointer w-full rounded-md"
          :label="t('document_translation.subtitle.export_bilingual_subtitle')"
          @click="exportBilingualSubtitles"
        />
        <UButton
          size="xl"
          color="secondary"
          variant="outline"
          class="cursor-pointer rounded-md"
          icon="i-heroicons-cog-6-tooth"
          @click="openSubtitleSettings"
        />
      </UButtonGroup>
    </template>
    <!-- 字幕解析后的内容 -->
    <SubtitleViewer v-if="parsedSubtitles.length > 0" :parsed-subtitles="parsedSubtitles" :file-name="fileName" />
  </DocumentLayouts>

  <!-- 导出双语字幕设置弹窗 -->
  <BilingualSubtitles
    v-model:open="showSettingsModal"
    :visible="showSettingsModal"
    :settings="subtitleSettings"
    @update:settings="subtitleSettings = $event"
    @save="saveSettings"
    @cancel="loadSettings"
  />
</template>

<script setup lang="ts">
// 翻译布局
import DocumentLayouts from '../components/DocumentLayouts.vue'
import SubtitleViewer from '@/pages/file/subtitle/components/SubtitleViewer.vue'
import { useFileStore } from '@/store/FileStore'
import BilingualSubtitles from '@/pages/file/subtitle/components/BilingualSubtitles.vue'
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { storeToRefs } from 'pinia'
import type { ParsedSubtitle } from '@/pages/file/subtitle/types/subtitle'
import { parseSrt } from '@/pages/file/subtitle/utils/srtParse.ts'
import { parseVtt } from '@/pages/file/subtitle/utils/vttParse.ts'
import { parseSbv } from '@/pages/file/subtitle/utils/sbvParse.ts'
import { parseLrc } from '@/pages/file/subtitle/utils/lrcParse.ts'
import { parseAss } from '@/pages/file/subtitle/utils/assParse.ts'

const fileStore = useFileStore()
const { t } = useI18n()
const documentTranslateStore = useDocumentTranslateStore()
const { targetLanguage } = storeToRefs(documentTranslateStore)

// 使用字幕解析 composable
// const { parseError, parsedSubtitles, fileName, isLoading, parseSubtitleFile } = useSubtitleParser()

// 禁用默认布局
definePageMeta({
  layout: false
})

// 字幕设置
const showSettingsModal = ref(false)
const parseError = ref('')
const parsedSubtitles = ref<ParsedSubtitle[]>([])
// 注意! 这个文件名 一定要在 pageTitle 前面
const fileName = ref('')
const isLoading = ref(false)

// 字幕设置的类型定义
interface SubtitleSettings {
  order: 'translationFirst' | 'originalFirst'
  track: 'single' | 'double'
}

// 默认设置
const defaultSubtitleSettings: SubtitleSettings = {
  order: 'translationFirst',
  track: 'single'
}

// 从 localStorage 获取设置或使用默认设置
const subtitleSettings = ref<SubtitleSettings>({ ...defaultSubtitleSettings })

const loadSettings = () => {
  const savedSettings = localStorage.getItem('subtitleExportSettings')
  if (savedSettings) {
    try {
      subtitleSettings.value = { ...defaultSubtitleSettings, ...JSON.parse(savedSettings) }
    }
    catch (e) {
      console.error('Failed to parse subtitle settings', e)
    }
  }
}

const saveSettings = () => {
  localStorage.setItem('subtitleExportSettings', JSON.stringify(subtitleSettings.value))
  showSettingsModal.value = false
}

// 打开设置弹窗
const openSubtitleSettings = () => {
  showSettingsModal.value = true
}

// 计算完整的页面标题
const pageTitle = computed(() => {
  // 字幕 文档翻译
  const baseTitle = t('document_translation.subtitle.title')
  const siteName = t('common.site_name')

  if (fileName.value) {
    return `${fileName.value} - ${siteName}`
  }
  return `${baseTitle} - ${siteName}`
})

useSeoMeta({
  titleTemplate: '%s',
  // 文档翻译
  title: pageTitle, // 这里不要使用 .value ，否则会导致 SEO 标题变回默认的，动态标题会不生效
  ogTitle: pageTitle
})

/**
 * 选择打开文件后回调
 * @param file
 */
const handleFileSelected = async (file: File) => {
  try {
    await parseSubtitleFile(file)
    fileStore.setFile(file)
  }
  catch (error) {
    // 错误已经在 composable 中处理
    console.error('字幕文件解析失败:', error)
  }
}

onMounted(() => {
  loadSettings()
  if (fileStore.uploadedFile) {
    handleFileSelected(fileStore.uploadedFile)
  }
})

/**
 * 导出译文字幕
 */
const exportTranslatedSubtitles = () => {
  // 获取所有译文单元格
  const translationCells = document.querySelectorAll('td[data-translation-result="true"]')

  // 构建 SRT 内容
  let srtContent = ''
  parsedSubtitles.value.forEach((subtitle, index) => {
    // 获取对应的译文内容
    const translatedText = translationCells[index]?.textContent || ''

    srtContent += `${index + 1}\n`
    srtContent += `${subtitle.srtFormatStartTime} --> ${subtitle.srtFormatEndTime}\n`
    srtContent += `${translatedText}\n\n`
  })

  // 创建并下载文件
  const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName.value.replace(/\.[^/.]+$/, '') + '(' + targetLanguage.value + ')' + '.srt'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 导出双语字幕
 */
const exportBilingualSubtitles = () => {
  // 获取所有译文单元格
  const translationCells = document.querySelectorAll('td[data-translation-result="true"]')

  // 构建 SRT 内容
  let srtContent = ''
  let indexCounter = 1

  parsedSubtitles.value.forEach((subtitle, arrayIndex) => {
    // 获取对应的译文内容
    const translatedText = translationCells[arrayIndex]?.textContent || ''
    const originalText = subtitle.originalText

    const { order, track } = subtitleSettings.value

    if (track === 'single') {
      // 单轨模式
      srtContent += `${indexCounter}\n`
      srtContent += `${subtitle.srtFormatStartTime} --> ${subtitle.srtFormatEndTime}\n`

      if (order === 'translationFirst') {
        srtContent += `${translatedText}\n`
        srtContent += `${originalText}\n`
      }
      else {
        srtContent += `${originalText}\n`
        srtContent += `${translatedText}\n`
      }

      srtContent += '\n'
      indexCounter++
    }
    else {
      // 双轨模式
      if (order === 'translationFirst') {
        // 译文在前
        srtContent += `${indexCounter}\n`
        srtContent += `${subtitle.srtFormatStartTime} --> ${subtitle.srtFormatEndTime}\n`
        srtContent += `${translatedText}\n\n`
        indexCounter++

        srtContent += `${indexCounter}\n`
        srtContent += `${subtitle.srtFormatStartTime} --> ${subtitle.srtFormatEndTime}\n`
        srtContent += `${originalText}\n\n`
        indexCounter++
      }
      else {
        // 原文在前
        srtContent += `${indexCounter}\n`
        srtContent += `${subtitle.srtFormatStartTime} --> ${subtitle.srtFormatEndTime}\n`
        srtContent += `${originalText}\n\n`
        indexCounter++

        srtContent += `${indexCounter}\n`
        srtContent += `${subtitle.srtFormatStartTime} --> ${subtitle.srtFormatEndTime}\n`
        srtContent += `${translatedText}\n\n`
        indexCounter++
      }
    }
  })

  // 创建并下载文件
  const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = fileName.value.replace(/\.[^/.]+$/, '') + '(' + targetLanguage.value + '-bilingual)' + '.srt'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
   * 解析字幕文件
   * @param file 字幕文件
   * @returns Promise<ParsedSubtitle[]>
   */
const parseSubtitleFile = async (file: File): Promise<ParsedSubtitle[]> => {
  // 清空之前的错误和数据
  parseError.value = ''
  parsedSubtitles.value = [] as ParsedSubtitle[]
  isLoading.value = true

  try {
    if (!file) {
      throw new Error(t('document_translation.subtitle.please_select_subtitle_file'))
    }

    // 使用FileReader异步读取文件
    const content = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = e => resolve(e.target?.result as string)
      reader.onerror = () => reject(new Error(t('document_translation.subtitle.failed_read_subtitle_file'))) // 字幕文件读取失败
      reader.readAsText(file, 'UTF-8')
    })

    // 获取文件后缀
    let subtitles: ParsedSubtitle[] = []
    const fileExtension = file.name.split('.').pop()?.toLowerCase()

    switch (fileExtension) {
      case 'srt':
        subtitles = parseSrt(content)
        break
      case 'vtt':
        subtitles = parseVtt(content)
        break
      case 'sbv':
        subtitles = parseSbv(content)
        break
      case 'lrc':
        subtitles = parseLrc(content)
        break
      case 'ass':
      case 'ssa':
        subtitles = parseAss(content)
        break
      default:
        // 不支持的文件格式
        throw new Error(t('document_translation.subtitle.unsupported_file_format'))
    }

    if (subtitles.length === 0) {
      // 未解析到任何字幕内容，请检查文件内容
      throw new Error(t('document_translation.subtitle.subtitle_file_content_error'))
    }

    fileName.value = file.name
    parsedSubtitles.value = [...subtitles]

    return subtitles
  }
  catch (error) {
    parseError.value = error.message || error.toString()
    throw error
  }
  finally {
    isLoading.value = false
  }
}
</script>
