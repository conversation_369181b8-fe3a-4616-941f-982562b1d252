<template>
  <div class="mx-auto mb-2 max-w-7xl space-y-4 p-6 pt-0 text-neutral-900 dark:text-white dark:bg-gray-800">
    <!-- 主要内容区域 -->
    <!-- 头部：显示文件名 -->
    <div v-if="props.fileName" class="file-header">
      <h2 class="text-2xl font-bold">
        {{ props.fileName }}
      </h2>
    </div>

    <!-- 表格容器 -->
    <div v-if="props.parsedSubtitles.length" class="subtitle-table-container">
      <table class="subtitle-table">
        <thead class="bg-gray-100 dark:bg-gray-800">
          <tr>
            <!-- 开始时间 -->
            <th>{{ t('document_translation.subtitle.viewer_table_head_start_time') }}</th>
            <!-- 结束时间 -->
            <th>{{ t('document_translation.subtitle.viewer_table_head_end_time') }}</th>
            <!-- 内容 -->
            <th>{{ t('document_translation.subtitle.viewer_table_head_content') }}</th>
            <!-- 译文(可编辑) -->
            <th>{{ t('document_translation.subtitle.viewer_table_head_translation') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="subtitle in props.parsedSubtitles" :key="subtitle.id" class="ztls-subtitle-file-subtitle-row">
            <td>{{ subtitle.showStartTime }}</td>
            <td>{{ subtitle.showEndTime }}</td>
            <!-- 这个data-translation="true" 是给翻译插件用的，表示这个元素需要被翻译，data-ztsl-subtitle-file="y"用于标识是字幕文件翻译 -->
            <td class="content-cell" data-translation="true" data-ztsl-subtitle-file="y">
              {{ subtitle.originalText }}
            </td>
            <!-- 这个是翻译结果 -->
            <td
              class="content-cell"
              data-translation-result="true"
              @click="handleTranslationResultClick"
            />
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { usePluginTranslate } from '@/composables/usePluginTranslate.ts'
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import type { ParsedSubtitle } from '@/pages/file/subtitle/types/subtitle.d.ts'

const { t } = useI18n()
const documentTranslateStore = useDocumentTranslateStore()
const { translateEngine, targetLanguage } = storeToRefs(documentTranslateStore)

const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()

interface Props {
  fileName: string
  parsedSubtitles: ParsedSubtitle[]
}
const props = defineProps<Props>()

onMounted(() => {
  // 初始化翻译进度监控
  initTranslationProgressMonitor()
  // 向插件发起字幕翻译请求
  usePluginWebDocumentTranslate({
    type: 'translateFilePage',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: 'fulltext',
    documentType: 'subtitle'
  })
})

/**
 * 设置可编辑状态前判断是否存在翻译结果
 * @param e
 */
const handleTranslationResultClick = (e) => {
  // 检查是否存在译文如果存在译文就将 dom 改成 可编辑状态
  if (e.target.textContent.trim()) {
    // 找到最近的父级元素（或自身）拥有 data-translation-result="true"
    // 防止只在 翻译结果的节点编辑（这个节点是文本节点区域很小）
    const editableEl = e.target.closest('[data-translation-result="true"]')
    if (editableEl) {
      editableEl.contentEditable = 'true'
      editableEl.focus()
    }
  }
}
</script>

<style scoped>
.subtitle-table-container {
  margin-top: 1rem;
  overflow-x: auto;
  width: 100%;
}

.subtitle-table {
  width: 100%;
  table-layout: fixed;
}

.subtitle-table th {
  padding: 0.5rem;
  border-left: none;
  border-right: none;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  text-align: left;
  white-space: nowrap;
}

/* 黑暗模式下的表头样式优化 */
.dark .subtitle-table th {
  border-top: 1px solid #4a5568;
  border-bottom: 1px solid #4a5568;
  background-color: #2d3748;
  color: #e2e8f0;
}

.subtitle-table td {
  padding: 0.5rem;
  border-left: none;
  border-right: none;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  text-align: left;
  font-size: 0.875rem;
}

/* 黑暗模式下的表格单元格样式优化 */
.dark .subtitle-table td {
  border-top: 1px solid #4a5568;
  border-bottom: 1px solid #4a5568;
  color: #cbd5e0;
}

.content-cell {
  white-space: normal; /* 允许换行 */
  word-wrap: break-word; /* 长单词换行 */
  word-break: break-word; /* 在适当位置断行 */
}

/* 使用百分比分配宽度 */
.subtitle-table th:nth-child(1),
.subtitle-table td:nth-child(1) {
  width: 10%;
} /* 开始时间 */

.subtitle-table th:nth-child(2),
.subtitle-table td:nth-child(2) {
  width: 10%;
} /* 结束时间 */

.subtitle-table th:nth-child(3),
.subtitle-table td:nth-child(3) {
  width: 40%;
} /* 内容 */

.subtitle-table th:nth-child(4),
.subtitle-table td:nth-child(4) {
  width: 40%;
} /* 译文 */

.subtitle-table tr:nth-child(even) {
  background-color: transparent;
}

.subtitle-table tr:hover {
  background-color: #f1f5f9;
}

/* 优化黑暗模式下的hover效果 */
.dark .subtitle-table tr:hover {
  background-color: #4a5568;
}

.ztls-subtitle-file-subtitle-row {
  transition: background-color 0.2s ease;
}

.file-header {
  padding: 1rem 0;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 黑暗模式下的文件头部样式优化 */
.dark .file-header {
  border-bottom: 1px solid #4a5568;
}
</style>
