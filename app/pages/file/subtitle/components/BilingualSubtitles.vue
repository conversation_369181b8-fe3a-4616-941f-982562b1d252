<template>
  <UModal v-model:open="modalVisible" :ui="{ footer: 'justify-end' }" :title="t('document_translation.subtitle.export_settings.title')">
    <template #body>
      <div class="space-y-6">
        <!-- 字幕顺序设置 -->
        <div class="flex items-center">
          <h4 class="text-md mr-4 font-medium">
            {{ t('document_translation.subtitle.export_settings.subtitle_order') }}
          </h4>
          <URadioGroup
            v-model="localSettings.order"
            orientation="horizontal"
            indicator="start"
            variant="list"
            :items="subtitlesOrder"
          />
        </div>

        <!-- 字幕轨道设置 -->
        <div class="flex items-center">
          <h4 class="text-md mr-4 font-medium">
            {{ t('document_translation.subtitle.export_settings.subtitle_track') }}
          </h4>
          <URadioGroup
            v-model="localSettings.track"
            orientation="horizontal"
            indicator="start"
            variant="list"
            :items="subtitlesTrack"
          />
        </div>
      </div>
    </template>

    <template #footer>
      <!-- 取消 -->
      <UButton
        :label="t('common.cancel')"
        color="neutral"
        variant="outline"
        size="md"
        class="rounded-md px-4 text-sm"
        @click="cancel"
      />
      <!-- 确认 -->
      <UButton
        :label="t('common.confirm')"
        size="md"
        color="primary"
        variant="solid"
        class="rounded-sm px-4 text-sm"
        @click="confirm"
      />
    </template>
  </UModal>
</template>

<script setup lang="ts">
import type { RadioGroupItem } from '@nuxt/ui'

const { t } = useI18n()

// 字幕顺序
const subtitlesOrder = ref<RadioGroupItem[]>([
  { value: 'translationFirst', label: t('document_translation.subtitle.export_settings.translation_first') }, // 译文在前
  { value: 'originalFirst', label: t('document_translation.subtitle.export_settings.original_first') } // 原文在前
])

// 字幕轨道
const subtitlesTrack = ref<RadioGroupItem[]>([
  { value: 'single', label: t('document_translation.subtitle.export_settings.single_track') }, // 单轨
  { value: 'double', label: t('document_translation.subtitle.export_settings.double_track') } // 双轨
])

// 字幕设置的类型定义
export interface SubtitleSettings {
  order: 'translationFirst' | 'originalFirst'
  track: 'single' | 'double'
}

const props = defineProps<{
  visible: boolean
  settings: SubtitleSettings
}>()

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
  (e: 'update:settings' | 'save', value: SubtitleSettings): void
  (e: 'cancel'): void
}>()

// 控制模态框显示
const modalVisible = computed({
  get: () => props.visible,
  set: value => emit('update:open', value)
})

// 本地设置副本，用于临时修改
const localSettings = ref<SubtitleSettings>({ ...props.settings })

// 监听设置变化，同步本地副本
watch(
  () => props.settings,
  (newSettings) => {
    localSettings.value = { ...newSettings }
  },
  { deep: true }
)

// 确认保存设置
const confirm = () => {
  emit('update:settings', localSettings.value)
  emit('save', localSettings.value)
  emit('update:open', false)
}

// 取消操作
const cancel = () => {
  // 重置为传入的设置
  localSettings.value = { ...props.settings }
  emit('cancel')
  emit('update:open', false)
}
</script>
