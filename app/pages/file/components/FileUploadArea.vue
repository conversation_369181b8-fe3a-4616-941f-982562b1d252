<!-- 文件打开区域， 没有打开文件时默认页面 -->
<template>
  <div
    class="relative flex flex-1 items-center justify-center"
    @dragenter.prevent="handleDragEnter"
    @dragover.prevent="handleDragOver"
    @dragleave.prevent="handleDragLeave"
    @drop.prevent="handleFileDrop"
  >
    <div class="mx-auto text-center">
      <div v-if="!isDragOver">
        <!-- 文档图标 -->
        <div class="mb-6">
          <img
            :src="documentConfig[documentType].icon"
            :alt="documentConfig[documentType].label"
            class="mx-auto size-10 sm:size-12 md:size-16 lg:size-20 select-none"
            draggable="false"
          />
        </div>

        <!-- 主标题 -->
        <h2 class="mb-3 text-2xl font-semibold text-gray-900 dark:text-white">
          {{ documentConfig[documentType].label }}
        </h2>

        <!-- 副标题 -->
        <p class="mb-8 text-gray-600 dark:text-gray-400">
          {{ t('free_pdf_translation.empty_state_description') }}
        </p>

        <!-- 操作按钮 -->
        <div class="space-y-4">
          <UButton
            size="lg"
            color="primary"
            class="rounded-md"
            @click="handleFileSelect"
          >
            <UIcon name="i-heroicons-folder-open" class="h-5 w-5" />
            <!-- 选择文件 -->
            {{ t('free_pdf_translation.select_file') }}
          </UButton>
        </div>
      </div>

      <!-- 拖拽提示 -->
      <div v-else class="bg-primary-50 dark:bg-primary-900/30 border-primary-400 absolute inset-0 flex items-center justify-center rounded-lg border-2 border-dashed">
        <div class="text-center">
          <UIcon name="i-heroicons-arrow-down-tray" class="text-primary-500 mx-auto mb-2 h-12 w-12" />
          <p class="text-primary-600 dark:text-primary-400 font-medium">
            {{ t('free_pdf_translation.drop_file_here') }}
          </p>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <FileSelect ref="fileSelect" :document-type="documentType" @file-selected="handleFileSelected" />
  </div>
</template>

<script setup lang="ts">
import FileSelect from './FileSelect.vue'

interface Props {
  documentType: 'pdf' | 'docx' | 'epub' | 'html' | 'txt' | 'markdown' | 'subtitle'
}

const props = withDefaults(defineProps<Props>(), {})
const { t, locale } = useI18n()

const emit = defineEmits<{
  fileSelected: [file: File]
  error: [message: string]
}>()

const fileSelect = ref<InstanceType<typeof FileSelect>>()

const isDragOver = ref(false)

// 文档类型配置
const documentConfig = {
  pdf: {
    icon: '/assets/images/document/bi--file-earmark-pdf.svg',
    accept: '.pdf',
    label: t('document_translation.pdf.open_file_tip') // 打开 PDF 文件开始翻译
  },
  docx: {
    icon: '/assets/images/document/bi--file-earmark-word2.svg',
    accept: '.doc,.docx',
    label: t('document_translation.docx.open_file_tip') // 打开 docx 文件开始翻译
  },
  epub: {
    icon: '/assets/images/document/bi--file-earmark-epub.svg',
    accept: '.epub',
    label: t('document_translation.epub.open_file_tip') // 打开 epub 文件开始翻译
  },
  html: {
    icon: '/assets/images/document/bi--file-earmark-html.svg',
    accept: '.html,.htm,.txt',
    label: t('document_translation.html.open_file_tip') // 打开 html 文件开始翻译
  },
  markdown: {
    icon: '/assets/images/document/bi--file-earmark-markdown2.svg',
    accept: '.md,.markdown',
    label: t('document_translation.markdown.open_file_tip') // 打开 markdown 文件开始翻译
  },
  subtitle: {
    icon: '/assets/images/document/bi--file-earmark-cc.svg',
    accept: '.srt,.vtt,.ass,.ssa,.sbv,.lrc',
    label: t('document_translation.subtitle.open_file_tip') // 打开 srt/vtt/ass/ssa/sbv/lrc 文件开始翻译
  }
}

const config = computed(() => documentConfig[props.documentType])
const iconName = computed(() => config.value.icon)

// 处理文件选择
const handleFileSelect = () => {
  fileSelect.value?.openFileSelect()
}

const handleFileSelected = (file: File) => {
  if (file) {
    emit('fileSelected', file)
  }
}

// 拖拽处理
const handleDragEnter = () => {
  isDragOver.value = true
}

const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  // 只有当离开整个拖拽区域时才隐藏提示
  const currentTarget = event.currentTarget as Element
  const relatedTarget = event.relatedTarget as Node
  if (!currentTarget?.contains(relatedTarget)) {
    isDragOver.value = false
  }
}

const handleFileDrop = (event: DragEvent) => {
  isDragOver.value = false

  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    const file = files[0]

    emit('fileSelected', file)
  }
}
</script>

<style scoped>
/* 组件样式 */
</style>
