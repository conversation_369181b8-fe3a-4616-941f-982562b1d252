<!-- 文档翻译布局 -->
<template>
  <div class="flex flex-col h-screen">
    <!-- 顶部 -->
    <DocumentTransHeader
      :document-type="documentType"
      :show-switch-bilingual="showSwitchBilingual"
      @file-open="handleFileOpen"
    >
      <template #right>
        <slot name="right" />
      </template>
    </DocumentTransHeader>

    <div class="h-(--ui-header-height) min-h-(--ui-header-height)" />

    <slot v-if="hasFile" />

    <!-- loading -->
    <div v-if="loading" class="fixed inset-0 bg-white bg-opacity-80 dark:bg-gray-900 dark:bg-opacity-80 flex items-center justify-center z-50">
      <UseLoading
        size="large"
        :z-index="50"
      />
    </div>

    <!-- 解析错误状态 -->
    <div v-if="parseError" class="fixed inset-0 bg-white bg-opacity-95 dark:bg-gray-900 dark:bg-opacity-95 flex items-center justify-center z-50">
      <div class="text-center max-w-md mx-auto p-6">
        <UIcon name="i-heroicons-exclamation-triangle" class="mx-auto h-16 w-16 text-red-500 mb-4" />
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          <!-- 解析错误 -->
          {{ $t('document_translation.parse_error') }}
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          {{ parseError }}
        </p>
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
          <UButton
            color="primary"
            size="lg"
            @click="handleRetryParse"
          >
            <UIcon name="i-heroicons-arrow-path" class="h-5 w-5" />
            <!-- 重试 -->
            {{ $t('common.retry') }}
          </UButton>
          <UButton
            variant="outline"
            size="lg"
            @click="handleSelectNewFile"
          >
            <UIcon name="i-heroicons-folder-open" class="h-5 w-5" />
            <!-- 选择新文件 -->
            {{ $t('document_translation.select_new_file') }}
          </UButton>
        </div>
      </div>
    </div>

    <!-- 空状态 - 没有文件时显示 -->
    <FileUploadArea v-if="!hasFile" :document-type="documentType" @file-selected="handleFileSelected" />
  </div>

  <!-- 隐藏的文件输入 -->
  <FileSelect ref="fileSelect" :document-type="documentType" @file-selected="handleFileSelected" />
</template>

<script setup lang="ts">
import FileUploadArea from '../components/FileUploadArea.vue'
import DocumentTransHeader from '@/pages/components/DocumentTransHeader/index.vue'
import { useFileStore } from '@/store/FileStore.ts'
import FileSelect from './FileSelect.vue'
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { useMobileDetection } from '@/composables/useMobileDetection.ts'
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'

const documentTranslateStore = useDocumentTranslateStore()
const { enabledEnginesList, translateEngine } = storeToRefs(documentTranslateStore)

// 移动端响应式检测
const { isMobile } = useMobileDetection()

const fileStore = useFileStore()
const { uploadedFile } = storeToRefs(fileStore)
const { locale } = useI18n()

const fileSelect = ref<InstanceType<typeof FileSelect>>()

interface Props {
  documentType: 'pdf' | 'docx' | 'epub' | 'html' | 'txt' | 'markdown' | 'subtitle'
  showSwitchBilingual?: boolean // 是否开启双语对照切换
  loading?: boolean
  parseError?: string
}

const props = withDefaults(defineProps<Props>(), {
  showSwitchBilingual: false, // 是否开启双语对照切换
  loading: false,
  parseError: ''
})

const emit = defineEmits<{
  fileSelected: [file: File]
  error: [message: string]
  retryParse: [file: File] // 重试解析事件
}>()

// 是否存在文件
const hasFile = computed(() => {
  return uploadedFile.value !== null && !props.parseError
})

/**
 * 初始化翻译配置
 * 如果没有翻译引擎，默认使用微软
 */
const initTranslationConfig = async () => {
  await sendMessageToChromeExtension(locale.value, translateEngine.value?.value || 'microsoft')

  // 确保翻译引擎有默认值
  if (!translateEngine.value || !translateEngine.value.value) {
    translateEngine.value = enabledEnginesList.value[0]
  }
}

onMounted(() => {
  initTranslationConfig()
})

/**
 * 文件选择
 * @param file 文件
 */
const handleFileSelected = (file: File) => {
  emit('fileSelected', file)
}

const handleFileOpen = () => {
  fileSelect.value?.openFileSelect()
}

// 组件离开的时候清空
onBeforeUnmount(() => {
  uploadedFile.value = null
})

/**
 * 重试解析当前文件
 */
const handleRetryParse = () => {
  if (uploadedFile.value) {
    emit('retryParse', uploadedFile.value)
  }
}

/**
 * 选择新文件
 */
const handleSelectNewFile = () => {
  // 清空当前文件
  uploadedFile.value = null
  // 打开文件选择器
  fileSelect.value?.openFileSelect()
}
</script>
