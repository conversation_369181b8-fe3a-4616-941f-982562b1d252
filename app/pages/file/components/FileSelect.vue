<template>
  <input
    ref="fileInput"
    type="file"
    :accept="acceptedTypes"
    class="hidden"
    @change="handleFileChange"
  />
</template>

<script setup lang="ts">
interface Props {
  documentType: 'pdf' | 'docx' | 'epub' | 'html' | 'txt' | 'markdown' | 'subtitle'
}

const props = withDefaults(defineProps<Props>(), {})

const emit = defineEmits<{
  fileSelected: [file: File]
  error: [message: string]
}>()

const documentConfig = {
  pdf: {
    accept: '.pdf',
    mimeTypes: ['application/pdf']
  },
  docx: {
    accept: '.doc,.docx',
    mimeTypes: ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  },
  epub: {
    accept: '.epub',
    mimeTypes: ['application/epub+zip']
  },
  html: {
    accept: '.html,.htm,.txt',
    mimeTypes: ['text/html', 'text/plain']
  },
  markdown: {
    accept: '.md,.markdown',
    mimeTypes: ['text/markdown', 'text/x-markdown']
  },
  subtitle: {
    accept: '.srt,.vtt,.ass,.ssa,.sbv,.lrc',
    mimeTypes: ['text/plain', 'text/vtt']
  }
}

const acceptedTypes = computed(() => {
  const config = documentConfig[props.documentType]
  if (!config) {
    console.warn(`Unsupported document type: ${props.documentType}`)
    return ''
  }
  return config.accept
})

const fileInput = ref<HTMLInputElement>()

// 验证文件类型
const validateFile = (file: File): boolean => {
  const config = documentConfig[props.documentType]
  if (!config) {
    console.warn(`Unsupported document type: ${props.documentType}`)
    return false
  }
  const fileName = file.name.toLowerCase()
  const fileType = file.type.toLowerCase()

  // 检查文件扩展名
  const acceptedExtensions = config.accept.split(',').map(ext => ext.trim())
  const hasValidExtension = acceptedExtensions.some((ext) => {
    const extension = ext.startsWith('.') ? ext : `.${ext}`
    return fileName.endsWith(extension)
  })

  // 检查MIME类型
  const hasValidMimeType = config.mimeTypes.some(mimeType => fileType === mimeType || fileType.startsWith(mimeType))

  return hasValidExtension || hasValidMimeType
}

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    if (validateFile(file)) {
      emit('fileSelected', file)
    }
    else {
      emit('error', `请选择有效的${props.documentType.toUpperCase()}文件`)
    }
  }

  // 重置input值，允许重复选择同一文件
  target.value = ''
}

const openFileSelect = () => {
  fileInput.value?.click()
}

defineExpose({
  openFileSelect
})
</script>
