<script setup lang="ts">
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'

const documentTranslateStore = useDocumentTranslateStore()
const { translateEngine, targetLanguageZingCode, translationsDisplay, targetLanguage, targetLanguageList } = storeToRefs(documentTranslateStore)// 操作切换目标语言

const { locale } = useI18n()

// 响应式
const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()

interface Props {
  totalPages?: number // 总页数
  documentType?: 'pdf' | 'pdf-plus' | 'docx' | 'epub' | 'html' | 'txt' | 'markdown' | 'subtitle' | 'miner-u'
}

const props = withDefaults(defineProps<Props>(), {})

/**
 * 根据文档类型使用 显示模式
 * pdf免费、字幕（subtitle），不支持切换 双语对照/全文模式
 * 注意要使用计算属性
 */
const getTranslationsDisplay = computed(() => {
  if (['pdf', 'subtitle', 'docx'].includes(props.documentType)) {
    return 'fulltext'
  }
  return translationsDisplay.value
})

// 翻译选项数据- 语言集
const languageList = computed({
  get() {
    return targetLanguageList.value
  },
  set(newValue) {
    // 通过 Store 方法或直接修改响应式变量
    targetLanguageList.value = newValue
  }
})

async function onSelectionChangeTargetLanguage() {
  // 更新本地存储目标语言
  documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value)

  // 更新目标语言配置
  await updateTargetLanguage()

  // 初始化翻译进度监控
  initTranslationProgressMonitor()

  // 重新翻译
  usePluginWebDocumentTranslate({
    type: 'reTranslateFilePage',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: getTranslationsDisplay.value,
    totalPages: props.totalPages,
    documentType: props.documentType
  })
}

// 操作切换翻译引擎
async function onSelectionChangeTranslateEngine(data: { value: string }) {
  // 使用该方法可以更新切换的翻译引擎的 语言集配置 以及模型列表
  await sendMessageToChromeExtension(locale.value, data.value)
  // 更新目标语言配置
  await updateTargetLanguage()

  // 初始化翻译进度监控
  initTranslationProgressMonitor()

  // 重新翻译
  usePluginWebDocumentTranslate({
    type: 'reTranslateFilePage',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: getTranslationsDisplay.value,
    totalPages: props.totalPages,
    documentType: props.documentType
  })
}

async function updateTargetLanguage() {
  // 从语言集合获取对应的lang_code
  const languageItem = languageList.value.find((item) => {
    return item.value === targetLanguageZingCode.value
  })

  if (languageItem) {
    targetLanguage.value = languageItem.lang_code
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
  else {
    // 从语言集合获取对应的lang_code， 不能直接赋值 zh-Hans, 因为部分模型 不支持
    const languageItem = languageList.value.find((item) => {
      return item.value === 'zh-Hans'
    })

    // 如果找不到，则将目标语言设置为简体中文
    targetLanguage.value = languageItem.lang_code
    targetLanguageZingCode.value = languageItem.zing_code

    documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value) // 更新目标语言智应编码
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
}

function onSelectionChangeTranslationsDisplay() {
  documentTranslateStore.setTranslationsDisplay(translationsDisplay.value)

  // 初始化翻译进度监控
  initTranslationProgressMonitor()

  // 取消翻译
  usePluginWebDocumentTranslate({
    type: 'reTranslateFilePage',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: getTranslationsDisplay.value,
    documentType: props.documentType
  })
}

// 暴露方法给父组件调用
defineExpose({
  onSelectionChangeTranslationsDisplay,
  onSelectionChangeTargetLanguage,
  onSelectionChangeTranslateEngine
})
</script>
