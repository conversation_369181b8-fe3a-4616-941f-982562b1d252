<script setup lang="ts">
definePageMeta({
  layout: 'docs' // 使用文档布局(全屏)
})

const route = useRoute()

const { locale } = useI18n()

// 文档目录路径（保持原始大小写）
const catalogsPath = route.path.toLowerCase()

const { data: navigation } = await useAsyncData('navigation', () => queryCollectionNavigation(`${locale.value}_docs`))
// console.log('navigation:', navigation.value);
const navigation_links = navigation.value[0].children[0].items
// console.log('navigation_links:', navigation_links);

// 文件内容搜索
const { data: files } = useLazyAsyncData('search', () => queryCollectionSearchSections(`${locale.value}_docs`), {
  server: false
})
// console.log('search files:', files.value);

// 根据文档路径获取文档内容
const { data: page, error: pageError } = await useAsyncData(catalogsPath, () => queryCollection(`${locale.value}_docs`).path(catalogsPath).first())
// console.log('page', page.value);
// console.log('pageError', pageError.value);

// 如果页面不存在则抛出404错误
if (!page.value) {
  throw createError({
    statusCode: 404,
    statusMessage: `Page not found for path: ${route.path}`, // 更具体的错误信息
    fatal: true
  })
}

/**
 * 获取文档上下文
 * 这个上下文是根据 目录名称来排序的，如果需要修改上下文关联的文档需要手动将content 的实际目录名称的前缀数字，
 * 比如 比如你想要让，"1.start" 文件放在 "2.service"的后面， 你只需要修改文件名的前缀数字就行
 */
const { data: surround } = await useAsyncData(`${catalogsPath}-surround`, () => {
  return queryCollectionItemSurroundings(`${locale.value}_docs`, catalogsPath, {
    fields: ['description']
  })
})
// console.log('surround', surround.value);

// 设置SEO元数据
useSeoMeta({
  title: page.value?.title,
  ogTitle: page.value?.title,
  description: page.value?.description,
  ogDescription: page.value?.description
})

onMounted(async () => {
  // 获取所有的a标签，设置target为_blank，解决Markdown中链接在新窗口打开的问题
  const faq_content_divs = document.querySelectorAll('[class="custom_docs_content"]')
  faq_content_divs.forEach(function (div) {
    div.querySelectorAll('a').forEach(function (link) {
      link.setAttribute('target', '_blank')
      link.setAttribute('rel', 'noopener noreferrer')
    })
  })
})

defineOgImageComponent('Saas')
</script>

<template>
  <!-- 2 -->
  <UPage v-if="page">
    <!-- 22 -->
    <template #left>
      <UPageAside>
        <template #top>
          <!-- 搜索组件 -->
          <UContentSearchButton :collapsed="false" class="w-full rounded-md" />
        </template>
        <!-- 文档目录 -->
        <UContentNavigation :navigation="navigation_links" highlight />
      </UPageAside>
    </template>

    <!-- 文档头部 -->
    <UPageHeader
      v-if="page?.title && page?.description && page?.links"
      :title="page.title"
      :description="page.description"
      :links="page.links"
    />

    <UPageBody>
      <!-- 文档内容 -->
      <ContentRenderer v-if="page?.body" :value="page" class="custom_docs_content" />

      <!-- 文档上下文分隔符 -->
      <USeparator v-if="surround?.length" />

      <!-- 前后文档（前一篇，后一篇） -->
      <UContentSurround :surround="surround" />
    </UPageBody>

    <!-- 注意添加判断 不然会报错-渲染不出来 -->
    <template v-if="page?.body?.toc?.links?.length" #right>
      <!-- 文档内容的目录 -->
      <UContentToc :title="$t('tutorial.table_of_contents')" :links="page.body?.toc?.links" />
    </template>

    <ClientOnly>
      <!-- ClientOnly 包裹 ContentSearch 组件仅限客户端组件，因此它不会在服务器上呈现 -->
      <!-- 内容搜索组件（全局），通过搜索按钮 UContentSearchButton 触发 -->
      <LazyUContentSearch
        :files="files"
        shortcut="meta_k"
        :navigation="navigation[0].children[0].children"
        :fuse="{ resultLimit: 42 }"
        :color-mode="false"
      />
    </ClientOnly>
  </UPage>
</template>
