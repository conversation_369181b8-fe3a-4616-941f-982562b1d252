<template>
  <div class="mx-auto mt-6 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mt-6">
      <UCard>
        <template #header>
          <!-- 服务订单 -->
          <h3 class="text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
            {{ t('billing.service_order') }}
          </h3>
        </template>
        <UTable :data="productOrderList" :columns="productOrderColumns" :loading="isLoading">
          <template #create_datetime-cell="{ row }">
            <div v-if="(row.original as ProductOrder).create_datetime">
              {{ dayjs((row.original as ProductOrder).create_datetime).format('YYYY-MM-DD') }}
              <br />
              {{ dayjs((row.original as ProductOrder).create_datetime).format('HH:mm:ss') }}
            </div>
          </template>

          <template #pay_order_no-cell="{ row }">
            <!-- 订单编号 -->
            <div v-if="(row.original as ProductOrder).status === 'refunded_success'">
              <!-- 退款成功 -->
              {{ (row.original as ProductOrder).pay_order_no }}
              <div class="inline-block">
                <UPopover mode="hover" arrow :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }">
                  <span class="refund-icon">{{ t('billing.refund_order.refund_success') }}</span>
                  <template #content>
                    <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                      {{ `${t('billing.refund_order.refund_amount')}: ¥ ${(row.original as ProductOrder).refund_amount}` }}
                      <br />
                      {{ `${t('billing.refund_order.refund_time')}: ${dayjs((row.original as ProductOrder).refund_time).format('YYYY-MM-DD HH:mm:ss')}` }}
                    </div>
                  </template>
                </UPopover>
              </div>
            </div>

            <div v-else>
              <!-- 订单号 -->
              {{ (row.original as ProductOrder).pay_order_no }}
            </div>
          </template>

          <template #product_name-cell="{ row }">
            <!-- 资源包详情 -->
            <div v-if="(row.original as ProductOrder).status === 'general_token'">
              <!-- 翻译额度加量包 -->
              {{ t('billing.product_names.general_token_name') }}
              <!-- 数量: M，总计 XXXX TQ -->
              <UPopover
                mode="hover"
                class="cursor-pointer align-bottom"
                arrow
                :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
              >
                <UIcon name="heroicons:information-circle" class="size-5 shrink-0 text-neutral-400" />
                <template #content>
                  <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                    {{
                      `${t('billing.product_names.quantity')}: ${(row.original as ProductOrder).buy_count}，${t('billing.product_names.tatal')} ${OrderTypeEnums.find((item) => item.value === (row.original as ProductOrder).order_type)?.label || 'None'} ${(row.original as ProductOrder).resource_pack_quantity} TQ`
                    }}
                  </div>
                </template>
              </UPopover>
            </div>
            <div v-else-if="(row.original as ProductOrder).order_type === 'pdf_plus'">
              <!-- PDF Plus 加量包 -->
              {{ t('billing.product_names.pdf_plus_name') }}
              <!-- 数量: M，总计 XXXX T页 -->
              <UPopover
                mode="hover"
                class="cursor-pointer align-bottom"
                arrow
                :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
              >
                <UIcon name="heroicons:information-circle" class="size-5 shrink-0 text-neutral-400" />
                <template #content>
                  <div class="block max-w-70 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                    {{
                      `${t('billing.product_names.quantity')}: ${(row.original as ProductOrder).buy_count}，${t('billing.product_names.tatal')} ${OrderTypeEnums.find((item) => item.value === (row.original as ProductOrder).order_type)?.label || 'None'} ${(row.original as ProductOrder).resource_pack_quantity} ${t('billing.product_names.pages')}`
                    }}
                  </div>
                </template>
              </UPopover>
            </div>
            <div v-else-if="(row.original as ProductOrder).order_type === 'plus'">
              <!-- Plus 会员 -->
              {{ t('billing.product_names.plus_name') }}
            </div>
            <div v-else>
              {{ (row.original as ProductOrder).product_name }}
            </div>
          </template>
          <template #pay_way-cell="{ row }">
            <div v-if="(row.original as ProductOrder).pay_way === 'Stripe' && (row.original as ProductOrder).pay_receipt_url && (row.original as ProductOrder).pay_receipt_url.trim().length > 0">
              {{ (row.original as ProductOrder).pay_way }}
              <UTooltip :text="t('billing.title')" class="cursor-pointer align-bottom" :popper="{ placement: 'right' }">
                <!-- 账单 -->
                <UIcon name="fluent:document-bullet-list-24-regular" class="h-5 w-5" @click="openPayReceipt(row)" />
              </UTooltip>
            </div>
            <div v-else>
              {{ (row.original as ProductOrder).pay_way }}
            </div>
          </template>
          <template #service_begin_time-cell="{ row }">
            <div v-if="(row.original as ProductOrder).service_begin_time">
              {{ dayjs((row.original as ProductOrder).service_begin_time).format('YYYY-MM-DD') }}
              <br />
              {{ dayjs((row.original as ProductOrder).service_begin_time).format('HH:mm:ss') }}
            </div>
          </template>
          <template #service_end_time-cell="{ row }">
            <div v-if="(row.original as ProductOrder).service_end_time">
              {{ dayjs((row.original as ProductOrder).service_end_time).format('YYYY-MM-DD') }}
              <br />
              {{ dayjs((row.original as ProductOrder).service_end_time).format('HH:mm:ss') }}
            </div>
          </template>
        </UTable>
        <div class="flex justify-end border-t border-gray-200 px-3 py-3.5 dark:border-gray-700">
          <UPagination
            v-model:page="currentPage"
            size="md"
            :default-page="1"
            :items-per-page="pageCount"
            :total="totalCount"
            :sibling-count="1"
            show-edges
            @update:page="changePage"
          />
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这里不需要引入 ref, reactive, computed, onMounted, h, resolveComponent 因为已经自动引入

import dayjs from 'dayjs'
import type { TableColumn } from '@nuxt/ui' // 表格列
import { getProductByPositionNoApi, getOrderPayReceiptUrlApi } from '@/api/product'
import type { GetProductType, MemberPayOrderType } from '@/api/types'
import type { HeadersTokenType, ProductOrderType } from '@/api/login/types'
import { getMemberProductOrderListApi } from '@/api/login'
import { selectDictLabel, type DictDetail } from '@/utils/dict'
import { useAuthStoreWithOut } from '@/store/modules/auth'

// definePageMeta 定义页面的布局和权限路由守卫，(此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'member',
  middleware: 'auth' // 使用 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
})

const { t, locale } = useI18n()

// 页面标题及描述，这个会员内部页面：不需要做SEO优化，所以不设置描述
useSeoMeta({
  titleTemplate: '',
  title: t('header.billing') + ' - ' + t('common.site_name') // 账单
})

const authStore = useAuthStoreWithOut()

const OrderTypeEnums = ref<DictDetail[]>([])
OrderTypeEnums.value = [
  // 订单类型（ plus:Plus 会员、general_token: 翻译额度(TQ)、pdf_plus:PDF Plus 页数 ）
  { value: 'plus', label: t('billing.product_names.plus_name') }, // Plus 会员
  { value: 'general_token', label: t('billing.product_names.translation_quota') }, //  翻译额度
  { value: 'pdf_plus', label: t('billing.product_names.pdf_plus') } // PDF Plus
]
/**
 * 购买模式字典
 */
const buyModeOptions = ref<DictDetail[]>([])
buyModeOptions.value = [
  { value: 'try', label: t('billing.buy_modes.try') }, // 试用
  { value: 'weekly', label: t('billing.buy_modes.weekly') }, // 按周(购买/订阅)
  { value: 'monthly', label: t('billing.buy_modes.monthly') }, // 按月(购买/订阅)
  { value: 'quarterly', label: t('billing.buy_modes.quarterly') }, // 按季(购买/订阅)
  { value: 'yearly', label: t('billing.buy_modes.yearly') }, // 按年(购买/订阅)
  { value: 'perpetual', label: t('billing.buy_modes.perpetual') } // 永久
]

/**
 * 支付方式字典
 */
const payWayOptions = ref<DictDetail[]>([])
payWayOptions.value = [
  { value: 'platform_gift', label: t('billing.pay_ways.platform_gift') }, // 平台赠送
  { value: 'wechat', label: t('billing.pay_ways.wechat') }, // '微信支付'
  { value: 'alipay', label: t('billing.pay_ways.alipay') }, // '支付宝'
  { value: 'stripe', label: t('billing.pay_ways.stripe') }, // 'Stripe'
  { value: 'coupon_code', label: t('billing.pay_ways.coupon_code') } // '兑换码'
]

/**
 * 产品服务订单列表
 */
interface ProductOrder {
  index: number
  create_datetime: any
  pay_order_no: string
  product_name: string
  buy_mode: string
  amount: number | string
  pay_way: string
  service_begin_time: any
  service_end_time: any
  pay_receipt_url: string
  resource_pack_quantity: number
  resource_quantity: number
  order_type: string
  buy_count: number
  status: string // 订单状态
  refund_amount?: number | string // 退款金额
  refund_time?: any // 退款时间
}

const orderListData = ref([])
const currentPage = ref(1)
const pageCount = ref(10) // 每页显示的条数
const totalCount = ref(0)
const productOrderList = ref<ProductOrder[]>([])
const isLoading = ref(false)

/**
 * 获取产品服务订单列表
 */
async function getMemberProductOrderPageData(page: number) {
  isLoading.value = true
  try {
    const headers: HeadersTokenType = {
      token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
    }
    const paramData: ProductOrderType = {
      platform: 'ztsl',
      page: page,
      limit: pageCount.value
    }

    const res = await getMemberProductOrderListApi(headers, paramData)
    orderListData.value = res.data
    totalCount.value = res.count || 0

    const orderListDict = orderListData.value.map((item: ProductOrder, index) => {
      return {
        index: index + 1,
        create_datetime: item.create_datetime,
        pay_order_no: item.pay_order_no,
        product_name: item.product_name,
        buy_mode: `${selectDictLabel(unref(buyModeOptions), item.buy_mode)}`,
        amount: '¥ ' + item.amount,
        pay_way: `${selectDictLabel(unref(payWayOptions), item.pay_way)}`,
        service_begin_time: item.service_begin_time,
        service_end_time: item.service_end_time,
        pay_receipt_url: item.pay_receipt_url,
        resource_pack_quantity: item.resource_pack_quantity,
        resource_quantity: item.resource_quantity,
        order_type: item.order_type,
        buy_count: item.buy_count,
        status: item.status, // 订单状态
        refund_amount: item.refund_amount, // 退款金额
        refund_time: item.refund_time // 退款时间
      }
    })
    // const jsonList = JSON.stringify(orderListDict)
    // alert(jsonList)
    productOrderList.value = orderListDict
  }
  finally {
    isLoading.value = false
  }
}

/**
 * 产品服务订单列表表头
 */
const productOrderColumns: TableColumn<ProductOrder>[] = [
  {
    accessorKey: 'index',
    header: t('billing.order_columns.index') // 序号
  },
  {
    accessorKey: 'create_datetime',
    header: t('billing.order_columns.pay_time') // '付款时间'
  },
  {
    accessorKey: 'pay_order_no',
    header: t('billing.order_columns.pay_order_no') // '订单号'
  },
  {
    accessorKey: 'product_name',
    header: t('billing.order_columns.product_name') // '产品名称'
  },
  {
    accessorKey: 'buy_mode',
    header: t('billing.order_columns.buy_mode') // '购买方式'
  },
  {
    accessorKey: 'amount',
    header: t('billing.order_columns.amount') // '金额'
  },
  {
    accessorKey: 'pay_way',
    header: t('billing.order_columns.pay_way') // '支付方式'
  },
  {
    accessorKey: 'service_begin_time',
    header: t('billing.order_columns.service_begin_time') // '服务开始时间'
  },
  {
    accessorKey: 'service_end_time',
    header: t('billing.order_columns.service_end_time') // '服务结束时间'
  }
]

/**
 * 分页切换
 * @param page
 */
function changePage(page: number) {
  currentPage.value = page
  getMemberProductOrderPageData(page)
}

async function openPayReceipt(row: any) {
  console.log(row.original)
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  const queryParams: MemberPayOrderType = {
    order_no: row.original.pay_order_no
  }
  const url = (await getOrderPayReceiptUrlApi(headers, queryParams)).message
  window.open(url, '_blank')
}

/** 平台在售产品信息 */
const product = ref({})

onMounted(async () => {
  isLoading.value = true
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const paramData: GetProductType = {
    platform: 'ztsl',
    position_no: 'ztsl-plus'
  }
  product.value = (await getProductByPositionNoApi(paramData)).data

  // 加载第1页
  currentPage.value = 1
  getMemberProductOrderPageData(1)
})
</script>

<style scoped>
.refund-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  /* 文字控制 */
  color: #56595e !important;
  font-weight: normal !important;
  font-size: 12px;
  line-height: 1;
  white-space: nowrap; /* 防止文字换行 */

  /* 边框控制 */
  border-radius: 3px;
  background: #e0e0e0;

  /* 智能间距 */
  padding: 4px 3px; /* 上下减小，左右保留 */
  box-sizing: border-box;

  /* 自动宽度 */
  width: auto; /* 默认值，可省略 */
  min-width: 0; /* 覆盖之前的固定宽度 */
}
</style>
