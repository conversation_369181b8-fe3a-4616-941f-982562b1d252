<template>
  <div class="mx-auto mt-4 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mt-6">
      <UCard class="relative mt-2 w-full">
        <template #header>
          <!-- 兑换码 -->
          <div class="flex flex-row">
            <div>
              <h3 class="text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
                {{ t('coupon.code.title') }}
              </h3>
            </div>
          </div>
        </template>
        <div class="text-center">
          <UIcon name="i-material-symbols-redeem" class="h-12 w-12 align-bottom text-amber-500 lg:h-16 lg:w-16" />
          <!-- 使用兑换码 -->
          <h1 class="p-2 text-2xl font-bold tracking-tight text-neutral-900 sm:text-2xl lg:text-3xl dark:text-neutral-200">
            {{ t('coupon.code.use_title') }}
          </h1>
        </div>
        <div class="mt-4 flex flex-col items-center justify-center gap-4 text-center tracking-tight">
          <!-- 请输入兑换码 -->
          <UInput
            v-model="code_value"
            size="xl"
            :placeholder="t('coupon.code.input_tips')"
            class="mb-4 w-72 lg:w-80"
          />
          <!-- 兑换 -->
          <UButton color="info" class="w-40 items-center justify-center rounded-md text-lg font-bold tracking-tight lg:w-48 lg:text-xl" @click="inputRedeemCode">
            {{ t('coupon.code.redeem') }}
          </UButton>
        </div>
        <div class="mt-8 text-sm text-neutral-600 dark:text-neutral-300">
          <!-- 兑换说明 -->
          <div class="font-bold">
            {{ t('coupon.code.instructions.title') }}:
          </div>
          <div class="mt-1">
            {{ t('coupon.code.instructions.instruction_01') }}
          </div>
          <div class="mt-1">
            {{ t('coupon.code.instructions.instruction_02') }}
          </div>
          <div class="mt-1">
            {{ t('coupon.code.instructions.instruction_03') }}
          </div>
          <div class="mt-1">
            {{ t('coupon.code.instructions.instruction_04') }}
          </div>
          <div class="mt-1">
            {{ t('coupon.code.instructions.instruction_05') }}
          </div>
        </div>
      </UCard>
    </div>
    <div class="mt-6">
      <UCard>
        <template #header>
          <!-- 我的兑换码 -->
          <div class="flex flex-row">
            <div>
              <h3 class="text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
                {{ t('coupon.code.my_code') }}
              </h3>
            </div>
            <div class="flex flex-auto justify-end">
              <USelect
                v-model="queryParams.used"
                :items="usedItems"
                class="w-32"
                @update:model-value="changeQuery"
              />
            </div>
          </div>
        </template>
        <UTable :data="couponCodeDataList" :columns="couponCodeColumnList">
          <template #product_specification-cell="{ row }">
            <!-- 兑换码对应的产品规格 -->
            <!-- 周会员 -->
            <span v-if="(row.original as any).product_specification === 'weekly'">{{ t('pricing.cycle_name.weekly') }}</span>
            <!-- 月度会员 -->
            <span v-if="(row.original as any).product_specification === 'monthly'">{{ t('pricing.cycle_name.monthly') }}</span>
            <!-- 季度会员 -->
            <span v-if="(row.original as any).product_specification === 'quarterly'">{{ t('pricing.cycle_name.quarterly') }}</span>
            <!-- 年度会员 -->
            <span v-if="(row.original as any).product_specification === 'yearly'">{{ t('pricing.cycle_name.yearly') }}</span>
          </template>
          <template #claim_time-cell="{ row }">
            <!-- 领取时间 -->
            {{ dayjs((row.original as any).claim_time).format('YYYY-MM-DD HH:mm') }}
          </template>
          <template #redemption_start_time-cell="{ row }">
            <!-- 兑换开始时间 -->
            {{ dayjs((row.original as any).redemption_start_time).format('YYYY-MM-DD HH:mm') }}
          </template>
          <template #redemption_end_time-cell="{ row }">
            <!-- 兑换结束时间 -->
            {{ dayjs((row.original as any).redemption_end_time).format('YYYY-MM-DD HH:mm') }}
          </template>
          <template #redemption_time-cell="{ row }">
            <!-- 兑换时间 -->
            {{ dayjs((row.original as any).redemption_time).format('YYYY-MM-DD HH:mm') }}
          </template>
          <template #code_status-cell="{ row }">
            <!-- 状态 -->
            <span v-if="(row.original as any).code_status === 2">
              <!-- 检查兑换状态 -->
              <UBadge
                v-if="getRedemptionStatus((row.original as any).redemption_start_time, (row.original as any).redemption_end_time) === 'not_started'"
                variant="subtle"
                color="neutral"
                class="capitalize"
              >
                未开启
              </UBadge>
              <UBadge
                v-else-if="getRedemptionStatus((row.original as any).redemption_start_time, (row.original as any).redemption_end_time) === 'expired'"
                variant="subtle"
                color="error"
                class="capitalize"
              >
                已过期
              </UBadge>
              <UBadge
                v-else
                variant="subtle"
                color="primary"
                class="capitalize"
              >待兑换</UBadge>
            </span>
            <UBadge
              v-if="(row.original as any).code_status === 3"
              variant="subtle"
              color="success"
              class="capitalize"
            >
              已兑换
            </UBadge>
          </template>
          <template #actions-cell="{ row }">
            <!-- 操作 -->
            <!-- 兑换 -->
            <UButton
              v-if="(row.original as any).code_status === 2 && getRedemptionStatus((row.original as any).redemption_start_time, (row.original as any).redemption_end_time) === 'valid'"
              color="info"
              class="rounded-md"
              :label="t('coupon.code.table.redeem')"
              :loading="redeemingCode === (row.original as any).code_value"
              @click="redeemCode(row)"
            />
          </template>
        </UTable>
        <div class="flex justify-end border-t border-gray-200 px-3 py-3.5 dark:border-gray-700">
          <UPagination
            v-model:page="currentPage"
            :default-page="1"
            :items-per-page="pageCount"
            :total="totalCount"
            show-edges
            @update:page="changePage"
          />
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { couponCodeRedeem, couponCodeMemberList } from '@/api/product'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import type { HeadersTokenType } from '@/api/login/types'

const { t } = useI18n()

useSeoMeta({
  titleTemplate: '',
  title: t('coupon.code.my_code') + ' - ' + t('common.site_name'), // '我的兑换码 - 网站名称'
  ogTitle: t('coupon.code.my_code') + ' - ' + t('common.site_name'), // '我的兑换码 - 网站名称'
  description: t('common.description'),
  ogDescription: t('common.description')
})

defineOgImageComponent('Saas')

const couponCodeDataList = ref<any[]>([])
const couponCodeColumnList = [
  {
    header: t('coupon.code.table.redemption_code'), // 兑换码
    accessorKey: 'code_value'
  },
  {
    header: t('coupon.code.table.product_specification'), // 商品规格
    accessorKey: 'product_specification'
  },
  // {
  //   header: t('coupon.code.table.usage_type'), // 使用类型
  //   accessorKey: 'usage_type_name'
  // },
  {
    header: t('coupon.code.table.claim_time'), // 领取时间
    accessorKey: 'claim_time'
  },
  {
    header: t('coupon.code.table.redemption_start_time'), // 兑换开始时间
    accessorKey: 'redemption_start_time'
  },
  {
    header: t('coupon.code.table.redemption_end_time'), // 兑换结束时间
    accessorKey: 'redemption_end_time'
  },
  {
    header: t('coupon.code.table.redemption_time'), // 兑换时间
    accessorKey: 'redeem_time'
  },
  {
    header: t('coupon.code.table.status'), // 状态(兑换码状态(1.未领取、2.已领取、3.已兑换、4.已作废)1.未领取:预发放的码,默认状态为"未领取";2.已领取:当领取人不为空时为"已领取";3.已兑换:当用户已使用时状态为"已兑换";4.已作废:当管理员对码作废处理后,状态为"已作废")
    accessorKey: 'code_status'
  },
  {
    header: t('coupon.code.table.operation'), // 操作
    accessorKey: 'actions'
  }
]

const usedItems = [
  {
    label: t('coupon.code.table.query.use_all'), // 全部
    value: null
  },
  {
    label: t('coupon.code.table.query.use_used'), // 已使用
    value: true
  },
  {
    label: t('coupon.code.table.query.use_unused'), // 未使用
    value: false
  }
]
const currentPage = ref<number>(1) // 当前页面
const pageCount = ref<number>(10) // 每页显示的条数
const totalCount = ref<number>(0) // 总记录数
const queryParams = reactive<any>({
  used: false
})
const changeQuery = (val: boolean) => {
  queryParams.used = val
  currentPage.value = 1
  handleQuery()
}
const changePage = (page: number) => {
  currentPage.value = page
  handleQuery()
}
const handleQuery = () => {
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }

  // 处理分页
  queryParams.page = currentPage.value
  queryParams.limit = pageCount.value

  couponCodeMemberList(headers, queryParams).then((res) => {
    if (res.code === 200 && res.data) {
      couponCodeDataList.value = res.data
      totalCount.value = res.count || 0
    }
  })
}

const code_value = ref('')
const redeemingCode = ref('') // 正在兑换的兑换码
const inputRedeemCode = () => {
  if (!code_value.value || code_value.value.length < 1) {
    toast.add({
      title: t('coupon.code.input_tips') // '请输入兑换码'
    })
  }
  else {
    couponCodeRedeem(code_value.value).then((res: any) => {
      if (res && res.code == 200) {
        toast.add({
          title: t('coupon.code.redeem_success') // '兑换成功'
        })
        code_value.value = ''
      }
    })
  }
}

const toast = useToast()

// 检查兑换码状态：未开始、已过期、有效
const getRedemptionStatus = (redemption_start_time: string, redemption_end_time: string) => {
  const now = dayjs()
  const startTime = dayjs(redemption_start_time)
  const endTime = dayjs(redemption_end_time)

  if (now.isBefore(startTime)) {
    return 'not_started' // 未开始
  }
  else if (now.isAfter(endTime)) {
    return 'expired' // 已过期
  }
  else {
    return 'valid' // 有效
  }
}

const redeemCode = (row: any) => {
  const codeValue = row.original.code_value
  redeemingCode.value = codeValue // 设置正在兑换的兑换码

  couponCodeRedeem(codeValue)
    .then((res: any) => {
      if (res && res.code == 200) {
        toast.add({
          title: t('coupon.code.redeem_success') // '兑换成功'
        })
        handleQuery()
      }
    })
    .finally(() => {
      redeemingCode.value = '' // 清除loading状态
    })
}

onMounted(() => {
  handleQuery()
})
</script>
