<template>
  <div>
    <!-- FAQ 常见问题 -->
    <FAQ />
  </div>
</template>

<script setup lang="ts">
import FAQ from './components/FAQ.vue'

const { t, locale } = useI18n()

useSeoMeta({
  titleTemplate: '',
  title: t('header.pricing') + ' - ' + t('common.site_name'),
  ogTitle: t('header.pricing') + ' - ' + t('common.site_name'),
  description: t('common.description'),
  ogDescription: t('common.description')
})

defineOgImageComponent('Saas')
</script>
