<template>
  <UContainer>
    <div class="mt-2 min-h-screen py-4 sm:py-8">
      <div class="mx-auto max-w-7xl px-4">
        <!-- 购买 / 订阅 Plus 会员 -->
        <h1 class="mb-8 text-center text-2xl font-bold sm:mb-12 sm:text-3xl">
          {{ t('buy.title') }}
        </h1>

        <div class="mb-8 sm:mb-10">
          <!-- 套餐 -->
          <h2 class="mb-3 text-base font-bold sm:mb-2">
            {{ t('buy.plans') }}
          </h2>
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-2 xl:grid-cols-4">
            <!-- Loading skeleton for plans 骨架 -->
            <template v-if="plansLoading">
              <div v-for="n in 2" :key="n" class="rounded-lg border-1 border-gray-200 bg-sky-50/40 p-4">
                <div class="space-y-3 sm:space-y-4">
                  <div class="flex items-center">
                    <div class="mr-2 size-6 animate-pulse rounded bg-gray-200 sm:size-8 md:size-8 lg:size-10 xl:size-10" />
                    <div class="h-6 flex-1 animate-pulse rounded bg-gray-200" />
                  </div>
                  <div class="mt-6 mb-3 ml-8 sm:mt-8 sm:mb-4 sm:ml-10">
                    <div class="mb-2 h-8 w-24 animate-pulse rounded bg-gray-200" />
                    <div class="h-4 w-16 animate-pulse rounded bg-gray-200" />
                  </div>
                  <div class="ml-8 sm:ml-10">
                    <div class="h-4 w-20 animate-pulse rounded bg-gray-200" />
                  </div>
                </div>
              </div>
            </template>
            <!-- Actual plan cards -->
            <template v-else>
              <UCard
                v-for="(item, index) in valid_product_list"
                :key="index"
                class="border-1 border-gray-200 bg-sky-50/40 transition-shadow duration-300 hover:shadow-md"
                :class="{ 'active-info bg-sky-100/60 dark:bg-sky-600': buyMode === item.code }"
                @click="selectProduct(item)"
              >
                <div class="space-y-3 sm:space-y-4">
                  <div class="flex items-center">
                    <UIcon name="i-ri-vip-crown-2-line" style="color: #ff8c00" class="mr-2 size-6 sm:size-8 md:size-8 lg:size-10 xl:size-10 dark:bg-gray-100" />
                    <span class="text-lg font-medium sm:text-xl">
                      {{ t('buy.plus_membership') }}
                    </span>
                  </div>
                  <!-- 显示格式：¥xxx.x / 月 -->
                  <div class="mt-6 mb-3 ml-8 sm:mt-8 sm:mb-4 sm:ml-10">
                    <span class="text-2xl font-bold sm:text-3xl dark:text-white">{{ formatCurrency(item.price) }}</span>
                    <span class="text-base font-bold text-neutral-500 sm:text-lg dark:text-neutral-200">&nbsp;/&nbsp;{{ item.label }}</span>
                  </div>
                  <span class="ml-8 text-sm sm:ml-10">
                    <!-- 套餐明细 -->
                    <NuxtLink :to="'/' + locale + '/pricing'" target="_blank" class="font-medium text-(--ui-primary) dark:text-sky-300">{{ t('buy.plans_detail') }}</NuxtLink>
                  </span>
                </div>
              </UCard>
            </template>
          </div>
        </div>

        <!-- 购买模式 -->
        <div class="mb-8 sm:mb-10">
          <!-- 购买模式 -->
          <h2 class="mb-3 text-base font-bold sm:mb-2">
            {{ t('buy.purchase_mode') }}
          </h2>
          <div class="mb-8 grid grid-cols-1 gap-3 sm:mb-10 sm:grid-cols-2 md:grid-cols-4 md:gap-6 lg:grid-cols-6 xl:grid-cols-6">
            <div
              v-for="item of pay_mode_list"
              :key="item.code"
              class="flex min-h-[60px] cursor-pointer items-center justify-center rounded-lg border-1 border-gray-200 bg-sky-50/40 p-4 transition-all duration-300 sm:justify-start"
              :class="{ 'active-info bg-sky-100/60 dark:bg-sky-600': payMode === item.code }"
              @click="selectPayMode(item.code)"
            >
              <UIcon v-if="item.code === 1" name="i-mingcute-card-pay-fill" class="size-6 text-sky-500 sm:size-8 md:size-8 lg:size-10 xl:size-10 dark:bg-gray-100" />
              <UIcon v-else name="i-eos-icons-subscriptions-created" class="size-6 text-sky-500 sm:size-8 md:size-8 lg:size-10 xl:size-10 dark:bg-gray-100" />
              <span class="ml-2 text-sm sm:text-base">{{ item.label }}</span>
            </div>
          </div>
        </div>

        <!-- 支付方式 -->
        <div class="mb-10 sm:mb-12">
          <h2 class="mb-3 text-base font-bold sm:mb-2">
            {{ t('buy.pay_way') }}
          </h2>
          <div class="mb-8 grid grid-cols-1 gap-3 sm:mb-10 sm:grid-cols-2 md:grid-cols-4 md:gap-6 lg:grid-cols-6 xl:grid-cols-6">
            <div
              v-for="item of valid_pay_way_list"
              :key="item.code"
              class="flex min-h-[60px] cursor-pointer items-center justify-center rounded-lg border-1 border-gray-200 bg-sky-50/40 p-4 transition-all duration-300 sm:justify-start"
              :class="{ 'active-info bg-sky-100/60 dark:bg-sky-600': payWay === item.code }"
              @click="selectPayWay(item.code)"
            >
              <UIcon :name="item.icon" class="size-6 flex-shrink-0 sm:size-8 md:size-8 lg:size-10 xl:size-10 dark:bg-gray-100" :class="[item.className ? item.className : '']" />
              <span class="ml-2 text-sm sm:text-base">{{ item.label }}</span>
            </div>
          </div>
        </div>

        <div class="px-4 text-center sm:px-0">
          <!-- 确认支付 -->
          <UButton
            color="primary"
            variant="solid"
            :label="confirm_button_lable"
            block
            class="mx-auto w-full max-w-md rounded-md sm:w-80"
            size="xl"
            :loading="loading"
            @click="payCreate()"
          />
        </div>

        <ProductPay
          ref="payModel"
          :pay-way="payInfo?.pay_way"
          :pay-way-name="payWayName"
          :pay-amount="payAmount"
          :pay-url="payInfo?.pay_info"
          :order-no="payInfo?.order_no"
          :expire-time="payInfo?.expire_time"
        />
      </div>
    </div>
  </UContainer>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
// import PlusActiveIcon from '~/components/icons/PlusActiveIcon.vue';
import ProductPay from '~/components/ProductPay.vue'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import { createTradeOrder, getProductByPositionNoApi } from '@/api/product'
import type { CreatePayOrderType } from '~/api/types'
import type { HeadersTokenType, MemberProductType } from '@/api/login/types'
import { getMemberProductInfo } from '@/api/login'

const { t, locale } = useI18n()
const router = useRouter()

// definePageMeta 定义页面的布局和权限路由守卫(此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'member',
  middleware: 'auth' // 使用 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
})

// 页面标题及描述，这个会员内部页面：不需要做SEO优化，所以不设置描述
useSeoMeta({
  // 购买 / 订阅 Plus 会员
  titleTemplate: '',
  title: t('buy.title') + ' - ' + t('common.site_name')
})

const payModel = ref()

const route = useRoute()
const product = ref(null)
const buyMode = ref('monthly')
const payWay = ref('wechat')
const payWayName = ref('')
const payInfo = ref(null)
const payAmount = ref(0) // 金额
const payMode = ref(1) // 1 购买 2 订阅

let stripe_payment_min_amount = 0 // Stripe支付的最小金额(在 nuxt.config.ts 中定义)

// 购买模式列表
const pay_mode_list = computed(() => {
  const list = [
    {
      label: t('buy.purchase'), // 购买
      code: 1
    }
  ]

  // 如果是 按月或按年，并且金额大于10元，则显示订阅模式
  if ((buyMode.value === 'monthly' || buyMode.value === 'yearly') && payAmount.value >= stripe_payment_min_amount) {
    list.push({
      label: t('buy.subscribe'), // 订阅
      code: 2
    })
  }
  return list
})

// 支付方式列表
const payWays = computed(() => {
  const payWayList = [
    {
      label: t('buy.pay_way_items.wechat'), // 微信支付
      icon: 'i-ri-wechat-pay-fill',
      code: 'wechat',
      support_subscription: false,
      className: 'wechat-pay-icon'
    },
    {
      label: t('buy.pay_way_items.alipay'), // 支付宝
      icon: 'i-bi-alipay',
      code: 'alipay',
      support_subscription: false,
      className: 'alipay-pay-icon'
    }
  ]
  if (payAmount.value >= stripe_payment_min_amount) {
    // 只有支付金额大于或等于设置的最小金额，才显示Stripe支付方式
    payWayList.push({
      label: t('buy.pay_way_items.stripe'), // Stripe
      icon: 'bi:stripe',
      code: 'stripe',
      support_subscription: true,
      className: 'stripe-pay-icon'
    })
  }
  return payWayList
})

// 有效的支付方式
const valid_pay_way_list = computed(() => {
  if (payMode.value === 2) {
    return payWays.value.filter(item => item.support_subscription) // 订阅模式，只显示支持订阅的支付方式
  }
  else {
    return payWays.value
  }
})

// 有效的产品规格
const valid_product_list = computed(() => {
  if (!product.value) {
    return []
  }
  const productValue = product.value

  const result = []
  if (productValue.weekly_enable === 'on' && payMode.value === 1) {
    // 购买模式下才有按周支付费
    result.push({
      code: 'weekly',
      label: t('buy.purchase_cycle.week'), // 周
      price: productValue.weekly_price
    })
  }
  if (productValue.monthly_enable === 'on') {
    result.push({
      code: 'monthly',
      label: t('buy.purchase_cycle.month'), // 月
      price: productValue.monthly_price
    })
  }
  if (productValue.quarterly_enable === 'on' && payMode.value === 1) {
    // 购买模式下才有按季度支付费
    result.push({
      code: 'quarterly',
      label: t('buy.purchase_cycle.quarter'), // 季
      price: productValue.quarterly_price
    })
  }
  if (productValue.yearly_enable === 'on') {
    result.push({
      code: 'yearly',
      label: t('buy.purchase_cycle.year'), // 年
      price: productValue.yearly_price
    })
  }
  return result
})

// 确认按钮的文本
const confirm_button_lable = computed(() => {
  if (payMode.value === 1) {
    return t('buy.confirm_payment')
  }
  else {
    return t('buy.confirm_subscribe')
  }
})

/** 会员已购买过的产品信息 */
const memberProduct = ref({})

/**
 * member_product_status
 * 会员产品 Plus会员 状态(API返回的业务状态，非数据库字段状态)：
 * 0：未试用（免费版），
 * 1：Plus会员试用中（试用版），
 * 2：Plus会员试用已过期（免费版），
 * 3：Plus会员套餐有效期内，
 * 4：Plus会员套餐已过期（免费版）
 */
const member_product_status = ref(0)

/**
 * subscription_status
 * 订阅状态(1:未订阅、2:订阅中、3:到期取消)
 */
const subscription_status = ref(1)

/**
 * subscription_buy_mode
 * 订阅购买模式（weekly:按周,monthly:按月,quarterly:按季,yearly:按年）
 */
const subscription_buy_mode = ref('')

/**
 * 获取会员已购买产品信息.
 */
async function memberProductInfo() {
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const authStore = useAuthStoreWithOut()

  // 获取会员已购买产品信息
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  const paramData: MemberProductType = {
    platform: 'ztsl'
  }
  const res = await getMemberProductInfo(headers, paramData)
  // 获取会员产品 Plus会员 状态
  // member_product_status.value = res.data?.member_product_status;
  // memberProduct.value = res.data;
  subscription_status.value = res.data?.subscription_status // 订阅状态
  // subscription_buy_mode.value = res.data?.subscription_buy_mode; // 订阅购买模式（weekly:按周,monthly:按月,quarterly:按季,yearly:按年）

  await authStore.updateMemberProduct(res.data) // 更新会员已购买产品信息
}

onMounted(async () => {
  try {
    plansLoading.value = true
    // Stripe支付的最小金额(在 nuxt.config.ts 中定义)
    stripe_payment_min_amount = useRuntimeConfig().public.stripePaymentMinAmount
    // console.log('stripe_payment_min_amount:', stripe_payment_min_amount);

    // 判断用户是否登录
    const authStore = useAuthStoreWithOut()
    if (authStore.getToken && authStore.getToken !== '') {
      await memberProductInfo() // 获取会员已购买产品信息

      // 订阅中，不允许再购买或订阅，则跳转到账户页面
      if (subscription_status.value == 2) {
        router.push({ path: '/' + locale.value + '/account' })
        return
      }
    }
    else {
      // 未登录
      router.push({ path: '/' + locale.value + '/login' })
      return
    }

    // 初始化价格
    payAmount.value = 0

    // 获取参数
    const queryParams = route.query
    buyMode.value = queryParams.buy_mode as string

    // 查询支付产品
    const productQueryResult = await getProductByPositionNoApi({
      platform: queryParams.platform as string,
      position_no: queryParams.position_no as string
    })
    product.value = productQueryResult.data
  }
  finally {
    plansLoading.value = false
  }
})

watch(valid_pay_way_list, (newValue) => {
  if (newValue && newValue.length > 0) {
    const val = newValue[0]
    payWay.value = val.code
    payWayName.value = val.label
  }
})
watch(valid_product_list, (newValue) => {
  if (newValue.length <= 0) {
    payAmount.value = 0
    return
  }

  let match = false
  newValue.forEach((item) => {
    if (item.code === buyMode.value) {
      match = true
      buyMode.value = item.code
      payAmount.value = item.price
    }
  })

  if (!match) {
    buyMode.value = newValue[0].code
    payAmount.value = newValue[0].price
  }
})

const loading = ref(false)
const plansLoading = ref(true) // 新增套餐loading状态

/** 创建支付订单 */
const payCreate = async () => {
  try {
    loading.value = true

    const authStore = useAuthStoreWithOut()
    const fromData: CreatePayOrderType = {
      platform: 'ztsl',
      platform_endpoint: 'web',
      token: authStore.getToken.slice(7), // 去掉token前缀 "bearer "
      product_no: product.value.product_no,
      pay_mode: payMode.value,
      buy_mode: buyMode.value,
      pay_way: payWay.value,
      pay_type: 'pcweb',
      pay_return_url: window.location.origin + '/' + locale.value + '/account',
      order_type: 'plus'
      // pay_return_url: 'https://www.baidu.com'
    }

    const res = await createTradeOrder(fromData)
    // console.log('payCreate res:', res);
    if (!res) {
      console.log('payCreate res:', res)
      return
    }
    const statusCode = res.code // 200: 成功
    if (statusCode != 200) {
      console.log('payCreate statusCode2:', statusCode)
      // 失败(在此之前系统已弹窗提示)
      return
    }
    const payResp = res.data
    payInfo.value = payResp
    payAmount.value = payResp.product_price
    const targetPayWay = payWays.value.filter(item => item.code === payResp.pay_way)
    payWayName.value = targetPayWay.length > 0 ? targetPayWay[0].label : ''
    if (payResp.pay_way === 'stripe') {
      window.location.href = payResp.pay_info
    }
    else {
      payModel.value.handleOpenModel()
    }
  }
  finally {
    loading.value = false
  }
}

/** 选择产品 */
const selectProduct = (item: any) => {
  buyMode.value = item.code // 月、年
  payAmount.value = item.price
  // 如果金额小于10,则不允许订阅,只能购买
  if (item.price < stripe_payment_min_amount) {
    payMode.value = 1
  }
}

/** 选择支付方式 */
const selectPayWay = (val: string) => {
  payWay.value = val
}

/** 选择购买方式 */
const selectPayMode = (val: number) => {
  payMode.value = val
}

/** 格式化货币 */
const formatCurrency = (val: string | number | bigint) => {
  if (typeof val !== 'number') {
    return val
  }
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 1,
    maximumFractionDigits: 2
  }).format(val)
}
</script>

<style scoped>
.active-info {
  border: 1px solid #409eff !important;
}

.wechat-pay-icon {
  color: #00c250;
}

.alipay-pay-icon {
  color: #0f8dff;
}

.stripe-pay-icon {
  color: #675dff;
}
</style>
