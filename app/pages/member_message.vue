<template>
  <div class="mx-auto mt-4 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mt-6">
      <!-- 消息导航栏 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <!-- 消息通知 -->
            <h4 class="text-base font-medium tracking-tight text-neutral-900 sm:text-xl dark:text-white">
              {{ t('member_message.title') }}
            </h4>
            <div class="flex gap-2">
              <UButton
                color="neutral"
                variant="outline"
                class="rounded-md font-medium shadow-xs"
                @click="readMessage"
              >
                <icon name="i-mdi-light:email-open" size="1.25rem" />
                {{ t('member_message.toast.mark_read') }}
                <!-- 标记已读 -->
              </UButton>
              <UButton
                color="neutral"
                variant="outline"
                class="rounded-md font-medium shadow-xs"
                @click="openBatchDeleteConfirm"
              >
                <icon name="i-mdi-light:delete" size="1.25rem" />
                {{ t('member_message.delete') }}
                <!-- 删除 -->
              </UButton>
            </div>
          </div>
        </template>
        <UTabs
          v-model="activeTabValue"
          :items="readItems"
          :content="false"
          variant="pill"
          color="neutral"
          class="mb-8 w-full gap-4"
          :ui="
            {
              list: 'bg-[#f3f4f6] p-1 rounded-md dark:bg-[#1f2937]', // 淡灰背景
              indicator: 'bg-[#03A9F4] rounded-md dark:bg-gray-900', // 亮蓝色背景
              trigger:
                'px-4 py-2 text-sm font-medium transition-colors duration-150 data-[state=active]:text-white data-[state=inactive]:text-gray-600 dark:data-[state=inactive]:text-gray-400 dark:data-[state=active]:text-white'
            } as any
          "
          @update:model-value="handleTabChange"
        />

        <!-- 消息列表 -->
        <div class="overflow-x-auto">
          <UTable
            v-model:row-selection="rowSelection"
            :data="memberMessageDataList"
            row-key="id"
            :columns="memberMessageColumnList"
            :loading="loading"
            loading-color="primary"
            loading-animation="carousel"
            class="w-full rounded-lg border border-gray-200 dark:border-gray-700"
            :ui="
              {
                td: { padding: 'px-3 py-2' },
                th: { width: 'min-content' }
              } as any
            "
            style="table-layout: fixed"
            @select="toContent"
          >
            <!-- 图标 -->
            <template #icon-cell="{ row }">
              <div class="flex items-center justify-center">
                <icon
                  :name="(row.original as any).is_read === t('member_message.unread') ? 'i-material-icon-theme:email' : 'i-line-md:email-opened'"
                  size="1.4rem"
                  iconclass="text-gray-900 dark:text-gray-100"
                />
              </div>
            </template>
            <!-- 消息标题 -->
            <template #message_title_zhhans-cell="{ row }">
              <!-- 未读消息 -->
              <UTooltip :text="(row.original as any)[`message_title_${getLocaleSuffix()}`] || (row.original as any).message_title_zhhans" arrow>
                <UButton
                  class="-ml-3 text-left text-sm font-medium hover:text-sky-500 dark:hover:text-sky-500"
                  :class="(row.original as any).is_read === t('member_message.unread') ? 'font-bold text-gray-800 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'"
                  variant="ghost"
                  color="white"
                  @click="toContent(row)"
                >
                  <div class="w-[280px] truncate text-left">
                    {{ (row.original as any)[`message_title_${getLocaleSuffix()}`] || (row.original as any).message_title_zhhans }}
                  </div>
                </UButton>
              </UTooltip>
            </template>
            <template #expanded="{ row }">
              <pre>{{ row.original }}</pre>
            </template>
            <!-- 操作 -->
            <template #actions-cell="{ row }">
              <UTooltip :text="t('member_message.delete')" arrow>
                <UButton
                  icon="i-mdi-light:delete"
                  color="neutral"
                  variant="ghost"
                  size="md"
                  class="rounded-full text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
                  @click="openDeleteConfirm(row.original.id)"
                />
              </UTooltip>
            </template>
          </UTable>
        </div>
        <!-- 分页 -->
        <div class="mt-5 flex justify-end">
          <UPagination
            v-model:page="currentPage"
            :items-per-page="pageCount"
            :total="totalCount"
            :sibling-count="2"
            size="lg"
            @update:page="changePage"
          />
        </div>
      </UCard>
    </div>
  </div>

  <UModal v-model:open="open" :title="t('member_message.toast.delete_title')" :ui="{ footer: 'justify-end' }">
    <template #body>
      <div class="flex items-center p-4">
        <UIcon name="i-heroicons-exclamation-triangle" class="mr-3 h-5 w-5 flex-shrink-0 text-orange-600" />
        <p>{{ t('member_message.toast.delete_tip') }}</p>
      </div>
    </template>

    <template #footer>
      <UButton
        :label="t('common.cancel')"
        color="neutral"
        variant="outline"
        size="md"
        class="rounded-md px-4 text-sm"
        @click="open = false"
      />
      <UButton
        :label="t('common.confirm')"
        color="error"
        variant="solid"
        size="md"
        class="rounded-md px-4 text-sm"
        @click="handleConfirmDelete"
      />
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { getMemberMessageList, updateMemberMessageReadStatus, deleteMemberMessage } from '@/api/member_message'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import { h, resolveComponent } from 'vue'

const { t, locale } = useI18n()

// definePageMeta 定义页面的布局和权限路由守卫，(此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'member',
  middleware: 'auth' // 使用 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
})

// 页面标题及描述，这个会员内部页面：不需要做SEO优化，所以不设置描述
useSeoMeta({
  titleTemplate: '',
  title: t('member_message.title') + ' - ' + t('common.site_name') // 消息通知
})

defineOgImageComponent('Saas')

const rowSelection = ref({})
const UCheckbox = resolveComponent('UCheckbox')
const UButton = resolveComponent('UButton')
const isRead = ref<DictDetail[]>([])
const unreadCount = ref<number>(0)
const allCount = ref<number>(0)
const loading = ref(false)
const open = ref(false)

onMounted(() => {
  initData()
})

isRead.value = [
  {
    label: t('member_message.unread'), // 未读
    value: 'false'
  },
  {
    label: t('member_message.read'), // 已读
    value: 'true'
  }
]

type MemberMessage = {
  id: number
  create_datetime: any
  message_title_zhhans: string
  message_title_zhhant: string
  message_title_en: string
  content_zhhans: string
  content_zhhant: string
  content_en: string
  email: string
  message_type: string
  is_read: string
  feedback_no?: string // 添加问题编号字段
  feedback_id?: number // 添加问题ID字段
}

const currentPage = ref<number>(1) // 当前页面
const pageCount = ref<number>(10) // 每页显示的条数
const totalCount = ref<number>(0) // 总记录数
const queryParams = reactive<any>({
  is_read: false,
  message_type: '2'
})
// 1. 添加激活标签变量
const activeTabValue = ref('0') // 默认选中未读消息

// 2. 定义 tabs 数据
const readItems = reactive([
  {
    label: `${t('member_message.tabs.unread_messages')} (${unreadCount.value})`, // 全部消息
    value: '0', // 未读消息 value 改为 '0'
    content: 'false'
  },
  {
    label: `${t('member_message.tabs.all_messages')} (${allCount.value})`, // 未读消息
    value: '1', // 全部消息 value 改为 '1'
    content: 'undefined'
  }
])

// 3. 修改 handleTabChange 函数
const handleTabChange = (value: string | number) => {
  // 根据 value 直接设置查询参数
  if (value === '0') {
    // 未读消息
    queryParams.is_read = false
  }
  else {
    // 全部消息
    queryParams.is_read = undefined
  }

  currentPage.value = 1
  handleQuery()
}

const changePage = (page: number) => {
  currentPage.value = page
  handleQuery()
}

const handleQuery = async () => {
  try {
    const authStore = useAuthStoreWithOut()
    const headers = { token: authStore.getToken.slice(7) }

    // 处理分页
    const params = {
      ...queryParams,
      page: currentPage.value,
      limit: pageCount.value
    }
    const res = await getMemberMessageList(headers, params)

    // 处理响应
    if (res.code === 200) {
      // 即使是空数组也进行处理
      totalCount.value = res.count || 0

      if (res.data && Array.isArray(res.data)) {
        const messageList = res.data.map((item: MemberMessage) => ({
          id: item.id,
          create_datetime: item.create_datetime,
          message_title_zhhans: item.message_title_zhhans,
          email: item.email,
          message_type: item.message_type,
          message_title_zhhant: item.message_title_zhhant,
          message_title_en: item.message_title_en,
          content_zhhans: item.content_zhhans,
          content_zhhant: item.content_zhhant,
          content_en: item.content_en,
          is_read: String(item.is_read).toLowerCase() === 'true' ? t('member_message.read') : t('member_message.unread'), // 已读/未读
          // 添加这两个字段
          feedback_no: item.feedback_no,
          feedback_id: item.feedback_id
        }))

        // 使用赋值而非修改
        memberMessageDataList.value = messageList
      }
      else {
        // 明确处理空数据情况
        memberMessageDataList.value = []
      }
    }
  }
  catch (error) {
    toast.add({
      title: t('member_message.toast.loading_failed'), // 加载失败
      description: error.message || t('member_message.toast.loading_failed_content'), // 加载失败，请重试
      color: 'red'
    })

    // 确保在出错时也有空数组
    memberMessageDataList.value = []
  }
}

const toast = useToast()

// 4. 修改 getCount 函数
const getCount = async () => {
  const authStore = useAuthStoreWithOut()
  const headers = { token: authStore.getToken.slice(7) }

  const [unreadRes, allRes] = await Promise.all([
    getMemberMessageList(headers, { ...queryParams, is_read: 'false', limit: 0 }),
    getMemberMessageList(headers, { ...queryParams, is_read: undefined, limit: 0 })
  ])

  unreadCount.value = (unreadRes?.code === 200 && unreadRes.count) || 0
  allCount.value = (allRes?.code === 200 && allRes.count) || 0

  // 更新标签显示
  readItems[0].label = `${t('member_message.tabs.unread_messages')} (${unreadCount.value})` // 未读消息
  readItems[1].label = `${t('member_message.tabs.all_messages')} (${allCount.value})` // 全部消息
}

// 统一初始化函数
const initData = async () => {
  loading.value = true
  try {
    // 并行请求，任一请求失败不影响另一个
    await Promise.allSettled([getCount(), handleQuery()])
  }
  catch (error) {
    toast.add({
      title: t('member_message.toast.loading_failed'), // 加载失败
      description: t('member_message.toast.loading_failed_content'), // 加载失败，请重试
      color: 'red'
    })
  }
  finally {
    loading.value = false
  }
}

// 2. 修改toContent函数使用统一方法
const toContent = async (row: any) => {
  const id = row.original.id
  if (!id) return

  try {
    const ids = [id.toString()]
    await updateMemberMessageReadStatus(ids)

    // 直接调用更新
    document.querySelector('#unread-messages')?.dispatchEvent(new Event('update-count'))

    // 获取完整的行数据
    const originalRow = row.original || row

    // 检查是否同时存在 feedback_no 和 feedback_id 字段
    if (originalRow.feedback_no && originalRow.feedback_id) {
      console.log('发现问题反馈相关消息，直接跳转到回复列表')

      // 跳转到反馈回复列表页面
      push({
        path: `/${locale.value}/reply_list`,
        query: {
          feedback_no: originalRow.feedback_no,
          feedback_id: originalRow.feedback_id,
          message_id: id.toString()
        }
      })
    }
    else {
      // 尝试通过 API 获取完整消息详情
      const authStore = useAuthStoreWithOut()
      const headers = { token: authStore.getToken.slice(7) }

      // 获取指定ID的消息详情
      const messageDetails = await getMemberMessageList(headers, {
        id: id,
        limit: 1
      })

      if (messageDetails.code === 200 && messageDetails.data && messageDetails.data.length > 0 && messageDetails.data[0].feedback_no && messageDetails.data[0].feedback_id) {
        // API 返回的消息包含问题信息，跳转到回复列表页面
        push({
          path: `/${locale.value}/reply_list`,
          query: {
            feedback_no: messageDetails.data[0].feedback_no,
            feedback_id: messageDetails.data[0].feedback_id,
            message_id: id.toString()
          }
        })
      }
      else {
        // 没有找到问题相关信息，使用默认跳转
        push({
          path: `/${locale.value}/message`,
          query: { id: id.toString() }
        })
      }
    }

    // 刷新数据
    await Promise.allSettled([getCount(), handleQuery()])
  }
  catch (error) {
    toast.add({
      title: t('member_message.toast.prompt_failed'),
      description: t('member_message.toast.prompt_failed_content'),
      color: 'red'
    })
  }
}

// 3. 批量标记已读
const readMessage = async (row?: any) => {
  let realIds: string[] = []

  // 新增：处理直接传入ID的情况
  if (typeof row === 'string' || typeof row === 'number') {
    realIds = [row.toString()]
  }
  // 处理传入row对象的情况
  else if (row?.original?.id) {
    realIds = [row.original.id.toString()]
  }
  // 原有批量选择逻辑
  else {
    const selectedIndices = Object.keys(rowSelection.value)
    realIds = selectedIndices
      .map((index) => {
        const item = memberMessageDataList.value[Number(index)]
        return item ? item.id.toString() : null
      })
      .filter(Boolean)
  }

  if (realIds.length === 0) {
    toast.add({
      icon: 'i-heroicons-exclamation-triangle',
      title: t('member_message.toast.prompt'), // 提示
      description: t('member_message.toast.prompt_content'), // 请选择要标记已读的消息
      color: 'warning'
    })
    return
  }

  try {
    loading.value = true
    const res = await updateMemberMessageReadStatus(realIds)

    if (res.code === 200) {
      toast.add({
        icon: 'i-heroicons-check-circle',
        title: t('member_message.toast.succeed'), // 成功
        description: t('member_message.toast.mark_read_success'), // 标记已读成功
        color: 'success'
      })

      rowSelection.value = {}

      // 直接调用更新
      document.querySelector('#unread-messages')?.dispatchEvent(new Event('update-count'))

      await Promise.allSettled([getCount(), handleQuery()])
    }
    else {
      throw new Error(res.message || t('member_message.toast.mark_read_failed')) // 标记已读失败
    }
  }
  catch (error) {
    toast.add({
      title: t('member_message.toast.prompt_failed'), // 标记失败
      description: error.message || t('member_message.toast.prompt_failed_content'), // 标记失败，请重试
      color: 'red'
    })
  }
  finally {
    loading.value = false
  }
}

// 删除消息
const deleteMessage = async (ids: string[]) => {
  if (ids.length === 0) {
    toast.add({
      title: t('member_message.toast.prompt'), // 提示
      description: t('member_message.toast.delete_prompt_content'), // 未选中任何消息
      icon: 'i-heroicons-exclamation-triangle',
      color: 'warning'
    })
    return
  }
  try {
    const res = await deleteMemberMessage(ids)
    if (res.code === 200) {
      toast.add({
        title: t('member_message.toast.delete_success'), // 删除成功
        description: t('member_message.toast.delete_success_content'), // 消息已删除
        color: 'success'
      })

      // 清空选择状态
      rowSelection.value = {}

      // 触发未读消息数量更新
      document.querySelector('#unread-messages')?.dispatchEvent(new Event('update-count'))

      // 刷新数据
      await Promise.allSettled([getCount(), handleQuery()])
    }
  }
  catch (error) {
    toast.add({
      title: t('member_message.toast.delete_failed'), // 删除失败
      description: error.message || t('member_message.toast.delete_failed_content'), // 删除失败，请重试
      color: 'error'
    })
  }
}

const { push } = useRouter()

// 消息列表
const memberMessageDataList = ref<MemberMessage[]>([])
const memberMessageColumnList = [
  {
    id: 'select',
    header: ({ table }) =>
      h(UCheckbox, {
        'modelValue': table.getIsSomePageRowsSelected() ? 'indeterminate' : table.getIsAllPageRowsSelected(),
        'onUpdate:modelValue': (value: boolean | 'indeterminate') => table.toggleAllPageRowsSelected(!!value),
        'ariaLabel': 'Select all'
      }),
    cell: ({ row }) =>
      h(UCheckbox, {
        'modelValue': row.getIsSelected(),
        'onUpdate:modelValue': (value: boolean | 'indeterminate') => row.toggleSelected(!!value),
        'ariaLabel': 'Select row'
      })
  },
  {
    header: '',
    accessorKey: 'icon'
  },
  {
    header: t('member_message.message_title'), // 消息标题
    accessorKey: 'message_title_zhhans'
  },
  {
    header: t('member_message.message_preview'), // 消息预览
    accessorKey: 'content_zhhans',
    cell: ({ row }) => {
      const content = row.original[`content_${getLocaleSuffix()}`] || row.original.content_zhhans
      const plainText = content
        .replace(/<[^>]+>/g, '') // 移除HTML标签
        .replace(/&nbsp;/g, ' ') // 将&nbsp;替换为普通空格
        .replace(/[\r\n]+/g, ' ') // 将换行符替换为空格
        .replace(/\s+/g, ' ') // 将多个空格合并为一个
        .trim() // 移除首尾空格

      return h('div', {
        class: [
          'text-sm font-medium w-[450px] truncate', // 固定宽度
          row.original.is_read === t('member_message.unread') ? 'text-gray-600 dark:text-gray-300 font-bold' : 'text-gray-400 dark:text-gray-500'
        ],
        style: {
          whiteSpace: 'nowrap',
          display: 'block'
        },
        innerText: plainText
      })
    },
    meta: {
      class: {
        td: 'w-auto' // 单元格宽度自适应
      }
    },
    size: 400 // 设置列最大宽度，确保有足够空间
  },
  {
    header: t('member_message.create_datetime'), // 接收时间
    accessorKey: 'create_datetime',
    cell: ({ row }) => {
      return h(
        'div',
        {
          class: [row.original.is_read === t('member_message.unread') ? 'text-gray-600 dark:text-gray-300 font-bold ' : 'text-gray-400 dark:text-gray-500']
        },
        getTimeFormat(row.original.create_datetime)
      )
    }
  },
  {
    header: '', // 操作
    accessorKey: 'actions'
  }
]

// 获取当前语言对应的后缀
const getLocaleSuffix = () => {
  switch (locale.value) {
    case 'zhHans':
      return 'zhhans'
    case 'zhHant':
      return 'zhhant'
    default:
      return 'en'
  }
}

// 获取时间格式
const getTimeFormat = (time: string) => {
  if (locale.value === 'zhHans' || locale.value === 'zhHant') {
    const month = new Date(time).getMonth() + 1
    const day = new Date(time).getDate()
    return `${month}月${day}日`
  }
  else {
    const monthsAbbr = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    const month = new Date(time).getMonth()
    const day = new Date(time).getDate()
    return `${monthsAbbr[month]}  ${day}`
  }
}

// 新增状态管理
const deleteTargetId = ref<string[]>([])

// 打开确认弹窗并记录ID
const openDeleteConfirm = (id: number) => {
  deleteTargetId.value = [id.toString()]
  open.value = true
}

// 实际删除操作
const handleConfirmDelete = async () => {
  await deleteMessage(deleteTargetId.value)
  open.value = false
}

// 打开批量删除确认弹窗
const openBatchDeleteConfirm = () => {
  if (Object.keys(rowSelection.value).length === 0) {
    toast.add({
      title: t('member_message.toast.prompt'), // 提示
      description: t('member_message.toast.delete_prompt_content'), // 未选中任何消息
      icon: 'i-heroicons-exclamation-triangle',
      color: 'warning'
    })
    return
  }
  const selectedIndices = Object.keys(rowSelection.value)
  const selectedIds = selectedIndices
    .map((index) => {
      const item = memberMessageDataList.value[Number(index)]
      return item ? item.id.toString() : null
    })
    .filter(Boolean)

  deleteTargetId.value = selectedIds
  open.value = true
}
</script>
