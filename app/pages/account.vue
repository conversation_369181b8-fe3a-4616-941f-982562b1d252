<template>
  <div class="mx-auto mt-6 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- 会员信息 -->
    <MemberInfoCard :skip-to-uri="'/' + locale + '/account'" />
    <!-- 套餐详情 -->
    <div>
      <UCard class="mt-6 pb-16">
        <template #header>
          <h3 class="text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
            <!-- 套餐 -->
            {{ t('account.plan') }}
          </h3>
        </template>

        <!-- 产品展示 -->
        <PricingPlan :skip-to-uri="'/' + locale + '/account'" :product="product" />
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import MemberInfoCard from './components/MemberInfoCard.vue'
import PricingPlan from './components/PricingPlan.vue'
import { getProductByPositionNoApi } from '@/api/product'
import type { GetProductType } from '@/api/types'

// definePageMeta 定义页面的布局和权限路由守卫，(此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'member',
  middleware: 'auth' // 使用 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
})

const { t, locale } = useI18n()

// 页面标题及描述，这个会员内部页面：不需要做SEO优化，所以不设置描述
useSeoMeta({
  titleTemplate: '',
  title: t('header.account') + ' - ' + t('common.site_name') // 账户
})

/** 平台在售产品信息 */
const product = ref({})

onMounted(async () => {
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const paramData: GetProductType = {
    platform: 'ztsl',
    position_no: 'ztsl-plus'
  }

  product.value = (await getProductByPositionNoApi(paramData)).data
})
</script>
