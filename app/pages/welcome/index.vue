<!-- 滚动式引导页面 -->
<template>
  <AppHeader :is-disabled-nav="true" class="fixed top-0 w-full" />
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-900 dark:to-gray-900">
    <!-- 滚动导航组件 -->
    <ScrolNavigation :sections="sections" :current-section="currentSection" @update:current-section="currentSection = $event" />

    <!-- 主要内容区域 -->
    <div class="pt-16">
      <!-- 欢迎区域 -->
      <section ref="section0" class="flex min-h-screen items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="mx-auto max-w-4xl text-center">
          <div class="mb-8">
            <h1 class="mb-6 text-2xl font-bold text-gray-900 sm:text-4xl md:text-5xl lg:text-6xl dark:text-white">
              <span class="mr-2">🎉</span>
              <!-- 欢迎使用精挑翻译 -->
              <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{{ t('welcome.title') }}</span>
            </h1>
            <!-- 下面通过几个步骤快速了解我们的核心功能 👇 -->
            <p class="mb-8 text-base text-gray-600 sm:text-lg md:text-xl dark:text-gray-300">
              {{ t('welcome.description') }}
            </p>
          </div>

          <!-- 滚动提示 -->
          <div class="animate-bounce cursor-pointer" @click="scrollToNextSection">
            <UIcon name="heroicons:chevron-down" class="mx-auto h-8 w-8 text-blue-500" />
            <!-- pc端：向下滚动开始探索， 移动端：向下滑动开始探索 -->
            <p class="mt-2 text-xs text-gray-500 sm:text-sm dark:text-gray-400">
              {{ isMobile ? t('welcome.tooltip_mobile') : t('welcome.tooltip_pc') }}
            </p>
          </div>
        </div>
      </section>

      <!-- 双语对照翻译 -->
      <section ref="section1" class="group flex min-h-screen items-center overflow-hidden bg-white px-4 sm:px-6 lg:px-8 dark:bg-slate-950">
        <div class="relative container mx-auto px-4 sm:px-6 lg:px-8">
          <!-- 主要内容区域
          group-hover:-translate-x-36： 悬停的时候向左侧偏移量
          -->
          <div
            :class="[
              'transition-all duration-500',
              showExperienceArea ? 'pointer-events-none opacity-0' : 'opacity-100',
              'grid items-center gap-6 group-hover:-translate-x-36 md:gap-8 lg:grid-cols-2 lg:gap-12'
            ]"
          >
            <div class="relative">
              <!-- 给 <img> 加 decoding="async" 和 loading="lazy"，提前做预加载， 减少过渡的时候的卡顿  -->
              <img
                :src="bilingualImage"
                decoding="async"
                loading="lazy"
                class="w-full rounded-sm shadow-lg transition-transform duration-300 sm:shadow-xl md:shadow-2xl"
                draggable="false"
              />
            </div>
            <div>
              <div class="mb-6">
                <h2 class="mb-4 text-xl font-bold text-gray-900 sm:text-2xl md:text-3xl lg:text-4xl dark:text-white">
                  <!-- 双语对照网页翻译 -->
                  {{ t('home.bilingual_translation.title') }}
                </h2>
                <p class="mb-6 text-sm text-gray-600 sm:text-base md:text-lg dark:text-gray-300">
                  <!-- 智能识别并翻译网页内容，在原文后显示译文，实现双语对照阅读。支持各种语言的网页翻译，助您无障碍畅游全球知识海洋！ -->
                  {{ t('home.bilingual_translation.description') }}
                </p>
              </div>

              <!-- 特性列表 -->
              <div class="space-y-3 sm:space-y-4">
                <div v-for="(feature, index) in bilingualFeatures" :key="index" class="flex items-center space-x-2 rounded-lg bg-gray-50 p-3 sm:space-x-3 sm:p-4 dark:bg-slate-800/50">
                  <div class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/30">
                    <UIcon :name="feature.icon" class="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <p class="text-sm text-gray-700 sm:text-base dark:text-gray-300">
                    {{ feature.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 双语对照体验区域 -->
          <div :class="['absolute inset-0 transition-all duration-500', showExperienceArea ? 'pointer-events-auto opacity-100' : 'pointer-events-none opacity-0']">
            <div class="flex h-full items-center">
              <!-- 返回按钮 -->
              <div class="absolute top-1/2 left-4 z-10 -translate-y-1/2">
                <UButton
                  color="gray"
                  variant="soft"
                  size="lg"
                  class="rounded-full shadow-lg"
                  @click="closeExperienceArea"
                >
                  <UIcon name="heroicons:arrow-left" class="h-5 w-5" />
                </UButton>
              </div>
              <!-- 双语对照体验区域 -->
              <div class="mx-auto w-full max-w-6xl px-16 max-sm:px-4">
                <Bilingual />
              </div>
            </div>
          </div>

          <!-- 右侧悬停按钮 -->
          <div
            :class="[
              'absolute top-1/2 right-0 translate-x-full -translate-y-1/2 transition-all duration-500',
              showExperienceArea ? 'pointer-events-none opacity-0' : 'opacity-0 group-hover:translate-x-0 group-hover:opacity-100'
            ]"
          >
            <!-- 整个区域可点击的卡片 -->
            <div
              class="cursor-pointer rounded-l-2xl border-l-3 border-green-500 bg-gradient-to-br from-white to-gray-50 p-2.5 shadow-xl backdrop-blur-sm transition-all duration-300 select-none hover:scale-105 hover:shadow-2xl hover:shadow-green-500/20 active:scale-95 dark:border-green-400 dark:from-slate-800 dark:to-slate-900"
              @click="startUsing"
            >
              <!-- 顶部图标区域 -->
              <div class="relative mb-3 flex justify-center">
                <div class="absolute inset-0 animate-pulse rounded-full bg-green-500 opacity-20 blur-md" />
                <div class="relative flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-green-600 shadow-md">
                  <UIcon name="heroicons:arrow-right" class="h-5 w-5 text-white" />
                </div>
              </div>

              <!-- 文字内容区域 -->
              <div class="text-center">
                <h3 class="mb-1 bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-sm font-bold text-transparent dark:from-green-400 dark:to-green-500">
                  <!-- 立即体验 -->
                  {{ t('welcome.button.experience') }}
                </h3>
                <div class="mt-2 h-0.5 w-full rounded-full bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-50" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- AI择优翻译 -->
      <section ref="section2" class="group flex min-h-screen items-center overflow-hidden bg-gray-50 px-4 sm:px-6 lg:px-8 dark:bg-gray-900">
        <div class="relative container mx-auto px-4 sm:px-6 lg:px-8">
          <!-- 主要内容区域
          group-hover:-translate-x-36： 悬停的时候向左侧偏移量
          -->
          <div :class="['transition-all duration-500', 'grid items-center gap-6 group-hover:-translate-x-36 md:gap-8 lg:grid-cols-2 lg:gap-12']">
            <div class="order-2 lg:order-1">
              <div class="mb-6">
                <h2 class="mb-4 text-xl font-bold text-gray-900 sm:text-2xl md:text-3xl lg:text-4xl dark:text-white">
                  <!-- AI 择优翻译 -->
                  {{ t('home.ai_selected_translation.title') }}
                </h2>
                <p class="mb-6 text-sm text-gray-600 sm:text-base md:text-lg dark:text-gray-300">
                  <!-- 使用多个翻译模型对同一原文进行翻译会得到多个不同的结果，通过智能评分从中择优获取最佳译文，能大幅提升翻译结果的准确度和可靠性。 -->
                  {{ t('home.ai_selected_translation.description') }}
                </p>
              </div>

              <!-- 特性列表 -->
              <div class="space-y-3 sm:space-y-4">
                <div v-for="(feature, index) in aiFeatures" :key="index" class="flex items-center space-x-2 rounded-lg bg-white p-3 sm:space-x-3 sm:p-4 dark:bg-gray-800/50">
                  <div class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                    <UIcon :name="feature.icon" class="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p class="text-sm text-gray-700 sm:text-base dark:text-gray-300">
                    {{ feature.description }}
                  </p>
                </div>
              </div>
            </div>

            <!-- 右侧图片 -->
            <div class="order-1 lg:order-2">
              <div class="relative">
                <!-- 给 <img> 加 decoding="async" 和 loading="lazy"，提前做预加载， 减少过渡的时候的卡顿  -->
                <img
                  :src="aiSelectedImage"
                  decoding="async"
                  loading="lazy"
                  class="w-full rounded-sm shadow-lg transition-transform duration-300 sm:shadow-xl md:shadow-2xl"
                  draggable="false"
                />
              </div>
            </div>
          </div>

          <!-- 右侧悬停按钮 -->
          <div :class="['absolute top-1/2 right-0 translate-x-full -translate-y-1/2 transition-all duration-500', 'opacity-0 group-hover:translate-x-0 group-hover:opacity-100']">
            <!-- 整个区域可点击的卡片 -->
            <div
              class="cursor-pointer rounded-l-2xl border-l-3 border-blue-500 bg-gradient-to-br from-white to-gray-50 p-2.5 shadow-xl backdrop-blur-sm transition-all duration-300 select-none hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20 active:scale-95 dark:border-blue-400 dark:from-gray-800 dark:to-gray-900"
              @click="handleSkip"
            >
              <!-- 顶部图标区域 -->
              <div class="relative mb-3 flex justify-center">
                <div class="absolute inset-0 animate-pulse rounded-full bg-blue-500 opacity-20 blur-md" />
                <div class="relative flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-md">
                  <UIcon name="heroicons:arrow-top-right-on-square" class="h-5 w-5 text-white" />
                </div>
              </div>

              <!-- 文字内容区域 -->
              <div class="text-center">
                <h3 class="mb-1 bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-sm font-bold text-transparent dark:from-blue-400 dark:to-blue-500">
                  <!-- 前往体验 -->
                  {{ t('welcome.button.go_experience') }}
                </h3>
                <div class="mt-2 h-0.5 w-full rounded-full bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-50" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 输入翻译 -->
      <section ref="section3" class="flex min-h-screen items-center bg-white px-4 sm:px-6 lg:px-8 dark:bg-slate-950">
        <div class="relative container mx-auto px-4 sm:px-6 lg:px-8">
          <div class="grid items-center gap-6 md:gap-8 lg:grid-cols-2 lg:gap-12">
            <div class="relative">
              <img :src="inputTranslationImage" class="w-full rounded-sm shadow-lg transition-transform duration-300 sm:shadow-xl md:shadow-2xl" draggable="false" />
            </div>

            <div>
              <div class="mb-6">
                <h2 class="mb-4 text-xl font-bold text-gray-900 sm:text-2xl md:text-3xl lg:text-4xl dark:text-white">
                  <!-- 双语对照网页翻译 -->
                  {{ t('home.input_translation.title') }}
                </h2>
                <p class="mb-6 text-sm text-gray-600 sm:text-base md:text-lg dark:text-gray-300">
                  <!-- 智能识别并翻译网页内容，在原文后显示译文，实现双语对照阅读。支持各种语言的网页翻译，助您无障碍畅游全球知识海洋！ -->
                  {{ t('home.input_translation.description') }}
                </p>
              </div>

              <!-- 特性列表 -->
              <div class="space-y-3 sm:space-y-4">
                <div v-for="(feature, index) in inputTranslation" :key="index" class="flex items-center space-x-2 rounded-lg bg-gray-50 p-3 sm:space-x-3 sm:p-4 dark:bg-slate-800/50">
                  <div class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/30">
                    <UIcon :name="feature.icon" class="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <p class="text-sm text-gray-700 sm:text-base dark:text-gray-300">
                    {{ feature.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 更多功能 -->
      <section ref="section4" class="flex min-h-screen items-center bg-gray-50 p-4 sm:p-6 lg:px-8 dark:bg-slate-900">
        <div class="relative container mx-auto px-4 sm:px-6 lg:px-8">
          <div class="mb-8 text-center sm:mb-10 md:mb-12">
            <!-- 丰富的翻译功能 -->
            <h2 class="mb-4 text-xl font-bold text-gray-900 sm:text-2xl md:text-3xl lg:text-4xl dark:text-white">
              {{ t('welcome.more_translation_features.title') }}
            </h2>
            <p class="mx-auto max-w-3xl text-sm text-gray-600 sm:text-base md:text-lg dark:text-gray-300">
              {{ t('welcome.more_translation_features.description') }}
            </p>
          </div>

          <!-- 功能网格 -->
          <div class="grid gap-3 sm:gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-3 lg:gap-8">
            <UPageCard
              v-for="(feature, index) in moreFeatures"
              :key="index"
              :title="feature.title"
              :description="feature.description"
              :icon="feature.icon"
              :spotlight="isMobile ? false : true"
              spotlight-color="info"
              class="h-full"
            />
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import ScrolNavigation from './components/ScrolNavigation.vue'
import Bilingual from './bilingual.vue'
import { useLanguageSelection } from '@/pages/welcome/useLanguageSelection'

const { initializeLanguages } = useLanguageSelection()

const { t, locale } = useI18n()

definePageMeta({
  layout: false
})

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('welcome.title'), // 欢迎使用精挑翻译
  ogTitle: t('welcome.title') // 欢迎使用精挑翻译
})

const isMobile = ref(false)

// 当前显示的区域
const currentSection = ref(0)

// 区域引用
const section0 = ref<HTMLElement>()
const section1 = ref<HTMLElement>()
const section2 = ref<HTMLElement>()
const section3 = ref<HTMLElement>()
const section4 = ref<HTMLElement>()

// 区域配置
const sections = [{ ref: section0 }, { ref: section1 }, { ref: section2 }, { ref: section3 }, { ref: section4 }]

// 图片资源
const aiSelectedImage = 'https://assets.selecttranslate.com/web/images/home/<USER>'
const bilingualImage = 'https://assets.selecttranslate.com/web/images/home/<USER>'
const inputTranslationImage = 'https://assets.selecttranslate.com/web/images/home/<USER>' // 输入翻译

// AI择优翻译特性
const aiFeatures = [
  {
    icon: 'i-ri-grid-fill',
    description: t('home.ai_selected_translation.feature1')
  },
  {
    icon: 'i-rivet-icons-check-all',
    description: t('home.ai_selected_translation.feature2')
  },
  {
    icon: 'i-tabler-analyze',
    description: t('home.ai_selected_translation.feature3')
  }
]

// 双语对照特性
const bilingualFeatures = [
  {
    icon: 'i-heroicons-check-circle-16-solid',
    description: t('home.bilingual_translation.feature1')
  },
  {
    icon: 'i-heroicons-adjustments-horizontal-20-solid',
    description: t('home.bilingual_translation.feature2')
  },
  {
    icon: 'i-hugeicons-artificial-intelligence-04',
    description: t('home.bilingual_translation.feature3')
  }
]

// 输入翻译
const inputTranslation = [
  {
    description: t('home.input_translation.feature1'), // 深入应用场景，摆脱使用传统翻译工具的复制、切换、粘贴等繁琐流程。
    icon: 'i-mdi-application-import'
  },
  {
    description: t('home.input_translation.feature2'), // 通过择优翻译，时刻给你最佳译文结果，让跨语言沟通和回复更自信。
    icon: 'i-tabler-thumb-up'
  },
  {
    description: t('home.input_translation.feature3'), // 翻译记录管理，方便您随时查看和复用翻译记录。
    icon: 'i-material-symbols-history-rounded'
  }
]

// 更多功能
const moreFeatures = [
  {
    icon: 'i-bi:file-earmark-pdf',
    title: t('home.pdf_free_translation.title'), // PDF 翻译
    description: t('home.pdf_free_translation.description') // 完全免费的 PDF 翻译功能，日常外语文档快速翻译，满足您大部分 PDF 文档的翻译需求。
  },
  {
    icon: 'i-bi:file-earmark-pdf-fill',
    title: t('home.pdf_plus_translation.title'), // PDF Plus
    description: t('home.pdf_plus_translation.description') // 专业文档翻译，公式、图表高保真还原
  },
  {
    icon: 'i-bi:book',
    title: t('home.epub_translation.title'), // ePub 电子书翻译
    description: t('home.epub_translation.description') // ePub 电子书翻译功能，可以将各种外语 ePub 电子书翻译为双语对照形式或纯目标语言形式进行阅读，完美兼容 Kindle 等各种电子书阅读器。
  },
  {
    icon: 'heroicons:sparkles',
    title: t('home.highlight_translation.title'), // 划词翻译
    description: t('home.highlight_translation.description') // 直接划选单词或文本即可翻译。给您随手、按需的轻量级翻译体验。
  },
  {
    icon: 'heroicons:cursor-arrow-rays',
    title: t('home.mouse_hover_translation.title'), // 鼠标悬停翻译
    description: t('home.mouse_hover_translation.description') // 支持【段落翻译】和【区域翻译】两种模式，鼠标悬停 + 快捷键，可快速翻译网页中单个段落文本或区域中的多个段落文本。
  },
  {
    icon: 'heroicons:language',
    title: t('home.text_translation.title'), // 文本翻译
    description: t('home.text_translation.description') // // 聚合多个翻译平台的引擎，供您随心切换。
  }
]

// 体验区域状态
const showExperienceArea = ref(false)

// 开始体验
const startUsing = () => {
  showExperienceArea.value = true
  // 打开引导气泡
  window.postMessage({ type: 'OPEN_GUIDE_BUBBLE' }, window.location.origin)
}

// 关闭体验区域
const closeExperienceArea = () => {
  showExperienceArea.value = false
}

// 防抖状态
const isScrolling = ref(false)

/**
 * 点击滚动到下一页
 */
const scrollToNextSection = () => {
  // 防抖：如果正在滚动中，则忽略点击
  if (isScrolling.value) {
    return
  }

  // 计算下一个区域的索引
  const nextSectionIndex = currentSection.value + 1

  // 如果已经是最后一个区域，则不执行滚动
  if (nextSectionIndex >= sections.length) {
    return
  }

  // 设置防抖状态
  isScrolling.value = true

  // 获取下一个区域的元素
  const nextSection = sections[nextSectionIndex]?.ref?.value

  if (nextSection) {
    // 滚动到下一个区域
    nextSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })

    // 更新当前区域索引
    currentSection.value = nextSectionIndex
  }

  // 设置防抖延迟（1秒后允许下次点击）
  setTimeout(() => {
    isScrolling.value = false
  }, 1000)
}

// 查看文档
const viewDocs = () => {
  navigateTo('/docs')
}

onMounted(async () => {
  // 初始化移动端检测
  isMobile.value = isMobileDevice()

  // Initialize languages using the composable
  await initializeLanguages(locale.value)
})

// 跳转AI 择优翻译页面
const handleSkip = () => {
  // 在新标签页中打开文本翻译页面
  window.open('/text', '_blank')
}

// 滚动监听功能已移至 ScrolNavigation 组件中
</script>

<style scoped></style>
