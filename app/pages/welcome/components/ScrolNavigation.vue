<!-- 滚动导航 -->
<template>
  <!-- 导航按钮 -->
  <div class="fixed right-13 bottom-15 z-50 transform max-sm:right-5 max-sm:bottom-5">
    <div class="flex flex-col items-center space-y-2 rounded-xl bg-white p-2 shadow-lg max-sm:space-y-2 max-sm:p-2 sm:space-y-3 sm:p-3 dark:bg-slate-800">
      <!-- 上一个区域按钮 -->
      <button
        :disabled="props.currentSection === 0"
        class="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100 text-gray-600 transition-all duration-300 hover:bg-blue-100 hover:text-blue-600 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-300 max-sm:h-7 max-sm:w-7 dark:bg-slate-700 dark:text-gray-300 dark:hover:bg-slate-600 dark:hover:text-blue-400 dark:disabled:bg-slate-800 dark:disabled:text-gray-600"
        @click="navigateSection(-1)"
      >
        <UIcon name="heroicons:chevron-up" class="h-4 w-4 max-sm:h-3.5 max-sm:w-3.5" />
      </button>

      <!-- 当前区域指示器 -->
      <div class="flex flex-col space-y-1 py-1 max-sm:space-y-1 max-sm:py-1 sm:space-y-2">
        <div
          v-for="(section, index) in props.sections"
          :key="index"
          class="h-2 w-2 cursor-pointer rounded-full transition-all duration-300 max-sm:h-2 max-sm:w-2"
          :class="[props.currentSection === index ? 'scale-125 bg-blue-500 max-sm:scale-120' : 'bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500']"
          @click="scrollToSection(index)"
        />
      </div>

      <!-- 下一个区域按钮 -->
      <button
        :disabled="props.currentSection === props.sections.length - 1"
        class="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100 text-gray-600 transition-all duration-300 hover:bg-blue-100 hover:text-blue-600 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-300 max-sm:h-7 max-sm:w-7 dark:bg-slate-700 dark:text-gray-300 dark:hover:bg-slate-600 dark:hover:text-blue-400 dark:disabled:bg-slate-800 dark:disabled:text-gray-600"
        @click="navigateSection(1)"
      >
        <UIcon name="heroicons:chevron-down" class="h-4 w-4 max-sm:h-3.5 max-sm:w-3.5" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义区域类型
interface Section {
  ref: Ref<HTMLElement | undefined>
}

// 接收父组件传递的参数
const props = defineProps<{
  sections: Section[]
  currentSection: number
}>()

const emit = defineEmits<{
  'update:currentSection': [index: number]
}>()

// 导航到指定区域
const scrollToSection = (index: number) => {
  const section = props.sections[index]?.ref?.value
  if (section) {
    section.scrollIntoView({ behavior: 'smooth' })
  }
  emit('update:currentSection', index)
}

// 导航到上一个或下一个区域
const navigateSection = (direction: number) => {
  const newIndex = props.currentSection + direction
  if (newIndex >= 0 && newIndex < props.sections.length) {
    scrollToSection(newIndex)
  }
}

// 监听滚动事件
const handleScroll = () => {
  const scrollY = window.scrollY
  const windowHeight = window.innerHeight

  props.sections.forEach((section, index) => {
    const element = section.ref?.value
    if (element) {
      const rect = element.getBoundingClientRect()
      if (rect.top <= windowHeight / 2 && rect.bottom >= windowHeight / 2) {
        emit('update:currentSection', index)
      }
    }
  })
}
// 使用 nuxt/ui 提供的节流函数优化滚动事件处理
const throttledHandleScroll = useThrottleFn(handleScroll, 16)

onMounted(() => {
  window.addEventListener('scroll', throttledHandleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', throttledHandleScroll)
})
</script>
