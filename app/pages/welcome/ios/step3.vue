<template>
  <GuideLayout
    :current-step="3"
    :total-steps="3"
    :step-data="stepData"
    :has-previous="true"
    :has-next="false"
    :preload-routes="['/welcome/ios/step2', '/welcome/ios/step4']"
    :show-language-select="true"
    @previous-step="previousStep"
    @finish="finishGuide"
  >
    <template #content>
      <div class="mx-auto w-full max-w-4xl text-left">
        <div class="translate-content-container prose-gray dark:prose-invert max-w-none">
          <!-- 标题 -->
          <div class="mb-6 text-center text-2xl font-bold">
            {{ t('safari_guide.step3.article.title') }}
          </div>

          <!-- 内容 -->
          <div class="mb-6 rounded-lg bg-gray-50 p-6 dark:bg-gray-800">
            <div class="space-y-3 text-gray-900 dark:text-gray-100">
              <p v-for="(paragraph, index) in articleContent" :key="index">
                {{ paragraph }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </GuideLayout>
</template>

<script setup lang="ts">
import GuideLayout from './GuideLayout.vue'

const router = useRouter()
const { t, locale } = useI18n()

// 页面元数据
definePageMeta({
  layout: 'simple'
})

// SEO 设置
useSeoMeta({
  titleTemplate: '',
  title: t('safari_guide.title') + ' - ' + t('common.site_name'), // Safari 扩展初始化设置 - 精挑翻译
  description: t('safari_guide.description') // 快速了解如何在 Safari 浏览器中启用精挑翻译扩展
})

// 获取文章内容 - 根据语言环境显示
const articleContent = computed(() => {
  // 英文文章 - 给中文用户测试翻译
  const englishArticle = [
    'Technology has transformed how we live and work. From morning coffee orders to evening entertainment, digital tools shape our daily experiences.',
    'Social media connects us with friends across the globe. We share photos, exchange messages, and stay updated on each other\'s lives instantly.',
    'Online learning has opened new educational opportunities. Students can now access courses from top universities without leaving their homes.',
    'Mobile payment systems have revolutionized shopping. A simple tap or scan can complete transactions in seconds.',
    'Smart home devices respond to voice commands. We can control lights, temperature, and music with just a few words.',
    'Video streaming platforms offer endless entertainment options. Movies, shows, and documentaries are available 24/7.',
    'Cloud storage keeps our files safe and accessible. Important documents are never lost and can be reached from anywhere.',
    'GPS navigation guides us through unfamiliar places. Getting lost is becoming a thing of the past.',
    'Digital creativity tools empower artists and designers. Professional-quality work can be created on personal devices.',
    'The future promises even more integration between digital and physical worlds. Tomorrow\'s innovations will continue to amaze us.'
  ]

  // 韩语文章 - 给英文用户测试翻译
  const koreanArticle = [
    '기술은 우리의 삶과 업무 방식을 근본적으로 변화시켰습니다. 아침 커피 주문부터 밤의 엔터테인먼트까지 디지털 도구는 일상의 경험을 재구성합니다.',
    '소셜 미디어는 전 세계 친구들과 연결해 줍니다. 우리는 사진을 공유하고 메시지를 주고받으며 서로의 일상을 실시간으로 확인합니다.',
    '온라인 학습은 새로운 교육 기회를 열어 주었습니다. 학생들은 집을 떠나지 않고도 명문 대학의 강의를 수강할 수 있습니다.',
    '모바일 결제 시스템은 쇼핑 방식을 혁신했습니다. 간단한 터치나 스캔 한 번으로 거래가 몇 초 만에 완료됩니다.',
    '스마트 홈 기기는 음성 명령에 응답합니다. 몇 마디 말로 조명, 온도, 음악을 제어할 수 있습니다.',
    '영상 스트리밍 플랫폼은 무한한 엔터테인먼트 선택지를 제공합니다. 영화, 드라마, 다큐멘터리를 24시간 언제든 감상할 수 있습니다.',
    '클라우드 스토리지는 우리의 파일을 안전하게 보관하고 접근할 수 있게 합니다. 중요한 문서는 어디서든 손쉽게 열람할 수 있습니다.',
    'GPS 내비게이션은 낯선 곳에서도 길잡이가 되어 줍니다. 길을 잃는 일은 점점 과거의 일이 되고 있습니다.',
    '디지털 창작 도구는 예술가와 디자이너에게 새로운 가능성을 열어 줍니다. 개인 기기에서도 전문적인 작품을 제작할 수 있습니다.',
    '미래에는 디지털과 물리적 세계가 더욱 통합될 것입니다. 내일의 혁신은 계속해서 우리를 놀라게 할 것입니다.'
  ]

  // 根据语言环境返回对应文章
  return locale.value === 'en' ? koreanArticle : englishArticle
})

// 当前步骤数据
const stepData = {
  // 体验网页翻译
  title: t('safari_guide.step3.title'),
  // 你可以通过点击右侧的悬浮按钮 (插件图标)，进行翻译
  description: ''
}

// 上一步
const previousStep = () => {
  window.location.href = '/welcome/ios/step2'
}

// 完成引导 - 跳转到功能介绍页面
const finishGuide = () => {
  window.location.href = '/welcome/ios/step4'
}

onMounted(() => {
  // 打开引导气泡
  window.postMessage({ type: 'OPEN_GUIDE_BUBBLE' }, window.location.origin)
})
</script>
