<!-- 指示器 -->
<template>
  <!-- 指示器 -->
  <div class="flex items-center justify-center space-x-3">
    <div
      v-for="(step, index) in totalSteps"
      :key="index"
      class="relative cursor-pointer"
      @click="emit('goToStep', index + 1)"
    >
      <!-- 初始值从1开始 -->
      <div class="h-3 w-3 rounded-full transition-all duration-300" :class="currentStep === index + 1 ? 'scale-125 bg-blue-500 shadow-lg' : 'bg-gray-300 dark:bg-gray-600'" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentStep: number // 当前步骤
  totalSteps: number // 总步骤
}

defineProps<Props>()

const emit = defineEmits<{
  previousStep: []
  nextStep: []
  goToStep: [index: number]
}>()
</script>
