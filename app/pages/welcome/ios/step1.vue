<template>
  <GuideLayout
    :current-step="1"
    :total-steps="3"
    :step-data="stepData"
    :has-previous="false"
    :has-next="true"
    :preload-routes="['/welcome/ios/step2']"
    @next-step="nextStep"
  />
</template>

<script setup lang="ts">
import GuideLayout from './GuideLayout.vue'

const { t, locale } = useI18n()

// 页面元数据
definePageMeta({
  layout: 'simple'
})

// SEO 设置
useSeoMeta({
  titleTemplate: '',
  title: t('safari_guide.title') + ' - ' + t('common.site_name'), // Safari 扩展初始化设置 - 精挑翻译
  description: t('safari_guide.description') // 快速了解如何在 Safari 浏览器中启用精挑翻译扩展
})

// 当前步骤数据
const stepData = {
  title: t('safari_guide.step1.title'), // 🎉 欢迎！现在启用插件
  description: t('safari_guide.step1.description'), // 精挑翻译 Safari 扩展已经成功安装，现在需要启用它并授予必要的权限。
  // image: `https://assets.selecttranslate.com/web/images/guide_page/${locale.value}/step-1.gif`,
  // 内容 数组
  contents: [
    {
      // 点击搜索框左侧图标
      title: t('safari_guide.step1.content.click_icon'),
      img: `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step1/1-1.png`
    },
    {
      // 点击选项里的「管理扩展」
      title: t('safari_guide.step1.content.click_manage_extensions'),
      img: `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step1/${locale.value}/1-2.png`
    },
    {
      // 开启「精挑翻译」
      title: t('safari_guide.step1.content.open_selecttranslate'),
      img: `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step1/${locale.value}/1-3.png`
    }
  ]
}

// 下一步
const nextStep = () => {
  window.location.href = '/welcome/ios/step2'
}
</script>
