<template>
  <GuideLayout
    :current-step="2"
    :total-steps="3"
    :step-data="stepData"
    :has-previous="true"
    :has-next="true"
    :preload-routes="['/welcome/ios/step1', '/welcome/ios/step3']"
    @previous-step="previousStep"
    @next-step="nextStep"
  />
</template>

<script setup lang="ts">
import GuideLayout from './GuideLayout.vue'

const router = useRouter()
const { t, locale } = useI18n()

// 页面元数据
definePageMeta({
  layout: 'simple'
})

// SEO 设置
useSeoMeta({
  titleTemplate: '',
  title: t('safari_guide.title') + ' - ' + t('common.site_name'), // Safari 扩展初始化设置 - 精挑翻译
  description: t('safari_guide.description') // 快速了解如何在 Safari 浏览器中启用精挑翻译扩展
})

// 当前步骤数据
const stepData = {
  title: t('safari_guide.step2.title'), //  允许扩展访问网站
  description: t('safari_guide.step2.description'), // 请选择"允许"以确保在所有网站上获得翻译功能
  // image: `https://assets.selecttranslate.com/web/images/guide_page/${locale.value}/step-2.gif`,
  contents: [
    {
      // 点击选项里的「精挑翻译」
      title: t('safari_guide.step2.content.click_selecttranslate'),
      img: `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step2/${locale.value}/2-1.png`
    },
    {
      // 选择「始终允许」
      title: t('safari_guide.step2.content.select_always'),
      img: `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step2/${locale.value}/2-2.png`
    },
    {
      // 选择「在每个网站上始终允许」
      title: t('safari_guide.step2.content.select_always_website'),
      img: `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step2/${locale.value}/2-3.png`
    }
  ]
}

// 上一步
const previousStep = () => {
  window.location.href = '/welcome/ios/step1'
}

// 下一步
const nextStep = () => {
  window.location.href = '/welcome/ios/step3'
}
</script>
