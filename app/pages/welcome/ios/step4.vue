<!-- 功能介绍 -->
<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 顶部导航 -->
    <div class="border-b border-gray-200 bg-white px-4 py-4 dark:border-gray-700 dark:bg-gray-800">
      <div class="mx-auto max-w-5xl">
        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">
          {{ stepData.title }}
        </h1>
        <p class="text-sm text-gray-600 dark:text-gray-300">
          {{ stepData.description }}
        </p>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="px-4 py-8">
      <div class="mx-auto w-full max-w-5xl">
        <!-- 功能卡片网格 -->
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="feature in featureList"
            :key="feature.title"
            class="group rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
          >
            <!-- 图片或图标 -->
            <div class="mb-4">
              <div v-if="feature.image" class="mb-4 flex h-32 w-full items-center justify-center overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-700">
                <img :src="feature.image" :alt="feature.title" class="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105" />
              </div>
              <div v-else class="flex h-12 w-12 items-center justify-center rounded-lg" :class="feature.iconBg">
                <UIcon :name="feature.icon" class="h-6 w-6" :class="feature.iconColor" />
              </div>
            </div>

            <h4 class="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
              {{ feature.title }}
            </h4>
            <p v-if="feature.description" class="mb-4 text-sm text-gray-600 dark:text-gray-300">
              {{ feature.description }}
            </p>
            <UButton
              :color="feature.buttonColor"
              variant="ghost"
              size="sm"
              :disabled="feature.disabled"
              :class="[feature.disabled ? 'cursor-not-allowed opacity-50' : feature.hoverClass]"
              @click="feature.disabled ? null : navigateToFeature(feature.route)"
            >
              {{ feature.buttonText }}
              <UIcon v-if="!feature.disabled" name="i-heroicons-arrow-right" class="ml-1 h-4 w-4" />
            </UButton>
          </div>
        </div>

        <!-- 底部提示 -->
        <!-- <div class="mt-8 rounded-lg bg-blue-50 p-6 text-center dark:bg-blue-900/20">
          <div class="mb-2 text-lg font-semibold text-blue-900 dark:text-blue-100">🎉 恭喜！你已完成所有设置</div>
          <p class="text-blue-700 dark:text-blue-200">现在可以开始使用精挑翻译的各种功能了，祝你翻译愉快！</p>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t, locale } = useI18n()

const router = useRouter()

// 页面元数据
definePageMeta({
  layout: 'simple'
})

// SEO 设置
useSeoMeta({
  titleTemplate: '',
  title: t('safari_guide.title') + ' - ' + t('common.site_name'), // Safari 扩展初始化设置 - 精挑翻译
  description: t('safari_guide.description') // 快速了解如何在 Safari 浏览器中启用精挑翻译扩展
})

/**
 * 当前步骤数据
 * 标题、描述
 */
const stepData = {
  title: '🎉 恭喜！你已完成所有设置',
  description: '现在可以开始使用精挑翻译的各种功能了，祝你翻译愉快！'
}

// 定义功能项类型
interface FeatureItem {
  icon: string
  iconBg: string
  iconColor: string
  title: string
  description?: string
  buttonText: string
  buttonColor: string
  hoverClass: string
  route?: string
  disabled: boolean
  image?: string // 新增图片字段
}

// 功能列表数据
const featureList: FeatureItem[] = [
  {
    icon: 'i-heroicons-globe-alt',
    iconBg: 'bg-green-100 dark:bg-green-900',
    iconColor: 'text-green-600 dark:text-green-400',
    // 双语对照网页翻译
    title: t('home.bilingual_translation.title'),
    // 智能识别并翻译网页内容，在原文后显示译文，实现双语对照阅读。支持各种语言的网页翻译，助您无障碍畅游全球知识海洋！
    description: t('home.bilingual_translation.description'),
    buttonText: '浏览器扩展功能',
    buttonColor: 'green',
    hoverClass: 'group-hover:bg-green-50 dark:group-hover:bg-green-900/20',
    disabled: true,
    image: 'https://assets.selecttranslate.com/web/images/home/<USER>'
  },
  // AI 择优翻译
  {
    icon: 'i-heroicons-sparkles',
    iconBg: 'bg-purple-100 dark:bg-purple-900',
    iconColor: 'text-purple-600 dark:text-purple-400',
    // AI 择优翻译
    title: t('home.ai_selected_translation.title'),
    // 使用多个翻译模型对同一原文进行翻译会得到多个不同的结果，通过智能评分从中择优获取最佳译文，能大幅提升翻译结果的准确度和可靠性。
    description: t('home.ai_selected_translation.description'),
    buttonText: '立即体验',
    buttonColor: 'purple',
    hoverClass: 'group-hover:bg-purple-50 dark:group-hover:bg-purple-900/20',
    route: '/text',
    disabled: false,
    image: 'https://assets.selecttranslate.com/web/images/home/<USER>'
  },
  // PDF 免费翻译
  {
    icon: 'i-heroicons-document-text',
    iconBg: 'bg-red-100 dark:bg-red-900',
    iconColor: 'text-red-600 dark:text-red-400',
    title: t('home.pdf_free_translation.title'),
    description: '上传PDF文档进行翻译，保持原有格式，支持双语对照显示',
    buttonText: '免费使用',
    buttonColor: 'red',
    hoverClass: 'group-hover:bg-red-50 dark:group-hover:bg-red-900/20',
    route: '/file',
    disabled: false,
    image: 'https://assets.selecttranslate.com/web/images/home/<USER>'
  },
  // PDF Plus
  {
    icon: 'i-heroicons-document-text',
    iconBg: 'bg-blue-100 dark:bg-blue-900',
    iconColor: 'text-blue-600 dark:text-blue-400',
    title: t('home.pdf_plus_translation.title'),
    description: 'AI智能解读PDF内容，不仅翻译还能解释、总结和回答文档相关问题',
    buttonText: '立即体验',
    buttonColor: 'blue',
    hoverClass: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    route: '/pdf-plus',
    disabled: false,
    image: 'https://assets.selecttranslate.com/web/images/home/<USER>'
  },
  {
    icon: 'i-heroicons-gift',
    iconBg: 'bg-yellow-100 dark:bg-yellow-900',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
    title: '翻译资源包',
    description: '购买翻译额度，享受无限制的AI翻译服务',
    buttonText: '查看套餐',
    buttonColor: 'yellow',
    hoverClass: 'group-hover:bg-yellow-50 dark:group-hover:bg-yellow-900/20',
    route: '/pricing',
    disabled: false
  }
]

// 返回上一页
const goBack = () => {
  window.location.href = '/welcome/ios/step3'
}

// 导航到功能页面
const navigateToFeature = (route: string) => {
  if (route) {
    window.location.href = route
  }
}
</script>
