<template>
  <div class="relative flex h-full w-full flex-col bg-gray-50 dark:bg-gray-900">
    <!-- 装饰性背景元素 -->
    <div class="pointer-events-none absolute inset-0 overflow-hidden">
      <div class="absolute -top-40 -right-40 h-80 w-80 animate-pulse rounded-full bg-blue-100/30 blur-3xl dark:bg-blue-900/20" />
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-white/90 backdrop-blur-sm dark:bg-gray-900/90">
      <div class="flex items-center space-y-4">
        <div class="relative">
          <div class="h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600" />
        </div>
      </div>
    </div>

    <!-- 引导内容 -->
    <div class="relative flex h-full w-full flex-col px-6 py-6 pb-32 md:px-8 md:py-8 lg:px-12">
      <!-- 标题和描述 -->
      <div class="mb-5 text-center">
        <div class="mb-3">
          <h2 class="text-2xl font-bold text-blue-600 md:text-3xl lg:text-4xl dark:text-blue-400">
            {{ stepData.title }}
          </h2>
        </div>
        <!-- 如果是第三页引导页 -->
        <!-- 你可以通过点击右侧的悬浮球 (插件图标)，进行翻译 -->
        <div v-if="currentStep === 3" class="flex items-center justify-center">
          {{ t('welcome.click_floating_button') }}
          <span><img :src="favicon" class="size-5 mx-2" /></img></span>
          {{ t('welcome.to_translate') }}
        </div>
        <p v-else class="mx-auto max-w-2xl text-base leading-relaxed text-gray-600 md:text-lg lg:text-xl dark:text-gray-300">
          {{ stepData.description }}
        </p>
      </div>

      <div v-if="showLanguageSelect" class="mb-8 flex items-center justify-center">
        <div class="rounded-2xl bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div class="flex items-center space-x-4">
            <!-- 目标语言 -->
            <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t('selected_trans.settings.target_language') }}：
            </div>
            <!-- 语言选择 -->
            <USelectMenu
              v-if="languagesReady"
              v-model="selectedLanguage"
              value-key="zing_code"
              searchable
              variant="ghost"
              :ui="{ content: 'w-full' }"
              :items="availableLanguages"
              class="w-42 min-w-0 cursor-pointer text-sm text-neutral-600 dark:text-neutral-400"
              @update:model-value="updateLanguageSelection"
            />
          </div>
        </div>
      </div>

      <!-- 自定义内容区域 -->
      <slot name="content">
        <div class="flex flex-1 flex-col items-center justify-center space-y-5">
          <div v-for="(item, index) in stepData.contents" :key="index" class="group">
            <div class="mb-2 text-center">
              <span
                class="inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium"
                :class="{ 'dark:bg-blue-900/30': index === 0, 'bg-orange-100 dark:bg-orange-900/30': index === 1, 'bg-red-100 dark:bg-red-900/30': index === 2 }"
              >
                <span :class="{ 'text-blue-800 dark:text-blue-200': index === 0, 'text-orange-800 dark:text-orange-200': index === 1, 'text-red-800 dark:text-red-200': index === 2 }">
                  {{ item.title }}
                </span>
              </span>
            </div>
            <NuxtImg
              :src="item.img"
              :alt="stepData.title"
              loading="eager"
              :placeholder="[50, 50, 75, 5]"
              format="webp"
              quality="90"
              class="mx-auto w-80 rounded-2xl object-cover"
            />
          </div>
        </div>
      </slot>
    </div>

    <div class="fixed right-0 bottom-0 left-0">
      <!-- 操作按钮 -->
      <div class="border-t border-gray-200/50 bg-white/80 px-4 py-4 backdrop-blur-md dark:border-gray-700/50 dark:bg-gray-800/80">
        <div class="relative mx-auto flex max-w-4xl items-center justify-between">
          <!-- 上一步 -->
          <UButton
            v-if="hasPrevious"
            color="primary"
            size="lg"
            class="rounded-xl bg-blue-500 px-4 py-3 font-medium hover:bg-blue-600"
            @click="handlePreviousStep"
          >
            <Icon name="heroicons:arrow-left" class="h-4 w-4" />
            {{ t('safari_guide.previous_step') }}
          </UButton>
          <div v-else class="w-24" />

          <Indicator :current-step="currentStep" :total-steps="totalSteps" />

          <!-- 下一步/开始使用 -->
          <UButton
            v-if="hasNext"
            color="primary"
            size="lg"
            class="rounded-xl bg-blue-500 px-4 py-3 font-medium hover:bg-blue-600"
            @click="handleNextStep"
          >
            {{ t('safari_guide.next_step') }}
            <Icon name="heroicons:arrow-right" class="h-4 w-4" />
          </UButton>
          <UButton
            v-else
            color="primary"
            size="lg"
            class="rounded-xl bg-red-500 px-4 py-3 font-medium hover:bg-red-600"
            @click="handleFinish"
          >
            <Icon name="heroicons:rocket-launch" class="h-4 w-4" />
            {{ t('safari_guide.get_started') }}
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Indicator from '@/pages/welcome/ios/indicator.vue'
import { useLanguageSelection } from '@/pages/welcome/useLanguageSelection'
import favicon from '@/assets/images/favicon.png'

const { t, locale } = useI18n()

// Use the language selection composable
const { selectedLanguage, availableLanguages, isReady: languagesReady, initializeLanguages, updateLanguageSelection } = useLanguageSelection()

interface StepData {
  title: string
  description: string
  // image?: string;
  contents?: {
    title: string
    img: string
  }[]
}

interface Props {
  currentStep: number
  totalSteps: number
  stepData: StepData
  hasPrevious?: boolean
  hasNext?: boolean
  preloadRoutes?: string[]
  showLanguageSelect?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  hasPrevious: true,
  hasNext: true,
  preloadRoutes: () => []
})

const emit = defineEmits<{
  previousStep: []
  nextStep: []
  goToStep: [index: number]
  finish: []
}>()

// 加载状态
const isLoading = ref(false)

// 事件处理函数
const handlePreviousStep = () => {
  isLoading.value = true
  // 添加短暂延迟以显示加载状态
  setTimeout(() => {
    emit('previousStep')
  }, 100)
}

// 下一步
const handleNextStep = () => {
  isLoading.value = true
  // 添加短暂延迟以显示加载状态
  setTimeout(() => {
    emit('nextStep')
  }, 100)
}

// 完成引导
const handleFinish = () => {
  isLoading.value = true
  // 添加短暂延迟以显示加载状态
  setTimeout(() => {
    emit('finish')
  }, 100)
}

// 页面加载前
onBeforeMount(async () => {
  // Initialize languages using the composable
  await initializeLanguages(locale.value)
})

// 页面生命周期
onMounted(async () => {
  // 预加载关键资源
  if (import.meta.client) {
    // 预加载下一步的图片
    preloadNextStepImages()

    // 预连接到CDN
    preconnectToCDN()
  }
})

// 预加载下一步图片
const preloadNextStepImages = () => {
  const nextStepImages = [
    // Step 1 images
    `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step1/1-1.png`,
    `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step1/${locale.value}/1-2.png`,
    `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step1/${locale.value}/1-3.png`,
    // Step 2 images
    `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step2/${locale.value}/2-1.png`,
    `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step2/${locale.value}/2-2.png`,
    `https://ztsl-pro.oss-cn-shanghai.aliyuncs.com/web/images/guide_page/ios/step2/${locale.value}/2-3.png`
  ]

  nextStepImages.forEach((imageSrc) => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = imageSrc
    document.head.appendChild(link)
  })
}

// 预连接到CDN
const preconnectToCDN = () => {
  const cdnDomains = ['https://ztsl-pro.oss-cn-shanghai.aliyuncs.com']

  cdnDomains.forEach((domain) => {
    const link = document.createElement('link')
    link.rel = 'preconnect'
    link.href = domain
    document.head.appendChild(link)
  })
}

// 离开组件
onBeforeUnmount(() => {
  // 重置 loading 状态
  isLoading.value = false
})
</script>
