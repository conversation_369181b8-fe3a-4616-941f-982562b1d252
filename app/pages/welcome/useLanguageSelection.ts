import { useLanguageList, useCurrentTargetLanguage } from '@/composables/usePluginData'

/**
 * 引导页面的语言选择组合式函数
 * 提供响应式语言列表和选中语言管理
 */
export const useLanguageSelection = () => {
  // 初始化状态
  const languageList = ref<any[]>([]) // 语言列表(包含自动检测语言)
  const selectedLanguage = ref<string>('en') // 选中的语言
  const isLoading = ref(false) // 加载状态

  /**
   * 初始化语言数据
   * 获取语言列表和当前目标语言
   */
  const initializeLanguages = async (locale: string) => {
    try {
      isLoading.value = true

      // 并行获取语言列表和当前目标语言
      const [languagesList, currentTargetLanguage] = await Promise.all([useLanguageList(locale), useCurrentTargetLanguage()])

      languageList.value = languagesList
      selectedLanguage.value = currentTargetLanguage

      // 确保选中的语言在语言列表中存在
      if (languagesList.length > 0 && currentTargetLanguage) {
        const foundLanguage = languagesList.find(lang => lang.zing_code === currentTargetLanguage)

        if (foundLanguage) {
          selectedLanguage.value = foundLanguage.zing_code
        }
        else {
          // 如果找不到，使用第一个非自动检测的语言
          const firstValidLanguage = languagesList.find(lang => lang.zing_code !== 'auto')
          if (firstValidLanguage) {
            selectedLanguage.value = firstValidLanguage.zing_code
          }
        }
      }
    }
    catch (error) {
      console.error('Failed to initialize languages:', error)
    }
    finally {
      isLoading.value = false
    }
  }

  /**
   * 处理语言变更
   * @param newLanguage - 新选择的语言代码
   */
  const updateLanguageSelection = (newLanguage: string) => {
    selectedLanguage.value = newLanguage
    // 可以在这里添加额外的逻辑，比如保存到存储等

    // 更新目标语言
    window.postMessage({
      type: 'updateTargetLanguage',
      targetLanguageZingCode: newLanguage
    })
  }

  /**
   * 获取可用语言列表（排除自动检测）
   */
  const availableLanguages = computed(() => {
    return languageList.value.slice(1) // 移除第一项（通常是自动检测）
  })

  /**
   * 检查语言是否准备就绪
   */
  const isReady = computed(() => {
    return languageList.value.length > 0 && !isLoading.value
  })

  /**
   * 获取当前选中的语言对象
   */
  const currentLanguageObject = computed(() => {
    return languageList.value.find(lang => lang.zing_code === selectedLanguage.value)
  })

  return {
    // 状态
    languageList: readonly(languageList),
    selectedLanguage,
    isLoading: readonly(isLoading),

    // 计算属性
    availableLanguages,
    isReady,
    currentLanguageObject,

    // 方法
    initializeLanguages,
    updateLanguageSelection
  }
}
