<template>
  <div>
    <UContainer>
      <!-- 资源包价格套餐 -->
      <ResourcePackPricingPlan :resource-type="resourceType" />
    </UContainer>
  </div>
</template>

<script setup lang="ts">
import ResourcePackPricingPlan from './components/ResourcePackPricingPlan.vue'

const { t, locale } = useI18n()
const route = useRoute()

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('resource.title') + ' - ' + t('common.site_name'),
  ogTitle: t('resource.title') + ' - ' + t('common.site_name'),
  description: t('common.description'),
  ogDescription: t('common.description')
})

defineOgImageComponent('Saas')

/** 资源包类型 */
const resourceType = ref('general_token') // 资源包类型: general_token 通用翻译额度（Token）流量包, pdf_plus PDF_Plus 加量包

onBeforeMount(() => {
  // 从路由中获取资源包类型
  resourceType.value = route.query.type as string // 资源包类型
})
</script>

<style lang="css" scoped>
.pt-2 {
  padding-top: 0;
}
</style>
