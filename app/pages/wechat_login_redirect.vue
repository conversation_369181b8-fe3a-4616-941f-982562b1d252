<template>
  <div class="text-center">
    <!-- 欢迎登录 -->
    {{ t('auth.common.welcome_to_login') }}
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import type { WechatLoginType } from '@/api/login/types'
import { useAuthStoreWithOut, COOKIE_EXPIRES_DATE } from '@/store/modules/auth'

// definePageMeta 定义页面的布局 (此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'authentication' // 使用 layouts/authentication.vue 布局
})

const { t, locale } = useI18n()

// const expiresDate = new Date()
// expiresDate.setTime(expiresDate.getTime() + COOKIE_EXPIRES_DATE * 24 * 60 * 60 * 1000) // 设置cookie有效时间（要与后端服务设置的token有效时长一致）
// const ztsl_token = useCookie('ztsl_token', { expires: expiresDate })
const expiresDate = new Date()
expiresDate.setDate(expiresDate.getDate() + COOKIE_EXPIRES_DATE) // 设置cookie有效天数（要与后端服务设置的token有效时长一致）
expiresDate.setHours(expiresDate.getHours() + 8) // 调整为 东八区 CST 时间，8小时时差
const ztsl_token = useCookie('ztsl_token', { expires: expiresDate })

useHead({
  // 欢迎登录
  titleTemplate: '',
  title: t('auth.common.welcome_to_login') + ' - ' + t('common.site_name')
})

const router = useRouter()
const route = useRoute()

// 登录来源：web:官网(默认)、extension：扩展程序
const from = route.query.from || 'web'

// 页面挂载时加载
onMounted(() => {
  wechatLogin()
})

const wechatLogin = async () => {
  const code = route.query.code || null
  const state = route.query.state || null
  // 用户授权登录
  if (code) {
    // 用户同意登录
    const loginData: WechatLoginType = {
      code: code ? code.toString() : '',
      state: state ? state.toString() : '',
      method: 'wechat', // 3：微信认证登录
      platform: 'web'
    }
    const from = route.query.from as string // 登录来源：extension：扩展程序
    const authStore = useAuthStoreWithOut() // 要在函数内部使用，不能放文件头部统一创建
    const res = await authStore.wechatLogin(loginData)
    if (res.code == 200) {
      ztsl_token.value = `${res.data.access_token}` // 将token保存到cookie,供扩展插件调用
      // 返回参数如果绑定微信返回token正常存储登录未绑定账号返回unionid绑定当然也可以以微信信息为主题自动生成账号
      // 登录成功，并提示,跳转到用户主页面
      if (from) {
        // 从扩展插件登录成功后，返回扩展插件
        // window.opener.postMessage('success', '*');
        window.close()
      }
      else {
        // 登录成功，并提示,跳转到用户主页面
        navigateTo({ path: '/' + locale.value + '/account' })
      }
    }
    else {
      // 登录失败
      console.log(res.message)
      alert(res.message)
      router.push({ path: '/' + locale.value + '/login' })
    }
  }
  else {
    // 用户取消登录
    router.push({ path: '/' + locale.value + '/login' })
  }
}
</script>

<style scoped>
.text-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
