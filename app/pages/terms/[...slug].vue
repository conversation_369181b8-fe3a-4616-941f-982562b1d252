<script setup lang="ts">
definePageMeta({
  layout: 'docs' // 使用文档布局(全屏)
})

const route = useRoute()

const { locale } = useI18n()

// 文档目录路径
const catalogsPath = route.path.toLocaleLowerCase()

const { data: navigation } = await useAsyncData('navigation', () => queryCollectionNavigation(`${locale.value}_terms`))
// console.log('navigation:', navigation.value);
const navigation_links = navigation.value[0].children[0].items
// console.log('navigation_links:', navigation_links);

// 文件内容搜索
const { data: files } = useLazyAsyncData('search', () => queryCollectionSearchSections(`${locale.value}_terms`), {
  server: false
})

/**
 * 根据文档当前路径来获取对应的文档数据
 * 当前路径 /zhhans/docs/installation
 * 他就会获取到 installation 的数据
 * */
const { data: page } = await useAsyncData(`${locale.value}_terms`, () => {
  return queryCollection(`${locale.value}_terms`).path(catalogsPath).first()
})

// 如果页面不存在则抛出404错误
if (!page.value) {
  throw createError({
    statusCode: 404,
    statusMessage: `Page not found for path: ${route.path}`, // 更具体的错误信息
    fatal: true
  })
}

const { data: surround } = await useAsyncData('surround', () => {
  return queryCollectionItemSurroundings(`${locale.value}_terms`, catalogsPath)
})

// console.log('surround', surround.value);

// 设置SEO元数据
useSeoMeta({
  title: page.value?.title,
  ogTitle: page.value?.title,
  description: page.value?.description,
  ogDescription: page.value?.description
})

onMounted(async () => {
  // 获取所有的a标签，设置target为_blank，解决Markdown中链接在新窗口打开的问题
  const faq_content_divs = document.querySelectorAll('[class="custom_terms_content"]')
  faq_content_divs.forEach(function (div) {
    div.querySelectorAll('a').forEach(function (link) {
      link.setAttribute('target', '_blank')
      link.setAttribute('rel', 'noopener noreferrer')
    })
  })
})

defineOgImageComponent('Saas')
</script>

<template>
  <!-- 1 -->
  <UPage v-if="page">
    <template #left>
      <UPageAside>
        <template #top>
          <!-- 搜索组件 -->
          <UContentSearchButton :collapsed="false" class="w-full rounded-md" />
        </template>
        <!-- 文档目录 -->
        <UContentNavigation :navigation="navigation_links" highlight />
      </UPageAside>
    </template>

    <!-- 11 -->
    <UPageHeader
      v-if="page.title && page.description && page.links"
      :title="page.title"
      :description="page.description"
      :links="page.links"
    />

    <UPageBody>
      <!-- 111 -->
      <!-- 注意添加判断 不然会报错-渲染不出来 -->
      <ContentRenderer v-if="page.body" :value="page" class="custom_terms_content" />

      <!-- 分割线 -->
      <USeparator v-if="surround?.length" />

      <UContentSurround :surround="surround" />
    </UPageBody>

    <!-- 注意添加判断 不然会报错-渲染不出来 -->
    <template v-if="page?.body?.toc?.links?.length" #right>
      <UContentToc :links="page.body?.toc?.links" :title="$t('tutorial.table_of_contents')" />
    </template>

    <ClientOnly>
      <!-- ClientOnly 包裹 ContentSearch 组件仅限客户端组件，因此它不会在服务器上呈现 -->
      <!-- 内容搜索组件（全局），通过搜索按钮 UContentSearchButton 触发 -->
      <LazyUContentSearch
        :files="files"
        shortcut="meta_k"
        :navigation="navigation[0].children[0].children"
        :fuse="{ resultLimit: 42 }"
        :color-mode="false"
      />
    </ClientOnly>
  </UPage>
</template>
