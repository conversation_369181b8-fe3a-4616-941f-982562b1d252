<template>
  <div>
    <!-- NuxtUI3 升级版本 移除ULdandingHero 组件 使用 PageHero -->
    <UPageHero :ui="{ container: 'py-12 sm:py-16 md:py-20 lg:py-20', title: 'text-3xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-neutral-900 dark:text-neutral-100' }">
      <template #title>
        <span>{{ t('extensions.crx.title') }}</span>
      </template>
      <div class="flex h-full items-center justify-center">
        <UButton
          size="xl"
          color="neutral"
          variant="subtle"
          :trailing="false"
          class="rounded-lg px-6 py-4"
          @click="getDownloadUrl"
        >
          <img src="../../assets/icon/icon-crx-puzzle.svg" class="iconify h-6 w-6 flex-shrink-0" />
          {{ t('extensions.crx.download_button') }}
        </UButton>
      </div>
      <div class="landing-grid absolute inset-0 z-[-1] [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]" />
    </UPageHero>

    <UPageSection
      v-for="(section, index) in sections"
      :key="index"
      :class="'relative py-5 sm:py-26 md:py-5'"
      :align="section.align"
    >
      <!-- NuxtUI3 版本迁移 不要使用（PageSection）， 请使用 -->
      <template #title>
        <span v-if="section.title" class="text-2xl sm:text-2xl md:text-3xl lg:text-3xl" v-html="section.title" />
      </template>
      <template #description>
        <span v-if="section.description" class="text-base sm:text-base md:text-lg lg:text-lg" v-html="section.description" />
      </template>
      <UCarousel
        v-if="index === 0"
        v-slot="{ item }"
        :items="crx_download_hans_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-else-if="index === 1"
        v-slot="{ item }"
        :items="crx_settings_hans_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-else-if="index === 2"
        v-slot="{ item }"
        :items="crx_install_step1_hans_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-else-if="index === 3"
        v-slot="{ item }"
        :items="crx_install_step2_hans_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-else-if="index === 4"
        v-slot="{ item }"
        :items="crx_install_step3_hans_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <ImagePlaceholder v-else />
    </UPageSection>

    <!-- crx faq 常见问题 -->
    <CrxFaq />
  </div>
</template>

<script setup lang="ts">
import type { GetAppDownloadUrl } from '@/api/types'
import { getAppDownloadUrl } from '@/api/resource'
import CrxFaq from '@/pages/extensions/CrxFaq.vue'

const { t: t } = useI18n()

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('extensions.crx.title'), // 精挑翻译 Chrome 插件下载安装教程
  ogTitle: t('extensions.crx.title'),
  description: t('common.description'),
  ogDescription: t('common.description')
})

const description = computed(() => {
  return (text: string) => {
    console.log('text', text)
  }
})

const getDownloadUrl = async () => {
  console.log('点击下载按钮')
  const data: GetAppDownloadUrl = {
    application_no: 'chrome'
  }
  const res = await getAppDownloadUrl(data)
  console.log('获取到的下载地址res.data=', res.data)
  const url = res.data
  if (url) {
    window.open(url, '_blank')
  }
}

/** 下载并解压 chrome crx 插件安装包 */
const crx_download_hans_images = ['https://assets.selecttranslate.com/web/images/download/selecttranslate-chrome-crx-download_hans.png']

/** 打开 chrome 扩展程序管理界面，开启 Developer mode */
const crx_settings_hans_images = ['https://assets.selecttranslate.com/web/images/download/selecttranslate-chrome-crx-settings_hans.png']

/** 拖放安装插件 crx 包 */
const crx_install_step1_hans_images = ['https://assets.selecttranslate.com/web/images/download/selecttranslate-chrome-crx-install-step1_hans.png']

/** 拖放安装插件 crx 包 */
const crx_install_step2_hans_images = ['https://assets.selecttranslate.com/web/images/download/selecttranslate-chrome-crx-install-step2_hans.png']

/** 拖放安装插件 crx 包 */
const crx_install_step3_hans_images = ['https://assets.selecttranslate.com/web/images/download/selecttranslate-chrome-crx-install-step3_hans.png']

// sections
const sections = [
  {
    title: t('extensions.crx.download_package.title'), // 下载 精挑翻译 crx 安装包
    description: t('extensions.crx.download_package.description'),
    align: 'right'
  },
  {
    title: t('extensions.crx.chrome_extensions_dev_mode.title'), // 安装 精挑翻译 插件
    description: t('extensions.crx.chrome_extensions_dev_mode.description'),
    align: 'right'
  },
  {
    title: t('extensions.crx.install_step1.title'), // 安装 精挑翻译 插件
    description: t('extensions.crx.install_step1.description'),
    align: 'right'
  },
  {
    title: t('extensions.crx.install_step2.title'), // 启用 精挑翻译 插件
    description: t('extensions.crx.install_step2.description'),
    align: 'right'
  },
  {
    title: t('extensions.crx.install_step3.title'), // 启用 精挑翻译 插件
    description: t('extensions.crx.install_step3.description'),
    align: 'right'
  }
]

/**
 * 下载crx文件动画gif.
 */
const download_crx_images = ['']

/**
 * 安装crx动画gif.
 *
 */
const install_translate_images = ['']

/**
 * 启用精挑翻译插件动画gif.
 */
const open_select_translate_images = ['']
</script>
