<template>
  <!-- CRX FAQ 常见问题 -->
  <!-- description: 有疑问？请查看下面的常见问题，或者联系我们的客服。 -->
  <UPageSection :description="t('extensions.crx.faq.desc')">
    <!-- NuxtUI3 版本迁移 不要使用（PageSection）， 请使用 -->
    <template #title>
      <span class="inline-flex">
        <span class="text-sky-500"><UIcon name="i-wpf-faq" /></span>
        <!-- title: crx 方式安装常见问题 -->
        <span class="ml-4">{{ t('extensions.crx.faq.title') }}</span>
      </span>
    </template>
    <!-- nuxtUi 版本迁移https://ui.nuxt.com/components/page-accordion -->
    <UPageAccordion
      :items="fqa_items"
      multiple
      :ui="{
        wrapper: 'divide-y divide-gray-200 dark:divide-gray-800 -mt-6',
        item: {
          size: 'text-base text-neutral-600 dark:text-neutral-400',
          padding: 'py-6'
        },
        button: {
          base: 'text-left text-lg py-3',
          label: 'text-neutral-900 dark:text-white',
          trailingIcon: {
            name: 'i-heroicons-chevron-down-20-solid',
            base: 'w-5 h-5 ms-auto transform transition-transform duration-200 flex-shrink-0 mr-1.5',
            active: '',
            inactive: '-rotate-90'
          }
        }
      }"
    />
  </UPageSection>
</template>

<script setup lang="ts">
const { t: t } = useI18n()

/** FQA 常见问题 */
const fqa_items = [
  {
    label: t('extensions.crx.faq.items.item1.title'), // 将 crx 文件拖入扩展程序管理界面没反应，或者提示已阻止 - 不明来源。
    content: t('extensions.crx.faq.items.item1.desc'), // 检查以上第2步操作：在浏览器地址栏输入 chrome://extensions 并回车，进入扩展程序管理界面，在右上角开启【开发者模式】，重新拖入 crx 文件即可。
    defaultOpen: 1
  },
  {
    label: t('extensions.crx.faq.items.item2.title'), // 通过 "加载已解压的拓展程序" 安装没有可选的文件，或者提示 "未能成功加载扩展程序"。
    content: t('extensions.crx.faq.items.item2.desc'), // 注意：不要通过【加载已解压的扩展程序】导入 crx，必须用拖入 crx 文件的方式安装 。
    defaultOpen: 1
  },
  {
    label: t('extensions.crx.faq.items.item3.title'), // 浏览器不支持 crx 文件。
    content: t('extensions.crx.faq.items.item3.desc'), // 如果浏览器不支持 crx 文件安装，可使用 zip 安装包方式。
    defaultOpen: 1
  }
]
</script>
