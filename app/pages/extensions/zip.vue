<template>
  <div>
    <!-- NuxtUI3 升级版本 移除ULdandingHero 组件 使用 PageHero -->
    <UPageHero :ui="{ container: 'py-12 sm:py-16 md:py-20 lg:py-20', title: 'text-4xl font-bold tracking-tight text-neutral-900 dark:text-white sm:text-6xl' }">
      <template #title>
        <span>{{ t('extensions.zip.title') }}</span>
      </template>
      <div class="flex h-full items-center justify-center">
        <UButton
          icon="line-md-download-outline"
          size="xl"
          color="primary"
          variant="solid"
          :label="t('extensions.zip.download_button')"
          :trailing="false"
          @click="getDownloadUrl"
        />
      </div>
      <div class="landing-grid absolute inset-0 z-[-1] [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]" />
    </UPageHero>
    <!-- NuxtUI3 版本迁移 不要使用（PageSection）， 请使用 -->
    <UPageSection
      v-for="(section, index) in sections"
      :key="index"
      :class="'relative py-5 sm:py-26 md:py-5'"
      :title="section.title"
      :description="section.description"
      :align="section.align"
      :features="section.features"
    >
      <UCarousel
        v-if="index === 0"
        v-slot="{ item }"
        :items="download_zip_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-else-if="index === 1"
        v-slot="{ item }"
        :class="'relative py-5 sm:py-26 md:py-15'"
        :items="open_dev_model_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-else-if="index === 2"
        v-slot="{ item }"
        :class="'relative py-5 sm:py-26 md:py-15'"
        :items="install_translate_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-else-if="index === 3"
        v-slot="{ item }"
        :class="'relative py-5 sm:py-26 md:py-15'"
        :items="open_select_translate_images"
        :ui="{ item: 'basis-full' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <ImagePlaceholder v-else />
    </UPageSection>
  </div>
</template>

<script setup lang="ts">
import type { GetAppDownloadUrl } from '@/api/types'
import { getAppDownloadUrl } from '@/api/resource'

const { t: t } = useI18n()

const getDownloadUrl = async () => {
  console.log('点击下载按钮')
  const data: GetAppDownloadUrl = {
    application_no: 'chrome-zip'
  }
  const res = await getAppDownloadUrl(data)
  console.log('获取到的下载地址res.data=', res.data)
  const url = res.data
  if (url) {
    window.open(url, '_blank')
  }
}

// sections
const sections = [
  {
    title: t('extensions.zip.download_install_package.title'), // 下载 crx 安装包
    align: 'left',
    features: [
      {
        name: t('extensions.zip.download_install_package.item1'), // 下载并且解压 zip 安装包
        icon: 'flat-color-icons-download'
      },
      {
        name: t('extensions.zip.download_install_package.item2'), // 解压后的文件夹需要永久保留，否则影响使用
        icon: 'icon-park-folder-open'
      }
    ]
  },
  {
    title: t('extensions.zip.open_dev_model.title'), // 开启开发者模式
    align: 'right',
    features: [
      {
        name: t('extensions.zip.open_dev_model.item1'), // 在地址栏输入 "chrome://extensions/" 并回车
        icon: 'flat-color-icons-link'
      },
      {
        name: t('extensions.zip.open_dev_model.item2'), // 入扩展程序管理页面，在右上角开启 "开发者模式"
        icon: 'codicon-gear'
      }
    ]
  },
  {
    title: t('extensions.zip.install_package.title'), // 安装 精挑翻译 插件
    align: 'left',
    features: [
      {
        name: t('extensions.zip.install_package.item1'), // 方式一：把解压出来的文件夹拖入扩展程序管理页面
        icon: 'tabler-point'
      },
      {
        name: t('extensions.zip.install_package.item2'), // 方式二：点击左上角 "加载已解压的拓展程序"，选择解压出来的文件夹
        icon: 'tabler-point'
      }
    ]
  },
  {
    title: t('extensions.zip.enable_package.title'), // 启用 精挑翻译 插件
    align: 'right',
    features: [
      {
        name: t('extensions.zip.enable_package.item1'), // 点击状态栏 "拓展程序" 按钮
        icon: 'lsicon-puzzle-outline'
      },
      {
        name: t('extensions.zip.enable_package.item2'), // 点击 "固定" 即可完成设置
        icon: 'gis-position-o'
      }
    ]
  }
]

/**
 * 下载zip文件动画gif.
 */
const download_zip_images = ['']

/**
 * 打开开发者模式动画gif.
 */
const open_dev_model_images = ['']

/**
 * 安装zip动画gif.
 *
 */
const install_translate_images = ['']

/**
 * 启用精挑翻译插件动画gif.
 */
const open_select_translate_images = ['']
</script>
