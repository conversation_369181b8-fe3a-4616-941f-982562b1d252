<template>
  <!-- 加载状态 -->
  <div v-if="loading" class="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="p-8 text-center">
      <div class="inline-block h-16 w-16">
        <svg
          class="h-full w-full animate-pulse text-blue-500 dark:text-blue-400"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M21 8C21 6.34315 19.6569 5 18 5H6C4.34315 5 3 6.34315 3 8V16C3 17.6569 4.34315 19 6 19H18C19.6569 19 21 17.6569 21 16V8Z" stroke="currentColor" stroke-width="2" />
          <path
            d="M3 8L10.4886 12.5098C11.4159 13.0342 12.5841 13.0342 13.5114 12.5098L21 8"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <p class="mt-4 font-serif text-blue-800 dark:text-blue-300">
        {{ t('member_message.loading.loading_message') }}
      </p>
    </div>
  </div>

  <!-- 消息内容 -->
  <div v-else-if="message" class="min-h-screen bg-gray-50 px-4 py-8 dark:bg-gray-800">
    <div class="mx-auto max-w-5xl">
      <div class="overflow-hidden rounded-lg bg-white shadow-md dark:bg-gray-900">
        <UButton
          variant="outline"
          size="lg"
          color="neutral"
          class=" rounded-md font-normal"
          style="margin-left: 3.75rem; margin-top: 1.75rem; margin-bottom: 0.75rem;"
          @click="goBack"
        >
          {{ '返回' }}
        </UButton>
        <!-- 标题区域 -->
        <div class="p-4 px-14 pb-5">
          <div class="flex items-center">
            <h1 class="ml-1 font-mono text-2xl leading-tight font-bold text-gray-800 dark:text-gray-100">
              {{ localizedTitle }}
            </h1>
          </div>

          <div class="-mb-1 flex justify-end font-mono text-sm text-gray-800 italic dark:text-gray-500/80">
            {{ formattedDateTime }}
          </div>
        </div>
        <!-- 分隔线 -->
        <div class="px-8">
          <div class="border-t border-gray-300 dark:border-gray-700" />
        </div>

        <!-- 内容区域 -->
        <div class="px-14 py-6">
          <div class="prose prose-blue dark:prose-invert prose-img:rounded-md max-w-none">
            <div @click="handleContentClick" v-html="localizedContent" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { getMemberMessageList } from '@/api/member_message'
import { useAuthStoreWithOut } from '@/store/modules/auth'

const { t, locale } = useI18n()
const router = useRouter()
const route = useRoute()
const authStore = useAuthStoreWithOut()

// 响应式状态
const message = ref<any>(null)
const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')

// 计算属性
const localizedTitle = computed(() => {
  const suffix = locale.value === 'zhHans' ? 'zhhans' : locale.value === 'zhHant' ? 'zhhant' : 'en'
  return message.value?.[`message_title_${suffix}`] || t('无标题')
})

const localizedContent = computed(() => {
  const suffix = locale.value === 'zhHans' ? 'zhhans' : locale.value === 'zhHant' ? 'zhhant' : 'en'
  return parseLinks(message.value?.[`content_${suffix}`] || '')
})

const messageStatus = computed(() => {
  return message.value?.is_read ? t('已读') : t('未读')
})

const formattedDateTime = computed(() => {
  return formatDateTime(message.value?.create_datetime)
})

// 方法
const fetchMessage = async () => {
  try {
    loading.value = true
    error.value = false

    if (!route.query.id) {
      throw new Error(t('消息ID不能为空'))
    }

    const headers = {
      token: authStore.getToken?.replace(/^bearer\s+/i, '') || ''
    }

    const res = await getMemberMessageList(headers, { id: route.query.id })

    if (res.code === 200 && res.data?.length) {
      message.value = res.data[0]
    }
    else {
      throw new Error(res.message || t('获取消息失败'))
    }
  }
  catch (err: any) {
    error.value = true
    errorMessage.value = err.message || t('网络错误')
    console.error('消息加载失败:', err)
  }
  finally {
    loading.value = false
  }
}

const retryLoading = () => {
  fetchMessage()
}

const goBack = () => {
  router.push({ path: `/${locale.value}/member_message` })
}

const parseLinks = (text: string) => {
  if (!text) return ''

  // 检查是否包含HTML样式标签或属性
  const hasHtmlAttributes = /<[a-z][^>]*>|&[a-z]+;|%3C/i.test(text)

  // 如果包含HTML属性，直接返回原始文本
  if (hasHtmlAttributes) {
    return text
  }

  // 没有HTML属性的情况，用简单正则处理纯URL
  const urlRegex = /\bhttps?:\/\/[^\s<>"]+/g
  return text.replace(
    urlRegex,
    url =>
      `<a href="${url}" 
        style="color: #3b82f6; text-decoration: underline; background: none; box-shadow: none;" 
        target="_blank" 
        rel="noopener noreferrer">${url}</a>`
  )
}

const handleContentClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  if (target.tagName === 'A') {
    event.preventDefault()
    const url = target.getAttribute('href')
    if (url) {
      window.open(url, '_blank')
    }
  }
}

const formatDateTime = (datetime: string) => {
  if (!datetime) return t('未知时间')

  const date = new Date(datetime)
  const formatter = new Intl.DateTimeFormat(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })

  return formatter.format(date)
}

// 生命周期
onMounted(() => {
  fetchMessage()
})
</script>

<style scoped>
/* 动画效果 */
.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 内容排版样式 */
:deep(.prose) {
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: 1.125rem;
}

:deep(.prose h2) {
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #1e40af;
}

.dark :deep(.prose h2) {
  color: #60a5fa;
}

:deep(.prose img) {
  margin: 2rem 0;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.prose blockquote) {
  font-style: italic;
  color: #1e40af;
  border-left-color: #60a5fa;
  background-color: rgba(59, 130, 246, 0.05);
}

.dark :deep(.prose blockquote) {
  color: #60a5fa;
  border-left-color: #1e40af;
  background-color: rgba(59, 130, 246, 0.05);
}

/* 链接样式重置 - 完全清除背景 */
:deep(.prose a),
:deep(.prose a:link),
:deep(.prose a:visited),
:deep(.prose a:hover),
:deep(.prose a:active) {
  color: #3b82f6 !important;
  text-decoration: underline !important;
  text-underline-offset: 2px !important;

  /* 彻底清除所有背景相关样式 */
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;

  /* 确保不会受到其他样式影响 */
  padding: 0 !important;
  margin: 0 !important;
  outline: none !important;

  /* 保持链接字体与周围一致 */
  font-weight: inherit !important;
}

:deep(.prose a:hover) {
  color: #2563eb !important;
}

.dark :deep(.prose a) {
  color: #60a5fa !important;
}

.dark :deep(.prose a:hover) {
  color: #93c5fd !important;
}

/* 确保img标签样式优先级高于a标签 */
:deep(a img) {
  border: none;
  margin: 2rem 0;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
