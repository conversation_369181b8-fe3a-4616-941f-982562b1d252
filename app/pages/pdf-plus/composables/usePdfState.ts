import { ref } from 'vue'

export function usePdfState() {
  const timer = ref(null)
  const translationPagesSelected = ref()
  const error = ref('')
  const translationPages = ref('')
  const progress = ref(0)
  const progressSwitch = ref(false)
  const pageCount = ref(0)
  const fileName = ref('')
  const file_id = ref('')
  const pdf_md5 = ref()
  const ossUrl = ref('')
  const storage_path = ref('')
  const parsePdfStatus = ref(false)
  const selectedFile = ref(null)
  const htmlContent = ref('')
  const fileInput = ref(null)
  const fileSelected = ref(false)

  return {
    timer,
    translationPagesSelected,
    error,
    translationPages,
    progress,
    progressSwitch,
    pageCount,
    fileName,
    file_id,
    pdf_md5,
    ossUrl,
    storage_path,
    parsePdfStatus,
    selectedFile,
    htmlContent,
    fileInput,
    fileSelected
  }
}
