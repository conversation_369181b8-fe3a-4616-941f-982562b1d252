import { useTranslationHistory } from '@/store/indexedDB/selectTransPdfPlusFileHistory'
import type { TranslationPdfPlusFileHistory } from '@/types/history'
/**
 * TODO 择优翻译-更新历史记录状态
 * 择优翻译历史记录状态管理
 */
export const useTranslationHistoryState = () => {
  const { initDB, addHistory, getHistoryByKey } = useTranslationHistory()

  // 初始化历史记录
  const initHistory = async () => {
    try {
      await initDB()
      // await loadAllHistory();
      return true
    }
    catch (error) {
      console.error('初始化历史记录失败:', error)
      return false
    }
  }

  /**
   * 获取历史记录
   * @param key 历史记录 key
   * @returns 历史记录
   */
  const loadHistoryByKey = async (key: string) => {
    return await getHistoryByKey(key)
  }

  // 开始新的翻译会话
  const startNewTranslation = async (id: string, mmd: string, fileName: string) => {
    // 创建初始历史记录
    const initialHistory: TranslationPdfPlusFileHistory = {
      id: id, // 历史记录 id
      member_id: '', // 用户ID，用于区分不同用户的历史记录
      mmd: mmd, // 原始文本
      fileName: fileName
    }

    try {
      await addHistory(initialHistory)
      return id
    }
    catch (error) {
      console.error('创建翻译历史记录失败:', error)
      return ''
    }
  }

  return {
    // 方法
    initHistory,
    loadHistoryByKey,
    startNewTranslation
  }
}
