import type { TranslationEngineState, TranslationEngine, ErrorInfo, RationaleInfo, ScoreInfo, TranslationResult, SortIndexInfo } from '@/types/translateOptions'
import { getTranslationEnginesConfig } from '@/utils/translationEnginesConfig'
import { TranslationStatus } from '@/types/translateOptions'
/**
 * !翻译状态管理组合式函数
 * !用于在多个组件之间共享翻译引擎状态
 * 其他组件直接调用就可以获取到模型数据， 也可以调用方法直接修改数据，因为使用了 ref 响应式原理
 */
export function useTranslationState() {
  // 使用 ref 创建一个响应式引用，可以在多个组件间共享
  const translationEngineOptions = ref<TranslationEngineState[]>([])

  // 初始化翻译引擎配置
  const initTranslationEngines = (enabledEngines: TranslationEngine[]) => {
    // 根据模型列表生成翻译引擎配置
    translationEngineOptions.value = getTranslationEnginesConfig(enabledEngines)
  }

  // 翻译擎列表// 翻译引擎选择
  const originalTranslationEngineList = computed(() => {
    return translationEngineOptions.value.map(item => ({
      value: item.value,
      label: item.label,
      target: '_blank',
      img: item.img
    }))
  })

  return {
    translationEngineOptions,
    initTranslationEngines,
    originalTranslationEngineList
  }
}

// 创建一个全局单例实例，确保在整个应用中共享相同的状态
export const translationState = useTranslationState()
