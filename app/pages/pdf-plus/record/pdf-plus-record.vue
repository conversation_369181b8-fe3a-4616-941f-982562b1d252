<template>
  <div v-if="getIsExtensionInstalled" class="mx-auto mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <div>
      <UCard>
        <template #header>
          <!-- PDF 解析记录 -->
          <h3 class="text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
            {{ t('pdf_plus_record.title') }}
          </h3>
        </template>
        <UTable :data="memberPdfRecordList" :columns="productOrderColumns">
          <template #parsing_status-cell="{ row }">
            {{ parsingStatusMap[row.original.parsing_status] }}
          </template>
          <template #actions-cell="{ row }">
            <!-- 查看 -->
            <UButton
              v-if="row.original.parsing_status !== 'failed'"
              :disabled="row.original.file_id == ''"
              style="margin-right: 5px"
              color="primary"
              variant="link"
              @click="handleView(row.original.file_id)"
            >
              {{ t('pdf_plus_record.view') }}
            </UButton>
            <!-- 删除 -->
            <UButton color="primary" variant="link" @click="openDeleteConfirm(row.original.id)">
              {{ t('pdf_plus_record.delete') }}
            </UButton>
          </template>
        </UTable>
        <div class="flex justify-end border-t border-gray-200 px-3 py-3.5 dark:border-gray-700">
          <UPagination
            v-model:page="currentPage"
            :default-page="1"
            :items-per-page="pageCount"
            :total="totalCount"
            @update:page="changePage"
          />
        </div>
      </UCard>
    </div>
  </div>
  <!-- 删除记录 -->
  <UModal v-model:open="open" :title="t('pdf_plus_record.delete_confirm_title')" :ui="{ footer: 'justify-end' }">
    <template #body>
      <div class="flex items-center p-4">
        <UIcon name="i-heroicons-exclamation-triangle" class="mr-3 h-5 w-5 flex-shrink-0 text-orange-600" />
        <!-- 确认删除选中记录吗？ -->
        <p>{{ t('pdf_plus_record.delete_confirm_content') }}</p>
      </div>
    </template>

    <template #footer>
      <!-- 取消 -->
      <UButton
        :label="t('common.cancel')"
        color="neutral"
        variant="outline"
        size="md"
        class="rounded-md px-4 text-sm"
        @click="open = false"
      />
      <!-- 确认 -->
      <UButton
        :label="t('common.confirm')"
        color="error"
        variant="solid"
        size="md"
        class="rounded-md px-4 text-sm"
        @click="handleConfirmDelete"
      />
    </template>
  </UModal>
</template>

<script setup lang="ts">
import type { HeadersTokenType } from '@/api/login/types'
import { getMemberFileRecordListApi, deleteMemberFileRecordApi } from '@/api/mathpix'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import { useExtensionStore } from '@/store/modules/extension'

import type { Pdfdelete } from '@/api/types'

const authStore = useAuthStoreWithOut()

const extensionStore = useExtensionStore()
const { getIsExtensionInstalled } = storeToRefs(extensionStore)

// definePageMeta (此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'member',
  middleware: 'auth' // 使用 middleware/auth.ts 中定义的页面路由守卫，判断是否登录，未登录则跳转到登录页
})

const router = useRouter()
const route = useRoute()

const { t, locale } = useI18n()

/**
 * 解析状态字典
 */

const parsingStatusMap = {
  unparsed: t('pdf_plus_record.parsing_status.unparsed'), // 未解析
  parsing: t('pdf_plus_record.parsing_status.parsing'), // 解析中
  failed: t('pdf_plus_record.parsing_status.failed'), // 解析失败
  success: t('pdf_plus_record.parsing_status.success') // 解析成功
}

interface MemberPdfRecord {
  index: number
  file_name: string
  parsed_page_count: number
  create_datetime: any
  parsing_status: string
  id: number
  file_id: string
}

const orderListData = ref([])
const currentPage = ref(1)
const pageCount = ref(10) // 每页显示的条数
const totalCount = ref(0)
const memberPdfRecordList = ref<MemberPdfRecord[]>([])
const open = ref(false)
const deleteId = ref()
const toast = useToast()

/**
 * 获取PDF解析记录列表
 */
async function getMemberPdfRecordPageData(page: number) {
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  const res = await getMemberFileRecordListApi(headers, {
    platform: 'ztsl',
    page: page,
    limit: pageCount.value
  })
  orderListData.value = res.data
  totalCount.value = res.count || 0

  const recordListDict = orderListData.value.map((item: MemberPdfRecord, index) => {
    return {
      index: index + 1,
      file_name: item.file_name,
      parsed_page_count: item.parsed_page_count,
      create_datetime: item.create_datetime,
      parsing_status: item.parsing_status,
      id: item.id,
      file_id: item.file_id
    }
  })
  // const jsonList = JSON.stringify(orderListDict)
  // alert(jsonList)
  memberPdfRecordList.value = recordListDict
}

/**
 * PDF解析记录列表表头
 */
const productOrderColumns = [
  {
    accessorKey: 'index',
    header: t('pdf_plus_record.table.index') // '序号'
  },
  {
    accessorKey: 'file_name',
    header: t('pdf_plus_record.table.file_name') // '文件名'
  },
  {
    accessorKey: 'parsed_page_count',
    header: t('pdf_plus_record.table.parsed_page_count') // '页数'
  },
  {
    accessorKey: 'create_datetime',
    header: t('pdf_plus_record.table.create_datetime') // '时间'
  },
  {
    accessorKey: 'parsing_status',
    header: t('pdf_plus_record.table.parsing_status') // '状态'
  },
  {
    accessorKey: 'actions',
    header: t('pdf_plus_record.table.actions') // '操作'
  } // 自定义列
]

/**
 * 分页切换
 * @param page
 */
function changePage(page: number) {
  currentPage.value = page
  getMemberPdfRecordPageData(page)
}
// 查看解析成功的记录
function handleView(file_id: string) {
  navigateTo({ path: '/' + locale.value + '/pdf-plus/' + file_id })
}
// 打开确认弹窗并记录ID
const openDeleteConfirm = (id: number) => {
  deleteId.value = id
  open.value = true
}
// 删除解析记录
const handleConfirmDelete = async () => {
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  const queryParams: Pdfdelete = {
    id: deleteId.value
  }
  const res = await deleteMemberFileRecordApi(headers, queryParams)
  open.value = false
  if (res.code === 200) {
    toast.add({
      title: t('pdf_plus_record.delete_success'), // 删除成功
      description: t('pdf_plus_record.delete_success_tips'), // 已删除
      color: 'success'
    })
    // 刷新数据
    await Promise.allSettled([(currentPage.value = 1), getMemberPdfRecordPageData(1)])
  }
}
/** 平台PDF解析记录信息 */
const product = ref({})

onMounted(async () => {
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  // 加载第1页
  currentPage.value = 1
  getMemberPdfRecordPageData(1)
})
</script>
