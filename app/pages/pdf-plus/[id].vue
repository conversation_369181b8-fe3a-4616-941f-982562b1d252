<template>
  <div class="flex flex-col h-screen">
    <!-- 顶部 -->
    <DocumentTransHeader
      document-type="pdf-plus"
      :show-switch-bilingual="true"
    >
      <template #right>
        <UDropdownMenu size="xl" :items="exportItem">
          <!-- 导出文件 -->
          <UButton
            :label="t('document_translation.export_file')"
            size="xl"
            color="secondary"
            variant="outline"
            class="rounded-md"
            trailing-icon="i-heroicons-chevron-down-20-solid"
          />
        </UDropdownMenu>
      </template>
    </DocumentTransHeader>
    <div class="h-(--ui-header-height) min-h-(--ui-header-height)" />

    <!-- 检测是否安装浏览器插件 -->
    <ExtensionInstalled
      translate-type="pdf-plus"
      loading-type="icon"
      :is-show-product="true"
      size="large"
    >
      <!-- 进度条正在解析文件 -->
      <div v-if="progressSwitch" class="flex h-[40em] items-center justify-center">
        <div class="flex w-[30%] flex-col items-center md:w-[15%]">
          <UProgress :value="progress" class="w-full" />
          <!-- 正在解析文件... -->
          <p class="mt-2 whitespace-nowrap">
            {{ t('pdf_plus.parsing_the_file') }}&nbsp;{{ progress }}%
          </p>
        </div>
      </div>
      <!-- 显示翻译内容 -->
      <div v-else-if="mmdContent && progress === 100">
        <div class="p-4">
          <MarkdownRenderer
            :id="id"
            class="mx-auto my-20"
            :markdown="mmdContent"
            @value-updated="handleValueUpdate"
          />
        </div>
        <iframe ref="printFrame" class="hidden" />
      </div>
    </ExtensionInstalled>
  </div>
</template>

<script setup lang="ts">
// 初始化数据
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'
import axios from 'axios'
import MarkdownRenderer from '@/pages/components/MarkdownRenderer.vue'
import { pixPdfSave, pixPdfStatus } from '@/api/mathpix'
import { useAuthStoreWithOut } from '@/store/modules/auth' // 文件名
import type { PdfStatus } from '@/api/types'
import type { HeadersTokenType } from '@/api/login/types'
// 组件相关
import ExtensionInstalled from '@/pages/components/ExtensionInstalled/index.vue'
import DocumentTransHeader from '@/pages/components/DocumentTransHeader/index.vue'

// 扩展
import { usePluginTranslate } from '@/composables/usePluginTranslate'
// 文档翻译设置存储
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { storeToRefs } from 'pinia'
import { useTranslationHistoryState } from './useData/historyState'

const { usePluginWebDocumentTranslate } = usePluginTranslate()
const { initTranslationProgressMonitor } = useTranslationProgress()
const { loadHistoryByKey, initHistory, startNewTranslation } = useTranslationHistoryState()
const documentTranslateStore = useDocumentTranslateStore()
const { enabledEnginesList, translateEngine, translationsDisplay, targetLanguageList, targetLanguageZingCode, targetLanguage } = storeToRefs(documentTranslateStore)

const { t, locale } = useI18n()
const route = useRoute()
const toast = useToast()
// 移动端响应式检测
const { isMobile } = useMobileDetection()

const id = ref(route.params.id as string | '')
const progress = ref(50) //  存储当前的解析进度。
const progressSwitch = ref(true) // 解析文件进度
const mmdContent = ref('') // 存储解析后的mmd内容。
const htmlContent = ref('') // 存储解析后的HTML内容。
const htmlContentDownload = ref('') // 存储解析翻译后的HTML内容。
const printFrame = ref(null)
const pdfName = ref('')

// 禁用默认布局
definePageMeta({
  layout: false
})

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  // 去除 pdfName.value 的 .pdf 后缀
  title: pdfName.value.replace('.pdf', '') // 文件名称
})

const exportItem = computed(() => [{
  // 导出 HTML
  label: t('document_translation.export_html'),
  value: 'html',
  onSelect: () => {
    handleExportHtml()
  }
},
{
  // 导出 PDF
  label: t('document_translation.export_pdf'),
  value: 'pdf',
  onSelect: () => {
    handleExportPDF()
  }
}])

// 在组件挂载时初始化 id
onMounted(async () => {
  // 检查本地浏览器是否存在PDF 文件解析历史记录
  // 如果存在，则直接加载历史记录，否则根据ID获取进度条并且记录
  initHistory()
  progress.value = 50
  loadHistoryByKey(id.value).then((existingHistory) => {
    if (existingHistory) {
      progress.value = 100 // 当 percent 等于 100 时，设置 progress.value 为 90
      mmdContent.value = existingHistory.mmd
      pdfName.value = existingHistory.fileName
      clearInterval()
      useSeoMeta({
        titleTemplate: '',
        title: pdfName.value.replace('.pdf', '') // 文件名称
      })
    }
    else {
      // 如果不存在历史记录，则开始新的翻译
      handleAnalyze()
    }
  })
  // 获取语言集+ 模型列表（存储到 pinia 本地）
  await sendMessageToChromeExtension(locale.value, translateEngine.value.value) // 发送消息到浏览器插件
})

// 组件卸载前
onBeforeUnmount(() => {
  // 组件卸载之前必须要取消翻译否则会翻译整个网站
  usePluginWebDocumentTranslate({
    type: 'cancelCurrentPageTranslate',
    translationEngine: translateEngine.value.value,
    targetLanguage: targetLanguage.value,
    transDisplayMode: 'bilingual',
    documentType: 'pdf-plus'
  })
})

// 获取文件信息
let intervalId = null // 存储定时器ID，用于后续清除定时器。

// 清除定时器
function clearInterval() {
  if (intervalId) {
    intervalId = null
  }
  clearTimeout(intervalId)
  progressSwitch.value = false // 关闭进度条
}

// 翻译选项数据- 语言集
const languageList = computed({
  get() {
    return targetLanguageList.value
  },
  set(newValue) {
    // 通过 Store 方法或直接修改响应式变量
    targetLanguageList.value = newValue
  }
})

// 开始解析PDF文件
// 根据file_id查询文件信息
// 如果不存在执行保存记录
// 如果存在直接显示文件内容
function handleAnalyze() {
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  const queryParams: PdfStatus = {
    file_id: id.value
  }
  // 查询进度
  pixPdfStatus(headers, queryParams)
    .then((res) => {
      pdfName.value = res.data.file_name
      useSeoMeta({
        titleTemplate: '',
        title: pdfName.value.replace('.pdf', '') // 文件名称
      })
      const percent = Number(res.data.percent_done) // 转换为数字
      if (res.data.status === 'error') {
        // 发生错误时也停止轮询
        clearInterval()
        toast.add({
          title: t('pdf_plus.messages.parsing_failed'), // 解析失败
          description: t('pdf_plus.messages.parsing_failed_tips_4'), // 请检查文件
          color: 'error'
        })
      }
      else {
        if (percent < 10 && percent > 40) {
          progress.value = 55
        }
        if (percent < 41 && percent > 60) {
          progress.value = 60
        }
        if (percent < 61 && percent > 70) {
          progress.value = 75
        }
        if (percent < 71 && percent > 80) {
          progress.value = 80
        }
        if (percent < 81 && percent > 90) {
          progress.value = 87
        }
        if (percent < 91 && percent > 99) {
          progress.value = 94
        }
        if (percent === 100) {
          progress.value = 97 // 当 percent 等于 100 时，设置 progress.value 为 90
        }
        if (percent < 100) {
          // 如果进度未达到100%，则继续轮询
          intervalId = setTimeout(handleAnalyze, 2000)
        }
        else {
          if (res.data.mmd_url !== null && res.data.mmd_url !== '') {
            // 根据返回的mmd文件OSS地址提取文件内容
            progress.value = 100
            fetchMarkdownText(res.data.mmd_url)
          }
          else {
            // 如果进度达到100%，清除定时器
            // 保存文件信息
            pixPdfSave(headers, queryParams)
              .then((res) => {
                // 根据返回的mmd文件OSS地址提取文件内容
                progress.value = 100
                fetchMarkdownText(res.data.mmd_url)
              })
              .catch((error) => {
                console.error('获取解析进度失败:', error)
                // 发生错误时也停止轮询
                clearInterval()
              })
          }
        }
      }
    })
    .catch((error) => {
      console.error('获取解析进度失败:', error)
      // 发生错误时也停止轮询
      clearInterval()
    })
}

// 根据mmd文件OSS地址提取文件内容
const fetchMarkdownText = async (url) => {
  try {
    const response = await axios.get(url, { responseType: 'text' })
    mmdContent.value = response.data
    console.log('html content:', mmdContent.value)
    // 进度达到100%，清除定时器
    if (mmdContent.value) {
      startNewTranslation(id.value, mmdContent.value, pdfName.value)
      clearInterval()
    }
  }
  catch (error) {
    console.error('Error fetching Markdown:', error)
    clearInterval()
  }
}
// 接收子组件的值html值
const handleValueUpdate = async (value: string) => {
  // 发起翻译监控
  initTranslationProgressMonitor()

  htmlContent.value = value
  htmlContentDownload.value = value
  // handleSendMessageTranslate();
  // 将当前上下文转换为异步函数，以支持 await 表达式
  console.log('targetLanguage', targetLanguage)

  setTimeout(() => {
    // 执行扩展翻译
    usePluginWebDocumentTranslate({
      type: 'translateFilePage',
      translationEngine: translateEngine.value.value,
      targetLanguage: targetLanguage.value,
      transDisplayMode: translationsDisplay.value,
      documentType: 'pdf-plus'
    })
  }, 1000)
}

// 定义一个函数来生成完整的 HTML 字符串
const generateFullHtml = (headContent, bodyContent) => {
  return `
    <!DOCTYPE html>
    <html lang="en">
        ${headContent}
      <body>
        ${bodyContent}
      </body>
    </html>
  `
}
// 导出为HTML文件
function handleExportHtml() {
  const htmlContent1 = document.getElementById(id.value).innerHTML
  const styleEl = document.getElementById('select_translate_style')
  const head = styleEl ? styleEl.outerHTML : ''
  const mathpixStyleEl = document.getElementById('Mathpix-styles')
  const mathpixHead = mathpixStyleEl ? mathpixStyleEl.outerHTML : ''
  // 定义预览区域的样式
  const previewStyle = `
    <style>
      @page {
        margin: 1cm;    /* 移除默认边距 */
      }
      h1, h2, h3, h4, h5, h6 {
        font-size: inherit;
      }
      #preview {
        font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif;
        padding: 2em;
        margin: auto;
        max-width: 800px;
      }
    </style>
  `
  // 生成完整的 HTML 字符串，将预览样式添加到头部
  htmlContentDownload.value = generateFullHtml(head + mathpixHead + previewStyle, htmlContent1)

  const blob = new Blob([htmlContentDownload.value], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  const languageItem = languageList.value.find((item) => {
    return item.value === targetLanguageZingCode.value
  })
  a.download = pdfName.value.replace('.pdf', '') + '-' + languageItem.lang_code + '.html'
  a.click()
  URL.revokeObjectURL(url)
}

// 导出为PDF文件
function handleExportPDF() {
  if (import.meta.client) {
    const htmlContent1 = document.getElementById(id.value).innerHTML
    const styleEl = document.getElementById('select_translate_style')
    const head = styleEl ? styleEl.outerHTML : ''

    const mathpixStyleEl = document.getElementById('Mathpix-styles')
    const mathpixHead = mathpixStyleEl ? mathpixStyleEl.outerHTML : ''

    // 添加打印样式，包含背景颜色打印支持
    const printStyles = `
      <style>
        @page {
          margin: 1cm;    /* 移除默认边距 */
        }
        h1, h2, h3, h4, h5, h6 {
          font-size: inherit;
        }
        #preview {
          font-family: 'CMU Serif', 'Georgia', Helvetica, Arial, sans-serif;
          padding: 2em;
          margin: auto;
          max-width: 800px;
        }
        /* 强制打印背景颜色和图像 */
        html, body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
      </style>
    `
    // 生成完整的 HTML 字符串
    htmlContentDownload.value = generateFullHtml(head + mathpixHead + printStyles, htmlContent1)
    const frame = printFrame.value
    if (frame) {
      // 确保这里做了null检查
      // 创建一个新的文档对象
      const doc = frame.contentDocument || frame.contentWindow.document
      // 设置文档标题，作为打印时的文件名
      doc.title = pdfName.value.replace('.pdf', '') + '-' + targetLanguage.value + '.pdf'
      frame.srcdoc = htmlContentDownload.value
      setTimeout(() => {
        frame.contentWindow.print()
      }, 100)
    }
  }
}
</script>

<style scoped>
#preview {
  padding: 2em;
  margin: auto;
  max-width: 800px;
}

/* 表格样式 */
.markdown_container :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.markdown_container :deep(th),
.markdown_container :deep(td) {
  border: 1px solid #d1d5db;
  padding: 8px 12px;
  text-align: left;
}

.markdown_container :deep(th) {
  background-color: #f3f4f6;
  font-weight: 600;
}

.dark .markdown_container :deep(th),
.dark .markdown_container :deep(td) {
  border-color: #4b5563 !important;
  background-color: var(--ui-color-neutral-950);
}

.dark .markdown_container :deep(th)
.dark .markdown_container :deep(td) {
  background-color: #374151;
}
</style>
