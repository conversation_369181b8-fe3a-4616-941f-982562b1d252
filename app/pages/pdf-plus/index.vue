<template>
  <div class="text-trans pt-6">
    <!-- 检测是否安装浏览器插件 -->
    <TranslateType :type="'pdf_plus'" />
    <div v-if="!progressSwitch && !htmlContent" class="mx-auto mb-2 min-h-[480px] max-w-7xl p-6 pt-0">
      <main class="rounded-md bg-gray-100 dark:bg-gray-800">
        <div class="flex min-h-92 flex-col items-center justify-center rounded-lg border-1 border-solid border-gray-200 p-5 md:p-12 dark:border-gray-700">
          <div v-if="!fileSelected" class="text-center">
            <div class="mx-auto mb-4 h-32 justify-items-center">
              <!-- PDF Plus -->
              <div class="inline-flex flex-col items-center">
                <UChip
                  :text="'Plus'"
                  color="warning"
                  size="3xl"
                  inset
                  :ui="{ base: 'rounded-xs p-[2px] h-[16px]' }"
                >
                  <img src="/assets/images/document/bi--file-earmark-pdf.svg" class="size-12 sm:size-12 md:flex md:size-16 lg:flex lg:size-20 xl:flex xl:size-20" />
                </UChip>
                <span class="mt-3 text-xs text-gray-600 sm:text-xs md:text-sm lg:text-sm">PDF 文件</span>
              </div>
            </div>
            <div class="mb-4 ">
              <ExtensionInstalled :is-show-product="false" loading-type="button">
                <!-- 上传文件 是否登录 新增加载状态 会员是否在有效期内，包含试用和套餐 -->
                <UButton
                  v-if="memberHasLogin && (isDataLoading || memberProduct.isMemberPlusValidity)"
                  class="rounded-lg px-12 py-2 whitespace-nowrap sm:text-base md:text-xl"
                  size="xl"
                  :disabled="isDataLoading"
                  @click="handleTriggerFileInput"
                >
                  {{ t('pdf_plus.upload_file') }}
                </UButton>
                <!-- 购买 / 订阅 Plus 会员 -->
                <UButton
                  v-if="memberHasLogin && !isDataLoading && !memberProduct.isMemberPlusValidity"
                  size="xl"
                  class="rounded-lg px-12 py-2 whitespace-nowrap sm:text-base md:text-xl"
                  @click="handlePricing"
                >
                  {{ t('buy.title') }}
                </UButton>
                <!-- 请先登录 -->
                <UButton
                  v-if="!memberHasLogin"
                  size="xl"
                  class="rounded-lg px-12 py-2 whitespace-nowrap sm:text-base md:text-xl"
                  @click="goLogin"
                >
                  {{ t('pdf_plus.please_log_in_first') }}
                </UButton>
              </ExtensionInstalled>
            </div>
            <!-- 请上传 PDF 格式文件 -->
            <p v-if="memberHasLogin && memberProduct.isMemberPlusValidity" class="text-gray-500">
              {{ t('pdf_plus.upload_file_tips') }}
            </p>
            <!-- 产品/会员服务已过期，请续费后重试 -->
            <p v-if="memberHasLogin && !isDataLoading && !memberProduct.isMemberPlusValidity" class="text-gray-500">
              {{ t('pdf_plus.messages.loading_failed_tips_2') }}
            </p>
            <!-- 用于高度占位（无内容） -->
            <p v-else class="min-h-6 text-gray-500" />
          </div>
          <!-- 显示文件信息 -->
          <div v-else class="w-full">
            <div class="relative mb-6 flex items-center justify-between md:relative">
              <!-- 移动端返回按钮 -->
              <UButton
                class="absolute top-0 left-0 whitespace-nowrap md:hidden"
                color="neutral"
                variant="ghost"
                leading-icon="i-material-symbols-light:arrow-back-ios-rounded"
                @click="resetFile"
              >
                {{ t('pdf_plus.back') }}
              </UButton>

              <div class="flex w-full flex-col items-center text-center md:w-auto md:flex-row md:text-left">
                <img src="/assets/images/document/bi--file-earmark-pdf.svg" class="mt-8 mr-0 mb-4 h-16 w-16 md:mt-0 md:mr-4 md:mb-0 md:h-12 md:w-12" />
                <div class="text-center md:text-left">
                  <!-- 文件名 -->
                  <h3 class="mb-2 text-sm font-medium text-gray-900 md:mb-0 md:text-base dark:text-gray-200">
                    {{ t('pdf_plus.upload_file_name') }}：{{ fileInfo.fileName }}
                  </h3>
                  <!-- 共 XX 页 -->
                  <p class="text-sm text-gray-500 md:text-base">
                    {{ t('pdf_plus.total') }}&nbsp;{{ fileInfo.pageCount }}&nbsp;{{ t('pdf_plus.pages') }}
                  </p>
                </div>
              </div>

              <!-- PC端取消按钮 -->
              <UButton
                class="hidden whitespace-nowrap md:block"
                color="neutral"
                variant="ghost"
                @click="resetFile"
              >
                <span class="flex items-center">
                  {{ t('common.cancel') }}
                  <UIcon name="i-heroicons-x-mark" class="ml-2 size-5" />
                </span>
              </UButton>
            </div>
            <!-- 翻译模型选择区域 -->
            <div class="rounded-md bg-gray-50 p-6 dark:bg-gray-900">
              <div class="grid grid-cols-1 justify-between gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-2 xl:grid-cols-4">
                <!-- 翻译模型 -->
                <div class="flex-1 basis-1/10">
                  <!-- 翻译模型 -->
                  <label class="mb-2 block text-sm text-gray-500 md:text-lg">{{ t('pdf_plus.translation_model') }}</label>
                  <DocumentModelSelect
                    :translate-engine-config="documentTranslateEngine"
                    :variant="'outline'"
                    :enabled-engines-list="enabledEnginesList"
                    @update:translate-engine-config="onSelectionChangeTranslateEngine"
                  />
                </div>
                <!-- 目标语言选择区域 -->
                <div class="flex-1">
                  <!-- 目标语言 -->
                  <label class="mb-2 block text-sm text-gray-500 md:text-lg">{{ t('pdf_plus.target_language') }}</label>
                  <USelectMenu
                    v-model="targetLanguageZingCode"
                    value-key="value"
                    :items="languageList"
                    class="w-full"
                    :size="isMobile ? 'lg' : 'xl'"
                    :ui="{ content: 'w-full' }"
                    @update:model-value="onSelectionChangeTargetLanguage"
                  />
                </div>
                <!-- 译文显示选择区域 -->
                <div class="flex-1">
                  <!-- 译文显示 -->
                  <label class="mb-2 block text-sm text-gray-500 md:text-lg">{{ t('pdf_plus.translation_mode') }}</label>
                  <USelectMenu
                    v-model="translationsDisplay"
                    value-key="value"
                    :search-input="false"
                    :items="displayOptions"
                    class="w-full"
                    :size="isMobile ? 'lg' : 'xl'"
                    :ui="{ content: 'w-full' }"
                    @update:model-value="onSelectionChangeTranslationsDisplay"
                  />
                </div>
                <div class="flex-1">
                  <!-- 页面范围 -->
                  <label class="mb-2 block text-sm text-gray-500 md:text-lg">{{ t('pdf_plus.page_range') }}</label>
                  <UPopover mode="click">
                    <template #default>
                      <UButton color="neutral" variant="outline" class="bg-default flex h-[36px] w-full items-center justify-center rounded-md border-gray-300 p-1 md:h-[40px]">
                        <span
                          :class="{
                            'flex-grow text-center md:text-base': true,
                            'text-red-500 dark:text-red-400': inputPagesError // 当 inputPagesError 为 true 时应用红色字体
                          }"
                        >
                          {{ selectedPages }}
                        </span>
                        <UIcon class="mr-2 size-5 text-gray-400 md:size-6" name="i-lucide:chevron-down" />
                      </UButton>
                    </template>
                    <template #content>
                      <div class="translation-tip bg-default rounded-md p-3 whitespace-pre-line">
                        <div class="mb-1 flex items-center justify-start">
                          <!-- 指定页面范围 -->
                          <label class="text-smmr-10 flex text-sm text-gray-500">{{ t('pdf_plus.specify_page_range') }}</label>
                          <UPopover
                            :mode="isMobile ? 'click' : 'hover'"
                            class="cursor-pointer align-bottom"
                            arrow
                            :ui="{ content: 'p-4 bg-neutral-900 dark:bg-neutral-600', arrow: 'fill-(--ui-color-neutral-800) dark:fill-(--ui-color-neutral-600)' }"
                          >
                            <UIcon name="heroicons:information-circle" class="ml-1 size-5 shrink-0 text-neutral-400" />
                            <template #content>
                              <div class="block max-w-100 rounded-md text-sm text-neutral-100 dark:text-neutral-200">
                                <!-- 1、不设置则，则默认翻译整个 PDF -->
                                <p class="mb-2">
                                  1、{{ t('pdf_plus.specify_page_range_tips_1') }}
                                </p>
                                <!-- 2、指定页码，使用英文逗号分隔：1,3,-1 -->
                                <p class="mb-2">
                                  2、{{ t('pdf_plus.specify_page_range_tips_2') }}
                                </p>
                                <!-- 3、使用"-"指定范围，如第1页到第5页：1-5 -->
                                <p class="mb-2">
                                  3、{{ t('pdf_plus.specify_page_range_tips_3') }}
                                </p>
                                <!-- 4、第5页到倒数第3页：5--3 -->
                                <p class="mb-2">
                                  4、{{ t('pdf_plus.specify_page_range_tips_4') }}
                                </p>
                                <!-- 5、倒数第5页到倒数第2页：-5--2 -->
                                <p class="mb-2">
                                  5、{{ t('pdf_plus.specify_page_range_tips_5') }}
                                </p>
                                <!-- 6、组合使用页码及范围，使用英文逗号分隔：1,3,5-8,-4--2 -->
                                <p>6、{{ t('pdf_plus.specify_page_range_tips_6') }}</p>
                              </div>
                            </template>
                          </UPopover>
                        </div>
                        <input
                          v-model="inputPages"
                          type="text"
                          :class="{
                            'bg-waite h-[40px] w-full rounded-md border border-gray-200 p-3 text-center text-sm md:text-base dark:bg-gray-800': true,
                            'text-red-500 dark:text-red-400': inputPagesError // 当 inputPagesError 为 true 时应用红色字体
                          }"
                          @update:model-value="handleTranslationPagesChange"
                        />
                      </div>
                    </template>
                  </UPopover>
                  <!-- <span class="w-full text-center text-sm break-words text-gray-400 dark:text-gray-600">{{ inputPages }}</span> -->
                </div>
              </div>
            </div>
            <div class="bg-gray-20 mt-7 flex justify-center">
              <div class="flex flex-col items-center">
                <!-- 提示信息区域 - 固定高度 -->
                <div v-if="isShowHint || isShowExceedHint || isTheFileSizeIsOversized" class="flex min-h-[60px] items-center justify-center">
                  <!-- 要翻译页数 {{ totalPages }} 页，超出了您的 PDF Plus 剩余页数 {{ remaining_total_page_num }} 页 -->
                  <!-- The number of pages to be translated is AA pages, which exceeds the remaining pages (BB pages) of your PDF Plus. -->
                  <span v-if="isShowHint" class="mb-4 text-sm text-red-500 md:mb-3 md:text-base dark:text-red-400">
                    {{ t('pdf_plus.exceeds_pdf_plus_limit_tips_1') }}&nbsp;{{ totalPages }}&nbsp;{{ t('pdf_plus.exceeds_pdf_plus_limit_tips_2') }}&nbsp;{{ remaining_total_page_num }}&nbsp;{{
                      t('pdf_plus.exceeds_pdf_plus_limit_tips_3')
                    }}
                  </span>
                  <!-- 当前文件仅有 {{ fileInfo.pageCount }} 页，请输入正确页码范围 -->
                  <!-- The current file has only XX pages, please enter the correct page range -->
                  <span v-if="isShowExceedHint" class="mb-4 text-sm text-red-500 md:mb-3 md:text-base dark:text-red-400">
                    {{ t('pdf_plus.page_range_limit_tips_1') }}&nbsp;{{ fileInfo.pageCount }}&nbsp;{{ t('pdf_plus.page_range_limit_tips_2') }}
                  </span>
                  <span v-if="isTheFileSizeIsOversized" class="mb-4 text-sm text-red-500 md:mb-3 md:text-base dark:text-red-400">
                    <!-- 上传文件大小不能超过 100MB -->
                    {{ t('pdf_plus.messages.parsing_failed_tips_1') }}
                  </span>
                </div>
                <!-- 立即翻译 -->
                <UButton
                  v-if="memberProduct.pdf_plus_remaining_page_num != null"
                  :size="isMobile ? 'lg' : 'xl'"
                  color="primary"
                  class="rounded-md px-10 py-2 whitespace-nowrap"
                  :disabled="inputPagesError || isFileEncrypted || isTheFileSizeIsOversized"
                  @click="startTranslate"
                >
                  {{ t('pdf_plus.translate_now') }}
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </main>
      <div v-if="memberHasLogin && memberProduct.pdf_plus_remaining_page_num != null" class="flex justify-center">
        <div class="mt-2 mr-4 text-center text-sm">
          <!-- PDF Plus 剩余页数：本月会员套餐剩余 AA 页，加量包剩余 BB 页 -->
          {{ t('pdf_plus.pdf_plus_remaining_pages_tips_1') }}&nbsp;
          <a href="account" target="_blank" class="font-medium text-sky-500">{{ memberProduct.pdf_plus_remaining_page_num ? memberProduct.pdf_plus_remaining_page_num : 0 }}</a>
          &nbsp;{{ t('pdf_plus.pdf_plus_remaining_pages_tips_2') }}&nbsp;
          <a href="account" target="_blank" class="font-medium text-sky-500">{{ memberProduct.pack_pdf_plus_remaining_num }}</a>
          &nbsp;{{ t('pdf_plus.pdf_plus_remaining_pages_tips_3') }}
          <!-- 购买加量包 -->
          <a href="resource?type=pdf_plus" target="_blank" class="ml-4 font-medium text-sky-500">{{ t('pdf_plus.purchase_add_on_package') }}</a>
        </div>
      </div>
    </div>
    <!-- 上传解析中进度条 -->
    <div v-else-if="progressSwitch" class="flex h-[40em] items-center justify-center">
      <div class="flex w-[30%] flex-col items-center md:w-[15%]">
        <UProgress :value="progress" class="w-full" />
        <!-- 正在解析文件... -->
        <p class="mt-2 whitespace-nowrap">
          {{ t('pdf_plus.parsing_the_file') }}&nbsp;{{ progress }}%
        </p>
      </div>
    </div>
    <div v-if="memberHasLogin">
      <!-- 翻译记录 -->
      <pdf-plus-record />
    </div>

    <!-- PDF Plus 总介绍 -->
    <UPageSection
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-2xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #title>
        <!-- AI 驱动的专业 PDF 文档翻译 -->
        <span class="color_text_title">{{ t('pdf_plus_introduction.title') }}</span>
      </template>
      <template #description>
        <!-- PDF Plus 基于领先的 AI 视觉处理技术，能精确解析 PDF 文档或图片中的手写文本、数学方程式、复杂表格、代码片段、化学公式等内容，适用于学术论文和各类专业文档的翻译。 -->
        <span v-html="t('pdf_plus_introduction.description')" />
        <!-- <br />
        <span class="font-semibold" v-html="t('home.mission.description2')" /> -->
      </template>
      <UPageGrid>
        <UPageCard v-for="(card, index) in parsing_types" :key="index" v-bind="card">
          <template #header>
            <UIcon :name="card.icon_custom" class="text-primary-500 size-10" />
            <span class="ml-2 inline-flex align-top text-xl leading-10 font-medium text-neutral-800 dark:text-neutral-100">{{ card.title_custom }}</span>
          </template>
          <img
            v-if="card.image"
            :src="`${card.image.path}`"
            :width="card.image.width"
            :height="card.image.height"
            loading="lazy"
            class="w-full"
          />
        </UPageCard>
      </UPageGrid>
    </UPageSection>

    <!-- PDF Plus 功能介绍 -->
    <UPageSection
      v-for="(section, index) in features"
      :key="index"
      :title="section.title"
      :description="section.description"
      :orientation="section.orientation"
      :reverse="section.reverse"
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-xl sm:text-xl md:text-xl lg:text-2xl xl:text-3xl',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg',
        features: 'text-sm sm:text-sm md:text-base lg:text-base xl:text-base'
      }"
    >
      <template #title>
        <span class="">{{ section.title }}</span>
      </template>
      <UCarousel
        v-if="index === 0"
        v-slot="{ item }"
        :items="bilingual_translation_images"
        :ui="{ item: 'basis-full' }"
        class="images_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-if="index === 1"
        v-slot="{ item }"
        :items="scanned_pdf_translation_images"
        :ui="{ item: 'basis-full' }"
        class="images_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <UCarousel
        v-if="index === 2"
        v-slot="{ item }"
        :items="typesetting_images"
        :ui="{ item: 'basis-full' }"
        class="images_container overflow-hidden rounded-lg"
        indicators
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
    </UPageSection>

    <!-- 翻译内容导出 -->
    <UPageSection
      :ui="{
        container: 'py-12 sm:py-12 md:py-16 lg:py-20 xl:py-20',
        title: 'text-2xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold tracking-tight text-neutral-900 dark:text-white',
        description: 'text-base sm:text-base md:text-lg lg:text-lg xl:text-lg'
      }"
    >
      <template #title>
        <!-- 导出翻译内容 -->
        <span>{{ t('pdf_plus_introduction.export.title') }}</span>
      </template>
      <template #description>
        <span v-html="''" />
        <!-- <br />
        <span class="font-semibold" v-html="t('home.mission.description2')" /> -->
      </template>
      <UPageGrid class="relative grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
        <UPageCard
          v-for="(card, index) in export_cards"
          :key="index"
          v-bind="card"
          :ui="{ header: 'w-full justify-items-center' }"
          spotlight
          spotlight-color="success"
        >
          <template #header>
            <div v-if="index === 0" class="flex size-16 justify-center sm:size-16 md:size-16 lg:size-20 xl:size-24">
              <!-- export HTML icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                shape-rendering="geometricPrecision"
                text-rendering="geometricPrecision"
                image-rendering="optimizeQuality"
                fill-rule="evenodd"
                clip-rule="evenodd"
                viewBox="0 0 424 511.54"
              >
                <path
                  fill="currentColor"
                  fill-rule="nonzero"
                  d="M86.37 413.44c-11.76 0-11.76-17.89 0-17.89H189.1c-.2 2.95-.31 5.93-.31 8.94s.11 5.99.31 8.95H86.37zm-3.24-147.13V237.1H63.31v29.21H36.88v-82.59h26.43v29.2h19.82v-29.2h26.43v82.59H83.13zm96.46-61.45h-19.16v61.45H134v-61.45h-19.15v-21.14h64.74v21.14zm29.61 61.45h-27.62l5.02-82.59h34.49l10.31 42.02h.92l10.31-42.02h34.48l5.03 82.59h-27.62l-1.59-40.04h-.92l-10.04 40.04h-20.22l-10.18-40.04h-.79l-1.58 40.04zm131.41 0h-52.86v-82.59h26.43v61.44h26.43v21.15zm-74.8-242.05v29.1c0 65.66 15.31 69.47 69.08 69.47h22.03l-91.11-98.57zm94.33 115.92h-21.48c-61.02 0-90.2-4.09-90.2-86.28V17.35H56.82c-21.7 0-39.47 17.78-39.47 39.47v264.79H219.2c-4.64 5.47-8.83 11.34-12.51 17.54H17.35v89.83c0 21.62 17.85 39.47 39.47 39.47h149.04c3.53 6.12 7.56 11.92 12.02 17.34H56.82C25.63 485.79 0 460.17 0 428.98V56.82C0 25.55 25.55 0 56.82 0h206.33a8.68 8.68 0 016.93 3.45l105.07 113.68c2.19 2.37 2.34 4.66 2.34 7.52v166.86c-5.55-2.98-11.35-5.56-17.35-7.71V140.18z"
                />
                <path fill="green" d="M316.95 297.45c59.12 0 107.05 47.93 107.05 107.05 0 59.11-47.93 107.04-107.05 107.04S209.9 463.61 209.9 404.5c0-59.12 47.93-107.05 107.05-107.05z" />
                <path
                  fill="#fff"
                  fill-rule="nonzero"
                  d="M337.9 356.54l-3.77 47.75 17.35-6.07c11.47-4.4 23.27 3.72 14.38 13.82-10.82 12.45-27.26 29.55-39.22 40.94-7.43 7.42-11.73 7.49-19.18.06-13.24-13-26.24-27.44-39.18-40.87-9.25-10.06 2.3-18.55 14.28-13.95l17.16 6.01c-1.25-16.28-2.82-31.84-3.77-48.1 0-2.99 2.5-5.39 5.42-5.61 10.31 0 20.84-.24 31.12 0 2.92.22 5.42 2.62 5.42 5.61l-.01.41z"
                />
              </svg>
            </div>
            <div v-if="index === 1" class="flex size-16 justify-center sm:size-16 md:size-16 lg:size-20 xl:size-24">
              <!-- export PDF icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                shape-rendering="geometricPrecision"
                text-rendering="geometricPrecision"
                image-rendering="optimizeQuality"
                fill-rule="evenodd"
                clip-rule="evenodd"
                viewBox="0 0 424 511.54"
              >
                <path
                  fill="currentColor"
                  fill-rule="nonzero"
                  d="M86.37 413.44c-11.76 0-11.76-17.89 0-17.89H189.1c-.2 2.95-.31 5.93-.31 8.94s.11 5.99.31 8.95H86.37zm35.31-167.09H104.5v19.96H78.08v-82.59h41.62c18.94 0 28.41 10.17 28.41 30.52 0 11.19-2.47 19.47-7.4 24.84-1.85 2.03-4.41 3.75-7.66 5.16-3.26 1.41-7.05 2.11-11.37 2.11zm-17.18-41.49v20.35h6.08c3.17 0 5.49-.33 6.94-.99 1.45-.66 2.18-2.18 2.18-4.56v-9.25c0-2.38-.73-3.9-2.18-4.56-1.45-.66-3.77-.99-6.94-.99h-6.08zm53.52 61.45v-82.59h37c14.89 0 25.11 3.17 30.66 9.51 5.55 6.34 8.32 16.94 8.32 31.78 0 14.85-2.77 25.44-8.32 31.78-5.55 6.34-15.77 9.52-30.66 9.52h-37zm37.4-61.45h-10.97v40.3h10.97c3.61 0 6.23-.41 7.86-1.25 1.63-.84 2.44-2.75 2.44-5.75v-26.3c0-2.99-.81-4.91-2.44-5.74-1.63-.84-4.25-1.26-7.86-1.26zm98.71 30.79h-22.47v30.66h-26.43v-82.59h54.18l-3.3 21.14h-24.45v11.1h22.47v19.69zM265.81 24.26v29.1c0 65.66 15.31 69.47 69.08 69.47h22.03l-91.11-98.57zm94.33 115.92h-21.48c-61.02 0-90.2-4.09-90.2-86.28V17.35H56.82c-21.7 0-39.47 17.78-39.47 39.47v264.79H219.2c-4.64 5.47-8.83 11.34-12.51 17.54H17.35v89.83c0 21.62 17.85 39.47 39.47 39.47h149.04c3.53 6.12 7.56 11.92 12.02 17.34H56.82C25.63 485.79 0 460.17 0 428.98V56.82C0 25.55 25.55 0 56.82 0h206.33a8.68 8.68 0 016.93 3.45l105.07 113.68c2.19 2.37 2.34 4.66 2.34 7.52v166.86c-5.55-2.98-11.35-5.56-17.35-7.71V140.18z"
                />
                <path fill="red" d="M316.95 297.45c59.12 0 107.05 47.93 107.05 107.05 0 59.11-47.93 107.04-107.05 107.04S209.9 463.61 209.9 404.5c0-59.12 47.93-107.05 107.05-107.05z" />
                <path
                  fill="#fff"
                  fill-rule="nonzero"
                  d="M337.9 356.54l-3.77 47.75 17.35-6.07c11.47-4.4 23.27 3.72 14.38 13.82-10.82 12.45-27.26 29.55-39.22 40.94-7.43 7.42-11.73 7.49-19.18.06-13.24-13-26.24-27.44-39.18-40.87-9.25-10.06 2.3-18.55 14.28-13.95l17.16 6.01c-1.25-16.28-2.82-31.84-3.77-48.1 0-2.99 2.5-5.39 5.42-5.61 10.31 0 20.84-.24 31.12 0 2.92.22 5.42 2.62 5.42 5.61l-.01.41z"
                />
              </svg>
            </div>
            <span class="mt-4 inline-flex align-top text-lg leading-8 font-medium text-neutral-800 sm:text-lg md:text-lg lg:text-xl xl:text-2xl dark:text-neutral-100">{{ card.title_custom }}</span>
          </template>
        </UPageCard>
      </UPageGrid>
    </UPageSection>
  </div>

  <input
    ref="fileInput"
    type="file"
    class="hidden"
    accept=".pdf"
    @change="handleFileChange"
  />
</template>

<script setup lang="ts">
import { PDFDocument } from 'pdf-lib'
import CryptoJS from 'crypto-js'
import { storeToRefs } from 'pinia'

// 初始化数据
import { sendMessageToChromeExtension } from '@/composables/useDocTranslateParams'

// 组件相关
import { uploadFile } from '@/utils/aliyun' // 上传文件
import type { ParsePdfPdf } from '@/api/types'
import type { HeadersTokenType, MemberProductTokenUsageType, MemberProductType } from '@/api/login/types'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import PdfPlusRecord from '@/pages/pdf-plus/record/pdf-plus-record.vue'
import { isMobileDevice } from '@/utils/utils'
import ExtensionInstalled from '@/pages/components/ExtensionInstalled/index.vue'

// 接口
import { parsePdfByPdfUrl } from '@/api/mathpix'
import { getMemberProductInfo, getMemberProductTokenUsage, getMemberResourceAccount } from '@/api/login'
// 翻译类型
import TranslateType from '@/pages/components/TranslateType.vue'

// 文档翻译设置存储
import { useDocumentTranslateStore } from '@/store/documentTranslate'

const documentTranslateStore = useDocumentTranslateStore()
const { enabledEnginesList, translateEngine, translationsDisplay, targetLanguageZingCode, targetLanguage, targetLanguageList } = storeToRefs(documentTranslateStore)

// 国际化
const { t, locale } = useI18n()
const toast = useToast()

useSeoMeta({
  titleTemplate: '',
  title: 'PDF Plus' + ' - ' + t('common.site_name')
})

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: t('pdf_plus.title') + ' - ' + t('common.site_name'), // PDF Plus（专业 PDF 文档翻译）
  ogTitle: t('pdf_plus.title') + ' - ' + t('common.site_name'),
  description: t('pdf_plus.description'), // 专业 PDF 文档翻译，公式、图表高保真还原
  ogDescription: t('pdf_plus.description')
})

// 定义ref 变量
// 译文显示选项
const displayOptions = ref([
  { value: 'bilingual', label: t('pdf_plus.bilingual_translation') }, // 双语对照（灰色背景）
  { value: 'fulltext', label: t('pdf_plus.fulltext_translation') } // 全文翻译（仅译文）
])
// 会员信息
const memberProduct = ref({
  /**
   * 会员产品 Plus会员 状态(API返回的业务状态，非数据库字段状态)：
   * 0：未试用（免费版），
   * 1：Plus会员试用中（试用版），
   * 2：Plus会员试用已过期（免费版），
   * 3：Plus会员套餐有效期内，
   * 4：Plus会员套餐已过期（免费版）
   */
  member_product_status: 0,
  isMemberPlusValidity: false, // 会员是否在有效期内，包含试用和套餐
  pdf_plus_remaining_page_num: null, // 月度剩余页数
  pack_pdf_plus_remaining_num: 0 // 资源包剩余页数
})

// 选择的翻译引擎
const documentTranslateEngine = computed({
  get() {
    return translateEngine.value // 从 Store 中获取值
  },
  set(newValue) {
    // 通过 Store 方法或直接修改响应式变量
    translateEngine.value = newValue
  }
})

// 翻译选项数据- 语言集
const languageList = computed({
  get() {
    return targetLanguageList.value
  },
  set(newValue) {
    // 通过 Store 方法或直接修改响应式变量
    targetLanguageList.value = newValue
  }
})

const timer = ref(null)
const error = ref('') // 错误信息
const progress = ref(0) //  存储当前的解析进度。
const progressSwitch = ref(false) // 解析文件进度开关
const parsePdfStatus = ref(false) // 解析状态
const selectedFile = ref(null) // 预上传文件
const htmlContent = ref('') // 解析后的HTML内容

const fileInput = ref(null) // 文件输入框引用
const fileSelected = ref(false) // 设置文件已选择状态
const isShowHint = ref(false) // 是否显示提示信息
const isShowExceedHint = ref(false) // 是否超出显示提示信息
const isTheFileSizeIsOversized = ref(false) // 文件超大提示信息
const memberHasLogin = ref(false) // 是否登录
const isDataLoading = ref(false) // 新增加载状态
const loading = ref(false)
const isMobile = ref(isMobileDevice())

// 剩余 PDF Plus 总页数 = 会员套餐剩余页数 + 加量包剩余页数
const remaining_total_page_num = ref(0) // 剩余 PDF Plus 总页数

// 翻译页数范围3:6
const selectedPages = ref('')
// 文件加密状态
const isFileEncrypted = ref(false)
// 输入页数范围 3,6,-2
const inputPages = ref('')
// 输入页数范围错误
const inputPagesError = ref(false)
const totalPages = ref(0) // 实际要求解析的总页数
// 文件信息
const fileInfo = ref({
  pageCount: 0, // PDF页数
  fileName: '', // 文件名
  file_id: '', // 文件id
  pdf_md5: '', // 文件md5
  ossUrl: '', // 文件ossUrl
  storage_path: '' // 存储路径
})

const authStore = useAuthStoreWithOut()

onBeforeMount(async () => {
  await sendMessageToChromeExtension(locale.value, translateEngine.value.value) // 发送消息到浏览器插件
  isDataLoading.value = true
  // console.log('documentTranslateStore', authStore.getToken);
  if (authStore.getToken && authStore.getToken !== '') {
    // 已登录
    memberHasLogin.value = true
    await fetchTokenUsage() // 请求会员套餐token、pdf使用情况
  }
  else {
    // 未登录
    memberHasLogin.value = false
    isDataLoading.value = true
  }
})
// 首先先判断是否有翻译引擎
onMounted(async () => {
  clearTimeout(timer.value) // 组件卸载时清除定时器
})

// 操作切换目标语言
function onSelectionChangeTargetLanguage() {
  // 更新本地存储目标语言
  documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value)

  // 更新目标语言配置
  updateTargetLanguage()
}

// 操作切换翻译引擎（同步插件）
async function onSelectionChangeTranslateEngine(data) {
  if (!data) return

  await sendMessageToChromeExtension(locale.value, data.value) // 发送消息到浏览器插件

  // 更新目标语言配置
  updateTargetLanguage()
}

// 操作切换译文显示
function onSelectionChangeTranslationsDisplay(value) {
  documentTranslateStore.setTranslationsDisplay(value)
}

// 更新目标语言配置
function updateTargetLanguage() {
  // 从语言集合获取对应的lang_code
  const languageItem = languageList.value.find((item) => {
    return item.value === targetLanguageZingCode.value
  })

  if (languageItem) {
    targetLanguage.value = languageItem.lang_code
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
  else {
    // 从语言集合获取对应的lang_code， 不能直接赋值 zh-Hans, 因为部分模型 不支持
    const languageItem = languageList.value.find((item) => {
      return item.value === 'zh-Hans'
    })

    // 如果找不到，则将目标语言设置为简体中文
    targetLanguage.value = languageItem.lang_code
    targetLanguageZingCode.value = languageItem.zing_code

    documentTranslateStore.setTargetLanguageZingCode(targetLanguageZingCode.value) // 更新目标语言智应编码
    documentTranslateStore.setTargetLanguage(targetLanguage.value)
  }
}

/**
 * 请求会员套餐token使用、资源包情况
 */
async function fetchTokenUsage() {
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  const paramData: MemberProductType = {
    platform: 'ztsl'
  }
  const res = await getMemberProductInfo(headers, paramData)
  memberProduct.value = res.data
  // 获取会员产品 Plus会员 状态
  memberProduct.value.member_product_status = res.data?.member_product_status
  /**
   * 会员产品 Plus会员 状态(API返回的业务状态，非数据库字段状态)：
   * 0：未试用（免费版），
   * 1：Plus会员试用中（试用版），
   * 2：Plus会员试用已过期（免费版），
   * 3：Plus会员套餐有效期内，
   * 4：Plus会员套餐已过期（免费版）
   */
  // 并行发起两个请求
  const [tokenUsage, resource] = await Promise.all([
    memberProduct.value.member_product_status === 1 || memberProduct.value.member_product_status === 3 ? getMemberProductTokenUsage(headers, paramData) : Promise.resolve({ code: 200, data: null }),
    getMemberResourceAccount(headers, paramData)
  ])
  if (memberProduct.value.member_product_status === 1 || memberProduct.value.member_product_status === 3) {
    // 会员试用中或套餐有效期内 获取会员pdf使用情况
    if (tokenUsage.code === 200 && tokenUsage.data) {
      memberProduct.value = tokenUsage.data
    }
    memberProduct.value.isMemberPlusValidity = true
  }
  else {
    memberProduct.value.isMemberPlusValidity = false
  }
  // 获取加量包额度
  if (resource.code === 200 && resource.data) {
    memberProduct.value.pack_pdf_plus_remaining_num = resource.data.pack_pdf_plus_remaining_num
  }
  isDataLoading.value = false
}

// 输入翻译页数范围
function handleTranslationPagesChange() {
  clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    // 用户停止输入后的操作
    isShowExceedHint.value = false
    isShowHint.value = false
    inputPagesError.value = false

    const successInputPages = new Set()
    if (inputPages.value !== null && inputPages.value !== '') {
      // 正则表达式，匹配数字、数字范围、负数形式的页码
      const regex = /^(-?\d+(-\d+)?|-\d+|(-?\d+)-(-?\d+))(,(-?\d+(-\d+)?|-\d+|(-?\d+)-(-?\d+)))*$/
      // 测试输入字符串
      if (!regex.test(inputPages.value)) {
        // 如果输入不合法，显示错误信息
        inputPagesError.value = true
        // error.value = '请输入正确的页面范围';
        isShowExceedHint.value = true
        return
      }
      else {
        inputPagesError.value = false
        error.value = ''
      }
      // 清除翻译结果
      // 分割字符串
      const parts = inputPages.value.split(',')
      parts.forEach((part) => {
        if (part.includes('-')) {
          // 判断是不是负数
          const negativeRegex = /^-\d+$/
          if (negativeRegex.test(part)) {
            const negativePart = parseInt(part, 10)
            if (Math.abs(negativePart) > fileInfo.value.pageCount) {
              // 如果负数超出范围
              inputPagesError.value = true
              // error.value = '请输入正确的页面范围';
              isShowExceedHint.value = true
              return
            }
            // 判断是不是负数,例如：-3
            const page = fileInfo.value.pageCount + negativePart + 1
            successInputPages.add(page)
            // 退出循环
            return
          }

          let partList = []
          if (part.includes('--')) {
            const parts = part.split('--')
            if (parts.length !== 2) return [] // 无效格式
            partList = [parseInt(parts[0], 10), -parseInt(parts[1], 10)]
          }
          else if (part[0] !== '-') {
            const parts = part.split('-')
            if (parts.length !== 2) return [] // 无效格式
            partList = [parseInt(parts[0], 10), parseInt(parts[1], 10)]
          }
          else if (part[0] === '-') {
            const match = part.match(/^(-?\d+)-(\d+)$/)
            if (!match) return [] // 格式无效
            partList = [parseInt(match[1], 10), parseInt(match[2], 10)]
          }

          const originalStart = partList[0]
          const originalEnd = partList[1]
          if (Math.abs(originalStart) > fileInfo.value.pageCount) {
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }
          if (Math.abs(originalEnd) > fileInfo.value.pageCount) {
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }
          // 处理负数范围
          if (originalStart < 0 && originalEnd < 0) {
            if (originalStart > originalEnd) {
              // 负数范围(结束页码大于开始页码)
              inputPagesError.value = true
              // error.value = '请输入正确的页面范围';
              isShowExceedHint.value = true
              return
            }
          }
          if (originalStart < 0 && originalEnd > 0) {
            // 开始页数为负数，结束页数为正数
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }

          // 处理范围
          const [start, end] = partList.map((num) => {
            if (Math.abs(num) > fileInfo.value.pageCount) {
              inputPagesError.value = true
              // error.value = '请输入正确的页面范围';
              isShowExceedHint.value = true
              return
            }
            if (num < 0) num = fileInfo.value.pageCount + num + 1 // 负数转换
            return Math.max(1, Math.min(num, fileInfo.value.pageCount)) // 限制在有效范围内
          })

          // 添加范围内的所有页码
          for (let i = Math.min(start, end); i <= Math.max(start, end); i++) {
            successInputPages.add(i)
          }
        }
        else {
          // 单个页码
          let page = parseInt(part, 10)
          if (page > fileInfo.value.pageCount) {
            inputPagesError.value = true
            // error.value = '请输入正确的页面范围';
            isShowExceedHint.value = true
            return
          }
          if (isNaN(page)) return // 忽略无效的页码
          if (page < 0) page = fileInfo.value.pageCount + page + 1 // 负数转换
          if (1 <= page && page <= fileInfo.value.pageCount) {
            successInputPages.add(page)
          }
        }
      })
      selectedPages.value = successInputPages.size + ' / ' + fileInfo.value.pageCount
      const remaining_page_num = memberProduct.value.pdf_plus_remaining_page_num + memberProduct.value.pack_pdf_plus_remaining_num
      remaining_total_page_num.value = remaining_page_num
      if (successInputPages.size > fileInfo.value.pageCount || successInputPages.size == 0) {
        // 如果翻译页数大于剩余页数 禁用翻译按钮 则允许翻译去除禁用翻译按钮
        // 请修改翻译页数，最多不超过
        inputPagesError.value = true
        isShowExceedHint.value = true
      }
      else {
        // 计算剩余页数
        if (remaining_page_num < successInputPages.size) {
          // 剩余账号页数不足
          isShowHint.value = true
          inputPagesError.value = true
        }
      }
      totalPages.value = successInputPages.size
    }
    else {
      error.value = ''
      // 清除所有内容
      selectedPages.value = fileInfo.value.pageCount + ' / ' + fileInfo.value.pageCount
      totalPages.value = fileInfo.value.pageCount
      const remaining_page_num = memberProduct.value.pdf_plus_remaining_page_num + memberProduct.value.pack_pdf_plus_remaining_num
      remaining_total_page_num.value = remaining_page_num
      if (remaining_page_num < fileInfo.value.pageCount) {
        // 剩余账号页数不足
        isShowHint.value = true
        inputPagesError.value = true
      }
    }
  }, 100) // 设置延迟时间，单位为毫秒
}

// 源文件MD5加密
function md5_encryption_pdf() {
  const reader = new FileReader()
  reader.onload = (e) => {
    const buffer = e.target.result
    const md5 = CryptoJS.MD5(buffer).toString()
    console.log('MD5:', md5)
    fileInfo.value.pdf_md5 = md5
    // 在这里处理MD5加密后的字符串
  }
  reader.readAsArrayBuffer(selectedFile.value)
}

// 点击选择文件
const handleTriggerFileInput = () => {
  fileInput.value?.click()
}
// 跳转到购买套餐页
function handlePricing() {
  navigateTo({ path: '/' + locale.value + '/pricing' })
}

// 选择文件
async function handleFileChange(event: Event) {
  selectedFile.value = (event.target as HTMLInputElement)?.files?.[0]
  fileInfo.value.fileName = selectedFile.value.name
  fileSelected.value = true // 设置文件已选择状态
  isTheFileSizeIsOversized.value = false
  if (!selectedFile.value) return
  const fileSizeMB = selectedFile.value.size / 1024 / 1024
  const isLt2M = fileSizeMB < 100
  if (!isLt2M) {
    // 上传文件大小不能超过 100MB
    isTheFileSizeIsOversized.value = true
    return
  }
  // 源文件MD5加密
  // md5_encryption_pdf();
  fileInfo.value.fileName = selectedFile.value.name
  isShowExceedHint.value = false
  try {
    const arrayBuffer = await selectedFile.value.arrayBuffer()
    // const pdf = await PDFDocument.load(arrayBuffer);
    const pdf = await PDFDocument.load(arrayBuffer, {
      ignoreEncryption: true,
      throwOnInvalidObject: false
    })

    // 无论是否加密都获取页数
    fileInfo.value.pageCount = pdf.getPageCount()
    // 标记加密状态
    isFileEncrypted.value = pdf.isEncrypted
    // 可以在这里做其他处理，例如显示页数
    inputPages.value = null
    parsePdfStatus.value = true

    selectedPages.value = fileInfo.value.pageCount + ' / ' + fileInfo.value.pageCount
    totalPages.value = fileInfo.value.pageCount
    const remaining_page_num = memberProduct.value.pdf_plus_remaining_page_num + memberProduct.value.pack_pdf_plus_remaining_num
    remaining_total_page_num.value = remaining_page_num
    if (remaining_page_num < fileInfo.value.pageCount) {
      // 剩余页数不足
      isShowHint.value = true
      inputPagesError.value = true
    }
    else {
      inputPagesError.value = false
      isShowHint.value = false
    }
    isFileEncrypted.value = false
    languageList.value = documentTranslateStore.targetLanguageList

    // PDF语言检测
    try {
      const detectedLanguage = await detectPdfLanguagePdfLib(pdf)
      if (detectedLanguage) {
        // 将检测到的语言代码发送给content脚本和Chrome扩展
        window.postMessage(
          {
            type: 'PDF_LANGUAGE_DETECTED',
            languageCode: detectedLanguage
          },
          '*'
        )

        console.log('PDF Plus语言检测完成:', detectedLanguage)
      }
    }
    catch (error) {
      console.error('PDF Plus语言检测失败:', error)
    }
  }
  catch (error) {
    console.error('PDF加载错误:', error)
    if (error.message.includes('PDFDict2') || error.message.includes('undefined')) {
      isFileEncrypted.value = true
      toast.add({
        title: t('pdf_plus.messages.parsing_failed'), // 解析失败
        description: t('pdf_plus.messages.parsing_failed_tips_2'), // 無法解析文件- 文件可能已損壞或加密方式不受支持
        color: 'error'
      })
    }
    else if (error.message.includes('encrypted')) {
      toast.add({
        title: t('pdf_plus.messages.parsing_failed'), // 解析失败
        description: t('pdf_plus.messages.parsing_failed_tips_3'), // '无法解析加密文件'
        color: 'error'
      })
      isFileEncrypted.value = true
    }
    else {
      console.log('解析PDF文件失败: ' + error.message)
    }
  }
}

// 重置文件
const resetFile = () => {
  fileSelected.value = false
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}
// 立即翻译
const startTranslate = () => {
  upload()
}

// 主流程立即翻译
// 执行解析PDF文件
async function upload() {
  if (isFileEncrypted.value == true) {
    toast.add({
      title: t('pdf_plus.messages.loading_failed'), // 加载失败
      description: t('pdf_plus.messages.loading_failed_tips_1'), // 该文件已加密，请解密后重新上传
      color: 'error'
    })
    return
  }

  if (memberProduct.value.isMemberPlusValidity == false) {
    toast.add({
      title: t('pdf_plus.messages.loading_failed'), // 加载失败
      description: t('pdf_plus.messages.loading_failed_tips_2'), // 产品/会员服务已过期，请续费后重试
      color: 'error'
    })
    return
  }
  progress.value = 0
  progressSwitch.value = true
  try {
    // 打开进度条
    const authStore = useAuthStoreWithOut()
    const headers: HeadersTokenType = {
      token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
    }
    if (inputPages.value === '' || inputPages.value === null) {
      // 如果没有输入页码范围，则默认为所有页码
      inputPages.value = 1 + '-' + fileInfo.value.pageCount
    }

    // 判断是否已经上传PDF到OSS
    if (fileInfo.value.ossUrl) {
      const queryParams: ParsePdfPdf = {
        platform: 'ztsl',
        url: fileInfo.value.ossUrl,
        total_pages: fileInfo.value.pageCount.toString(),
        page_ranges: inputPages.value,
        storage_bucket: 'pdf',
        storage_path: fileInfo.value.storage_path,
        storage_cloud: 'aliyun',
        coded_text: fileInfo.value.pdf_md5,
        name: fileInfo.value.fileName
      }
      // 发起解析PDF中的内容
      console.log('发起解析PDF中的内容:', queryParams)
      parsePdfByPdfUrl(headers, queryParams).then((res) => {
        if (res.code !== 200) {
          console.log('发起解析PDF错误:', res)
        }
        fileInfo.value.file_id = res.data
        progress.value = 30
        // 查询解析状态
        handleclick()
      })
    }
    else {
      // 上传PDF到OSS
      const fileObj = {
        status: 1,
        progress: 0 // 上传进度
      }
      //  pdf_plus 为上传目录
      const uploadResult = await uploadFile(selectedFile.value, fileObj, 'pdf_plus')
      if (uploadResult.status === 3) {
        progress.value = 30
        // 请求解析PDF
        fileInfo.value.ossUrl = uploadResult.ossUrl
        fileInfo.value.storage_path = uploadResult.storage_path
        const queryParams: ParsePdfPdf = {
          platform: 'ztsl',
          url: uploadResult.ossUrl,
          total_pages: fileInfo.value.pageCount.toString(),
          page_ranges: inputPages.value,
          storage_bucket: uploadResult.storage_bucket,
          storage_path: uploadResult.storage_path,
          storage_cloud: uploadResult.storage_cloud,
          coded_text: fileInfo.value.pdf_md5,
          name: fileInfo.value.fileName
        }
        // 发起解析PDF中的内容返回文件ID
        const parseResult = await parsePdfByPdfUrl(headers, queryParams)
        console.log('发起解析PDF错误:', parseResult)
        if (parseResult.code == 200) {
          fileInfo.value.file_id = parseResult.data
          handleclick()
        }
      }
    }
  }
  catch (error) {
    console.error('Error in the main process:', error)
  }
}

function handleclick() {
  clearInterval()
  deleteRequest()
  // 跳转pdf解析页面
  // 假设这里设置的 title 名称为 'PDF Plus 详情页'，可根据实际需求修改
  navigateTo({ path: '/' + locale.value + '/pdf-plus/' + fileInfo.value.file_id })
}
// 调用开始轮询请求获取文件解析进度
// 清除定时器
function clearInterval() {
  let intervalId = null // 存储定时器ID，用于后续清除定时器。
  if (intervalId) {
    intervalId = null
  }
  clearTimeout(intervalId)
}

function deleteRequest() {
  // 删除已上传的文件
  selectedFile.value = null
  fileInfo.value.fileName = ''
  fileInfo.value.ossUrl = ''
  htmlContent.value = ''
}

// 跳转到会员PDF解析记录
function handleMemberPdfRecord() {
  navigateTo({ path: '/' + locale.value + '/pdf-plus-record' })
}

function goLogin() {
  navigateTo({ path: '/' + locale.value + '/login' })
}

/** 解析类型 */
const parsing_types = ref([
  {
    title_custom: t('pdf_plus_introduction.parsing_type.text_and_handwriting'), // 文本和手写内容
    description: t('pdf_plus_introduction.parsing_type.text_and_handwriting_desc'), // 从图像和 PDF 中智能提取打印文本和手写内容。
    icon_custom: 'i-fluent-draw-text-20-filled',
    class: 'lg:col-span-1',
    variant: 'soft' as const,
    image: {
      path: '../../assets/images/pdf_plus/text.webp',
      width: 200,
      height: 100
    },
    reverse: false
  },
  {
    title_custom: t('pdf_plus_introduction.parsing_type.mathematical_equations'), // 数学公式
    description: t('pdf_plus_introduction.parsing_type.mathematical_equations_desc'), // 采用博士级别的数学算法，精确解析图片和文档中的方程式。
    icon_custom: 'i-fluent-math-formula-20-filled',
    class: 'lg:col-span-2',
    variant: 'soft' as const,
    image: {
      path: '../../assets/images/pdf_plus/math.webp',
      width: 200,
      height: 100
    },
    reverse: false
  },
  {
    title_custom: t('pdf_plus_introduction.parsing_type.complex_tables'), // 复杂表格
    description: t('pdf_plus_introduction.parsing_type.complex_tables_desc'), // AI 视觉处理，轻松应对复杂的表格。
    icon_custom: 'i-ph-table-duotone',
    class: 'lg:col-span-1',
    variant: 'soft' as const,
    image: {
      path: '../../assets/images/pdf_plus/table.webp',
      width: 200,
      height: 100
    },
    reverse: false
  },
  {
    title_custom: t('pdf_plus_introduction.parsing_type.code_snippets'), // '代码片段'
    description: t('pdf_plus_introduction.parsing_type.code_snippets_desc'), // '从图像和 PDF 中提取格式完美的代码片段。'
    icon_custom: 'i-heroicons-outline-code',
    class: 'lg:col-span-1',
    variant: 'soft' as const,
    image: {
      path: '../../assets/images/pdf_plus/code.webp',
      width: 200,
      height: 100
    },
    reverse: false
  },
  {
    title_custom: t('pdf_plus_introduction.parsing_type.chemical_formulas'), // '化学公式'
    description: t('pdf_plus_introduction.parsing_type.chemical_formulas_desc'), // '基于 OCR 技术，识别并提取化学公式和图表。'
    icon_custom: 'i-lets-icons-chemistry',
    class: 'lg:col-span-1',
    variant: 'soft' as const,
    image: {
      path: '../../assets/images/pdf_plus/chemical.webp',
      width: 200,
      height: 100
    },
    reverse: false
  }
])

// 功能
const features = [
  {
    title: t('pdf_plus_introduction.features.bilingual_translation'), // 逐段双语对照
    description: t('pdf_plus_introduction.features.bilingual_translation_desc'), // 在每段原文后以鲜明的格式显示译文，逐段对比，清晰易读。
    orientation: 'horizontal', // 布局方向 horizontal | vertical
    reverse: true // 是否反转布局
  },
  {
    title: t('pdf_plus_introduction.features.supports_scanned_versions'), // 支持扫描版 PDF
    description: t('pdf_plus_introduction.features.supports_scanned_versions_desc'), // 采用 AI + OCR（光学字符识别）技术，支持扫描版 PDF 或文档中图片格式内容的精确解析翻译。
    orientation: 'horizontal', // 布局方向 horizontal | vertical
    reverse: false // 是否反转布局
  },
  {
    title: t('pdf_plus_introduction.features.layout_conversion'), // 双栏、三栏布局转换
    description: t('pdf_plus_introduction.features.layout_conversion_desc'), // 针对学术论文常用的双栏、三栏布局，统一转换为易于阅读单栏格式，实现更优雅的内容排版，提升阅读体验。
    orientation: 'horizontal', // 布局方向 horizontal | vertical
    reverse: true // 是否反转布局
  }
]

/**
 * PDF Plus 逐行对照翻译
 */
const bilingual_translation_images = ['../../assets/images/pdf_plus/bilingual_translation_2.webp']

/**
 * PDF Plus 扫描版PDF翻译
 */
const scanned_pdf_translation_images = ['../../assets/images/pdf_plus/scanned_pdf_translation_2.webp']

/**
 * PDF Plus 统一单栏格式展示（排版）
 */
const typesetting_images = ['../../assets/images/pdf_plus/typesetting.webp']

/** 翻译内容导出 */
const export_cards = ref([
  {
    title_custom: t('pdf_plus_introduction.export.html'), // 导出为 HTML
    description: t('pdf_plus_introduction.export.html_desc'), // 可将翻译后的内容以 HTML 格式导出保存，方便在不同设备的浏览器中查看。
    class: 'lg:col-span-1',
    image_path: '../../assets/images/pdf_plus/download-html-icon.svg',
    reverse: true
  },
  {
    title_custom: t('pdf_plus_introduction.export.pdf'), // 导出为 PDF
    description: t('pdf_plus_introduction.export.pdf_desc'), // 可将翻译后的内容以 PDF 格式导出保存，会原样保留在线翻译阅读时的排版和样式。
    class: 'lg:col-span-1',
    image_path: '../../assets/images/pdf_plus/download-html-icon.svg',
    reverse: true
  }
])

// ------------------------------------------------- PDF 语言检测 -------------------------------------------------

/**
 * 检测PDF语言（使用pdf-lib）
 * @param {PDFDocument} pdfDocument - PDF文档对象
 * @returns {Promise<string>} - 检测到的语言代码
 */
const detectPdfLanguagePdfLib = async (pdfDocument: any): Promise<string | null> => {
  try {
    // 提取前3页的文本内容作为样本
    const maxPages = Math.min(3, pdfDocument.getPageCount())
    const textPromises = []

    for (let i = 0; i < maxPages; i++) {
      try {
        const page = pdfDocument.getPage(i)
        // 由于pdf-lib主要用于生成PDF，文本提取功能有限
        // 我们需要使用另一种方法来获取文本内容
        // 这里先使用简单的方法，如果失败则跳过该页
        textPromises.push(Promise.resolve(''))
      }
      catch (error) {
        console.warn(`无法提取第${i + 1}页文本:`, error)
        textPromises.push(Promise.resolve(''))
      }
    }

    const texts = await Promise.all(textPromises)
    const sampleText = texts.join(' ').trim()

    // 如果无法从pdf-lib提取文本，我们使用原始文件重新处理
    if (sampleText.length < 10) {
      console.warn('pdf-lib无法提取足够文本，尝试使用其他方法')
      return await detectLanguageFromPdfFile(selectedFile.value)
    }

    // 使用Google Translate API检测语言
    const detectedLang = await detectLanguageWithGoogle(sampleText.slice(0, 1000))

    // 将Google Translate的语言代码转换为Zing代码
    return mapGoogleLangToZingCode(detectedLang)
  }
  catch (error) {
    console.error('PDF语言检测失败:', error)
    return null
  }
}

/**
 * 从PDF文件直接检测语言（备用方法）
 * @param {File} pdfFile - PDF文件对象
 * @returns {Promise<string>} - 检测到的语言代码
 */
const detectLanguageFromPdfFile = async (pdfFile: File): Promise<string | null> => {
  try {
    // 读取PDF文件的原始内容
    const arrayBuffer = await pdfFile.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)

    // 简单的文本提取：查找PDF中的文本内容
    let text = ''
    const decoder = new TextDecoder('utf-8', { fatal: false })

    // 将二进制内容转换为字符串并提取可读文本
    const fullText = decoder.decode(uint8Array)

    // 使用正则表达式提取可能的文本内容
    const textMatches = fullText.match(/[\u0020-\u007E\u00A0-\u024F\u1E00-\u1EFF\u2000-\u206F\u3000-\u303F\u4E00-\u9FFF]+/g)

    if (textMatches) {
      text = textMatches.join(' ').slice(0, 1000)
    }

    if (text.length < 50) {
      console.warn('PDF文本内容太少，无法进行语言检测')
      return null
    }

    // 使用Google Translate API检测语言
    const detectedLang = await detectLanguageWithGoogle(text)
    return mapGoogleLangToZingCode(detectedLang)
  }
  catch (error) {
    console.error('PDF文件语言检测失败:', error)
    return null
  }
}

/**
 * 使用Google Translate API检测语言
 * @param {string} text - 要检测的文本
 * @returns {Promise<string>} - 检测到的语言代码
 */
const detectLanguageWithGoogle = async (text: string): Promise<string> => {
  try {
    // 清理文本，移除特殊字符
    const cleanText = text
      .replace(/[^\u0020-\u007E\u00A0-\u024F\u1E00-\u1EFF\u2000-\u206F\u3000-\u303F\u4E00-\u9FFF]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()

    if (cleanText.length < 20) {
      return 'en' // 默认返回英语
    }

    // 使用Google Translate免费检测API
    const response = await fetch(`https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=en&dt=t&q=${encodeURIComponent(cleanText.slice(0, 500))}`)
    const data = await response.json()

    // Google Translate API返回格式：[[[翻译结果, 原文, null, null, 检测到的语言代码]], null, 检测到的语言代码]
    if (data && data.length > 2 && data[2]) {
      return data[2] // 返回检测到的语言代码
    }

    return 'en' // 默认返回英语
  }
  catch (error) {
    console.error('Google语言检测API调用失败:', error)
    return 'en' // 默认返回英语
  }
}

/**
 * 将Google Translate语言代码映射为Zing代码
 * @param {string} googleLangCode - Google Translate语言代码
 * @returns {string} - Zing语言代码
 */
const mapGoogleLangToZingCode = (googleLangCode: string): string => {
  const langMapping: Record<string, string> = {
    'zh-cn': 'zh-Hans',
    'zh-tw': 'zh-Hant',
    'zh': 'zh-Hans',
    'en': 'en',
    'ja': 'ja',
    'ko': 'ko',
    'fr': 'fr',
    'de': 'de',
    'es': 'es',
    'pt': 'pt',
    'ru': 'ru',
    'ar': 'ar',
    'th': 'th',
    'vi': 'vi',
    'it': 'it',
    'nl': 'nl',
    'pl': 'pl',
    'tr': 'tr',
    'sv': 'sv',
    'da': 'da',
    'no': 'no',
    'fi': 'fi',
    'cs': 'cs',
    'hu': 'hu',
    'ro': 'ro',
    'bg': 'bg',
    'hr': 'hr',
    'sk': 'sk',
    'sl': 'sl',
    'et': 'et',
    'lv': 'lv',
    'lt': 'lt',
    'uk': 'uk',
    'el': 'el',
    'he': 'he',
    'fa': 'fa',
    'hi': 'hi',
    'bn': 'bn',
    'ur': 'ur',
    'ms': 'ms',
    'id': 'id',
    'tl': 'tl'
  }

  return langMapping[googleLangCode.toLowerCase()] || googleLangCode
}
</script>

<style scoped>
.text-trans {
  width: 100%;
}
.flex-gap2 {
  width: 100%;
}
.file-set-select {
  padding: 3px 0 3px 15px;
  border-radius: 30px;
  background-color: #f7f7f7;
  margin-right: 15px;
  align-items: center;
  display: flex;
}

.upload_box {
  width: 70%;
  height: 550px;
  padding: 2px 23px 23px 23px;
  margin: 20px auto;
  border-radius: 10px;
  background-color: #f9f9fa;
  .upload_flex {
    cursor: pointer;
    box-sizing: border-box;
    background-color: transparent;
    padding: 10%;
    width: 100%;
    height: 93%;
  }
  .upload_record {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      font-size: 14px;
      margin-right: 15px;
    }
  }

  .upload_content {
    cursor: pointer;

    .content_box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .desc {
      padding: 0 40px;
    }
  }
}
.error {
  color: red;
  font-size: 0.5em;
}
/* 新增样式：设置虚线边框并移除默认边框 */
.dashed-border {
  max-width: none;
  border: 2px dashed #cccccc !important;
  border-radius: 10px;
}
.uploadContent {
  width: 80%;
  height: 80%;
  margin: 20px auto;
  cursor: pointer;
}

.engine-select-icon {
  display: inline-flex;
  width: 20px;
  height: 20px;
  vertical-align: middle;
}

.translation-tip {
  position: absolute;
  top: 60%; /* 垂直居中对齐图标 */
  left: 80%; /* 显示在图标右侧 */
  transform: translateX(-50%);
  padding: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 190px;
  z-index: 1000;
  word-break: break-all;
}
.translation-tip-icon {
  position: absolute;
  top: 60%; /* 垂直居中对齐图标 */
  left: 80%; /* 显示在图标右侧 */
  margin-left: 42px; /* 与图标间距 */
  transform: translateX(-50%);
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 300px;
  z-index: 1000;
  word-break: break-all;
}

.images_container {
  box-shadow: #0003 0 0px 8px 0px;
}

.color_text_title {
  background: -webkit-linear-gradient(120deg, #1da5d6 20%, #2761ee);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
