<template>
  <UCard class="mt-24 mb-24 w-full max-w-sm bg-white/75 backdrop-blur lg:mt-32 dark:bg-white/5">
    <div id="wechat_login_container" class="wechat_login_container" />
  </UCard>
</template>

<script setup lang="ts">
// 微信登录：https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html
import { onMounted } from 'vue'

// definePageMeta 定义页面的布局 (此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'authentication' // 使用 layouts/authentication.vue 布局
})

const { t, locale } = useI18n()

useHead({
  // 使用微信账号登录
  titleTemplate: '',
  title: t('auth.common.login_with_wechat') + ' - ' + t('common.site_name')
})

// 页面挂载时加载
onMounted(() => {
  loadWechatLoginQRCode()
})

// 实例微信js对象，加载微信登录二维码
const loadWechatLoginQRCode = () => {
  const s = document.createElement('script')
  s.type = 'text/javascript'
  s.src = 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js'
  const wechatElement = document.body.appendChild(s)

  // 内嵌样式，1、如果电脑登录登录了微信，会显示快捷登录按钮，需要调整位置；2、隐藏扫码页脚的‘精挑翻译’标签
  const customCSS = `
    .js_quick_login.web_qrcode_panel_quick_login .qlogin_mod {
      margin-top: 90px !important;
    }

    #wx_default_tip p:nth-child(2) {
      display: none !important;
    }
  `
  // 转换为base64
  const cssBase64 = btoa(customCSS)
  const cssDataUrl = `data:text/css;base64,${cssBase64}`

  wechatElement.onload = function () {
    const obj = new WxLogin({
      self_redirect: false,
      id: 'wechat_login_container', // 显示登录二维码的容器id
      appid: 'wx0054e9a78f0591b2', // 应用appid
      scope: 'snsapi_login', // scope网页登录默认snsapi_login
      redirect_uri: encodeURIComponent('https://selecttranslate.com/' + locale.value + '/wechat_login_redirect'), // 授权成功后重定向地址，需要进行UrlEncode
      state: Math.ceil(Math.random() * 1000), // 设置为简单的随机数加session用来校验
      style: 'black', // 提供"black"、"white"可选。二维码被嵌入页面的样式
      href: cssDataUrl, // 外部自定义css样式文件url，第三方可根据实际需求覆盖默认样式，需要https
      lang: locale.value === 'zhHans' || locale.value === 'zhHant' ? 'cn' : 'en' // 只支持cn、en，如果是简体或者繁体，默认为cn
    })
  }
}
</script>

<style scoped>
.wechat_login_container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
