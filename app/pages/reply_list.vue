<template>
  <div class="mx-auto mt-4 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- 在模板顶部创建一个全局共享的文件输入元素 -->
    <input
      ref="fileInputRef"
      type="file"
      multiple
      accept="image/jpeg,image/png"
      style="display: none"
      @change="handleNativeFileChange"
    />

    <div class="mt-6">
      <!-- 聊天界面容器 -->
      <div class="chat-container flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-900">
        <!-- 标题头部 -->
        <div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
          <div class="flex flex-row items-center justify-between">
            <div>
              <!-- 问题反馈回复 -->
              <h3 class="text-left text-lg font-bold tracking-tight text-neutral-900 sm:text-xl lg:text-2xl dark:text-white">
                {{ t('reply_list.title') }}
              </h3>
            </div>
          </div>
        </div>
        <!-- 聊天消息区域 -->
        <div ref="chatMessagesWrapperRef" class="chat-messages-wrapper chat-scroll-area flex-1 p-4" style="background: transparent">
          <UChatMessages
            :messages="chatMessages"
            :user="{
              side: 'right',
              variant: 'soft'
              // avatar: member.signup_mode === 'wechat' && member.avatar ? { src: member.avatar, size: '2xl' } : { icon: 'carbon:user-avatar', size: '2xl' }
            }"
            :assistant="{
              side: 'left',
              variant: 'outline'
            }"
            :should-scroll-to-bottom="true"
            :should-auto-scroll="true"
          >
            <template #leading="{ message }">
              <img v-if="message.role === 'assistant'" :src="translateModeIcon['AISelectResults']" class="h-5 w-5" />
            </template>
            <template #content="{ message }">
              <!-- 图片消息 -->
              <div v-if="(message as any).isImageMessage" class="">
                <div v-if="(message as any).images && (message as any).images.length > 0" class="space-y-0">
                  <div
                    v-for="(image, index) in (message as any).images"
                    :key="index"
                    class="cursor-pointer"
                    @click="openImageViewer((message as any).images, index)"
                  >
                    <img
                      :src="ensureHttps(image.image_url)"
                      class="w-full max-w-sm rounded-lg"
                      :alt="`图片 ${index + 1}`"
                      style="object-fit: cover; min-height: 100px; max-height: 300px"
                    />
                  </div>
                </div>
              </div>

              <!-- 文本消息 -->
              <div v-else class="space-y-0">
                <!-- 消息内容 -->
                <div class="text-sm leading-relaxed break-words whitespace-pre-wrap">
                  {{ message.content }}
                </div>

                <!-- 时间戳 -->
                <div class="flex items-center justify-between">
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ (message as any).timestamp }}
                  </div>
                </div>
              </div>
            </template>
          </UChatMessages>

          <!-- 加载更多区域 -->
          <div class="flex justify-center py-3">
            <div v-if="loadingMore" class="flex items-center gap-2 text-gray-500">
              <UIcon name="i-heroicons-arrow-path-20-solid" class="h-4 w-4 animate-spin" />
              <span class="text-sm">{{ t('reply_list.loading.loading_more') }}</span>
            </div>
            <div v-else-if="replyDataList.length >= totalCount && replyDataList.length > 0" class="text-sm text-gray-500">
              {{ t('reply_list.loading.all_loaded') }}
            </div>
            <!-- 自动加载生效后无需手动按钮，保留占位 -->
          </div>
          <div ref="loadMoreTriggerRef" style="height: 1px" />
        </div>

        <!-- 输入区域分割线 - 只在未完成状态下显示 -->
        <div
          v-if="!(feedbackData && feedbackData.length > 0 && (feedbackData[0].feedback_status === '2' || feedbackData[0].feedback_status === 2))"
          class="border-t border-gray-200 dark:border-gray-700"
        />

        <!-- 回复输入框 (直接作为文本输入区域) - 只在未完成状态下显示 -->
        <div
          v-if="!(feedbackData && feedbackData.length > 0 && (feedbackData[0].feedback_status === '2' || feedbackData[0].feedback_status === 2))"
          class="relative flex min-h-[120px] flex-col bg-white p-4 dark:bg-gray-900"
          @dragover.prevent
          @dragenter.prevent="dragActive = true"
          @dragleave.prevent="handleDragLeave"
          @drop.prevent="handleFileDrop"
        >
          <!-- 可编辑内容区域 (直接使用可编辑div替代textarea) -->
          <div
            ref="contentEditableRef"
            class="reply-editor focus:border-primary-300 dark:focus:border-primary-600 mb-3 min-h-[80px] flex-1 p-2 break-words whitespace-pre-wrap transition-colors outline-none"
            contenteditable="true"
            :data-placeholder="placeholder"
            @input="handleContentEditableInput"
            @keydown="handleEnterKey"
            @paste="handlePaste"
            @focus="handleContentFocus"
            @blur="handleContentBlur"
          />

          <!-- 底部工具栏 -->
          <div class="mt-auto flex items-center justify-between">
            <!-- 左侧：图片上传按钮和图片预览区域 -->
            <div class="flex items-center space-x-2">
              <UButton
                icon="i-heroicons-photo"
                size="sm"
                color="gray"
                variant="ghost"
                class="rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                :disabled="fileList.length >= maxLimit"
                @click="safeOpenFilePicker"
              />

              <!-- 图片预览区域 - 移动到右侧 -->
              <div v-if="fileList.length > 0" class="flex items-center">
                <div class="mr-2 flex flex-wrap gap-1">
                  <div v-for="(file, index) in filePreviewList" :key="index" class="group relative">
                    <img
                      :src="file.preview"
                      class="hover:border-primary-300 dark:hover:border-primary-500 h-8 w-8 cursor-pointer rounded-lg border-2 border-gray-200 object-cover shadow-sm transition-all hover:shadow-md dark:border-gray-600"
                      @click="openInlineImageViewer(index)"
                    />
                    <UButton
                      icon="i-heroicons-x-mark"
                      color="red"
                      variant="solid"
                      size="2xs"
                      class="absolute -top-1 -right-1 !h-4 !w-4 !min-w-0 rounded-full opacity-0 shadow-sm transition-opacity group-hover:opacity-100"
                      @click="removeImage(index)"
                    />
                  </div>
                </div>
                <div class="text-xs text-gray-500">
                  {{ fileList.length }}/{{ maxLimit }}
                </div>
              </div>
            </div>

            <!-- 右侧状态和发送按钮 -->
            <div class="flex items-center space-x-3">
              <!-- 字符计数和提示 -->
              <div class="flex items-center space-x-2 text-xs text-gray-400">
                <!-- <span>Enter {{ 'send' }}</span> -->
                <span :class="characterCountClass">{{ content.length }}/{{ maxContentLength }}</span>
              </div>

              <!-- 发送按钮 -->
              <UButton
                size="sm"
                color="primary"
                variant="solid"
                class="flex items-center justify-center rounded-md px-4 py-2 font-bold"
                :loading="submitting"
                :disabled="!content.trim()"
                @click="submitReply(feedback_no, null)"
              >
                {{ t('reply_list.reply.submit') }}
              </UButton>
            </div>
          </div>

          <!-- 拖放覆盖层，只在拖拽时显示 -->
          <div
            v-show="dragActive"
            class="bg-primary-50/95 dark:bg-primary-900/95 absolute inset-0 z-10 flex items-center justify-center rounded-xl backdrop-blur-sm"
            @dragover.prevent
            @dragenter.prevent="dragActive = true"
            @dragleave.prevent="dragActive = false"
            @drop.prevent="handleFileDrop"
          >
            <div class="animate-pulse text-center">
              <UIcon name="i-heroicons-cloud-arrow-up" class="text-primary-500 mx-auto h-16 w-16" />
              <p class="text-primary-600 dark:text-primary-400 mt-3 text-xl font-semibold">
                {{ t('reply_list.reply.drop_text') }}
              </p>
              <p class="text-primary-500 dark:text-primary-300 mt-1 text-sm">
                {{ t('reply_list.reply.drop_hint', 'Release to upload images') }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 在分页控件前添加处理完成区域 -->
      <div class="flex items-center justify-between px-4 py-4">
        <div class="flex items-center">
          <!-- 是否完结 -->
          <div class="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            {{ t('reply_list.status.completion') }}：
          </div>
          <!-- 标记为已完成 -->
          <UButton
            v-if="feedbackData && feedbackData.length > 0 && feedbackData[0].feedback_status !== '2' && feedbackData[0].feedback_status !== 2"
            size="md"
            color=""
            class="ml-2"
            :loading="processingStatus"
            icon="mdi:tick-circle-outline"
            :title="t('reply_list.status.mark_as_completed')"
            @click="handleCompleteStatus"
          />
          <div v-else class="text-black-600 ml-2 text-xs font-medium">
            {{
              feedbackData && feedbackData.length > 0 && (feedbackData[0].feedback_status === '2' || feedbackData[0].feedback_status === 2)
                ? t('reply_list.status.completed')
                : t('reply_list.status.not_completed')
            }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 图片查看器 -->
  <UModal v-model:open="isImageViewerOpen" fullscreen>
    <template #content>
      <div class="relative flex h-full flex-col items-center justify-center p-4">
        <!-- 关闭按钮 -->
        <UButton
          icon="i-heroicons-x-mark"
          size="4xl"
          color="neutral"
          variant="ghost"
          class="absolute top-10 right-10 z-10 text-5xl"
          aria-label="Close"
          @click="isImageViewerOpen = false"
        />

        <div class="image-viewer flex h-full w-full items-center justify-center">
          <UColorModeImage :light="currentImageUrl" :dark="currentImageUrl" class="max-h-full max-w-full object-contain" />
        </div>
        <div class="mt-4 flex justify-center gap-2">
          <UButton
            icon="i-heroicons-arrow-left"
            size="sm"
            :disabled="currentImageIndex === 0"
            @click="changeImage(-1)"
          />
          <span class="text-sm">{{ currentImageIndex + 1 }} / {{ viewerImages.length }}</span>
          <UButton
            icon="i-heroicons-arrow-right"
            size="sm"
            :disabled="currentImageIndex >= viewerImages.length - 1"
            @click="changeImage(1)"
          />
        </div>
      </div>
    </template>
  </UModal>

  <!-- 确认完结对话框 -->
  <UModal v-model:open="isConfirmModalOpen" :ui="{ width: 'md:max-w-md' }">
    <template #content>
      <div class="p-4">
        <div class="mb-4 flex items-center justify-between">
          <h3 class="text-base leading-6 font-semibold text-neutral-900 dark:text-white">
            <!-- 确认提示 -->
            {{ t('reply_list.status.confirm_title') }}
          </h3>
        </div>

        <div class="py-4">
          <p class="text-sm text-neutral-500 dark:text-neutral-400">
            {{ confirmModalMessage }}
          </p>
        </div>

        <div class="mt-4 flex justify-end gap-3">
          <UButton
            color="neutral"
            variant="subtle"
            class="rounded-md"
            @click="isConfirmModalOpen = false"
          >
            <!-- 取消 -->
            {{ t('reply_list.status.cancel') }}
          </UButton>
          <UButton
            color="primary"
            variant="subtle"
            class="rounded-md"
            @click="confirmComplete"
          >
            <!-- 确认 -->
            {{ t('reply_list.status.confirm') }}
          </UButton>
        </div>
      </div>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, reactive, computed, nextTick } from 'vue'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import { storeToRefs } from 'pinia'
import { getReplyByFeedbackNoApi, getFeedbackImages, getFeedbackListApi, createReply, uploadFeedbackImage, updateFeedbackStatusBatch } from '@/api/feedback'
import type { HeadersTokenType } from '@/api/login/types'
import { useRoute, useRouter } from 'vue-router'
// import favicon from '@/assets/images/favicon.png';
import { translateModeIcon } from '@/utils/translateModeIcon'

const { t, locale } = useI18n()

// 添加布局定义，确保使用member布局
definePageMeta({
  layout: 'member',
  middleware: 'auth'
})

// 页面标题及描述，这个会员内部页面：不需要做SEO优化，所以不设置描述
useSeoMeta({
  titleTemplate: '',
  title: t('reply_list.title') + ' - ' + t('common.site_name')
})

const toast = useToast()

// 修改为更清晰的名称并声明正确的类型
const fileInputRef = ref<HTMLInputElement | null>(null)
const contentEditableRef = ref<HTMLDivElement | null>(null)
const chatMessagesWrapperRef = ref<HTMLDivElement | null>(null)
const placeholder = ref(t('reply_list.reply.content_placeholder'))

// 获取用户信息
const authStore = useAuthStoreWithOut()
const { member } = storeToRefs(authStore)

// 处理可编辑div的输入事件
const handleContentEditableInput = (event: Event) => {
  if (contentEditableRef.value) {
    // 获取contentEditable的文本内容
    const text = contentEditableRef.value.innerText || ''

    // 更新content变量
    content.value = text

    // 检查是否超过最大长度
    if (text.length > maxContentLength.value) {
      // 如果超过最大长度，截断文本
      const selection = window.getSelection()
      const range = document.createRange()

      contentEditableRef.value.innerText = text.slice(0, maxContentLength.value)
      content.value = text.slice(0, maxContentLength.value)

      // 恢复光标位置到末尾
      if (selection) {
        range.selectNodeContents(contentEditableRef.value)
        range.collapse(false) // 将范围折叠到末尾
        selection.removeAllRanges()
        selection.addRange(range)
      }
    }

    // 若已被全部删除，清理内部遗留的 <br> 或空节点，确保 :empty 触发占位符
    if (text.trim() === '') {
      contentEditableRef.value.innerHTML = ''
      content.value = ''
    }
  }
}

// 处理粘贴事件，只保留纯文本
const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()

  // 获取纯文本
  const text = event.clipboardData?.getData('text/plain') || ''

  // 插入纯文本
  document.execCommand('insertText', false, text)
}

// 焦点/失焦：占位符交给 CSS :empty:before
const handleContentFocus = (_event: FocusEvent) => {}
const handleContentBlur = (event: FocusEvent) => {
  const target = event.target as HTMLDivElement
  if (!target.innerText.trim()) {
    // 彻底清空，确保为空状态可匹配 :empty
    target.innerHTML = ''
    content.value = ''
  }
}

// 添加安全的打开文件选择器方法
const safeOpenFilePicker = () => {
  const inputEl = fileInputRef.value
  if (inputEl && typeof inputEl.click === 'function') {
    inputEl.click()
  }
  else {
    console.error('文件输入元素不正确:', inputEl)
    toast.add({
      title: t('reply_list.messages.file_input_error'), // 文件选择器无法打开
      description: t('reply_list.messages.reload_or_use_drag'), // 请尝试刷新页面或改用拖拽方式上传图片
      color: 'red'
    })
  }
}

// 保留原来的方法兼容现有代码，但内部调用新的安全方法
const openFilePicker = () => {
  safeOpenFilePicker()
}

// 添加文本截断辅助函数
const truncateText = (text: string, maxLength: number): string => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const currentPage = ref<number>(1) // 当前页面
const pageCount = ref<number>(10) // 每页显示的条数
const totalCount = ref<number>(0) // 总记录数
const paramData = reactive<any>({})
const router = useRouter()
const feedback_no = ref('')
const parent_id = ref('')
const content = ref('')
const platform = 'ztsl'
const reply_user_type = ref(0)
// 添加状态更新中状态变量
const processingStatus = ref(false)

// 反馈状态和类型映射
const statusMap = {
  0: t('feedback.list.status.pending'), // 待处理
  1: t('feedback.list.status.processing'), // 处理中
  2: t('feedback.list.status.completed') // 处理完成
}

const typeMap = {
  0: t('feedback.list.type.suggestion'), // 功能建议
  1: t('feedback.list.type.feedback') // 问题反馈
}

// 确认对话框状态变量
const isConfirmModalOpen = ref(false)
const confirmModalMessage = ref('')

// 反馈内容相关
const feedbackContent = ref('')
const feedbackData = ref<any[]>([])
const feedbackImages = ref<any[]>([])

// 回复表单相关变量
const maxContentLength = ref(500)
const submitting = ref(false)

// 图片上传相关变量
const maxLimit = ref(5)
const fileList = ref<File[]>([])
const filePreviewList = ref<{ file: File, preview: string }[]>([])
const dragActive = ref(false)
const uploadedImages = ref<string[]>([])

// 处理内容输入，确保不超过最大长度（兼容保留，实际逻辑移到handleContentEditableInput）
const handleContentInput = () => {
  if (content.value.length > maxContentLength.value) {
    content.value = content.value.slice(0, maxContentLength.value)
  }
}

// 处理回车键发送消息
const handleEnterKey = (event: KeyboardEvent) => {
  // Enter 发送消息（不包含 Shift 键）
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    if (content.value.trim() && !submitting.value) {
      submitReply(feedback_no.value, null)
    }
    return
  }

  // Shift + Enter 添加换行（默认行为，不需要处理）
}

// 根据剩余字符数改变计数器颜色
const characterCountClass = computed(() => {
  const remaining = maxContentLength.value - content.value.length
  if (remaining <= 50) return 'text-red-500 font-medium'
  if (remaining <= 100) return 'text-orange-500'
  return 'text-neutral-500'
})

// 获取路由参数中的 feedback_no 和 parent_id
const route = useRoute()
if (route.query.feedback_no) {
  feedback_no.value = route.query.feedback_no as string
}
if (route.query.parent_id) {
  parent_id.value = route.query.parent_id as string
}

// 修改处理原生文件输入变化的方法
const handleNativeFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (!target || !target.files) {
    console.error('文件输入事件缺少目标或文件')
    return
  }

  const files = Array.from(target.files) as File[]
  console.log('文件选择器选择的文件:', files.length)
  handleFileChange(files)

  // 重置input以允许选择同一文件
  target.value = ''
}

// 处理文件拖放
const handleFileDrop = (event: DragEvent) => {
  dragActive.value = false
  if (!event.dataTransfer?.files) return

  const files = Array.from(event.dataTransfer.files) as File[]
  handleFileChange(files)
}

// 处理拖拽离开事件
const handleDragLeave = (event: DragEvent) => {
  // 只有当鼠标真正离开容器时才隐藏拖拽状态
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    dragActive.value = false
  }
}

// 处理文件更改
const handleFileChange = (files: File[]) => {
  console.log('处理文件更改, 文件数量:', files.length)
  // 添加新文件到现有文件列表(合并处理)
  const combinedFiles = [...fileList.value]
  for (const file of files) {
    if (combinedFiles.length < maxLimit.value) {
      combinedFiles.push(file)
    }
  }

  // 限制文件数量
  if (combinedFiles.length > maxLimit.value) {
    toast.add({
      title: t('reply_list.messages.limit_exceeded'), // 超出最大上传限制
      description: t('reply_list.messages.max_images', { count: maxLimit.value }), // 最多只能上传{count}张图片
      color: 'red'
    })
    // 只保留最大数量的文件
    combinedFiles.splice(maxLimit.value)
  }

  // 验证文件类型和大小
  const validFiles = combinedFiles.filter((file) => {
    const isValidType = ['image/jpeg', 'image/png'].includes(file.type)
    const isValidSize = file.size / 1024 / 1024 < 3 // 3MB限制

    if (!isValidType) {
      toast.add({
        title: t('reply_list.messages.invalid_file_type'), // 文件格式错误
        description: t('reply_list.messages.only_jpg_png'), // 只能上传JPG或PNG格式图片
        color: 'red'
      })
    }

    if (!isValidSize) {
      toast.add({
        title: t('reply_list.messages.file_too_large'), // 文件过大
        description: t('reply_list.messages.max_file_size'), // 图片大小不能超过3MB
        color: 'red'
      })
    }

    return isValidType && isValidSize
  })

  fileList.value = validFiles

  // 生成预览
  filePreviewList.value = validFiles.map(file => ({
    file,
    preview: URL.createObjectURL(file)
  }))
}

// 移除图片
const removeImage = (index: number) => {
  filePreviewList.value.splice(index, 1)
  fileList.value.splice(index, 1)
}

// 图片查看器
const openInlineImageViewer = (index: number) => {
  if (!filePreviewList.value || filePreviewList.value.length === 0) {
    console.warn('没有图片可查看')
    return
  }

  // 将 filePreviewList 转换为图片数组格式
  const images = filePreviewList.value.map(file => ({
    image_url: file.preview
  }))

  // 调用现有的图片查看器逻辑
  openImageViewer(images, index)
}

// 获取反馈内容和相关图片
const fetchFeedbackContent = async () => {
  // 如果没有问题ID或问题编号，则返回
  if (!route.query.feedback_id && !route.query.feedback_no) {
    console.log('缺少必要的查询参数，无法获取反馈数据')
    return
  }

  try {
    const authStore = useAuthStoreWithOut()
    const headers: HeadersTokenType = {
      token: authStore.getToken.slice(7)
    }

    // 根据是否有问题ID选择不同的查询方式
    let queryParams: any

    if (route.query.feedback_id) {
      // 使用问题ID查询 - 完全独立的查询参数
      queryParams = {
        platform: 'ztsl',
        id: Number(route.query.feedback_id) // 显式转换为数字类型
      }
      console.log('使用问题ID查询，ID值:', queryParams.id, '类型:', typeof queryParams.id)
    }
    else {
      // 使用问题编号查询
      queryParams = {
        platform: 'ztsl',
        feedback_no: route.query.feedback_no,
        page: 1,
        limit: 100
      }
      console.log('使用问题编号查询:', queryParams)
    }

    // 调用API获取反馈内容
    const res = await getFeedbackListApi(headers, queryParams)
    console.log('获取反馈内容结果:', res)

    if (res.code === 200 && res.data && res.data.length > 0) {
      // 记录获取到的问题数据ID
      console.log('获取到的问题数据ID:', res.data[0].id)

      feedbackContent.value = res.data[0].feedback_content
      feedbackData.value = res.data

      // 获取该问题的图片
      const feedbackId = res.data[0].id
      if (feedbackId) {
        try {
          const imagesRes = await getFeedbackImages(headers, {
            page: 1,
            limit: 100
          })

          if (imagesRes.code === 200 && imagesRes.data) {
            // 过滤出与当前问题相关的图片
            feedbackImages.value = imagesRes.data.filter(image => image.feedback_id === feedbackId)
            console.log(`找到与问题ID ${feedbackId} 相关的图片:`, feedbackImages.value.length, '张')
          }
        }
        catch (imgError) {
          console.error('获取问题图片失败:', imgError)
          feedbackImages.value = []
        }
      }
    }
    else {
      console.warn('API返回成功但没有找到匹配的问题数据')
      feedbackContent.value = ''
      feedbackData.value = []
      feedbackImages.value = []
    }
  }
  catch (error) {
    console.error('获取反馈内容失败:', error)
    feedbackContent.value = ''
    feedbackData.value = []
    feedbackImages.value = []
  }
}

// 处理完成状态更新函数
// 处理完成状态更新函数
const handleCompleteStatus = async () => {
  if (!feedbackData.value || feedbackData.value.length === 0) {
    toast.add({
      title: t('reply_list.messages.missing_data'), // 缺少问题数据
      description: t('reply_list.status.update_failed_message'), // 无法更新反馈状态
      color: 'red'
    })
    return
  }

  // 无论是否有回复信息，都显示相同的确认消息
  confirmModalMessage.value = t('reply_list.status.confirm_complete') // 如果您反馈的问题已解决，可以点击「确认」以关闭该问题。问题关闭后可能无法再收到回复，但您仍可以随时创建新的反馈。

  // 无论是否有回复信息，都显示确认对话框
  isConfirmModalOpen.value = true
}

// 新增处理完成状态更新的实际执行函数
const updateFeedbackStatusToComplete = async () => {
  try {
    processingStatus.value = true
    const authStore = useAuthStoreWithOut()
    const headers: HeadersTokenType = {
      token: authStore.getToken.slice(7)
    }

    const response = await updateFeedbackStatusBatch(headers, {
      id: feedbackData.value[0].id,
      feedback_status: '2' // 设置为"处理完成"状态
    })

    if (response.code === 200) {
      toast.add({
        title: t('reply_list.status.update_success'), // 更新成功
        description: t('reply_list.status.update_success_message'), // 问题状态已更新为处理完成
        color: 'green'
      })

      // 更新本地状态
      feedbackData.value[0].feedback_status = '2'

      // 重新获取数据，确保所有相关UI都更新
      await fetchFeedbackContent()
      await handleQuery()
    }
    else {
      throw new Error(response.message || t('reply_list.status.update_failed')) // 更新失败
    }
  }
  catch (error) {
    console.error('更新问题状态失败:', error)
    toast.add({
      title: t('reply_list.status.update_failed'), // 更新失败
      description: error.message || t('reply_list.status.update_failed_message'), // 无法更新反馈状态
      color: 'red'
    })
  }
  finally {
    processingStatus.value = false
  }
}

// 确认完结问题
const confirmComplete = async () => {
  isConfirmModalOpen.value = false // 关闭确认对话框
  // 执行完结操作
  await updateFeedbackStatusToComplete()
}

const changePage = (page: number) => {
  currentPage.value = page
  handleQuery()
}

// 确保URL包含https://前缀
const ensureHttps = (url) => {
  if (!url) return ''
  // 添加对 blob: 和 data: 开头的URL的支持，避免修改这些特殊格式
  if (url.startsWith('blob:') || url.startsWith('data:') || url.startsWith('https://') || url.startsWith('http://')) {
    return url
  }
  return `https://${url}`
}

// 图片查看器相关
const isImageViewerOpen = ref(false)
const viewerImages = ref<any[]>([])
const currentImageIndex = ref(0)
const currentImageUrl = computed(() => {
  if (viewerImages.value.length === 0) return ''
  return ensureHttps(viewerImages.value[currentImageIndex.value]?.image_url || '')
})

const openImageViewer = (images: any[], index: number) => {
  if (!images || images.length === 0) {
    console.warn('没有图片可查看')
    return
  }
  viewerImages.value = images
  currentImageIndex.value = index
  isImageViewerOpen.value = true
}

// 打开问题图片查看器
const openFeedbackImageViewer = (images: any[], index: number) => {
  openImageViewer(images, index)
}

const changeImage = (direction: number) => {
  const newIndex = currentImageIndex.value + direction
  if (newIndex >= 0 && newIndex < viewerImages.value.length) {
    currentImageIndex.value = newIndex
  }
}

// 上传图片
const uploadImages = async (replyId: number): Promise<string[]> => {
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7)
  }

  const uploadPromises = fileList.value.map(async (file) => {
    try {
      const response = await uploadFeedbackImage(headers, file, null, replyId)
      if (response.code === 200) {
        return response.data.image_url
      }
      else {
        throw new Error(response.message || t('reply_list.messages.image_upload_failed')) // 图片上传失败
      }
    }
    catch (error) {
      console.error('上传图片失败:', error)
      throw error
    }
  })

  return Promise.all(uploadPromises)
}

// 提交回复
const submitReply = async (feedbackNo: string, parentId: number | null = null) => {
  if (!content.value || content.value.length < 1) {
    toast.add({
      title: t('reply_list.messages.content_required') // 请输入回复内容
    })
    return
  }

  submitting.value = true
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7)
  }

  try {
    const replyRes = await createReply(headers, {
      content: content.value, // 问题内容
      platform: platform, // 平台
      feedback_no: feedbackNo || feedback_no.value, // 使用传入的问题编号
      reply_user_type: reply_user_type.value, // 设置为用户
      parent_id: parentId ? String(parentId) : null // 如果有parentId则转换为字符串
    })

    if (replyRes.code === 200) {
      // 回复创建成功，获取回复ID并上传图片
      const replyId = replyRes.data.id

      // 如果有图片需要上传
      if (fileList.value.length > 0) {
        try {
          const imageUrls = await uploadImages(replyId)
          uploadedImages.value = imageUrls
        }
        catch (error) {
          toast.add({
            title: t('reply_list.messages.image_upload_failed'), // 图片上传失败
            description: t('reply_list.messages.partial_success'), // 回复已提交，但图片上传失败
            color: 'yellow'
          })
        }
      }

      toast.add({
        title: t('reply_list.messages.success'), // 成功
        description: t('reply_list.messages.submit_success'), // 您的回复已成功提交！
        color: 'green'
      })

      // 重置表单
      content.value = ''
      fileList.value = []
      filePreviewList.value = []

      // 清空可编辑div
      if (contentEditableRef.value) {
        contentEditableRef.value.innerText = ''
      }

      // 刷新回复列表
      handleQuery()
    }
    else {
      toast.add({
        title: replyRes.message || t('reply_list.messages.submit_failed') // 提交失败
      })
    }
  }
  catch (error: any) {
    toast.add({
      title: t('reply_list.messages.submit_failed'), // 提交失败
      description: error.message || t('reply_list.messages.unknown_error'), // 未知错误
      color: 'red'
    })
  }
  finally {
    submitting.value = false
  }
}

// 添加到现有变量区域
const loadingMore = ref(false)

// 修改handleQuery方法，使其支持增量加载
const handleQuery = async (isLoadMore = false) => {
  const authStore = useAuthStoreWithOut()
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }

  paramData.page = currentPage.value
  paramData.limit = pageCount.value
  paramData.platform = 'ztsl'
  paramData.feedback_no = route.query.feedback_no

  try {
    // 获取回复列表
    const res = await getReplyByFeedbackNoApi(headers, paramData)

    if (res.code === 200 && res.data) {
      // 确保 res.data 是一个数组
      const replyData = Array.isArray(res.data) ? res.data : [res.data]

      // 处理回复类型
      const processedData = replyData.map(item => ({
        ...item,
        reply_user_type: item.reply_user_type === 1 ? t('reply_list.reply.manager') : t('reply_list.reply.user'),
        parent_id: item.parent_id ? String(item.parent_id) : null, // 确保 parent_id 为字符串
        all_images: [] // 初始化图片数组
      }))

      // 获取所有回复ID
      const replyIds = processedData.map(item => item.id)

      if (replyIds.length > 0) {
        // 获取这些回复相关的图片
        const imagesRes = await getFeedbackImages(headers, {
          page: 1,
          limit: 100 // 获取足够多的图片以覆盖所有回复
        })

        if (imagesRes.code === 200 && imagesRes.data) {
          // 创建图片映射
          const imageMap = {}
          imagesRes.data.forEach((image) => {
            // 检查feedback_reply_id匹配
            if (image.feedback_reply_id) {
              if (!imageMap[image.feedback_reply_id]) {
                imageMap[image.feedback_reply_id] = []
              }
              imageMap[image.feedback_reply_id].push(image)
            }
          })

          // 合并数据
          processedData.forEach((item) => {
            item.all_images = imageMap[item.id] || []
          })
        }
      }

      // 根据是否为加载更多模式来决定如何设置数据
      if (isLoadMore) {
        // 加载更多模式：合并数据
        replyDataList.value = [...replyDataList.value, ...processedData]
      }
      else {
        // 常规查询模式：替换数据
        replyDataList.value = processedData
        // 如果不是加载更多模式，延迟滚动到底部以确保DOM更新完成
        nextTick(() => {
          scrollToBottom()
        })
      }

      totalCount.value = res.count || 0

      // DOM 更新后，尝试自动继续加载（内容不足或已在底部）
      nextTick(() => {
        setTimeout(() => {
          tryAutoLoadMore()
        }, 0)
      })
    }
  }
  catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 添加滚动加载所需的引用
const loadMoreTriggerRef = ref<HTMLDivElement | null>(null)
let observer: IntersectionObserver | null = null

// 添加滚动到底部的方法
const scrollToBottom = () => {
  if (chatMessagesWrapperRef.value) {
    chatMessagesWrapperRef.value.scrollTop = chatMessagesWrapperRef.value.scrollHeight
  }
}

// 在内容不足（不可滚动）或已在底部时尝试继续加载
const tryAutoLoadMore = () => {
  if (loadingMore.value) return
  if (replyDataList.value.length >= totalCount.value) return
  const wrapper = chatMessagesWrapperRef.value
  if (!wrapper) return
  const threshold = 30
  const atBottom = wrapper.scrollHeight - wrapper.scrollTop - wrapper.clientHeight <= threshold
  const notScrollable = wrapper.scrollHeight <= wrapper.clientHeight + threshold
  if (atBottom || notScrollable) {
    loadMoreData()
  }
}

// 修改加载更多的方法，使其支持自动滚动加载
const loadMoreData = async () => {
  // 避免重复加载或已加载全部数据
  if (loadingMore.value || replyDataList.value.length >= totalCount.value) return

  loadingMore.value = true
  currentPage.value++

  await handleQuery(true) // 传入true表示这是加载更多操作

  loadingMore.value = false
}

// 更新onMounted钩子
onMounted(() => {
  // 同时检查 feedback_id 和 feedback_no 参数
  if (route.query.feedback_id || route.query.feedback_no) {
    console.log('页面加载，路由参数:', {
      feedback_id: route.query.feedback_id,
      feedback_no: route.query.feedback_no
    })
    fetchFeedbackContent() // 优先获取反馈内容
    handleQuery()
  }
  else {
    console.warn(t('feedback.list.errors.missing_feedback_no')) // 缺少必要的反馈编号信息
  }

  // 初始化contentEditable
  if (contentEditableRef.value && content.value) {
    // 若已有内容（例如从草稿或其他来源恢复），填充它
    contentEditableRef.value.innerText = content.value
  }

  // 创建并注册 IntersectionObserver，实现滚动到底部自动加载
  if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
    const rootEl = chatMessagesWrapperRef.value || null
    observer = new IntersectionObserver((entries) => {
      const entry = entries[0]
      if (!entry || !entry.isIntersecting) return
      // 触底时尝试加载更多
      if (!loadingMore.value && replyDataList.value.length < totalCount.value) {
        loadMoreData()
      }
    }, {
      root: rootEl,
      rootMargin: '0px 0px 200px 0px',
      threshold: 0.01
    })

    if (loadMoreTriggerRef.value) {
      observer.observe(loadMoreTriggerRef.value)
    }
  }
})

onBeforeUnmount(() => {
  if (observer) {
    observer.disconnect()
    observer = null
  }
})

// 反馈回复列表
const replyDataList = ref<any[]>([])

// 布尔值转换文字
const formattedReplyDataList = computed(() => {
  return replyDataList.value
})

// 为ChatMessages组件格式化消息数据
const chatMessages = computed(() => {
  // 创建包含反馈内容作为第一条消息的数组
  const messagesArray = []

  // 如果存在反馈内容，将其添加为第一条消息（用户发送）
  if (feedbackContent.value) {
    messagesArray.push({
      id: 'feedback-content',
      role: 'user' as 'user' | 'assistant',
      content: feedbackContent.value,
      timestamp: feedbackData.value && feedbackData.value.length > 0 ? feedbackData.value[0].create_datetime : '',
      images: []
    })

    // 如果反馈内容有图片，为每张图片添加单独的消息
    if (feedbackImages.value && feedbackImages.value.length > 0) {
      feedbackImages.value.forEach((image, index) => {
        messagesArray.push({
          id: `feedback-image-${index}`,
          role: 'user' as 'user' | 'assistant',
          content: '',
          timestamp: '',
          images: [image],
          isImageMessage: true
        })
      })
    }
  }

  // 添加所有回复消息
  replyDataList.value.forEach((reply) => {
    // 确定角色，售后经理为assistant，用户为user
    const role = reply.reply_user_type === t('reply_list.reply.manager') ? 'assistant' : 'user'

    // 添加文本消息
    messagesArray.push({
      id: reply.id.toString(),
      role: role as 'user' | 'assistant',
      content: reply.content,
      timestamp: reply.create_datetime,
      images: []
    })

    // 如果有图片，为每张图片添加单独的消息
    if (reply.all_images && reply.all_images.length > 0) {
      reply.all_images.forEach((image, index) => {
        messagesArray.push({
          id: `${reply.id}-image-${index}`,
          role: role as 'user' | 'assistant',
          content: '',
          timestamp: '',
          images: [image],
          isImageMessage: true
        })
      })
    }
  })

  return messagesArray
})
</script>

<style scoped>
.image-viewer img {
  object-fit: contain;
}

/* 移除聊天消息头像的背景色 */
:deep(.bg-elevated) {
  background-color: transparent !important;
}

/* 聊天消息区域滚动样式 */
.chat-scroll-area {
  scrollbar-width: thin;
}

.chat-scroll-area::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll-area::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scroll-area::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.chat-scroll-area::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* 确保聊天容器内的滚动 */
.chat-container {
  overflow: hidden;
}

.chat-messages-wrapper {
  height: 500px;
  max-height: 500px;
  overflow-y: auto;
  scroll-behavior: smooth;
}

/* 可编辑回复区域占位符与方向控制 */
.reply-editor {
  direction: ltr;
  text-align: left;
  word-break: break-word;
  white-space: pre-wrap;
  outline: none;
}
.reply-editor:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
}
.reply-editor:focus:empty:before {
  content: '';
}
.dark .reply-editor:empty:before {
  color: #6b7280;
}
</style>
