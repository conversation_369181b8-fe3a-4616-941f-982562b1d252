<template>
  <div>
    <UContainer>
      <UPageHero align="center">
        <template #title>
          <!-- 图标来源： https://uxwing.com/midjourney-color-icon/ -->
          <img src="@/assets/images/logos/midjourney-color-icon.svg" class="mr-1 inline-flex h-10 w-10 align-text-top text-neutral-900 lg:h-16 lg:w-16 dark:bg-gray-100 dark:text-neutral-100" />
          用精挑翻译玩转 AI 绘画
        </template>
        <template #description>
          <span class="leading-6 font-bold tracking-normal text-neutral-600 lg:leading-8 dark:text-neutral-200">
            <NuxtLink to="https://www.midjourney.com/" target="_blank" class="font-medium text-(--ui-primary)">Midjourney</NuxtLink>
            是行业领先的 AI 绘画平台，同类平台大多只提供英文界面，且创作指令仅接受英文提示词。
          </span>
          <br />
          <span class="leading-6 font-bold tracking-normal text-neutral-600 lg:leading-8 dark:text-neutral-200">
            【精挑翻译】提供 AI 绘画全场景翻译解决方案，助力创作者突破语言壁垒，零障碍探索智能艺术创作，释放无限创意潜能。
          </span>
        </template>
      </UPageHero>
    </UContainer>

    <UPageSection
      v-for="(section, index) in sections"
      :key="index"
      :description="section.description"
      align="center"
      :features="section.features"
    >
      <!-- NuxtUI3 版本迁移 不要使用（PageSection）， 请使用 -->
      <template #title>
        <span v-html="section.title" />
      </template>
      <template #description>
        <span v-if="section.description" v-html="section.description" />
        <br />
        <span>
          马上安装精挑翻译浏览器插件，即刻体验
          <NuxtLink to="https://www.midjourney.com/imagine" target="_blank" class="font-medium text-(--ui-primary)">Midjourney</NuxtLink>
        </span>
      </template>
      <!-- 双语对照网页翻译 -->
      <UCarousel
        v-if="index === 0"
        v-slot="{ item, index }"
        :items="input_translate_items"
        :ui="carouselUI"
        :prev-button="{ color: 'gray' }"
        :next-button="{ color: 'gray' }"
        class="video_container overflow-hidden rounded-lg"
        indicators
        arrows
      >
        <div class="mx-auto text-center">
          <video
            v-if="item.media_type === 'video'"
            :src="item.media_url"
            muted
            autoplay
            loop
            playsinline
            class="w-full"
            style="border: 2px solid rgb(var(--color-gray-400)); border-radius: 8px"
          />
          <img :src="item.media_url" class="w-full" draggable="false" />
          <p class="mt-4 text-lg font-semibold">
            {{ item.desc }}
          </p>
        </div>
      </UCarousel>
      <!-- 鼠标悬停翻译 -->
      <UCarousel
        v-else-if="index === 1"
        v-slot="{ item }"
        :items="hover_translate_images"
        :ui="carouselUI"
        class="video_container overflow-hidden rounded-lg"
        indicators
        arrows
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <!-- 文本翻译 -->
      <UCarousel
        v-else-if="index === 2"
        v-slot="{ item }"
        :items="text_translate_images"
        :ui="carouselUI"
        class="video_container overflow-hidden rounded-lg"
        indicators
        arrows
      >
        <img :src="item" class="w-full" draggable="false" />
      </UCarousel>
      <ImagePlaceholder v-else />
    </UPageSection>
  </div>
</template>

<script setup lang="ts">
import PricingPlan from './components/PricingPlan.vue'
import FAQ from './components/FAQ.vue'
import { getProductByPositionNoApi } from '@/api/product'
import type { GetProductType } from '@/api/types'

const { t, locale } = useI18n()

// 页面标题及描述，用于SEO优化
useSeoMeta({
  titleTemplate: '',
  title: '玩转 AI 绘画' + ' - ' + t('common.site_name'),
  ogTitle: '玩转 AI 绘画' + ' - ' + t('common.site_name'),
  description: t('common.description'),
  ogDescription: t('common.description')
})

defineOgImageComponent('Saas')

// const isYearly = ref(false)

/** 平台在售产品信息 */
const product = ref({})

onMounted(async () => {
  // 在onMounted中加载数据，可以避免服务器端渲染 (SSR) 和客户端渲染 (CSR) 之间的不一致
  const paramData: GetProductType = {
    platform: 'ztsl',
    position_no: 'ztsl-plus'
  }
  product.value = (await getProductByPositionNoApi(paramData)).data
})

/**
 * 输入翻译场景图片.
 */
const input_translate_items = [
  {
    media_type: 'video',
    media_url: 'https://assets.selecttranslate.com/web/videos/scenarios_ai_painting_midjourney_01.mp4',
    title: '输入翻译1',
    desc: '输入翻译1输入翻译场景，支持多种输入方式，如：文本、图片、语音、视频等，支持多种输出方式，如：文本、图片、语音、视频等。'
  },
  {
    media_type: 'image',
    media_url: 'https://assets.selecttranslate.com/web/images/home/<USER>',
    title: '输入翻译2',
    desc: '输入翻译2输入翻译场景，支持多种输入方式，如：文本'
  },
  {
    media_type: 'image',
    media_url: 'https://assets.selecttranslate.com/web/images/home/<USER>',
    title: '输入翻译3',
    desc: '输入翻译3输入翻译场景，支持多种输入方式，如：文本'
  }
]

/**
 * 对照翻译场景图片.
 */
const bilingual_translation_images = [
  'https://assets.selecttranslate.com/web/images/home/<USER>',
  'https://assets.selecttranslate.com/web/images/home/<USER>',
  'https://assets.selecttranslate.com/web/images/home/<USER>'
]

/**
 * 悬停翻译场景图片.
 */
const hover_translate_images = [
  'https://assets.selecttranslate.com/web/images/home/<USER>',
  'https://assets.selecttranslate.com/web/images/home/<USER>',
  'https://assets.selecttranslate.com/web/images/home/<USER>'
]

/**
 * 文本翻译场景图片.
 */
const text_translate_images = [
  'https://assets.selecttranslate.com/web/images/home/<USER>',
  'https://assets.selecttranslate.com/web/images/home/<USER>',
  'https://assets.selecttranslate.com/web/images/home/<USER>'
]

// AI 绘画的功能使用场景
const sections = [
  {
    title: '绘画提示词翻译', //
    description: '【<span class=\'text-orange-500 font-bold\'>输入翻译</span>】深入场景，让您直接用母语编写绘画提示词并一键翻译，实现母语自由，畅享 AI 绘画创作之旅！', //
    align: 'center',
    features: [
      {
        name: '让创作更高效', //
        description: '深入场景的输入翻译功能，摆脱传统翻译工具的复制、切换、粘贴等繁琐流程，让您的创作更高效。', //
        icon: 'i-wpf-cursor' // https://icon-sets.iconify.design/ix/mouse-select-filled/
      },
      {
        name: '让作品更优质', //
        description: '强大的输入翻译择优功能，能极大提升提示词翻译准确性，让创作效果更合意更优质。', //
        icon: 'i-wpf-cursor' // https://icon-sets.iconify.design/ix/mouse-select-filled/
      },
      {
        name: '让管理更便捷', //
        description: '实用的翻译历史记录管理功能，让您从容应对提示词频繁调整的需求，让创作管理更便捷。', //
        icon: 'i-fluent-keyboard-mouse-16-filled' // https://icon-sets.iconify.design/fluent/keyboard-mouse-16-filled/
      }
    ]
  },
  {
    title: '绘画教程文档翻译', // 鼠标悬停翻译
    description: '【<span class=\'text-orange-500 font-bold\'>双语对照网页翻译</span>】跨语言学习原来如此简单！', //
    align: 'center',
    features: [
      {
        name: t('home.mouse_hover_translation.feature1.name'), // 段落翻译
        description: t('home.mouse_hover_translation.feature1.description'), // 只翻译鼠标悬停段落的单段文字。
        icon: 'i-wpf-cursor' // https://icon-sets.iconify.design/ix/mouse-select-filled/
      },
      {
        name: t('home.mouse_hover_translation.feature2.name'), // 区域翻译
        description: t('home.mouse_hover_translation.feature2.description'), // 翻译鼠标悬停所选中网页区域下的所有段落，让悬停翻译更灵活。
        icon: 'i-vaadin-area-select' // https://icon-sets.iconify.design/vaadin/area-select/
      },
      {
        name: t('home.mouse_hover_translation.feature3.name'), // 自定义快捷键
        description: t('home.mouse_hover_translation.feature3.description'), // 可自定义悬停翻译的快捷键，满足您的个性化需求。
        icon: 'i-fluent-keyboard-mouse-16-filled' // https://icon-sets.iconify.design/fluent/keyboard-mouse-16-filled/
      }
    ]
  },
  {
    title: t('home.text_translation.title'), // 文本翻译
    description: t('home.text_translation.description'), // 打开插件面板，即可便捷地使用包含多种翻译引擎的文本翻译功能。从此再也无需在多个独立翻译平台之间来回切换，只用精挑翻译就够了。
    align: 'center',
    features: [
      {
        name: t('home.text_translation.feature1.name'), // 多引擎切换
        description: t('home.text_translation.feature1.description'), // 聚合多个翻译平台的引擎，供您随心切换。
        icon: 'i-mingcute-switch-line'
      },
      {
        name: t('home.text_translation.feature2.name'), // 实时翻译
        description: t('home.text_translation.feature2.description'), // 切换翻译引擎后，实时更新翻译结果。
        icon: 'i-heroicons-chart-bar'
      },
      {
        name: t('home.text_translation.feature3.name'), // 语音朗读
        description: t('home.text_translation.feature3.description'), // 原文和译文内容都支持语音朗读，帮助您更好地理解和学习。
        icon: 'i-heroicons-speaker-wave'
      }
    ]
  }
]

// 定义 UCarousel 的 UI 配置类型
interface CarouselUI {
  item: string
  indicators?: {
    wrapper: string
    base: string
    active: string
    inactive: string
  }
}

// 定义 UCarousel 的 UI 配置
const carouselUI: CarouselUI = {
  item: 'basis-full',
  indicators: {
    wrapper: 'absolute flex items-center justify-center gap-3 bottom-14 inset-x-0',
    base: 'rounded-full h-4 w-4',
    active: 'bg-primary-500 dark:bg-primary-400',
    inactive: 'bg-gray-300 dark:bg-gray-800'
  }
}
</script>

<style lang="css" scoped>
/* .trans_engine {
  @apply inline-flex w-32 lg:w-40 align-bottom;
}
.trans_engine_img {
  @apply w-4 h-4 lg:w-6 lg:h-6 flex-shrink-0;
}
.trans_engine_text {
  @apply ml-1 text-zinc-500 leading-4 lg:leading-6;
} */
</style>
