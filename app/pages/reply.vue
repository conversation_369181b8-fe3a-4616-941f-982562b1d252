<template>
  <div class="mx-auto mt-4 mb-32 max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="text-center">
      <!-- <div class="font-bold text-3xl">
        {{ '问题反馈' }}
      </div> -->
    </div>

    <UCard class="mt-6 pb-16">
      <div class="text-3xl font-bold">
        {{ '反馈回复' }}
      </div>

      <div class="mt-11">
        <div class="mb-2 flex w-full justify-between">
          <label for="content" class="font-semibold">回复内容</label>
          <span class="text-xs" :class="characterCountClass">{{ content.length }}/{{ maxContentLength }}</span>
        </div>
        <UTextarea
          v-model="content"
          size="lg"
          :placeholder="'请输入您的回复内容'"
          style="height: 150px"
          :maxlength="maxContentLength"
          @input="handleContentInput"
        />
      </div>

      <!-- 新增图片上传区域 -->
      <div class="mt-11">
        <div class="mb-2 flex w-full justify-between">
          <label for="images" class="font-semibold">上传图片</label>
          <span class="text-xs font-light text-neutral-500">最大数量限制：{{ fileList.length }}/{{ maxLimit }}</span>
        </div>
        <div class="resource-image-uploader">
          <!-- 隐藏的文件输入 -->
          <input
            ref="fileInput"
            type="file"
            multiple
            accept="image/jpeg,image/png"
            style="display: none"
            @change="handleNativeFileChange"
          />

          <!-- 拖放区域 -->
          <div
            class="mt-4 rounded-lg border-2 border-dashed border-gray-300 p-6 text-center"
            :class="{ 'border-primary-500 bg-gray-50': dragActive }"
            @dragover.prevent
            @dragenter.prevent="dragActive = true"
            @dragleave.prevent="dragActive = false"
            @drop.prevent="handleFileDrop"
          >
            <div class="py-4">
              <div v-if="dragActive">
                释放鼠标上传图片
              </div>
              <div v-else class="flex justify-center">
                <UButton
                  icon="i-heroicons-photo"
                  class="rounded-md"
                  size="xl"
                  color="neutral"
                  variant="subtle"
                  :disabled="fileList.length >= maxLimit"
                  @click="$refs.fileInput.click()"
                >
                  点击或拖拽到此处添加图片
                </UButton>
              </div>
            </div>
          </div>

          <!-- 图片预览区域 -->
          <div v-if="fileList.length > 0" class="mt-4 flex flex-wrap gap-4">
            <div v-for="(file, index) in filePreviewList" :key="index" class="relative">
              <img :src="file.preview" class="h-24 w-24 rounded border object-cover" @click="openImageViewer(index)" />
              <UButton
                icon="i-heroicons-x-mark"
                color="error"
                variant="soft"
                size="xs"
                class="absolute top-1 right-1"
                @click="removeImage(index)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="mt-11">
        <label for="feedback_no" class="block mb-2 font-semibold">问题编号</label>
        <UInput v-model="feedback_no" size="lg" :placeholder="'问题编号'" disabled />
      </div>

      <div class="mt-11">
        <label for="parent_id" class="block mb-2 font-semibold">回复编号</label>
        <UInput v-model="parent_id" size="lg" :placeholder="'回复编号'" disabled />
      </div> -->

      <div class="mt-20">
        <UButton color="sky" class="flex w-full items-center justify-center text-xl font-bold sm:float-right sm:w-36" @click="reply">
          {{ '提交' }}
        </UButton>
      </div>

      <!-- 图片查看器 -->
      <UModal v-model:open="imageViewerOpen" :ui="{ width: 'md:max-w-3xl' }">
        <img v-if="selectedImage" :src="selectedImage" class="h-auto w-full" />
        <template #footer>
          <div class="flex w-full justify-between">
            <UButton :disabled="currentImageIndex <= 0" @click="prevImage">
              上一张
            </UButton>
            <UButton :disabled="currentImageIndex >= filePreviewList.length - 1" @click="nextImage">
              下一张
            </UButton>
          </div>
        </template>
      </UModal>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import { createReply, uploadFeedbackImage } from '@/api/feedback'
import { useAuthStoreWithOut } from '@/store/modules/auth'
import type { HeadersTokenType } from '@/api/login/types'
import { useRoute } from 'vue-router'

// 输入内容的最大长度
const maxContentLength = ref(500)

// 处理内容输入，确保不超过最大长度
const handleContentInput = () => {
  if (content.value.length > maxContentLength.value) {
    content.value = content.value.slice(0, maxContentLength.value)
  }
}

// 根据剩余字符数改变计数器颜色
const characterCountClass = computed(() => {
  const remaining = maxContentLength.value - content.value.length
  if (remaining <= 50) return 'text-red-500 font-medium'
  if (remaining <= 100) return 'text-orange-500'
  return 'text-neutral-500'
})

const { t, locale } = useI18n()
const toast = useToast()
defineOgImageComponent('Saas')
const authStore = useAuthStoreWithOut()
const content = ref('')
const platform = 'ztsl'
const feedback_no = ref('')
const parent_id = ref('')
const reply_user_type = ref(0)

// 图片上传相关变量
const maxLimit = ref(3)
const fileList = ref<File[]>([])
const filePreviewList = ref<{ file: File, preview: string }[]>([])
const imageViewerOpen = ref(false)
const currentImageIndex = ref(0)
const selectedImage = ref('')
const uploadedImages = ref<string[]>([])
const fileInput = ref(null)
const dragActive = ref(false)

// 获取路由参数中的 feedback_no 和 parent_id
const route = useRoute()
if (route.query.feedback_no) {
  feedback_no.value = route.query.feedback_no as string
}
if (route.query.parent_id) {
  parent_id.value = route.query.parent_id as string
}

// 处理原生文件输入变化
const handleNativeFileChange = (event) => {
  const files = Array.from(event.target.files)
  console.log('原生文件输入:', files)
  handleFileChange(files)
  // 重置input以允许选择同一文件
  event.target.value = ''
}

// 处理文件拖放
const handleFileDrop = (event) => {
  dragActive.value = false
  const files = Array.from(event.dataTransfer.files)
  console.log('拖放文件:', files)
  handleFileChange(files)
}

// 处理文件更改
const handleFileChange = (files: File[]) => {
  console.log('handleFileChange:', files)

  // 添加新文件到现有文件列表(合并处理)
  const combinedFiles = [...fileList.value]
  for (const file of files) {
    if (combinedFiles.length < maxLimit.value) {
      combinedFiles.push(file)
    }
  }

  // 限制文件数量
  if (combinedFiles.length > maxLimit.value) {
    toast.add({
      title: '超出最大上传限制',
      description: `最多只能上传${maxLimit.value}张图片`,
      color: 'red'
    })
    // 只保留最大数量的文件
    combinedFiles.splice(maxLimit.value)
  }

  // 验证文件类型和大小
  const validFiles = combinedFiles.filter((file) => {
    const isValidType = ['image/jpeg', 'image/png'].includes(file.type)
    const isValidSize = file.size / 1024 / 1024 < 3 // 3MB限制

    if (!isValidType) {
      toast.add({
        title: '文件格式错误',
        description: '只能上传JPG或PNG格式图片',
        color: 'red'
      })
    }

    if (!isValidSize) {
      toast.add({
        title: '文件过大',
        description: '图片大小不能超过3MB',
        color: 'red'
      })
    }

    return isValidType && isValidSize
  })

  fileList.value = validFiles

  // 生成预览
  filePreviewList.value = validFiles.map(file => ({
    file,
    preview: URL.createObjectURL(file)
  }))
}

// 移除图片
const removeImage = (index: number) => {
  filePreviewList.value.splice(index, 1)
  fileList.value.splice(index, 1)
}

// 图片查看器
const openImageViewer = (index: number) => {
  currentImageIndex.value = index
  selectedImage.value = filePreviewList.value[index].preview
  imageViewerOpen.value = true
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value -= 1
    selectedImage.value = filePreviewList.value[currentImageIndex.value].preview
  }
}

const nextImage = () => {
  if (currentImageIndex.value < filePreviewList.value.length - 1) {
    currentImageIndex.value += 1
    selectedImage.value = filePreviewList.value[currentImageIndex.value].preview
  }
}

// 上传图片
const uploadImages = async (replyId: number): Promise<string[]> => {
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7)
  }

  const uploadPromises = fileList.value.map(async (file) => {
    try {
      const response = await uploadFeedbackImage(headers, file, null, replyId)
      if (response.code === 200) {
        return response.data.image_url
      }
      else {
        throw new Error(response.message || '上传失败')
      }
    }
    catch (error) {
      console.error('上传图片失败:', error)
      throw error
    }
  })

  return Promise.all(uploadPromises)
}

const router = useRouter()

const reply = async () => {
  const headers: HeadersTokenType = {
    token: authStore.getToken.slice(7) // 去掉token前缀 "bearer "
  }
  if (!content.value || content.value.length < 1) {
    toast.add({
      title: '请输入反馈内容'
    })
  }
  else {
    try {
      const replyRes = await createReply(headers, {
        content: content.value, // 问题内容
        platform: platform, // 平台
        feedback_no: feedback_no.value, // 问题编号
        reply_user_type: reply_user_type.value, // 设置为用户
        parent_id: parent_id.value // 设置 parent_id
      })

      if (replyRes.code == 200) {
        // 回复创建成功，获取回复ID并上传图片
        const replyId = replyRes.data.id

        // 如果有图片需要上传
        if (fileList.value.length > 0) {
          try {
            const imageUrls = await uploadImages(replyId)
            uploadedImages.value = imageUrls

            toast.add({
              title: '成功',
              description: '您的回复和图片已成功提交！',
              color: 'green'
            })

            // 短暂延迟后跳转到回复列表页面
            setTimeout(() => {
              router.push({
                path: '/zhHans/reply_list',
                query: { feedback_no: feedback_no.value }
              })
            }, 500)
          }
          catch (error) {
            toast.add({
              title: '图片上传失败',
              description: '回复已提交，但图片上传失败',
              color: 'yellow'
            })

            // 尽管图片上传失败，回复已提交，仍然跳转
            setTimeout(() => {
              router.push({
                path: '/zhHans/reply_list',
                query: { feedback_no: feedback_no.value }
              })
            }, 500)
          }
        }
        else {
          toast.add({
            title: '成功',
            description: '您的回复已成功提交！',
            color: 'green'
          })

          // 短暂延迟后跳转到回复列表页面
          setTimeout(() => {
            router.push({
              path: '/zhHans/reply_list',
              query: { feedback_no: feedback_no.value }
            })
          }, 500)
        }

        // 重置表单
        content.value = '' // 重置 content
        fileList.value = [] // 重置文件列表
        filePreviewList.value = [] // 重置文件预览
        uploadedImages.value = [] // 重置上传的图片
      }
      else {
        toast.add({
          title: replyRes.message
        })
      }
    }
    catch (error: any) {
      toast.add({
        title: '提交失败', // '提交失败'
        description: error.message || '未知错误', // 显示错误信息
        color: 'red'
      })
    }
  }
}
</script>

<style scoped>
.resource-image-uploader {
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  transition: border-color 0.3s;
}

.resource-image-uploader:hover {
  border-color: #409eff;
}
</style>
