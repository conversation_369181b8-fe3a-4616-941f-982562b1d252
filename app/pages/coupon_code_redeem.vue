<template>
  <div class="mx-auto mt-16 mb-2 w-full px-2 pt-10 sm:w-2/3 md:w-2/4 lg:w-2/5">
    <div class="text-center">
      <div class="text-3xl font-bold">
        {{ t('coupon.code.redeem_main_title') }}
      </div>
      <div class="mt-6 mb-12 text-base">
        {{ t('coupon.code.redeem_abstract') }}
      </div>
    </div>

    <UCard class="relative mt-2 h-64 w-full">
      <UIcon name="i-material-symbols-redeem" class="h-8 align-bottom" />
      <div class="text-2xl font-bold">
        {{ t('coupon.code.use_title') }}
      </div>
      <div class="mt-3">
        <UInput v-model="code_value" size="lg" :placeholder="t('coupon.code.input_tips')" />
      </div>
      <div class="mt-2">
        <UButton color="sky" class="flex w-full items-center justify-center text-xl font-bold sm:float-right sm:w-36" @click="redeemCode">
          {{ t('coupon.code.redeem') }}
        </UButton>
      </div>
      <div class="absolute right-3 bottom-2">
        <UButton
          color="sky"
          variant="link"
          class="float-right"
          @click="toMemberCouponCodeList"
        >
          {{ t('coupon.code.my_code') }}
        </UButton>
      </div>
    </UCard>

    <!-- <div class="mt-24">
        <h3 class="font-bold text-2xl mb-3">常见问题</h3>
        <UAccordion multiple color="neutral" variant="outline" size="xl" :items="questionItems"/>
      </div> -->
  </div>
</template>

<script setup lang="ts">
import { couponCodeRedeem } from '@/api/product'
import { useAuthStoreWithOut } from '@/store/modules/auth'

const { t, locale } = useI18n()
const toast = useToast()

useSeoMeta({
  titleTemplate: '',
  title: t('coupon.code.use_title') + ' - ' + t('common.site_name'), // '使用兑换码 - 网站名称'
  ogTitle: t('coupon.code.use_title') + ' - ' + t('common.site_name'), // '使用兑换码 - 网站名称'
  description: t('common.description'),
  ogDescription: t('common.description')
})

defineOgImageComponent('Saas')

const code_value = ref('')
const redeemCode = () => {
  if (!code_value.value || code_value.value.length < 1) {
    toast.add({
      title: t('coupon.code.input_tips') // '请输入兑换码'
    })
  }
  else {
    couponCodeRedeem(code_value.value).then((res: any) => {
      if (res.code == 200) {
        toast.add({
          title: t('coupon.code.redeem_success') // '兑换成功'
        })
        code_value.value = ''
      }
    })
  }
}

const authStore = useAuthStoreWithOut()
const router = useRouter()
const toMemberCouponCodeList = () => {
  if (authStore.getToken && authStore.getToken !== '') {
    router.push({
      path: '/' + locale.value + '/coupon_code_list'
    })
  }
  else {
    // 打开未登录提示框
    router.push({
      path: '/' + locale.value + '/login'
    })
  }
}

// const questionItems = reactive([
//   {
//     label: '使用兑换码需要绑定支付方式吗？',
//     content: '不需要，兑换后直接成为 Plus 会员，无需绑定任何支付方式。'
//   },
//   {
//     label: '我已经是会员了，还可以使用兑换码吗？',
//     content: '可以，如果已经是会员的话，使用兑换码将会自动延长会员有效期。'
//   },
//   {
//     label: '使用兑换码之后，可以升级为连续包月会员吗？',
//     content: '可以，使用兑换码之后，可以在账户中进行升级。'
//   }
// ])
</script>

<style scoped></style>
