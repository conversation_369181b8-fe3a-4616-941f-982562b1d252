<template>
  <UCard class="mt-24 mb-24 w-10/12 max-w-sm bg-white/75 backdrop-blur lg:mt-32 lg:w-full dark:bg-white/5">
    <!-- 验证邮箱地址 -->
    <UAuthForm
      :fields="fields"
      :schema="schema"
      icon="i-heroicons-user-plus"
      :title="t('auth.signupconfirm.title')"
      :submit="{
        label: t('auth.signupconfirm.title'), // 注册
        size: 'lg',
        loading: loading
      }"
      @submit="onSubmit"
    >
      <template #description>
        <!-- 已向您的邮箱地址【xxxx】发送了验证码，请查收邮件！ -->
        {{ t('auth.common.messages.verify_code_email_send_tip_1') }} {{ $route.query.email }} {{ t('auth.common.messages.verify_code_email_send_tip_2') }}
      </template>

      <template #verify_code-hint>
        <!-- 修改按钮的类，使用条件绑定来切换颜色 -->
        <button
          type="button"
          :disabled="!canResend"
          class="text-sm font-medium"
          :class="canResend ? 'text-sky-500 hover:text-sky-700 dark:text-sky-400 dark:hover:text-sky-300' : 'text-gray-400 dark:text-gray-500'"
          @click="sendEmailVerifyCode($route.query.email as string)"
        >
          {{ canResend ? t('auth.common.resend') : `${countdown}秒后重试` }}
        </button>
      </template>
      <template #footer>
        <!-- 注册即表示您同意《使用条款》和《隐私政策》 -->
        {{ t('auth.common.confirm_agreement') }}《
        <NuxtLink :to="'/' + locale + '/terms/terms-of-service'" target="_blank" class="font-medium text-(--ui-primary)">{{ t('auth.common.terms_of_service') }}</NuxtLink>
        》 {{ t('auth.common.and') }}《
        <NuxtLink :to="'/' + locale + '/terms/privacy-policy'" target="_blank" class="font-medium text-(--ui-primary)">{{ t('auth.common.privacy_policy') }}</NuxtLink>
        》
      </template>
    </UAuthForm>
  </UCard>
</template>

<script setup lang="ts">
import * as z from 'zod' // 引入zod库（ https://zod.dev ）用于表单数据校验
import type { FormSubmitEvent } from '@nuxt/ui' // 引入表单提交事件类型
import { sendEmailVerifyCodeApi, verifyEmailApi } from '@/api/login'
import type { EmailVerifyCodeType, SendEmailVerifyCodeType } from '@/api/login/types'

// definePageMeta 定义页面的布局 (此代码要放最前面，不要然会加载默认布局)
definePageMeta({
  layout: 'authentication' // 使用 layouts/authentication.vue 布局
})

const { t, locale } = useI18n()

const route = useRoute()

useSeoMeta({
  // 验证邮箱地址
  titleTemplate: '',
  title: t('auth.signupconfirm.title') + ' - ' + t('common.site_name')
})

const loading = ref(false)

const fields = [
  {
    name: 'verify_code',
    label: t('auth.common.verify_code_label'), // 验证码
    type: 'text' as const,
    size: 'lg',
    placeholder: t('auth.common.verify_code_label') // 验证码
  }
]

// 表单数据校验 https://zod.dev
const schema = z.object({
  // 邮件验证码校验
  verify_code: z.string({
    required_error: t('auth.common.messages.verify_code_required') // 请输入邮件验证码
  })
})
// 表单数据校验模式
type Schema = z.output<typeof schema>

// 添加防抖计时器相关变量
const canResend = ref(true)
const countdown = ref(60)
let timer = null

async function onSubmit(formData: FormSubmitEvent<Schema>) {
  try {
    console.log('Submitted', formData)

    loading.value = true
    const queryEmail = `${route.query.email}` // 从路由参数中获取邮箱地址
    // 表单数据在formData.data中
    const formValues = formData.data || {}

    // 构建提交数据
    const verifyData: EmailVerifyCodeType = {
      email: queryEmail,
      verify_code: formValues.verify_code,
      platform: 'web', // 用户在注册时使用的入口平台（web、app等）
      method: 'email' // 邮箱
    }

    const res = await verifyEmailApi(verifyData)
    // alert('res：' + res)
    if (res.message === 'success') {
      // console.log('验证成功，Email:', res.data.email)
      alert(t('auth.common.messages.email_confirm_success')) // 邮箱地址验证成功，请登录
      // alert('res：' + res)
      navigateTo({ path: '/' + locale.value + '/login' }) // 跳转到登录页面，带上邮箱账号
    }
    else {
      // alert(res.message)
      console.log('verify email address failed!') // 验证码不正确
    }
  }
  finally {
    loading.value = false
  }
}

const toast = useToast()
async function sendEmailVerifyCode(email: string) {
  if (!canResend.value) return

  const formData = {} as SendEmailVerifyCodeType
  formData.email = email
  formData.platform = 'web'
  formData.interface_language = locale.value

  try {
    // 1. 先设置倒计时值和禁用按钮
    countdown.value = 60
    canResend.value = false

    const res = await sendEmailVerifyCodeApi(formData)

    if (res.message === 'success') {
      toast.add({
        title: t('auth.common.verify_code_has_been_send'),
        description: t('auth.common.messages.verify_code_email_send_tip_1') + email + t('auth.common.messages.verify_code_email_send_tip_2'),
        icon: 'i-heroicons-envelope'
      })

      if (timer) clearInterval(timer)

      timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          canResend.value = true
          clearInterval(timer)
          timer = null
        }
      }, 1000)
    }
  }
  catch (error) {
    console.error('发送验证码失败:', error)
    canResend.value = true
    countdown.value = 0
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 页面加载时自动开始倒计时
onMounted(() => {
  // 开始倒计时
  countdown.value = 60
  canResend.value = false

  if (timer) clearInterval(timer)
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      canResend.value = true
      clearInterval(timer)
      timer = null
    }
  }, 1000)
})
</script>
