<template>
  <UApp :toaster="{ position: 'top-center' }">
    <NuxtLoadingIndicator />

    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>

    <ErrorDialog />
  </UApp>
</template>

<script setup lang="ts">
import { useAuthStoreWithOut } from '@/store/modules/auth'
import ErrorDialog from '~/components/ErrorDialog.vue'
import { updateTranslateParams } from '@/composables/updateTranslateParams'
import { useExtensionStore } from '@/store/modules/extension'

const extensionStore = useExtensionStore()

const { getIsExtensionInstalled } = storeToRefs(extensionStore)

const colorMode = useColorMode()
const { t, locale } = useI18n()
const color = computed(() => (colorMode.value === 'dark' ? '#111827' : 'white'))
const authStore = useAuthStoreWithOut()

useHead({
  meta: [{ charset: 'utf-8' }, { name: 'viewport', content: 'width=device-width, initial-scale=1' }, { key: 'theme-color', name: 'theme-color', content: color }],
  link: [{ rel: 'icon', href: '/favicon.ico' }],
  htmlAttrs: {
    lang: locale.value === 'zhHans' ? 'zh-Hans' : locale.value === 'zhHant' ? 'zh-Hant' : 'en' // 页面语言随设置的语言变化而变化
  }
})

/**
 * 从插件获取界面语言
 */
const getChromeInterfaceLanguage = (): Promise<string> => {
  return new Promise(async (resolve) => {
    window.postMessage(
      {
        type: 'getChromeInterfaceLanguage'
      },
      window.location.origin
    )

    const messageHandler = async (event) => {
      if (event.data.type === 'SEND_WEB_INTERFACE_LANGUAGE') {
        const lang = event.data.interfaceLanguage || ''
        resolve(lang) // 返回插件的界面语言
      }
    }

    window.addEventListener('message', messageHandler)
  })
}

onMounted(async () => {
  // 初始化扩展状态
  await extensionStore.initExtensionState()

  // 检查扩展是否安装
  if (getIsExtensionInstalled.value) {
    // 扩展已安装，发送消息获取插件的界面语言
    const chromeInterfaceLanguage = await getChromeInterfaceLanguage()
    if (chromeInterfaceLanguage) {
      // 如果获取到插件的界面语言，则设置为当前语言
      authStore.interfaceLanguage = chromeInterfaceLanguage
    }
    else {
      // 如果没有获取到插件的界面语言，则设置为默认语言
      authStore.interfaceLanguage = locale.value
    }
  }
  else {
    // 存储页面语言
    authStore.interfaceLanguage = locale.value
  }

  updateTranslateParams()
})

const site_name = computed(() => {
  return t('common.site_name')
}) // 精挑翻译

useSeoMeta({
  titleTemplate: '%s - ' + site_name.value, // 精挑翻译
  // ogImage: 'https://saas-template.nuxt.dev/social-card.png', // 社交媒体标签 https://blog.csdn.net/qq_38865642/article/details/129066964
  // twitterImage: 'https://saas-template.nuxt.dev/social-card.png', // 社交媒体标签 https://blog.csdn.net/qq_38865642/article/details/129066964
  twitterCard: 'summary_large_image' // 社交媒体标签 https://blog.csdn.net/qq_38865642/article/details/129066964
})
</script>

<style>
html,
body,
#__nuxt {
  height: 100%;
}
</style>
