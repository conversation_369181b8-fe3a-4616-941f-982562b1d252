/**
 * 翻译类型
 */

export interface OriginalArray {
  /** 原文 */
  text: string
  /** 翻译结果 */
  statusNodeGlobalId: string
  /** 段落id */
  paragraphId: string
}

/**
 * 文本处理结果接口
 */
export interface TextProcessResult {
  paragraphs: ParagraphData[]
  // 所有文本连接在一起（包含换行符）
  combinedText: string
  // 是否已翻译
  isTranslated: boolean
}

/**
 * 段落数据接口
 */
export interface ParagraphData {
  items: OriginalArray[]
  paragraphId: string
}

export interface TranslationArray {
  /** 翻译结果 */
  text: string
  /** 翻译结果 */
  statusNodeGlobalId: string
}

/**
 * 下拉框选项数据
 */
// export interface AlternativeItem {
//   /** 翻译结果 */
//   text: string;
//   /** 翻译结果 */
//   statusNodeGlobalId: string;
//   /** 翻译结果 */
//   engine: string;
//   /** 排序索引 */
//   sortIndex: number;
// }

export interface AiSelectFinallyResultText {
  /** 翻译结果 */
  text: string
  /** 翻译结果的id */
  statusNodeGlobalId: string
  /** 翻译引擎 */
  engine: string
  /** 段落id */
  paragraphId: string
}

// 翻译模式类型 1. 传统翻译 2. 择优翻译 3. 对比翻译
export type TranslateMode = 'traditional' | 'aiSelect' | 'compare'

// 语言集
export interface Language {
  label: string // 翻译引擎名称
  value: string // 翻译引擎值
  en_name: string // 英文名称
  zing_code: string // 自定义语言代码
  lang_code: string // 语言代码
  original_name: string // 原始名称
  sort: number // 排序
  zht_name: string // 繁体中文名称
  zhs_name: string // 简体中文名称
}

// 择优翻译模型
export interface AiSelectModel {
  label: string // 翻译引擎名称
  value: string // 翻译引擎值
  modelVersion: string // 模型版本
  modelName: string // 模型名称
  serviceModeName: string // 服务模式名称 （custom 自定义API Key， free 免费 , plus 会员）
  serviceMode: string // 服务模式 （custom 自定义API Key， free 免费 , plus 会员）
  isAvailable?: boolean // 模型是否可用
}

// 传统翻译模型
export interface TraditionalEngines {
  label: string // 翻译引擎名称
  value: string // 翻译引擎值
}

// 输入翻译的存储
export interface State {
  enabledEnginesList: string[] // 启用的翻译引擎列表
  traditionalEngines: TraditionalEngines // 传统翻译引擎
  targetLanguage: string // 目标语言
  originalLanguage: string // 源语言
  isAiSelectAnalysis: boolean // 是否是择优翻译分析
  isSingleSentenceAiSelect: boolean // 是否是单句择优翻译
  isAutoTranslate: boolean // 是否是自动翻译
  aiSelectModel: AiSelectModel // 择优翻译模型
  aiSelectModelList: AiSelectModel[] // 择优翻译模型列表
  originalLanguageList: Language[] // 源语言列表
  targetLanguageList: Language[] // 目标语言列表
  isSettingsPanelCollapsed: boolean // 设置面板是否收起
  currentTranslateMode: TranslateMode // 当前翻译模式：传统翻译或择优翻译
}
