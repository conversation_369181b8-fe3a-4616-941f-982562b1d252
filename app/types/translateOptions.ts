/**
 * 翻译引擎配置
 */

export interface TranslationEngine {
  value: string
  label: string
  modelVersion: string // 模型版本
  modelName: string // 模型名称
  serviceMode: 'custom' | 'free' | 'plus' // 服务模式
  serviceModeName: string // 服务模式名称 （custom 自定义API Key， free 免费 , plus 会员）
  isAvailable: boolean // 是否可用
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  message: string
  statusNodeGlobalId: string
}

/**
 * 择优分析接口
 */
export interface RationaleInfo {
  rationale: string
  statusNodeGlobalId: string
}

/**
 * 择优评分接口
 */
export interface ScoreInfo {
  score: string
  statusNodeGlobalId: string
}

/**
 * 翻译结果接口
 */
export interface TranslationResult {
  text: string
  statusNodeGlobalId: string
  engine: string
  paragraphId?: string
}

/**
 * 反向翻译结果接口
 */
// export interface ReverseTransResult {
//   error: ErrorInfo[]; // 错误信息
//   result: {
//     text: string;
//     statusNodeGlobalId: string;
//     engine: string;
//   }[]; // 翻译结果
// }

/**
 * 排序索引接口
 */
export interface SortIndexInfo {
  sortIndex: number
  statusNodeGlobalId: string
}

/**
 * 翻译状态枚举
 * 1: 未翻译 2: 翻译中 3: 翻译完成 4: 翻译失败, 5. 择优中, 6. 空, 7. 等待择优
 */
export enum TranslationStatus {
  NotTranslated = 1,
  Translating = 2,
  Completed = 3,
  Failed = 4,
  Scoring = 5,
  Empty = 6,
  WaitingForOptimization = 7
}

/**
 * 择优翻译状态接口
 */
export interface AiSelectTranslationStatus {
  status: TranslationStatus // 翻译状态
  statusNodeGlobalId: string // 状态节点全局ID
}

/**
 * 翻译引擎状态接口
 */
export interface TranslationEngineState extends TranslationEngine {
  translationsResult: TranslationResult[] // 翻译结果
  // reverseTransResult: ReverseTransResult; // 反向翻译结果
  error: ErrorInfo[] // 错误信息
  score: ScoreInfo[] // 择优评分
  rationale: RationaleInfo[] // 择优分析
  status: TranslationStatus // 翻译状态
  sortIndexArray: SortIndexInfo[] // 排序索引数组
  selectedTransStatus?: AiSelectTranslationStatus[] // 择优翻译状态
}
