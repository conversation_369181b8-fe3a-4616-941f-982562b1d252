import type { TranslationEngine, ErrorInfo, RationaleInfo, ScoreInfo, SortIndexInfo, TranslationStatus } from './translateOptions'
import type { TranslateMode, AiSelectFinallyResultText } from './translate'

/**
 * TODO 择优翻译-历史记录状态类型
 */

/**
 * 翻译结果
 * 用于存储翻译结果
 */
export interface TranslationResult {
  text: string
  statusNodeGlobalId: string
  engine: string
}

// 多模型翻译项
export interface MultiModelTranslationOptions {
  label: string // 模型名称
  value: string // 模型值
  modelVersion: string // 模型版本
  modelName: string // 模型名称
  sortIndexArray?: SortIndexInfo[] // 排序索引数组
  rationale?: RationaleInfo[] // 择优分析
  score?: ScoreInfo[] // 择优评分
  translationsResult?: TranslationResult[] // 翻译结果
}

// 精挑翻译模型
export interface AiSelectModel {
  label?: string // 模型名称
  value?: string // 模型值
  img?: string // 模型图标
  modelVersion?: string // 模型版本
  serviceMode?: string // 服务模式
}

// 历史记录模型配置项
export interface HistoryModelOptions {
  label: string // 模型名称
  value: string // 模型值
  img?: string // 模型图标
  modelVersion?: string // 模型版本
  modelName?: string // 模型名称
  status?: TranslationStatus // 翻译状态
  sortIndexArray?: SortIndexInfo[] // 排序索引数组
  rationale?: RationaleInfo[] // 择优分析
  score?: ScoreInfo[] // 择优评分
  translationsResult?: TranslationResult[] // 翻译结果
  error?: ErrorInfo[] // 错误信息
}

// 择优设置
export interface AiSelectSetting {
  isSingleSentenceAiSelect: boolean // 是否单句精挑翻译
  isAiSelectAnalysis: boolean // 是否精挑分析
  aiSelectModel?: AiSelectModel // 精挑翻译模型
}

// 源文本dom
export interface InputDom {
  sourceDom: {
    content: string
  }[] // 原始文本节点
}

// 历史记录
export interface TranslationHistory {
  id?: number // 历史记录 id
  key: string // 历史记录 key
  sourceDOMStructure: string // 原始文本DOM结构
  sourceText: string // 原始文本
  inputDom?: InputDom // 原始文本节
  timestamp: number // 时间戳
  member_id?: string // 用户ID，用于区分不同用户的历史记录
  aiSelectSetting?: AiSelectSetting // 择优设置
  translateType?: TranslateMode // 翻译类型
  showModelList?: any[]
  targetLanguage?: string // 目标语言
  originalLanguage?: string // 源语言
  multiModelTranslationOptions?: MultiModelTranslationOptions[] // 多模型翻译项
  aiSelectTranslationResult?: AiSelectFinallyResultText[] // 精挑翻译结果
  userCustomTranslationResult?: AiSelectFinallyResultText[] // 用户自定义翻译结果
}

// PdfPlusFile历史记录
export interface TranslationPdfPlusFileHistory {
  id?: string // 历史记录 id
  member_id: string // 用户ID，用于区分不同用户的历史记录
  mmd: string // 原始文本
  fileName: string
}
