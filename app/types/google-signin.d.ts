// Google Sign-In 插件类型定义
export interface GoogleSignInResponse {
  code: string
  state?: string
  error?: string
}

export interface GoogleSignInPlugin {
  init(): Promise<any>
  renderButton(elementId: string, options: any): Promise<void>
  requestCode(options?: any): Promise<GoogleSignInResponse>
}

// 扩展 NuxtApp 类型
declare module '#app' {
  interface NuxtApp {
    $googleSignIn: GoogleSignInPlugin
  }
}

// 扩展全局 Window 接口
declare global {
  interface Window {
    google: any
  }
}

export {}
