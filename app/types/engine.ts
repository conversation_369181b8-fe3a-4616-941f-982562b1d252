/**
 * 翻译引擎类型定义
 * @typedef {Object} Engine
 * @property {string} value - 引擎唯一标识符
 * @property {string} label - 显示名称
 * @property {string} img - 图标路径
 */
export interface Engine {
  /** 引擎唯一标识符 */
  value: string
  /** 显示名称 */
  label: string
  /** 图标路径 */
  img: string
}

/**
 * 引擎支持状态类型
 * @typedef {Object} EngineSupport
 * @property {string} engineId - 引擎ID
 * @property {string[]} supportedLanguages - 支持的语言列表
 */
export type EngineSupport = {
  engineId: string
  supportedLanguages: string[]
}
