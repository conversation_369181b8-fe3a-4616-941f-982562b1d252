/**
 * 翻译进度
 */

export interface PageStatus {
  pageNumber: number // 页码
  status: 'completed' | 'in-progress' | 'failed' | 'not-started' | 'no-translation-needed' // 翻译状态
  progress: number // 翻译进度
  failedNodes: number // 失败节点数
}

/**
 * 文档翻译翻译进度监控（带每一个翻译进度的进度监控）
 * 比如：pdf 免费的就会使用这个
 */
export interface TranslationProgressType {
  overallProgress: number // 整体翻译进度
  pagesStatus: PageStatus[] // 页面翻译状态
  completedPages: number // 已完成页数
  totalPages: number // 总页数
  hasError: boolean // 是否翻译错误
}

/**
 * 文档翻译翻译进度监控（不带每一个翻译进度的进度监控）
 * 节点翻译状态接口
 * 整片文章监控使用
 */
export interface NodeTranslationProgress {
  // 基础统计信息
  totalNodes: number // 总节点数量
  completedNodes: number // 已完成翻译的节点数量
  inProgressNodes: number // 正在翻译的节点数量
  failedNodes: number // 翻译失败的节点数量
  notStartedNodes: number // 未开始翻译的节点数量

  // 进度信息
  overallProgress: number // 整体翻译进度百分比 (0-100)

  // 错误和状态信息
  hasError: boolean // 是否存在翻译错误
  errorRate: number // 错误率百分比 (0-100)
}
