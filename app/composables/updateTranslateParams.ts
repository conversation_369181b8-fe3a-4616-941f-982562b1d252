import { useDocumentTranslateStore } from '@/store/documentTranslate'

/**
 * 更新文档翻译参数，包含 pdf 免费、pdf plus
 */
export const updateTranslateParams = () => {
  const documentTranslateStore = useDocumentTranslateStore()
  document.addEventListener('updateWebTranslateParams', (event) => {
    const { translateModel, targetLanguageZingCode, updateType } = event.detail

    if (['webTranslateModel', 'webTranslateTargetLang'].includes(updateType)) {
      if (translateModel) {
        documentTranslateStore.updateTranslateEngine(translateModel)
      }

      if (targetLanguageZingCode) {
        documentTranslateStore.updateTargetLanguage(targetLanguageZingCode)
      }
    }
  })
}
