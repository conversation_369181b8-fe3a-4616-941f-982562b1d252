/**
 * 扩展程序翻译
 */

/**
 * 翻译类型
 * 如果翻译类型为 reTranslateFilePage，则表示重新翻译
 * 如果翻译类型为 translateFilePage，则表示翻译， 如果当前已经开启了翻译， 再次传入 translateFilePage 会取消当前的翻译状态
 * 如果翻译类型为 cancelCurrentPageTranslate，则表示取消当前页面翻译
 * 如果翻译类型为 translateSubtitleFile，则表示翻译字幕文件
 * 如果翻译类型为 reTranslateSubtitleFile，则表示重新翻译字幕文件
 * 如果翻译类型为 cancelTranslateSubtitleFile，则表示取消翻译字幕文件
 */
type TranslateType = 'reTranslateFilePage' | 'translateFilePage' | 'cancelCurrentPageTranslate'

/**
 * 插件翻译参数接口
 */
interface PluginTranslateParams {
  type: TranslateType // 翻译类型
  translationEngine: string // 翻译引擎
  targetLanguage: string // 目标语言
  transDisplayMode: TransDisplayMode // 译文显示
  totalPages?: number // 总页数
  documentType: DocumentType // 文档类型
}

/**
 * 译文显示
 */
type TransDisplayMode = 'bilingual' | 'fulltext' // 双语对照 | 全文翻译

/**
 * 文档类型
 */
type DocumentType = 'pdf' | 'pdf-plus' | 'docx' | 'epub' | 'html' | 'txt' | 'markdown' | 'subtitle' | 'miner-u'

/**
 * 使用插件翻译
 * 注意要使用 zing_code 的目标语言编码
 */
export const usePluginTranslate = () => {
  // 插件翻译- 单向的
  function usePluginWebDocumentTranslate(params: PluginTranslateParams) {
    const { type, translationEngine, targetLanguage, transDisplayMode, totalPages, documentType } = params
    const message = {
      type: type, // 翻译类型
      translationEngine: translationEngine, // 翻译引擎
      targetLanguage: targetLanguage, // 目标语言
      transDisplayMode: transDisplayMode, // 译文显示
      // !重要：当前页面url， 传这个 url 地址给插件, 插件拿到这个 url 这个传给 background.js ，根据这个 url 指定翻译的 tab
      // 如果不加上，用户执行翻译的时候有可能会翻译到其他网站的页面
      url: window.location.href,
      // !重要：当前 文档的总页数， 传给插件，插件根据这个总页数来计算翻译进度
      totalPages: totalPages,
      documentType: documentType
    }

    window.postMessage(message, window.location.origin)
  }

  // 发起翻译监控
  function initTranslationMonitor(totalPages?: number) {
    const message = {
      type: 'startMonitorDocTransStatus', // 开启监控 文档翻译的翻译状态
      totalPages: totalPages // 总页数
    }

    window.postMessage(message, window.location.origin)
  }

  return {
    usePluginWebDocumentTranslate,
    initTranslationMonitor
  }
}
