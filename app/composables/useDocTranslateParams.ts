// 文档翻译设置存储
import { useDocumentTranslateStore } from '@/store/documentTranslate'
import { translateModeIcon } from '@/utils/translateModeIcon' // 翻译引擎的图标icon
// 共享状态
import { useModelList, useLanguageList } from '@/composables/usePluginData'

// 发送消息给chrome端获取输入翻译数据
export const sendMessageToChromeExtension = async (localeLanguage: string, translationEngine: string) => {
  // 获取输入翻译数据
  const [languagesList, translationEngineList] = await Promise.all([useLanguageList(localeLanguage, translationEngine), useModelList(localeLanguage)])

  const documentTranslateStore = useDocumentTranslateStore()

  // 目标语言集 - 去掉数组中的第一个就是目标语言集
  documentTranslateStore.targetLanguageList = languagesList.slice(1)

  // 翻译引擎列表
  const enabledEnginesList = translationEngineList.map(item => ({
    label: item.label,
    value: item.value,
    img: translateModeIcon[item.value] || '',
    modelVersion: item.modelVersion,
    modelName: item.modelName,
    serviceMode: item.serviceMode,
    serviceModeName: item.serviceModeName
  }))

  documentTranslateStore.enabledEnginesList = enabledEnginesList

  // 翻译引擎
  if (!translationEngine || !documentTranslateStore.translateEngine) {
    documentTranslateStore.translateEngine = enabledEnginesList.find(item => item.value === 'microsoft')
  }
  else {
    documentTranslateStore.translateEngine = enabledEnginesList.find(item => item.value === translationEngine)
  }
}
