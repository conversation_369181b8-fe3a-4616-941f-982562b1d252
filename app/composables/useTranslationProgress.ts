/**
 * 文档翻译翻译进度监控
 * 包含所有的翻译进度统计
 * 如果传了 总页数就会按照每一页翻译进度进行监控
 * 如果没有传，如：txt 就只会监控整个文件的翻译进度
 */

import type { NodeTranslationProgress, TranslationProgressType } from '@/types/translationProgress'
import { useDocTranslationProgressStore } from '@/store/documentTranslate/translationProgress'

const { initTranslationMonitor } = usePluginTranslate()

/**
 * 连带着初始化翻译进度监控+ 监听翻译进度变化
 */
export const useTranslationProgress = () => {
  /**
   * 初始化翻译进度监控
   */
  const initTranslationProgressMonitor = (totalPages?: number) => {
    initTranslationMonitor(totalPages)

    // 监听翻译进度变化
    listenTranslationProgress()

    // 取消翻译监听
    cancelTranslationProgressMonitor()
  }

  /**
   * 监听翻译进度变化
   */
  const listenTranslationProgress = () => {
    const docTranslationProgressStore = useDocTranslationProgressStore()

    // 监听翻译状态
    window.addEventListener('message', (event) => {
      if (event.data.type === 'TRANSLATION_PROGRESS_UPDATE') {
        // 浅拷贝防止重复数据重复触发
        const newData = { ...event.data.data }

        let oldData = null

        if (newData.totalPages) {
          oldData = docTranslationProgressStore.translationProgress ? { ...docTranslationProgressStore.translationProgress } : {}
        }
        else {
          oldData = docTranslationProgressStore.nodeTranslationProgress ? { ...docTranslationProgressStore.nodeTranslationProgress } : {}
        }

        // 只有当数据真正发生变化时才更新
        if (JSON.stringify(newData) !== JSON.stringify(oldData)) {
          if (!newData.totalPages) {
            console.log('节点翻译进度', newData)

            docTranslationProgressStore.nodeTranslationProgress = newData as NodeTranslationProgress
          }
          else {
            console.log('页数翻译进度', newData)

            docTranslationProgressStore.translationProgress = newData as TranslationProgressType
          }
        }
      }
    })
  }

  /**
   * 取消翻译监听
   */
  const cancelTranslationProgressMonitor = () => {
    const docTranslationProgressStore = useDocTranslationProgressStore()

    window.addEventListener('message', (event) => {
      if (event.data.type === 'CANCEL_TRANSLATION_PROGRESS_MONITOR') {
        docTranslationProgressStore.translationProgress = null
        docTranslationProgressStore.nodeTranslationProgress = null
      }
    })
  }

  return {
    initTranslationProgressMonitor,
    listenTranslationProgress,
    cancelTranslationProgressMonitor
  }
}
