/**
 * 检查当前页面是否开启了翻译
 * @returns currentTabTransEnable
 */
export const useCheckCurrentTabTransEnable = () => {
  return new Promise((resolve) => {
    window.postMessage({ type: 'checkPageTranslationStatus' }, window.location.origin)

    window.addEventListener('message', (event) => {
      if (event.data.type === 'CHECK_PAGE_TRANSLATION_STATUS') {
        const { currentTabTransEnable } = event.data

        resolve(currentTabTransEnable)
      }
    })
  })
}
