import type { TranslationEngine } from '@/types/translateOptions'

/**
 * 获取插件数据
 */

// 语言集
type languageList = {
  value: string
  label: string
  zing_code: string
  lang_code: string
  sort: number
  original_name: string
  en_name: string
  zhs_name: string
  zht_name: string
}

type aiSelectModelList = {
  aiSelectModel: string // 择优模型
  aiSelectModelList: TranslationEngine[] // 择优模型列表
}

/**
 * 模型列表
 */
export const useModelList = (locale) => {
  return new Promise<TranslationEngine[]>((resolve) => {
    window.postMessage(
      {
        type: 'getModelList',
        interfaceLanguage: locale
      },
      window.location.origin
    )

    const messageHandler = (event) => {
      if (event.data.type === 'GET_MODEL_LIST') {
        window.removeEventListener('message', messageHandler) // 只监听一次
        resolve(event.data.data.modelList)
      }
    }

    window.addEventListener('message', messageHandler)
  })
}

/**
 * 获取语言集数据
 */
export const useLanguageList = (locale: string, translationEngine?: string) => {
  return new Promise<languageList[]>((resolve) => {
    window.postMessage(
      {
        type: 'getLanguageList',
        interfaceLanguage: locale,
        translationEngine: translationEngine
      },
      window.location.origin
    )

    const messageHandler = (event) => {
      if (event.data.type === 'GET_LANGUAGE_LIST') {
        window.removeEventListener('message', messageHandler) // 只监听一次
        resolve(event.data.data.languageList)
      }
    }

    window.addEventListener('message', messageHandler)
  })
}

// 获取当前插件目标语言
export const useCurrentTargetLanguage = () => {
  return new Promise<string>((resolve) => {
    window.postMessage(
      {
        type: 'getCurrentTargetLanguage'
      },
      window.location.origin
    )

    const messageHandler = (event) => {
      if (event.data.type === 'GET_CURRENT_TARGET_LANGUAGE') {
        window.removeEventListener('message', messageHandler) // 只监听一次
        resolve(event.data.data.targetLanguage)
      }
    }

    window.addEventListener('message', messageHandler)
  })
}

/**
 * 获取择优模型列表
 */
export const useAiSelectModelList = (locale) => {
  return new Promise<aiSelectModelList>((resolve) => {
    window.postMessage(
      {
        type: 'getAiSelectModelList',
        interfaceLanguage: locale
      },
      window.location.origin
    )

    const messageHandler = (event) => {
      if (event.data.type === 'GET_AI_SELECT_MODEL_LIST') {
        window.removeEventListener('message', messageHandler) // 只监听一次
        resolve(event.data.data)
      }
    }

    window.addEventListener('message', messageHandler)
  })
}

/**
 * 获取MinerU配置
 */
export const useMinerUConfig = () => {
  return new Promise<any>((resolve) => {
    window.postMessage(
      {
        type: 'getMinerUConfig'
      },
      window.location.origin
    )

    const messageHandler = (event) => {
      if (event.data.type === 'GET_MINER_U_CONFIG') {
        window.removeEventListener('message', messageHandler)
        resolve(event.data.data)
      }
    }

    window.addEventListener('message', messageHandler)
  })
}
