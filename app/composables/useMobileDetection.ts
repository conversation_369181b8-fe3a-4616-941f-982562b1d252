/**
 * 响应式移动端设备检测 composable
 * 提供实时的移动端设备检测功能，支持窗口大小变化的响应式更新
 */
import { isMobileDevice } from '@/utils/utils'

export const useMobileDetection = () => {
  // 响应式的移动端状态
  const isMobile = ref(false)

  /**
   * 更新移动端检测状态
   */
  const updateMobileStatus = () => {
    isMobile.value = isMobileDevice()
  }

  // 组件挂载前初始化
  onBeforeMount(() => {
    // 初始化移动端检测
    updateMobileStatus()

    // 使用 Nuxt 的节流函数优化 resize 事件处理，防止窗口变化时卡顿
    const throttledResize = useThrottleFn(() => {
      updateMobileStatus()
    }, 200) // 200ms 的节流时间

    // 监听窗口大小变化
    window.addEventListener('resize', throttledResize)

    // 清理事件监听器
    onUnmounted(() => {
      window.removeEventListener('resize', throttledResize)
    })
  })

  return {
    isMobile: readonly(isMobile), // 返回只读的响应式状态
    updateMobileStatus // 手动更新状态的方法
  }
}
