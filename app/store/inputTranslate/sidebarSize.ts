/**
 * 侧边栏尺寸存储
 */
import { defineStore } from 'pinia'

interface SidebarSizeState {
  // aiSelect模式下的宽度（像素）
  aiSelectModeWidth: number
  // aiSelect模式下的最大宽度（像素）
  aiSelectModeMaxWidth: number
  // 非aiSelect模式下的宽度（像素）
  normalModeWidth: number
  // 非aiSelect模式下的最大宽度（像素）
  normalModeMaxWidth: number
  // 是否已经设置了默认值
  isSetDefaultWidth: boolean
}

export const useSidebarSize = defineStore('select_translate_sidebar_size', {
  state: (): SidebarSizeState => ({
    aiSelectModeWidth: 0, // aiSelect模式下的宽度
    aiSelectModeMaxWidth: 0, // aiSelect模式下的最大宽度
    normalModeWidth: 0, // 非aiSelect模式下的宽度
    normalModeMaxWidth: 0, // 非aiSelect模式下的最大宽度
    isSetDefaultWidth: false // 是否已经设置了默认值
  }),

  actions: {
    /**
     * 计算当前的默认值
     * 1. 需要兼容两种翻译模型下的宽度(aiSelect模式下的宽度/非aiSelect模式下的宽度)
     */
    setDefaultWidth() {
      // 如果当前是aiSelect模式，则设置为屏幕宽度的40%
      this.aiSelectModeWidth = window.innerWidth * 0.35
      // 如果不是择优模式就默认是屏幕的一半宽度
      this.normalModeWidth = window.innerWidth / 2
    },
    /**
     * 设置当前模式下的最大宽度
     */
    setCurrentModeMaxWidth() {
      // 如果当前是aiSelect模式，则设置为屏幕宽度的40%
      this.aiSelectModeMaxWidth = window.innerWidth * 0.35
      this.normalModeMaxWidth = window.innerWidth / 2
      // 设置默认值-已经设置过默认值了之后就不会重新计算默认值
      // this.isSetDefaultWidth = true;
    }
  },

  // 持久化存储到localStorage
  // 一定要使用 piniaPluginPersistedstate.localStorage() 否则无法持久化 存储
  persist: {
    storage: piniaPluginPersistedstate.localStorage()
  }
  // persist: true
})
