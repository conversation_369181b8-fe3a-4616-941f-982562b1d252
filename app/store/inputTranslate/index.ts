/**
 * 输入翻译的存储
 */
import { defineStore } from 'pinia'
import type { TranslateMode, Language, AiSelectModel, TraditionalEngines, State } from '@/types/translate'

export const useInputTranslate = defineStore('select_translate_input_translate', {
  state: (): State => ({
    // 已开启的模型列表
    enabledEnginesList: ['microsoft', 'google', 'transmart', 'zhipu'],
    // 传统翻译模型
    traditionalEngines: {
      label: '',
      value: 'microsoft'
    },
    targetLanguage: 'zh-Hans', // 翻译目标语言
    originalLanguage: 'auto', // 原文语言
    isAiSelectAnalysis: true, // 是否精挑分析
    isSingleSentenceAiSelect: false, // true 为逐句翻译，false 为全文翻译
    isAutoTranslate: false, // 是否开启自动翻译
    // 精挑翻译模型
    aiSelectModel: {
      label: '',
      value: '',
      modelVersion: '',
      modelName: '',
      serviceMode: '',
      serviceModeName: ''
    },
    aiSelectModelList: [], // 精挑翻译模型列表
    originalLanguageList: [], // 原文语言列表
    targetLanguageList: [], // 翻译目标语言列表
    isSettingsPanelCollapsed: true, // 设置面板状态，true为收起，false为展开，不传则切换当前状态
    currentTranslateMode: 'aiSelect' // 默认使用择优翻译模式
  }),
  actions: {
    setEnabledEnginesList(engines: string[]) {
      this.enabledEnginesList = engines
    },
    setTraditionalEngines(engines: TraditionalEngines) {
      this.traditionalEngines = engines
    },
    setTargetLanguage(language: string) {
      this.targetLanguage = language
    },
    setOriginalLanguage(language: string) {
      this.originalLanguage = language
    },
    setIsAiSelectAnalysis(is: boolean) {
      this.isAiSelectAnalysis = is
    },
    setIsSingleSentenceAiSelect(is: boolean) {
      this.isSingleSentenceAiSelect = is
    },
    setAiSelectModel(model: AiSelectModel) {
      this.aiSelectModel = model
    },
    setAiSelectModelList(models: AiSelectModel[]) {
      this.aiSelectModelList = models
    },
    setOriginalLanguageList(languages: Language[]) {
      this.originalLanguageList = languages
    },
    setTargetLanguageList(languages: Language[]) {
      this.targetLanguageList = languages
    },
    /**
     * 设置当前翻译模式
     * @param mode 翻译模式：'traditional' 传统翻译 或 'aiSelect' 择优翻译
     */
    setCurrentTranslateMode(mode: TranslateMode) {
      this.currentTranslateMode = mode
    },
    /**
     * 切换设置面板的收起/展开状态
     * @param status 设置面板状态，true为收起，false为展开，不传则切换当前状态
     */
    toggleSettingsPanelCollapsed(status?: boolean) {
      if (status !== undefined) {
        this.isSettingsPanelCollapsed = status
      }
      else {
        this.isSettingsPanelCollapsed = !this.isSettingsPanelCollapsed
      }
    }
  },

  getters: {
    getEnabledEnginesList: state => state.enabledEnginesList,
    getTraditionalEngines: state => state.traditionalEngines,
    getTargetLanguage: state => state.targetLanguage,
    getOriginalLanguage: state => state.originalLanguage,
    getIsAiSelectAnalysis: state => state.isAiSelectAnalysis,
    getIsSingleSentenceAiSelect: state => state.isSingleSentenceAiSelect,
    getAiSelectModel: state => state.aiSelectModel,
    getAiSelectModelList: state => state.aiSelectModelList,
    getOriginalLanguageList: state => state.originalLanguageList,
    getTargetLanguageList: state => state.targetLanguageList,
    getIsSettingsPanelCollapsed: state => state.isSettingsPanelCollapsed,
    getCurrentTranslateMode: state => state.currentTranslateMode
  },
  // https://prazdevs.github.io/pinia-plugin-persistedstate/frameworks/nuxt.html#localstorage
  // 一定要使用 piniaPluginPersistedstate.localStorage() 否则无法持久化 存储
  persist: {
    storage: piniaPluginPersistedstate.localStorage()
  }
  // persist: true
})
