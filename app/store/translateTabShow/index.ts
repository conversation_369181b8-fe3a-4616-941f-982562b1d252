// 翻译类型tab栏展开/收缩状态
export const useTranslateTabShow = defineStore('select_translate_tab_show', {
  state: () => ({
    // 是否显示翻译类型tab栏
    isTabBarExpanded: true
  }),
  actions: {
    toggleTabBar() {
      this.isTabBarExpanded = !this.isTabBarExpanded
    }
  },
  // https://prazdevs.github.io/pinia-plugin-persistedstate/frameworks/nuxt.html#localstorage
  // 一定要使用 piniaPluginPersistedstate.localStorage() 否则无法持久化 存储
  persist: {
    storage: piniaPluginPersistedstate.localStorage()
  }
})
