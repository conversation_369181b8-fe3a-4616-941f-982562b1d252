import { defineStore } from 'pinia'
import type { HeadersTokenType, EmailLoginType, WechatLoginType, GoogleLoginType, MemberProductType } from '@/api/login/types'
import { emailLogin<PERSON>pi, wechatLoginApi, googleLoginApi, getCurrentMemberInfo, getMemberProductInfo, getMemberResourceAccount, getMemberProductTokenUsage } from '@/api/login'
import { useStorage } from '@/hooks/web/useStorage'

// pinia 与 nuxt-3 的整合文档：https://prazdevs.github.io/pinia-plugin-persistedstate/zh/guide/
// 特别注意：不要使用以下方式正式，因为nuxt的配置中已经配置了piniaPersistedstate
// ---- import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
// ---- const store  = createPinia()
// ---- store.use(piniaPluginPersistedstate)

const { clear } = useStorage()

/**
 * 用户登录授权的 ztsl_token 对应的 cookie 有效期天数（要与后端服务的token有效天数保持一致）
 */
export const COOKIE_EXPIRES_DATE = 365

export interface MemberState {
  member_id?: string
  signup_mode?: string
  email?: string
  wechat_unionid?: string
  google_sub?: string
  google_email?: string
  phone_number?: string
  nickname?: string
  avatar?: string
  create_datetime?: string
  last_login_time?: string
  last_login_ip?: string
  member_access_token?: string
}

export interface MemberProductState {
  platform?: string
  member_no?: string
  product_no?: string
  product_name?: string
  service_status?: string
  service_expire_time?: string
  subscription_buy_mode?: string // 订阅模式：yearly-年度订阅
  subscription_pay_way?: string // 支付方式：stripe
  subscription_id?: string // 订阅ID
  subscription_status?: number // 订阅状态
  subscription_renewal_status?: number // 续订状态
  error_message?: string // 错误信息
  error_code?: string // 错误代码
  next_renewal_time?: string // 下次续订时间
  id?: number // 会员产品ID
  update_datetime?: string // 更新时间
  create_datetime?: string // 创建时间
  delete_datetime?: string | null // 删除时间
  is_delete?: number // 是否删除：0-未删除，1-已删除
  /**
   * 会员产品 Plus会员 状态(API返回的业务状态，非数据库字段状态)：
   * 0：未试用（免费版），
   * 1：Plus会员试用中（试用版），
   * 2：Plus会员试用已过期（免费版），
   * 3：Plus会员套餐有效期内，
   * 4：Plus会员套餐已过期（免费版）
   */
  member_product_status?: number // 会员产品状态
}

export interface MemberProductTokenUsageState {
  pack_tokens_current_total_num?: number // tokens资源包当前总量
  pack_tokens_remaining_num?: number // tokens资源包剩余量
  pack_pdf_plus_current_total_num?: number // PDF Plus资源包当前总量
  pack_pdf_plus_remaining_num?: number // PDF Plus资源包剩余量
  tokens_remaining_num?: number // tokens剩余量
  tokens_total_num?: number // tokens总量
  pdf_plus_total_page_num?: number // PDF Plus总页数
  pdf_plus_remaining_page_num?: number // PDF Plus剩余页数
  cycle_end_time?: string // 周期结束时间
}

export interface AuthState {
  member: MemberState // 当前用户基本信息
  memberProduct: MemberProductState // 当前用户购买的产品信息
  memberProductTokenUsage: MemberProductTokenUsageState // 当前用户购买的产品的token使用情况
  isMember: boolean // 是否已经登录并获取到用户信息
  tokenKey: string // 提交认证请求时，设置的 header key
  token: string // 认证 token
  refreshToken: string // 刷新 token
  interfaceLanguage: string // 页面语言
}

export const useAuthStore = defineStore('select_translate__member', {
  state: (): AuthState => {
    return {
      member: {},
      memberProduct: {
        platform: '',
        member_no: '',
        product_no: '',
        product_name: '',
        service_status: '',
        service_expire_time: '',
        subscription_buy_mode: '',
        subscription_pay_way: '',
        subscription_id: '',
        subscription_status: 0,
        subscription_renewal_status: 0,
        error_message: '',
        error_code: '',
        next_renewal_time: '',
        id: 0,
        update_datetime: '',
        create_datetime: '',
        delete_datetime: null,
        is_delete: 0,
        member_product_status: 0
      },
      memberProductTokenUsage: {
        pack_tokens_current_total_num: 0,
        pack_tokens_remaining_num: 0,
        pack_pdf_plus_current_total_num: 0,
        pack_pdf_plus_remaining_num: 0,
        tokens_remaining_num: 0,
        tokens_total_num: 0,
        pdf_plus_total_page_num: 0,
        pdf_plus_remaining_page_num: 0,
        cycle_end_time: ''
      },
      isMember: false,
      tokenKey: 'Authorization',
      token: '',
      refreshToken: '',
      interfaceLanguage: ''
    }
  },
  getters: {
    getTokenKey(): string {
      return this.tokenKey
    },
    getToken(): string {
      return this.token
    },
    getRefreshToken(): string {
      return this.refreshToken
    },
    getMember(): MemberState {
      return this.member
    },
    getMemberProduct(): MemberProductState {
      return this.memberProduct
    },
    getIsMember(): boolean {
      return this.isMember
    },
    getInterfaceLanguage(): string {
      return this.interfaceLanguage
    }
  },
  actions: {
    setToken(token: string) {
      this.token = token
    },
    setRefreshToken(refreshToken: string) {
      this.refreshToken = refreshToken
    },
    async emailLogin(formData: EmailLoginType) {
      // 邮箱登录
      formData.platform = 'web' // 平台，web:门户系统，app：移动端
      const res = await emailLoginApi(formData)
      if (res) {
        this.token = `${res.data.token_type} ${res.data.access_token}`
        this.refreshToken = res.data.refresh_token
        // 获取当前登录用户的信息
        await this.setMemberInfo()
        await this.setMemberProductInfo()
      }
      return res
    },
    async wechatLogin(formData: WechatLoginType) {
      // 微信登录
      formData.platform = 'web' // 平台，web:门户系统，app：移动端
      const res = await wechatLoginApi(formData)
      if (res) {
        this.token = `${res.data.token_type} ${res.data.access_token}`
        this.refreshToken = res.data.refresh_token
        // 获取当前登录用户的信息
        await this.setMemberInfo()
        await this.setMemberProductInfo()
      }
      return res
    },
    async googleLogin(formData: GoogleLoginType) {
      // 谷歌登录
      formData.platform = 'web' // 平台，web:门户系统，app：移动端
      const res = await googleLoginApi(formData)
      if (res) {
        this.token = `${res.data.token_type} ${res.data.access_token}`
        this.refreshToken = res.data.refresh_token
        // 获取当前登录用户的信息
        await this.setMemberInfo()
        await this.setMemberProductInfo()
      }
      return res
    },
    reset() {
      this.member = {}
      this.memberProduct = {}
      this.isMember = false
      this.token = ''
      this.refreshToken = ''
      this.memberProductTokenUsage = {}
    },

    // 清除所有登录信息， 并重定向到登录页面
    logout() {
      clear(['ztsl_member']) // 清除本地存储中的键和值，传入字符串数组（可以同时清除多个）
      this.reset()
      navigateTo(`/${this.interfaceLanguage}/login`)
    },
    // 清除本地存储
    clearLocalStorage() {
      clear(['ztsl_member']) // 清除本地存储中的键和值，传入字符串数组（可以同时清除多个）
      this.reset()
    },
    // 这里更新用户是自己在个人中心更新自己的用户信息，不包括在用户列表中更新的，所以不包含权限角色等信息
    // 用户信息取消使用持久化存储，仅使用共享存储
    updateMember(data: MemberState) {
      this.member.member_id = data.member_id
      this.member.signup_mode = data.signup_mode
      this.member.email = data.email
      this.member.wechat_unionid = data.wechat_unionid
      this.member.google_sub = data.google_sub
      this.member.google_email = data.google_email
      this.member.phone_number = data.phone_number
      this.member.nickname = data.nickname
      this.member.avatar = data.avatar
      this.member.create_datetime = data.create_datetime
      this.member.last_login_ip = data.last_login_ip
      this.member.last_login_time = data.last_login_time
    },
    // 获取会员详细信息
    // 会员信息取消使用持久化存储，仅使用共享存储
    async setMemberInfo() {
      const res = await getCurrentMemberInfo()
      this.isMember = true
      this.member = res.data
    },
    // 获取会员购买货订阅的产品详细信息
    async setMemberProductInfo() {
      // 获取会员已购买产品信息
      const headers: HeadersTokenType = {
        token: this.getToken.slice(7) // 去掉token前缀 "bearer "
      }
      const paramData: MemberProductType = {
        platform: 'ztsl'
      }
      const res = await getMemberProductInfo(headers, paramData)
      this.memberProduct = res.data
    },
    // 更新会员购买货订阅的产品详细信息
    async updateMemberProduct(memberProduct: MemberProductState) {
      this.memberProduct = memberProduct
    },
    // 获取会员资源包账户信息
    async setMemberResourceAccount() {
      // 获取会员资源包账户
      const headers: HeadersTokenType = {
        token: this.getToken.slice(7) // 去掉token前缀 "bearer "
      }
      const paramData: MemberProductType = {
        platform: 'ztsl'
      }

      const res = await getMemberResourceAccount(headers, paramData)
      // 将资源包账户数据添加到现有数据中
      Object.assign(this.memberProductTokenUsage, res.data)

      // 1. 会员产品状态为0（新注册会员）时，不请求套餐额度使用情况
      // 2. 会员产品状态为1（正常）或3（已过期）时，请求套餐额度使用情况
      if ([1, 3].includes(this.memberProduct.member_product_status)) {
        // 套餐额度
        const tokenUsage = await getMemberProductTokenUsage(headers, paramData)
        // 将套餐额度数据添加到现有数据中
        Object.assign(this.memberProductTokenUsage, {
          tokens_remaining_num: tokenUsage.data.tokens_remaining_num,
          tokens_total_num: tokenUsage.data.tokens_total_num,
          pdf_plus_total_page_num: tokenUsage.data.pdf_plus_total_page_num,
          pdf_plus_remaining_page_num: tokenUsage.data.pdf_plus_remaining_page_num,
          cycle_end_time: tokenUsage.data.cycle_end_time
        })
      }

      return res.data
    }
  },
  // https://prazdevs.github.io/pinia-plugin-persistedstate/frameworks/nuxt.html#localstorage
  // 一定要使用 piniaPluginPersistedstate.localStorage() 否则无法持久化 存储
  persist: {
    storage: piniaPluginPersistedstate.localStorage()
  }
  // persist: true
})

export const useAuthStoreWithOut = () => {
  return useAuthStore()
}
