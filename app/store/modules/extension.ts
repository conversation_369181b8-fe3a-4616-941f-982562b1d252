import { defineStore } from 'pinia'

interface ExtensionState {
  isInstalled: boolean
  isInstalledDefault: boolean
  version?: string
  nodeEnv?: string
  isChecking: boolean
  extensionVersionStatus?: string
  lastCheckTime?: number
  isEnvMatching?: boolean
}

export interface ExtensionStoreState {
  extensionState: ExtensionState
}

export const useExtensionStore = defineStore('extension', {
  state: (): ExtensionStoreState => ({
    extensionState: {
      isInstalled: true, // 初始状态设为 true
      isInstalledDefault: true, // 如果你是给按钮使用的就使用 isInstalledDefault，否则就使用isInstalled
      isChecking: true, // 初始状态设为 true
      version: undefined, // 插件版本
      nodeEnv: undefined, // 插件运行环境
      extensionVersionStatus: undefined, // 插件版本状态
      lastCheckTime: undefined, // 最后检测时间
      // 当前环境是否跟 插件环境不一致, 默认只设置 true否则会导致没有检测完成就显示环境不一致
      isEnvMatching: true
    }
  }),

  getters: {
    getExtensionState(): ExtensionState {
      return this.extensionState
    },
    getIsExtensionInstalled(): boolean {
      return this.extensionState.isInstalled
    },
    getIsExtensionChecking(): boolean {
      return this.extensionState.isChecking
    },
    getExtensionVersion(): string | undefined {
      return this.extensionState.version
    },
    getExtensionEnv(): string | undefined {
      return this.extensionState.nodeEnv
    },
    getExtensionVersionStatus(): string | undefined {
      return this.extensionState.extensionVersionStatus
    },
    getIsExtensionInstalledDefault(): boolean {
      return this.extensionState.isInstalledDefault
    },
    getIsEnvMatching(): boolean {
      return this.extensionState.isEnvMatching
    }
  },

  actions: {
    /**
     * 设置扩展状态
     */
    setExtensionState(state: Partial<ExtensionState>) {
      this.extensionState = {
        ...this.extensionState,
        ...state,
        lastCheckTime: Date.now()
      }
    },

    /**
     * 设置检测中状态
     */
    setChecking(isChecking: boolean) {
      this.extensionState.isChecking = isChecking
    },

    /**
     * 检查扩展是否安装的核心逻辑
     */
    checkExtensionInstall(retryCount = 0): Promise<ExtensionState> {
      return new Promise((resolve) => {
        let hasResponse = false

        // console.log(`开始检测扩展安装状态 (尝试 ${retryCount + 1}/3)`);

        window.postMessage(
          {
            type: 'CHECK_EXTENSION_INSTALLED'
          },
          window.location.origin
        )

        const messageHandler = (event: MessageEvent) => {
          if (event.data.type === 'EXTENSION_INSTALLED') {
            hasResponse = true
            // console.log('扩展检测成功:', event.data);

            const result = {
              isInstalled: event.data.message,
              isInstalledDefault: this.extensionState.isInstalledDefault,
              isEnvMatching: true,
              version: event.data.version,
              nodeEnv: event.data.nodeEnv,
              isChecking: false,
              extensionVersionStatus: event.data.extensionVersionStatus
            }

            console.log('扩展检测结果:', result)

            // 检查环境是否匹配
            result.isEnvMatching = result.nodeEnv === useRuntimeConfig().public.env

            resolve(result)
            window.removeEventListener('message', messageHandler)
          }
        }

        window.addEventListener('message', messageHandler)

        setTimeout(() => {
          if (!hasResponse) {
            window.removeEventListener('message', messageHandler)

            // 重试逻辑
            if (retryCount < 2) {
              // console.warn(`扩展检测超时，正在重试... (${retryCount + 1}/2)`);
              resolve(this.checkExtensionInstall(retryCount + 1))
            }
            else {
              // console.warn('扩展检测最终超时，判定为未安装');
              resolve({
                isInstalled: false,
                isInstalledDefault: false,
                isChecking: false
              })
            }
          }
        }, 1000) // 增加超时时间到 1000ms
      })
    },

    /**
     * 初始化扩展状态检测
     */
    async initExtensionState() {
      // console.log('初始化扩展状态检测');

      // 设置检测中状态
      this.setChecking(true)

      try {
        const result = await this.checkExtensionInstall()

        // 更新状态
        this.setExtensionState(result)

        // console.log('扩展状态检测完成:', result);
        return result
      }
      catch (error) {
        // console.error('扩展状态检测失败:', error);
        this.setExtensionState({
          isInstalled: false,
          isChecking: false
        })
        throw error
      }
    },

    /**
     * 重置扩展状态
     */
    resetExtensionState() {
      this.extensionState = {
        isInstalled: true,
        isInstalledDefault: true,
        isChecking: true,
        version: undefined,
        nodeEnv: undefined,
        extensionVersionStatus: undefined,
        lastCheckTime: undefined
      }
    }
  }

  // 不持久化存储，每次刷新都重新检测
  // persist: false
})
