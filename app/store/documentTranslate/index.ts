/**
 * 文档翻译设置的存储
 */
import { defineStore } from 'pinia'

interface Language {
  label: string
  value: string
  en_name: string
  zing_code: string
  lang_code: string
  original_name: string
  sort: number
  zht_name: string
  zhs_name: string
}

interface EngineModel {
  label: string
  value: string
  img?: string
  modelVersion: string // 模型版本
  modelName: string // 模型名称
  serviceModeName: string // 服务模式名称 （custom 自定义API Key， free 免费 , plus 会员）
  serviceMode: string // 服务模式 （custom 自定义API Key， free 免费 , plus 会员）
}

/**
 * 译文显示
 */
type TransDisplayMode = 'bilingual' | 'fulltext' // 双语对照 | 全文翻译

/**
 * 文本翻译
 */
interface State {
  enabledEnginesList: EngineModel[]
  translateEngine: EngineModel
  translationsDisplay: TransDisplayMode
  targetLanguageList: Language[]
  targetLanguageZingCode: string
  targetLanguage: string
}

export const useDocumentTranslateStore = defineStore('select_translate_document_translate', {
  state: (): State => ({
    enabledEnginesList: [], // 翻译引擎列表
    translateEngine: {
      label: '',
      value: '',
      img: '',
      modelVersion: '',
      modelName: '',
      serviceMode: '',
      serviceModeName: ''
    }, // 翻译引擎
    translationsDisplay: 'bilingual', // 译文显示
    targetLanguageList: [], // 目标语言列表
    // 注意这个 targetLanguageZingCode 主要是为什么获取语言集中的 lang_code（该模型支持的语言编码）
    targetLanguageZingCode: 'zh-Hans', // 目标语言的zing_code
    targetLanguage: 'zh-Hans' // 目标语言
  }),

  actions: {
    setEnabledEnginesList(engines: EngineModel[]) {
      this.enabledEnginesList = engines
    },
    setTranslateEngine(engine: EngineModel) {
      this.translateEngine = engine
    },
    setTranslationsDisplay(engine: string) {
      this.translationsDisplay = engine as TransDisplayMode
    },
    setTargetLanguageList(languages: Language[]) {
      this.targetLanguageList = languages
    },
    setTargetLanguageZingCode(language: string) {
      this.targetLanguageZingCode = language
    },
    setTargetLanguage(language: string) {
      this.targetLanguage = language
    },
    // 通过 模型的 value 找到 模型列表的 对应模型的值更新
    updateTranslateEngine(engineValue: string) {
      const engineModel = this.enabledEnginesList.find(item => item.value === engineValue)
      if (engineModel) {
        this.translateEngine = engineModel
      }
    },
    // 通过 目标语言的 zing_code 找到 目标语言列表的 对应目标语言的值更新
    updateTargetLanguage(languageZingCode: string) {
      const languageModel = this.targetLanguageList.find(item => item.zing_code === languageZingCode)
      if (languageModel) {
        this.targetLanguageZingCode = languageModel.zing_code
        this.targetLanguage = languageModel.lang_code
      }
    }
  },

  getters: {
    getEnabledEnginesList: state => state.enabledEnginesList,
    getTranslateEngine: state => state.translateEngine,
    getTargetLanguageList: state => state.targetLanguageList,
    getTargetLanguageZingCode: state => state.targetLanguageZingCode,
    getTargetLanguage: state => state.targetLanguage
  },
  // https://prazdevs.github.io/pinia-plugin-persistedstate/frameworks/nuxt.html#localstorage
  // 一定要使用 piniaPluginPersistedstate.localStorage() 否则无法持久化 存储
  persist: {
    storage: piniaPluginPersistedstate.localStorage()
  }
  // persist: true
})
