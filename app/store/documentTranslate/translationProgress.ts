/**
 * 文档翻译进度
 */

import type { TranslationProgressType, NodeTranslationProgress } from '@/types/translationProgress'

export const useDocTranslationProgressStore = defineStore('select_translate_document_translate_progress', {
  state: () => ({
    translationProgress: {
      overallProgress: 0, // 整体翻译进度
      pagesStatus: [], // 页面翻译状态
      completedPages: 0, // 已完成页数
      totalPages: 0, // 总页数
      hasError: false // 是否翻译错误
    } as TranslationProgressType,
    nodeTranslationProgress: {
      // 基础统计信息
      totalNodes: 0, // 总节点数量
      completedNodes: 0, // 已完成翻译的节点数量
      inProgressNodes: 0, // 正在翻译的节点数量
      failedNodes: 0, // 翻译失败的节点数量
      notStartedNodes: 0, // 未开始翻译的节点数量

      // 进度信息
      overallProgress: 0, // 整体翻译进度百分比 (0-100)

      // 错误和状态信息
      hasError: false, // 是否存在翻译错误
      errorRate: 0 // 错误率百分比 (0-100)
    } as NodeTranslationProgress
  })
})
