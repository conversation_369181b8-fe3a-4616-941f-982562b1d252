/**
 * TODO PDF PLUS历史记录
 */

// 数据库名称
// 增加版本号，以触发数据库升级

// 导入历史记录类型
import type { TranslationPdfPlusFileHistory } from '@/types/history'
import { useAuthStore } from '@/store/modules/auth'

const DB_NAME = 'select-translate-pdf-plus-store'
// 历史记录存储名称
const STORE_NAME = 'pdf_plus_file_history'
// 数据库版本
const DB_VERSION = 2

export const useTranslationHistory = () => {
  let db: IDBDatabase | null = null

  /**
   * 获取当前用户ID
   * 如果用户未登录则返回'guest'作为默认ID
   */
  const getCurrentMemberId = (): string => {
    try {
      const authStore = useAuthStore()
      return authStore.member?.member_id || 'guest'
    }
    catch (error) {
      console.warn('获取用户ID失败，使用guest作为默认ID', error)
      return 'guest'
    }
  }

  /**
   * 迁移历史记录
   * 将未登录状态(guest)的历史记录迁移到登录用户名下
   * 在用户成功登录后调用
   */
  const migrateGuestHistory = async (): Promise<boolean> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        const currentMemberId = getCurrentMemberId()

        // 如果当前是guest用户或获取用户ID失败，则不执行迁移
        if (currentMemberId === 'guest') {
          resolve(false)
          return
        }

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 使用member_id索引查询guest用户的历史记录
        const index = store.index('member_id')
        const request = index.getAll('guest')

        request.onsuccess = () => {
          const guestRecords = request.result || []

          if (guestRecords.length === 0) {
            // 没有需要迁移的记录
            resolve(true)
            return
          }

          let migratedCount = 0
          let errorOccurred = false

          // 更新每条记录的member_id
          guestRecords.forEach((record) => {
            record.member_id = currentMemberId

            const updateRequest = store.put(record)

            updateRequest.onsuccess = () => {
              migratedCount++
              if (migratedCount === guestRecords.length && !errorOccurred) {
                console.log(`成功迁移 ${migratedCount} 条历史记录到账户 ${currentMemberId}`)
                resolve(true)
              }
            }

            updateRequest.onerror = (event) => {
              console.error('迁移历史记录失败:', event)
              if (!errorOccurred) {
                errorOccurred = true
                reject('迁移历史记录失败')
              }
            }
          })
        }

        request.onerror = (event) => {
          console.error('获取guest历史记录失败:', event)
          reject('获取guest历史记录失败')
        }
      }
      catch (error) {
        console.error('迁移历史记录出错:', error)
        reject(`迁移历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 初始化数据库
   */
  const initDB = (): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      // 如果数据库已经打开，则返回 true
      if (db) {
        resolve(true)
        return
      }

      // 打开数据库
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      // 打开数据库失败
      request.onerror = (event) => {
        console.error('IndexedDB 打开失败:', event)
        reject(false)
      }

      // 打开数据库成功
      request.onsuccess = (event) => {
        db = (event.target as IDBOpenDBRequest).result
        resolve(true)
      }

      // 数据库升级
      request.onupgradeneeded = (event) => {
        const database = (event.target as IDBOpenDBRequest).result

        // 检查存储对象是否存在
        if (!database.objectStoreNames.contains(STORE_NAME)) {
          // 创建新的存储对象
          const store = database.createObjectStore(STORE_NAME, {
            keyPath: 'id',
            autoIncrement: true
          })

          // 创建索引
          store.createIndex('key', 'key', { unique: true })
          store.createIndex('id', 'id', { unique: false })
          store.createIndex('member_id', 'member_id', { unique: false }) // 添加用户ID索引
          store.createIndex('member_id_id', ['member_id', 'id'], { unique: false }) // 复合索引
        }
        else {
          // 如果存储对象已存在但需要添加新索引
          const transaction = (event.target as IDBOpenDBRequest).transaction
          const store = transaction.objectStore(STORE_NAME)

          // 检查索引是否存在，不存在则添加
          if (!store.indexNames.contains('member_id')) {
            store.createIndex('member_id', 'member_id', { unique: false })
          }
          if (!store.indexNames.contains('member_id_id')) {
            store.createIndex('member_id_id', ['member_id', 'id'], { unique: false })
          }
        }
      }
    })
  }

  /**
   * 添加新的文件历史记录
   */
  const addHistory = async (history: TranslationPdfPlusFileHistory): Promise<string> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      // 添加新的翻译历史记录
      try {
        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 添加当前用户ID
        history.member_id = getCurrentMemberId()

        // 添加历史记录
        const request = store.add(history)

        // 添加成功
        request.onsuccess = () => {
          resolve(history.id)
        }

        // 添加失败
        request.onerror = (event) => {
          console.error('添加历史记录失败:', event)
          reject('添加历史记录失败')
        }
      }
      catch (error) {
        console.error('添加历史记录出错:', error)
        reject(`添加历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 根据 key 获取历史记录
   */
  const getHistoryByKey = async (key: string): Promise<TranslationPdfPlusFileHistory | null> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readonly')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 创建索引，便于按 key 查询
        const index = store.index('id')

        // 获取历史记录 key
        const request = index.get(key)

        // 获取历史记录 key 成功
        request.onsuccess = () => {
          console.info('获取历史记录出错:', request)
          resolve(request.result || null)
        }

        request.onerror = (e) => {
          reject('获取历史记录失败' + e)
        }
      }
      catch (error) {
        console.error('获取历史记录出错:', error)
        reject(`获取历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 清空所有历史记录
   * 只清空当前用户的历史记录
   */
  const clearAllHistory = async (): Promise<boolean> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 获取当前用户ID
        const memberId = getCurrentMemberId()

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 获取当前用户的所有记录
        const index = store.index('member_id')
        const getRequest = index.getAllKeys(memberId)

        getRequest.onsuccess = () => {
          const keys = getRequest.result

          if (keys.length === 0) {
            // 没有记录需要删除
            resolve(true)
            return
          }

          let deletedCount = 0
          let errorOccurred = false

          // 逐个删除记录
          keys.forEach((id) => {
            const deleteRequest = store.delete(id)

            deleteRequest.onsuccess = () => {
              deletedCount++
              if (deletedCount === keys.length && !errorOccurred) {
                resolve(true)
              }
            }

            deleteRequest.onerror = () => {
              if (!errorOccurred) {
                errorOccurred = true
                reject('清空历史记录失败')
              }
            }
          })
        }

        getRequest.onerror = () => {
          reject('获取历史记录键失败')
        }
      }
      catch (error) {
        console.error('清空历史记录出错:', error)
        reject(`清空历史记录出错: ${error}`)
      }
    })
  }

  return {
    initDB,
    addHistory,
    getHistoryByKey,
    clearAllHistory,
    migrateGuestHistory
  }
}
