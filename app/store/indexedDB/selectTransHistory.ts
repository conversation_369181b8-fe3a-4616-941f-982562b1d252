/**
 * TODO 择优翻译-择优翻译历史记录
 */

// 数据库名称
// 从2增加到3，触发数据库升级以添加新索引

// 导入历史记录类型
import type { TranslationHistory } from '@/types/history'
import { useAuthStore } from '@/store/modules/auth'

const DB_NAME = 'select-translate-store'
// 历史记录存储名称
const STORE_NAME = 'select_translate_history'
// 数据库版本
const DB_VERSION = 3

export const useTranslationHistory = () => {
  let db: IDBDatabase | null = null

  /**
   * 获取当前用户ID
   * 如果用户未登录则返回'guest'作为默认ID
   */
  const getCurrentMemberId = (): string => {
    try {
      const authStore = useAuthStore()
      return authStore.member?.member_id || 'guest'
    }
    catch (error) {
      console.warn('获取用户ID失败，使用guest作为默认ID', error)
      return 'guest'
    }
  }

  /**
   * 迁移历史记录
   * 将未登录状态(guest)的历史记录迁移到登录用户名下
   * 在用户成功登录后调用
   */
  const migrateGuestHistory = async (): Promise<boolean> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        const currentMemberId = getCurrentMemberId()

        // 如果当前是guest用户或获取用户ID失败，则不执行迁移
        if (currentMemberId === 'guest') {
          resolve(false)
          return
        }

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 使用member_id索引查询guest用户的历史记录
        const index = store.index('member_id')
        const request = index.getAll('guest')

        request.onsuccess = () => {
          const guestRecords = request.result || []

          if (guestRecords.length === 0) {
            // 没有需要迁移的记录
            resolve(true)
            return
          }

          let migratedCount = 0
          let errorOccurred = false

          // 更新每条记录的member_id
          guestRecords.forEach((record) => {
            record.member_id = currentMemberId

            const updateRequest = store.put(record)

            updateRequest.onsuccess = () => {
              migratedCount++
              if (migratedCount === guestRecords.length && !errorOccurred) {
                console.log(`成功迁移 ${migratedCount} 条历史记录到账户 ${currentMemberId}`)
                resolve(true)
              }
            }

            updateRequest.onerror = (event) => {
              console.error('迁移历史记录失败:', event)
              if (!errorOccurred) {
                errorOccurred = true
                reject('迁移历史记录失败')
              }
            }
          })
        }

        request.onerror = (event) => {
          console.error('获取guest历史记录失败:', event)
          reject('获取guest历史记录失败')
        }
      }
      catch (error) {
        console.error('迁移历史记录出错:', error)
        reject(`迁移历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 初始化数据库
   */
  const initDB = (): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      // 如果数据库已经打开，则返回 true
      if (db) {
        resolve(true)
        return
      }

      // 打开数据库
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      // 打开数据库失败
      request.onerror = (event) => {
        console.error('IndexedDB 打开失败:', event)
        reject(false)
      }

      // 打开数据库成功
      request.onsuccess = (event) => {
        db = (event.target as IDBOpenDBRequest).result
        resolve(true)
      }

      // 数据库升级
      request.onupgradeneeded = (event) => {
        const database = (event.target as IDBOpenDBRequest).result

        // 检查存储对象是否存在
        if (!database.objectStoreNames.contains(STORE_NAME)) {
          // 创建新的存储对象
          const store = database.createObjectStore(STORE_NAME, {
            keyPath: 'id',
            autoIncrement: true
          })

          // 创建索引
          store.createIndex('key', 'key', { unique: true })
          store.createIndex('timestamp', 'timestamp', { unique: false })
          store.createIndex('member_id', 'member_id', { unique: false })
          store.createIndex('member_id_timestamp', ['member_id', 'timestamp'], { unique: false })
          store.createIndex('translateType', 'translateType', { unique: false }) // 添加翻译类型索引
          store.createIndex('member_id_translateType', ['member_id', 'translateType'], { unique: false }) // 用户ID和翻译类型的组合索引
          store.createIndex('member_id_translateType_timestamp', ['member_id', 'translateType', 'timestamp'], { unique: false }) // 三重组合索引
        }
        else {
          // 如果存储对象已存在但需要添加新索引
          const transaction = (event.target as IDBOpenDBRequest).transaction
          const store = transaction.objectStore(STORE_NAME)

          // 检查索引是否存在，不存在则添加
          if (!store.indexNames.contains('member_id')) {
            store.createIndex('member_id', 'member_id', { unique: false })
          }
          if (!store.indexNames.contains('member_id_timestamp')) {
            store.createIndex('member_id_timestamp', ['member_id', 'timestamp'], { unique: false })
          }
          if (!store.indexNames.contains('translateType')) {
            store.createIndex('translateType', 'translateType', { unique: false })
          }
          if (!store.indexNames.contains('member_id_translateType')) {
            store.createIndex('member_id_translateType', ['member_id', 'translateType'], { unique: false })
          }
          if (!store.indexNames.contains('member_id_translateType_timestamp')) {
            store.createIndex('member_id_translateType_timestamp', ['member_id', 'translateType', 'timestamp'], { unique: false })
          }
        }
      }
    })
  }

  /**
   * 添加新的翻译历史记录
   */
  const addHistory = async (history: TranslationHistory): Promise<string> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      // 添加新的翻译历史记录
      try {
        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 设置时间戳
        if (!history.timestamp) {
          history.timestamp = Date.now()
        }

        // 添加当前用户ID
        history.member_id = getCurrentMemberId()

        // 添加历史记录
        const request = store.add(history)

        // 添加成功
        request.onsuccess = () => {
          resolve(history.key)
        }

        // 添加失败
        request.onerror = (event) => {
          console.error('添加历史记录失败:', event)
          reject('添加历史记录失败')
        }
      }
      catch (error) {
        console.error('添加历史记录出错:', error)
        reject(`添加历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 更新已有的翻译历史记录
   * 更新这个key 的整个数据
   */
  const updateHistory = async (history: TranslationHistory): Promise<boolean> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 创建索引，便于按 key 查询
        const index = store.index('key')

        // 获取历史记录 key
        const request = index.getKey(history.key)

        // 获取历史记录 key 成功
        request.onsuccess = (event) => {
          const id = request.result
          if (id) {
            // 记录存在，进行更新
            history.id = id as number
            // 确保member_id不变
            getHistoryByKey(history.key).then((existingHistory) => {
              if (existingHistory) {
                history.member_id = existingHistory.member_id
                const updateRequest = store.put(history)

                // 更新成功
                updateRequest.onsuccess = () => {
                  resolve(true)
                }

                // 更新失败
                updateRequest.onerror = () => {
                  reject('更新历史记录失败')
                }
              }
              else {
                reject('记录不存在')
              }
            })
          }
          else {
            // 记录不存在，添加新记录
            history.member_id = getCurrentMemberId()
            const addRequest = store.add(history)

            addRequest.onsuccess = () => {
              resolve(true)
            }

            addRequest.onerror = () => {
              reject('添加历史记录失败')
            }
          }
        }

        request.onerror = () => {
          reject('查询历史记录失败')
        }
      }
      catch (error) {
        console.error('更新历史记录出错:', error)
        reject(`更新历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 更新历史记录的特定字段
   * @param key - 记录的唯一标识
   * @param updates - 要更新的字段集合
   * @returns 更新是否成功的Promise
   */
  const updateHistoryFields = async (key: string, updates: Partial<TranslationHistory>): Promise<boolean> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 创建索引，便于按 key 查询
        const index = store.index('key')

        // 先获取完整记录
        const getRequest = index.get(key)

        getRequest.onsuccess = () => {
          const record = getRequest.result
          if (!record) {
            reject('记录不存在')
            return
          }

          // 只更新指定的字段
          Object.assign(record, updates)

          // 更新时间戳（如果未在updates中指定）
          if (!('timestamp' in updates)) {
            record.timestamp = Date.now()
          }

          // 确保member_id不被修改
          delete updates.member_id

          // 更新记录
          const updateRequest = store.put(record)

          updateRequest.onsuccess = () => {
            resolve(true)
          }

          updateRequest.onerror = (event) => {
            console.error('更新字段失败:', event)
            reject('更新字段失败')
          }
        }

        getRequest.onerror = (event) => {
          console.error('获取记录失败:', event)
          reject('获取记录失败')
        }
      }
      catch (error) {
        console.error('更新字段出错:', error)
        reject(`更新字段出错: ${error}`)
      }
    })
  }

  /**
   * 根据 key 获取历史记录
   */
  const getHistoryByKey = async (key: string): Promise<TranslationHistory | null> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readonly')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 创建索引，便于按 key 查询
        const index = store.index('key')

        // 获取历史记录 key
        const request = index.get(key)

        // 获取历史记录 key 成功
        request.onsuccess = () => {
          resolve(request.result || null)
        }

        request.onerror = () => {
          reject('获取历史记录失败')
        }
      }
      catch (error) {
        console.error('获取历史记录出错:', error)
        reject(`获取历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 获取所有历史记录
   * 仅返回当前用户的历史记录
   * 如果是已登录用户，会自动合并guest的历史记录
   */
  const getAllHistory = async (): Promise<TranslationHistory[]> => {
    await initDB()

    // 先尝试合并历史记录
    const memberId = getCurrentMemberId()
    if (memberId !== 'guest') {
      try {
        await migrateGuestHistory()
      }
      catch (error) {
        console.warn('自动合并历史记录失败，继续获取当前用户历史记录', error)
      }
    }

    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 获取当前用户ID
        const memberId = getCurrentMemberId()

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readonly')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 使用member_id索引查询当前用户的历史记录
        const index = store.index('member_id')
        const request = index.getAll(memberId)

        // 获取所有历史记录成功
        request.onsuccess = () => {
          // 按时间戳降序排序
          const results = request.result || []
          results.sort((a, b) => b.timestamp - a.timestamp)
          resolve(results)
        }

        request.onerror = () => {
          reject('获取所有历史记录失败')
        }
      }
      catch (error) {
        console.error('获取所有历史记录出错:', error)
        reject(`获取所有历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 分页获取历史记录
   * @param page 页码，从1开始
   * @param pageSize 每页数量
   * @returns 当前页的历史记录和总记录数
   */
  const getHistoryByPage = async (page: number, pageSize: number): Promise<{ data: TranslationHistory[], total: number }> => {
    await initDB()

    // 先尝试合并历史记录
    const memberId = getCurrentMemberId()
    if (memberId !== 'guest') {
      try {
        await migrateGuestHistory()
      }
      catch (error) {
        console.warn('自动合并历史记录失败，继续获取当前用户历史记录', error)
      }
    }

    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 获取当前用户ID
        const memberId = getCurrentMemberId()

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readonly')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 获取当前用户的历史记录总数
        const index = store.index('member_id')
        const countRequest = index.count(memberId)

        countRequest.onsuccess = () => {
          const total = countRequest.result

          // 如果没有记录，直接返回空结果
          if (total === 0) {
            resolve({ data: [], total: 0 })
            return
          }

          // 获取当前用户的所有记录，然后在内存中分页
          const getAllRequest = index.getAll(memberId)

          getAllRequest.onsuccess = () => {
            const results = getAllRequest.result || []

            // 按时间戳降序排序
            results.sort((a, b) => b.timestamp - a.timestamp)

            // 计算分页范围
            const startIndex = (page - 1) * pageSize
            const endIndex = startIndex + pageSize

            // 截取当前页的数据
            const pagedData = results.slice(startIndex, endIndex)

            resolve({
              data: pagedData,
              total
            })
          }

          getAllRequest.onerror = (event) => {
            console.error('获取历史记录失败:', event)
            reject('获取历史记录失败')
          }
        }

        countRequest.onerror = (event) => {
          console.error('获取历史记录总数失败:', event)
          reject('获取历史记录总数失败')
        }
      }
      catch (error) {
        console.error('分页获取历史记录出错:', error)
        reject(`分页获取历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 根据翻译类型分页获取历史记录
   * @param page 页码，从1开始
   * @param pageSize 每页数量
   * @param translateType 翻译类型
   * @returns 当前页的历史记录和总记录数
   */
  const getHistoryByTypeAndPage = async (page: number, pageSize: number, translateType: string): Promise<{ data: TranslationHistory[], total: number }> => {
    await initDB()

    // 先尝试合并历史记录
    const memberId = getCurrentMemberId()
    if (memberId !== 'guest') {
      try {
        await migrateGuestHistory()
      }
      catch (error) {
        console.warn('自动合并历史记录失败，继续获取当前用户历史记录', error)
      }
    }

    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 获取当前用户ID
        const memberId = getCurrentMemberId()

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readonly')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 使用组合索引获取特定用户和翻译类型的记录
        const index = store.index('member_id_translateType')

        // 创建范围查询，查找特定用户和翻译类型的记录
        const range = IDBKeyRange.only([memberId, translateType])

        // 获取记录总数
        const countRequest = index.count(range)

        countRequest.onsuccess = () => {
          const total = countRequest.result

          // 如果没有记录，直接返回空结果
          if (total === 0) {
            resolve({ data: [], total: 0 })
            return
          }

          // 使用组合索引获取特定用户和翻译类型的所有记录
          const getAllRequest = index.getAll(range)

          getAllRequest.onsuccess = () => {
            const results = getAllRequest.result || []

            // 按时间戳降序排序
            results.sort((a, b) => b.timestamp - a.timestamp)

            // 计算分页范围
            const startIndex = (page - 1) * pageSize
            const endIndex = startIndex + pageSize

            // 截取当前页的数据
            const pagedData = results.slice(startIndex, endIndex)

            resolve({
              data: pagedData,
              total
            })
          }

          getAllRequest.onerror = (event) => {
            console.error('获取历史记录失败:', event)
            reject('获取历史记录失败')
          }
        }

        countRequest.onerror = (event) => {
          console.error('获取历史记录总数失败:', event)
          reject('获取历史记录总数失败')
        }
      }
      catch (error) {
        console.error('按翻译类型分页获取历史记录出错:', error)
        reject(`按翻译类型分页获取历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 删除单个历史记录
   */
  const deleteHistory = async (key: string): Promise<boolean> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 创建索引，便于按 key 查询
        const index = store.index('key')

        // 获取历史记录
        const getRequest = index.get(key)

        getRequest.onsuccess = () => {
          const record = getRequest.result
          const currentMemberId = getCurrentMemberId()

          // 检查记录是否存在且属于当前用户
          if (record && record.member_id === currentMemberId) {
            const deleteRequest = store.delete(record.id)

            deleteRequest.onsuccess = () => {
              resolve(true)
            }

            deleteRequest.onerror = () => {
              reject('删除历史记录失败')
            }
          }
          else {
            // 记录不存在或不属于当前用户
            resolve(false)
          }
        }

        getRequest.onerror = () => {
          reject('查询历史记录失败')
        }
      }
      catch (error) {
        console.error('删除历史记录出错:', error)
        reject(`删除历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 清空所有历史记录
   * 只清空当前用户的历史记录
   */
  const clearAllHistory = async (): Promise<boolean> => {
    await initDB()
    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 获取当前用户ID
        const memberId = getCurrentMemberId()

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readwrite')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 获取当前用户的所有记录
        const index = store.index('member_id')
        const getRequest = index.getAllKeys(memberId)

        getRequest.onsuccess = () => {
          const keys = getRequest.result

          if (keys.length === 0) {
            // 没有记录需要删除
            resolve(true)
            return
          }

          let deletedCount = 0
          let errorOccurred = false

          // 逐个删除记录
          keys.forEach((id) => {
            const deleteRequest = store.delete(id)

            deleteRequest.onsuccess = () => {
              deletedCount++
              if (deletedCount === keys.length && !errorOccurred) {
                resolve(true)
              }
            }

            deleteRequest.onerror = () => {
              if (!errorOccurred) {
                errorOccurred = true
                reject('清空历史记录失败')
              }
            }
          })
        }

        getRequest.onerror = () => {
          reject('获取历史记录键失败')
        }
      }
      catch (error) {
        console.error('清空历史记录出错:', error)
        reject(`清空历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 根据搜索关键词和翻译类型在数据库中搜索历史记录
   * @param query 搜索关键词
   * @param translateType 翻译类型
   * @param page 页码，从1开始
   * @param pageSize 每页数量
   * @returns 搜索结果和总记录数
   */
  const searchHistoryByQueryAndType = async (query: string, translateType: string, page: number, pageSize: number): Promise<{ data: TranslationHistory[], total: number }> => {
    await initDB()

    return new Promise((resolve, reject) => {
      if (!db) {
        reject('数据库未初始化')
        return
      }

      try {
        // 获取当前用户ID
        const memberId = getCurrentMemberId()

        // 创建事务
        const transaction = db.transaction([STORE_NAME], 'readonly')

        // 获取存储对象
        const store = transaction.objectStore(STORE_NAME)

        // 使用组合索引获取特定用户和翻译类型的记录
        const index = store.index('member_id_translateType')

        // 创建范围查询，查找特定用户和翻译类型的记录
        const range = IDBKeyRange.only([memberId, translateType])

        // 获取所有匹配记录
        const getAllRequest = index.getAll(range)

        getAllRequest.onsuccess = () => {
          const results = getAllRequest.result || []

          // 在获取的结果中进行搜索过滤(不区分大小写)
          const filteredResults = results.filter(item => item.sourceDOMStructure && item.sourceDOMStructure.toLowerCase().includes(query.toLowerCase()))

          // 计算总记录数
          const total = filteredResults.length

          // 按时间戳降序排序
          filteredResults.sort((a, b) => b.timestamp - a.timestamp)

          // 计算分页范围
          const startIndex = (page - 1) * pageSize
          const endIndex = startIndex + pageSize

          // 截取当前页的数据
          const pagedData = filteredResults.slice(startIndex, endIndex)

          resolve({
            data: pagedData,
            total
          })
        }

        getAllRequest.onerror = (event) => {
          console.error('搜索历史记录失败:', event)
          reject('搜索历史记录失败')
        }
      }
      catch (error) {
        console.error('搜索历史记录出错:', error)
        reject(`搜索历史记录出错: ${error}`)
      }
    })
  }

  /**
   * 生成唯一的历史记录 key
   */
  const generateHistoryKey = (): string => {
    return `trans_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`
  }

  return {
    initDB,
    addHistory,
    updateHistory,
    updateHistoryFields,
    getHistoryByKey,
    getAllHistory,
    getHistoryByPage,
    getHistoryByTypeAndPage,
    deleteHistory,
    clearAllHistory,
    generateHistoryKey,
    migrateGuestHistory,
    searchHistoryByQueryAndType
  }
}
